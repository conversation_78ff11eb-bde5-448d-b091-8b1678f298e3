include:
  - project: 'platforms/magento/tools/gitlab-ci'
    ref: flow
    file: '.gitlab-ci-template.yml'
  - template: Security/SAST.gitlab-ci.yml

variables:
  BRANCHER_HOST: <EMAIL>

'Static Content: Magento/backend':
  extends: .static-content
  variables:
    LANGUAGE: en_US nl_NL
    THEME: Magento/backend

'Static Content: Life/default':
  extends: .static-content
  variables:
    LANGUAGE: en_US nl_NL de_DE
    THEME: Life/default

'Static Content: Life/showroom':
  extends: .static-content
  variables:
    LANGUAGE: en_US nl_NL
    THEME: Life/showroom

'Package Artifact':
  extends: .package-artifact
  needs:
    - !reference [.package-artifact, needs]
    - 'Static Content: Magento/backend'
    - 'Static Content: Life/default'
    - 'Static Content: Life/showroom'
  dependencies:
    - Create source package
    - DI Compile
    - 'Static Content: Magento/backend'
    - 'Static Content: Life/default'
    - 'Static Content: Life/showroom'

'Deploy review':
  script:
    - !reference ['.hypernode-brancher-deploy', 'script']
    - SHOWROOM_HOST="showroom.$(cat brancher-environment.json | jq -r '.host')"
    - ssh $HOST "hypernode-manage-vhosts --https --force-https --varnish ${SHOWROOM_HOST}"
    - ssh $HOST "/data/web/magento2/current/bin/magento config:set -e --scope=website --scope-code=showroom web/unsecure/base_url https://${SHOWROOM_HOST}/"
    - ssh $HOST "/data/web/magento2/current/bin/magento app:config:import"

'Test review':
  variables:
    GIT_STRATEGY: clone
    GIT_SUBMODULE_STRATEGY: recursive
    NO_COOKIEBOT: 1

'Deploy staging':
  extends: .deploy-source
  rules: !reference ['.rules', 'staging']
  environment:
    name: staging
    url: https://staginglife.hypernode.io
  variables:
    HOST: <EMAIL>
    DEPLOY_ROOT: /data/web/magento2
    PUBLIC_ROOT: /data/web/public
    CLEAR_OPCACHE: ''

'Test staging':
  extends: .test-e2e
  variables:
    GIT_STRATEGY: clone
    GIT_SUBMODULE_STRATEGY: recursive
    WEBSITE_URL: !reference ['Deploy staging', 'environment', 'url']
  needs:
    - Deploy staging

'Deploy production':
  extends: .deploy-source
  rules: !reference ['.rules', 'deploy']
  needs:
    - job: Package Artifact
    - job: Deploy staging
    - job: Test staging
      optional: true
  environment:
    name: production
    url: https://life.hypernode.io
  variables:
    HOST: <EMAIL>
    DEPLOY_ROOT: /data/web/magento2
    PUBLIC_ROOT: /data/web/public
    CLEAR_OPCACHE: ''
  when: manual
