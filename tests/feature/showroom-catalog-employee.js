const { Given, When, Then, Before } = require('@cucumber/cucumber');
const assert = require('assert');
const { COMMON_SELECTORS } = require('./common-steps');
const { login } = require('./helpers');

const BASE_URL = process.env.WEBSITE_URL.replace(/\/$/, '');
const LOGIN_PAGE_URL = `${BASE_URL}/customer/account/login`;

const SELECTORS = {
    ...COMMON_SELECTORS,
    productLink: '.products .item a',
    productWrapper: '.product-wrapper',
    searchInput: '.search-input',
    noResults: '.no-results'
};

Before(async function () {
    // Seed and log in as employee
    this.users = this.users || {};
    this.users.employee = { username: 'employee1', password: 'Test123!' };
    await page.goto(LOGIN_PAGE_URL, { waitUntil: 'networkidle' });
    await login(this.users.employee.username, this.users.employee.password);
});

Given('Employee is authorized in Showroom', async function () {
    // Already logged in via Before hook
});

When('the Employee visits the catalog page', async function () {
    await page.goto(`${BASE_URL}/showroom/catalog`, { waitUntil: 'networkidle' });
    await page.waitForSelector(COMMON_SELECTORS.productCard);
});

When('the Employee search for {string}', async function (slug) {
    await page.fill(SELECTORS.searchInput, slug);
    await page.press(SELECTORS.searchInput, 'Enter');
});

Then('product {string} should be visible', async function (slug) {
    const count = await page.locator(`${COMMON_SELECTORS.productCard}:has-text("${slug}")`).count();
    assert.strictEqual(count, 1);
});

Then('the Employee see empty result Search Page', async function () {
    const empty = await page.locator(SELECTORS.noResults).isVisible();
    assert.strictEqual(empty, true);
})
