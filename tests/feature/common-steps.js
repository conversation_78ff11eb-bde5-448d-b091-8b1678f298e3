const {Given, When, Then} = require('@cucumber/cucumber');
const assert = require('assert');
const COMMON_SELECTORS = {
    usernameInput: '#username',
    passwordInput: '#pass',
    loginButton: "button[name='send']",
    productCard: '.products .item',
    employeeLoggedIn: '.employee-logged-in',
    searchInput: '#product-search',
    searchButton: '#maincontent > div.columns > div > header > div > div.max-w-lg.mx-auto.py-14 > search > form > button'
};

Given('an Employee exists', function () {
    if (!this.users) {
        this.users = {};
    }

    if (!this.users.employee) {
        this.users.employee = {
            username: 'employee1', password: 'Test123!'
        };
    }
});
Given('an Dealer exists', function () {
    if (!this.users) {
        this.users = {};
    }
    if (!this.users.dealer) {
        this.users.dealer = {
            username: 'dealer1',
            password: 'Test123!'
        };
    }
});
Given('Product {int} exist', async function (productNumber) {
    this.numProducts = await page.locator(COMMON_SELECTORS.productCard).count();
});
Given('the Dealer visits the category {string}', async function (slug) {
    await page.goto(`${process.env.WEBSITE_URL}/nl/${slug}`, { waitUntil: 'networkidle' });
});
When('the Dealer checks for products', async function () {
    await page.waitForSelector(COMMON_SELECTORS.productCard);
});
When('the Dealer searches for {string}', async function (searchString) {
    await page.fill(COMMON_SELECTORS.searchInput, searchString);
    await page.click(COMMON_SELECTORS.searchButton);
});
// Export selectors for reuse
module.exports = {
    COMMON_SELECTORS
};
