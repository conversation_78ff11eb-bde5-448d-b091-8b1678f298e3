const { Given, When, Then, Before } = require('@cucumber/cucumber');
const { expect } = require('@playwright/test');
const { COMMON_SELECTORS } = require('./common-steps');
const { login } = require('./helpers');

const BASE_URL = process.env.WEBSITE_URL.replace(/\/$/, '');
const LOGIN_PAGE_URL = `${BASE_URL}/customer/account/login`;

const SELECTORS = {
    ...COMMON_SELECTORS,
    customerSelectionLink: 'a[href*="/showroom/customer/selection"]'
};

Before(async function () {
    // Seed dealer and log in
    this.users = this.users || {};
    this.users.dealer = { username: 'dealer1', password: 'Test123!' };
    await page.goto(LOGIN_PAGE_URL, { waitUntil: 'networkidle' });
    await login(this.users.dealer.username, this.users.dealer.password);
});

Given('Dealer goes to the Showroom Main Page', async function () {
    await page.goto(BASE_URL, { waitUntil: 'networkidle' });
});

When('Dealer clicks Customer Selection Page Link', async function () {
    await page.click(SELECTORS.customerSelectionLink);
});

Then('Dealer is redirected to the Showroom Main Page', async function () {
    const current = await page.url();
    expect(current).toBe(BASE_URL);
});
