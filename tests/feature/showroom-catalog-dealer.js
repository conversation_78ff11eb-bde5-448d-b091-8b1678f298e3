const {Given, When, Then, Before} = require('@cucumber/cucumber');
const {expect} = require('@playwright/test');
const {login} = require('./helpers');
const {COMMON_SELECTORS} = require('./common-steps');
const MAIN_PAGE = process.env.WEBSITE_URL;
Before(async function () {
    this.users = this.users || {};
    this.users.dealer = this.users.dealer || {
        username: 'dealer1',
        password: 'Test123!'
    };
});
Given('Dealer is authorized in Showroom', async function () {
    await page.goto(`${MAIN_PAGE}/customer/account/login`, {waitUntil: 'networkidle'});
    await login(this.users.dealer.username, this.users.dealer.password);
    await page.waitForLoadState('networkidle');
});
Given('the Dealer visits home page', async function () {
    await page.goto(MAIN_PAGE, {waitUntil: 'networkidle'});
});
Given('the Dealer visits a product detail page for {string}', async function (slug) {
    await page.goto(`${MAIN_PAGE}/nl/${slug}`, {waitUntil: 'networkidle'});
});
When('the Dealer selects a product', async function () {
    await page.locator('.products .item a').first().click();
    await page.waitForLoadState('networkidle');
});
When('the Dealer checks the price of the product', async function () {
    this.price = await page.locator('.product-wrapper span.price');
});
Then('products should be visible without prices', async function () {
    const numProducts = await page.locator(COMMON_SELECTORS.productCard).count();
    expect(numProducts).toBeGreaterThan(0);
    const productsWithoutPrices = await page.locator(COMMON_SELECTORS.productCard).evaluateAll(cards =>
        cards.every(card => !card.querySelector('.product-wrapper span.price'))
    );
    expect(productsWithoutPrices).toBe(true);
});
Then('the price should not be visible for Dealer', async function () {
    const priceVisible = await this.price.isVisible();
    expect(priceVisible).toBe(false);
});
Then('product {string} should be visible without prices for Dealer', async function (slug) {
    await page.waitForSelector(`#product-list .product-card[data-slug="${slug}"]`);
    const productVisible = await page.locator(`#product-list .product-card[data-slug="${slug}"]`).isVisible();
    expect(productVisible).toBe(true);
    const productPriceVisible = await page.locator(`#product-list .product-card[data-slug="${slug}"] .product-wrapper span.price`).isVisible();
    expect(productPriceVisible).toBe(false);
});
Then('the Dealer see empty result Search Page', async function () {
    const noResultsSelector = '.no-results-message';
    await page.waitForSelector(noResultsSelector);
    const isVisible = await page.isVisible(noResultsSelector);
    expect(isVisible).toBe(true);
});
