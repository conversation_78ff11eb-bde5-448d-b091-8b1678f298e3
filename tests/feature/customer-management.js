// customer-management.js
const { Given, When, Then, Before, setDefaultTimeout } = require('@cucumber/cucumber');
const { expect } = require('@playwright/test');

setDefaultTimeout(300000); // 5-minute timeout

const BASE_URL = process.env.WEBSITE_URL.replace(/\/$/, '');
const LOGIN_PAGE_URL = `${BASE_URL}/customer/account/login`;

const PAGES = {
    CART:             `${BASE_URL}/checkout/cart/index`,
    CUSTOMER_SELECTION: `${BASE_URL}/showroom/customer/selection/index/`,
    CUSTOMER_CREATE:    `${BASE_URL}/showroom/customer/selection/create/`,
    CUSTOMER_INFORMATION: new RegExp(`^${BASE_URL}/showroom/customer/selection/details/customer/\\d+`),
    QUOTE_SELECTION:   `${BASE_URL}/showroom/quote/selection/index/`
};

const SELECTORS = {
    usernameInput: '#username',
    passwordInput: '#pass',
    loginButton: "button[name='send']",
    saveLocationButton: '#save-location',
    chooseCustomerButton: [
        "button:has-text('Change Customer')",
        "button:has-text('Wijzig klant')",
        "button[data-action='change-customer']"
    ].join(','),
    assignCustomerButton: '#assign-customer, button:has-text("Wijzig klant"), button[data-action="update-customer"]',
    customerSearchInput: '#customer-search',
    searchButton: 'button[type="submit"][form="customer-search-form"]',
    customerResultsList: 'output[name="customer-search-results"] ul',
    customerResultItem: 'output[name="customer-search-results"] li',
    createCustomerButton: 'a:has-text("Create new Customer"), a:has-text("Nieuwe klant aanmaken")',
    saveButton: '#create-customer-form button',
    chooseQuoteButton: '#header nav a:has-text("Parked"), a:has-text("Geparkeerd")',
    quoteList: '#maincontent div.columns details ul',
    quoteItem: '#maincontent div.columns details ul li',
    quoteFilters: {
        employeeUsername: '#filter-employee',
        shopName: '#filter-shop',
        customerEmail: '#filter-customer',
        dateCreation: '#filter-date'
    },
    customerAttachmentIndicators: 'button:has-text("Remove Customer"), button:has-text("Verwijder klant"), .customer-info',
    updateIndicators: '.updated-fields, .success-message, .alert-success'
};

const creationSelectors = {
    'Name': '#billing_firstname',
    'Surname': '#billing_lastname',
    'Email Address': '#email',
    'Shipping Address': '#shipping-address',
    'Shipping Address with Phone Number': '#shipping-address',
    'Phone Number': '#billing_phone',
    'Billing Address': '#billing_street'
};

Before({tags: '@customer-mgmt' }, async function () {
    // Seed a test employee
    this.users = { employee: { username: 'employee1', password: 'Test123!' } };
    // Log in
    await page.goto(LOGIN_PAGE_URL, { waitUntil: 'networkidle' });
    await page.fill(SELECTORS.usernameInput, this.users.employee.username);
    await page.fill(SELECTORS.passwordInput, this.users.employee.password);
    await page.click(SELECTORS.loginButton);
    // Accept location dialog
    await page.click(SELECTORS.saveLocationButton);
    await page.waitForURL(/.*/, { waitUntil: 'networkidle' });
});

/* ─── BUTTON PRESSES ───────────────────────────────────────────────────────── */

When('the employee presses the {string} button', async (buttonName) => {
    switch (buttonName) {
        case 'Change Customer':
            await page.click(SELECTORS.chooseCustomerButton);
            break;
        case 'Create new Customer':
            await page.click(SELECTORS.createCustomerButton);
            break;
        case 'Save':
            await page.click(SELECTORS.saveButton);
            break;
        default:
            throw new Error(`Unknown button: ${buttonName}`);
    }
});
Given('the employee presses the {string} button in the Menu', async (buttonName) => {
    if (buttonName === 'Choose Quote') {
        await page.click(SELECTORS.chooseQuoteButton);
    } else {
        throw new Error(`Unknown Menu button: ${buttonName}`);

});

/* ─── CART / CUSTOMER SELECTION ─────────────────────────────────────────────── */

Given('the employee navigates to the Cart Page', async () => {
    await page.goto(PAGES.CART, { waitUntil: 'networkidle' });
    await page.waitForSelector('#maincontent', { state: 'visible' });
});

Then('the employee sees a pop-up screen with search options by Customer Postcode, Phone, or Email', async () => {
    await page.waitForSelector(SELECTORS.customerSearchInput, { timeout: 5000 });
});

Then('the following information about the customer is shown:', async (dataTable) => {
    await page.waitForSelector(SELECTORS.customerResultsList, { timeout: 5000 });
    const pageText = await page.content();
    dataTable.hashes().forEach(({ Field, Description }) => {
        expect(pageText).toContain(Field);
        expect(pageText).toContain(Description);
    });
});

When('the employee selects a customer by pressing on it', async () => {
    await page.click(SELECTORS.customerResultItem);
});

Then('the selected customer is attached to the quote', async () => {
    await page.waitForSelector(SELECTORS.customerAttachmentIndicators, { timeout: 5000 });
});

/* ─── CUSTOMER CREATION ────────────────────────────────────────────────────── */

Given('the employee navigates to the Customer Selection Page', async () => {
    await page.goto(PAGES.CUSTOMER_SELECTION, { waitUntil: 'networkidle' });
    await page.waitForSelector('#maincontent', { state: 'visible' });
});

Given('the employee searches for a customer', async () => {
    await page.waitForSelector(SELECTORS.customerSearchInput, { timeout: 30000 });
    await page.fill(SELECTORS.customerSearchInput, '819273');
    await page.click(SELECTORS.searchButton);
    await page.waitForTimeout(2000);
});

Then('the employee can see the "Create new Customer" button', async () => {
    await page.waitForSelector(SELECTORS.createCustomerButton);
});

Then('the employee is redirected to the Customer Creation Page', async () => {
    await page.waitForURL(PAGES.CUSTOMER_CREATE);
});

Then('the employee can add all typical fields, with the following required:', async (dataTable) => {
    for (const { Field, Description } of dataTable.hashes()) {
        const selector = creationSelectors[Field];
        await page.fill(selector, Description);
    }
});

When('the employee presses the "Save" button', async () => {
    await page.click(SELECTORS.saveButton);
});

Then('the employee is redirected to the Customer Information Page', async () => {
    await page.waitForURL(PAGES.CUSTOMER_INFORMATION);
});

/* ─── CUSTOMER UPDATE ──────────────────────────────────────────────────────── */

Given('the employee finds a customer using the Customer Selection Page', async () => {
    await page.goto(PAGES.CUSTOMER_SELECTION, { waitUntil: 'networkidle' });
    await page.fill(SELECTORS.customerSearchInput, 'cucumber');
    await page.click(SELECTORS.searchButton);
    await page.waitForTimeout(2000);
});

Given('the employee presses on the chosen customer and is redirected to the Customer Information Page', async () => {
    await page.click(SELECTORS.customerResultItem);
    await page.waitForURL(PAGES.CUSTOMER_INFORMATION);
});

When('the employee edits the customer fields, with the following required:', async (dataTable) => {
    for (const { Field, Description } of dataTable.hashes()) {
        const selector = creationSelectors[Field];
        await page.fill(selector, Description);
    }
});

Then('the employee is redirected to the Customer Information Page with updated information', async () => {
    await page.waitForURL(PAGES.CUSTOMER_INFORMATION);
});

/* ─── QUOTE SWITCHING ──────────────────────────────────────────────────────── */

Then('the employee is redirected to the Quote Selection Page', async () => {
    await page.waitForURL(PAGES.QUOTE_SELECTION);
    await page.waitForSelector('#maincontent');
});

Then('only quotes without orders are shown', async () => {
    const items = await page.locator(SELECTORS.quoteItem).count();
    expect(items).toBeGreaterThan(0);
});

Then('the following information about each quote is displayed:', async (dataTable) => {
    const pageText = await page.content();
    dataTable.hashes().forEach(({ Field }) => {
        expect(pageText).toContain(Field);
    });
});

Then('the employee can filter quotes by Employee Username, Shop Name, Customer Email, or Date of Creation', async () => {
    await page.waitForSelector(Object.values(SELECTORS.quoteFilters).join(', '));
});

Then('the employee selects a quote by pressing on it', async () => {
    await page.click(SELECTORS.quoteItem);
});
