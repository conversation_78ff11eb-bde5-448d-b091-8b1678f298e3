// showroom-login.js
const {Given, When, Then, Before} = require('@cucumber/cucumber');
const {expect} = require('@playwright/test');
const {login, verifyErrorDisplayed} = require('./helpers');
const BASE_URL = process.env.WEBSITE_URL.replace(/\/$/, '');
const LOGIN_PAGE_URL = `${BASE_URL}/customer/account/login`;
const MAIN_PAGE_URL = `${BASE_URL}/showroom/main`;

Before(async function () {
    // Seed users
    this.users = this.users || {};
    this.users.employee = {username: 'employee1', password: 'Test123!'};
    this.users.dealer = {username: 'dealer1', password: 'Test123!'};
    this.users.customer = {username: 'cust1', password: 'WrongPass'};
});

Given('an Employee exists', async function () {
    // Already seeded in Before
});

Given('a Dealer exists', async function () {
    // Already seeded in Before
});

Given('a Customer exists', async function () {
    // Already seeded in Before
});

Given('Employee goes to the Showroom website Login Page', async function () {
    await page.goto(LOGIN_PAGE_URL, {waitUntil: 'networkidle'});
});

When('Employee enters the correct username and correct password', async function () {
    await login(this.users.employee.username, this.users.employee.password);
});

Then('Employee is redirected to the Main Page of the Showroom website', async function () {
    expect(await page.url()).toBe(MAIN_PAGE_URL);
});

When('Employee clicks the Logout button', async function () {
    await page.click('#logout');
});

Then('Employee is redirected to the Showroom website Login Page', async function () {
    expect(await page.url()).toBe(LOGIN_PAGE_URL);
});

When('Dealer enters the correct username and correct password', async function () {
    await login(this.users.dealer.username, this.users.dealer.password);
});

Then('Dealer is redirected to the Main Page of the Showroom website', async function () {
    expect(await page.url()).toBe(MAIN_PAGE_URL);
});

When('Customer enters the correct username and correct password', async function () {
    await login(this.users.customer.username, this.users.customer.password);
});

Then('Customer can see an error', async function () {
    await verifyErrorDisplayed();
});

When('Employee enters a wrong username and correct password', async function () {
    await login('wrong_user', this.users.employee.password);
});

When('Employee enters the correct username and wrong password', async function () {
    await login(this.users.employee.username, 'WrongPass');
});

When('Employee enters their email instead of username and correct password', async function () {
    await login(this.users.employee.username + '@example.com', this.users.employee.password);
});

Then('Employee can see an error', async function () {
    await verifyErrorDisplayed();
});
