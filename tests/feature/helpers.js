module.exports = {
    /**
     * Logs in with the given credentials.
     * @param {string} username
     * @param {string} password
     */
    async login(username, password) {
        await page.waitForSelector('#username', {timeout: 60000});
        await page.fill('#username', username);
        await page.fill('#pass', password);
        await Promise.all([
            page.waitForNavigation({waitUntil: 'networkidle'}),
            page.click('button[type="submit"]')
        ]);
    },

    /**
     * Verifies an error message is displayed.
     */
    async verifyErrorDisplayed() {
        const errorVisible = await page.locator('#messages > div span').isVisible();
        if (!errorVisible) {
            throw new Error('Expected an error message to be shown');
        }
    }
};
