const assert = require('assert');
const { Given, When, Then } = require('@cucumber/cucumber');

Given('the visitor visits the category {string}', async function (slug) {
  this.response = await page.goto(`${process.env.WEBSITE_URL}/nl/${slug}`);
});

When('the visitor checks for products', async function () {});

When('the visitor selects a product', async function () {
  await page.locator('.products .item a').first().click();
});

Then(
  'the visitor should be directed to the product detail page',
  async function () {
    const count = await page.locator('.catalog-product-view').count();

    assert.strictEqual(1, count);
  },
);

Given(
  'the visitor visits a product detail page for {string}',
  async function (slug) {
    this.response = await page.goto(`${process.env.WEBSITE_URL}/nl/${slug}`);
  },
);

When('the visitor checks the price of the product', async function () {
  this.price = await page.locator('.product-wrapper span.price');
});
