<?xml version="1.0"?>
<page
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd"
>
    <body>
        <referenceBlock name="category.products.list">
            <block
                class="Magento\LayeredNavigation\Block\Navigation\State"
                name="product.list.active.filters"
                template="Hyva_SmileElasticsuite::swatch/product/listing/filter.phtml"
            />
        </referenceBlock>

        <referenceContainer name="before.body.end">
            <block
                name="product.list.active.filters.ui"
                template="Hyva_SmileElasticsuite::swatch/product/listing/filter-ui.phtml"
            />
        </referenceContainer>
    </body>
</page>
