<?php

/**
 * @var \Amasty\Mostviewed\Block\Widget\Related $block
 * @var \Magento\Framework\Escaper $escaper
 * @var \Hyva\Theme\Model\ViewModelRegistry $viewModels
 * @codingStandardsIgnoreFile
 */

use Hyva\Theme\ViewModel\SvgIcons;
use Life\ShowroomDeliveryDate\ViewModel\DeliveryDate;
use Life\ShowroomProduct\ViewModel\ProductSubtitle;
use Magento\Catalog\Block\Product\ReviewRendererInterface;
use Magento\Catalog\Pricing\Price\FinalPrice;

if (!$block->getProductCollection() || !$block->getProductCollection()->getSize()) {
    return;
}

/** @var SvgIcons $svgIcons */
$svgIcons = $viewModels->require(SvgIcons::class);
/** @var ProductSubtitle $productSubtitleViewModel */
$productSubtitleViewModel = $viewModels->require(ProductSubtitle::class);

/** @var DeliveryDate $deliveryDateViewModel */
$deliveryDateViewModel = $viewModels->require(DeliveryDate::class);

$type = 'widget-product-grid grid gap-4 md:grid-cols-2';

$mode = 'grid';

$image = 'related_products_content';
$items = $block->getProductCollection()->getItems();

$isSlider = $block->getBlockLayout();
$tagWrap = $isSlider ? 'div' : 'ol';
$tag = $isSlider ? 'div' : 'li';

$showWishlist = $block->getDisplayButtonWishlist();
$showCompare = $block->getDisplayButtonCompare();
$showCart = $block->getAddToCart();
$templateType = ReviewRendererInterface::SHORT_VIEW;
$description = false;
?>

<?php if ($isSlider) : ?>
    <div class="block <?= /* @noEscape */
    $block->getGroupId(); ?>">
        <?php if ($block->getTitle()) : ?>
            <div class="block-title">
                <strong><?= $escaper->escapeHtml(__($block->getTitle())) ?></strong>
            </div>
        <?php endif; ?>
    </div>
<?php else : ?>
<div class="container">
    <details>
        <summary>
            <?php if ($block->getTitle()) : ?>
                <?= $escaper->escapeHtml(__($block->getTitle())) ?>
            <?php endif; ?>
        </summary>
        <?php endif; ?>
        <div class="block-content">
            <div class="products-<?= /* @noEscape */
            $mode ?> <?= /* @noEscape */
            $mode ?>">
                <<?= /* @noEscape */
                $tagWrap ?> class="product-items <?= /* @noEscape */
                $type ?>" <?= /* @noEscape */
                $isSlider ? 'data-amrelated-js="slider"' : '' ?>>
                <?php $iterator = 1; ?>
                <?php foreach ($items as $item) : ?>
                    <?= /* @noEscape */
                    ($iterator++ == 1) ? '' : '</' . $tag . '>' ?><<?= /* @noEscape */
                    $tag ?> class="product-item relative isolate border border-secondary-900/50 rounded-xl overflow-clip bg-white">

                    <div class="product-item-info flex flex-col h-full">
                        <figure class="product-item-photo flex py-2 justify-center border-b border-secondary-900/50">
                            <img
                                src="<?= $escaper->escapeHtmlAttr($block->getImage($item, $image)->getImageUrl()) ?>"
                                alt="<?= $escaper->escapeHtml($item->getName()) ?>"
                                width="288"
                                height="128"
                                class="object-contain w-72 h-32"
                            />
                        </figure>
                        <div class="product-item-details flex flex-col grow p-6">
                            <a
                                href="<?= $escaper->escapeUrl($block->getProductUrl($item)) ?>"
                                class="product-item-link after:absolute after:inset-0"
                            >
                                <?= $escaper->escapeHtml($item->getName()) ?>
                            </a>

                            <ul class="flex flex-wrap gap-2 mt-1.5 text-sm text-primary/50">
                                <?= $productSubtitleViewModel->getSubtitle($item) ?>
                            </ul>

                            <div class="mt-auto pt-2 text-sm">
                                <span><?= $escaper->escapeHtml(__('Expected delivery date')) ?>:&nbsp;</span>
                                <span data-sku-delivery-date="<?= $item->getSku() ?>" class="font-bold text-red-500">
                                    <?=
                                    $item->getData('shipping_date') ?
                                        $deliveryDateViewModel->getLocaleDate($item->getData('shipping_date')) :
                                        $escaper->escapeHtml(__('No delivery date available'))
                                    ?>
                                </span>
                            </div>

                            <div class="mt-4 font-semibold">
                                <?= /* @noEscape */
                                $block->getProductPriceHtml($item, FinalPrice::PRICE_CODE) ?>
                            </div>

                            <?php if ($showWishlist || $showCompare || $showCart) : ?>
                                <div class="product-item-actions mt-5 relative z-10">
                                    <?php if ($showCart) : ?>
                                        <div class="actions-primary flex flex-col gap-2 md:flex-row">
                                            <?php if ($item->isSaleable()) : ?>
                                                <?php $qtyBlock = $block->getChildBlock('qty'); ?>
                                                <?php if ($qtyBlock): ?>
                                                    <?= $qtyBlock->setData('product', $item)->toHtml() ?>
                                                <?php endif; ?>
                                                <button
                                                    aria-label="<?= /* @noEscape */
                                                    __('Add to Cart') ?>"
                                                    class="btn btn-primary gap-2 action tocart primary grow"
                                                    data-addto="cart"
                                                    form="product_addtocart_form_<?= $item->getId() ?>"
                                                >
                                                    <?= /* @noEscape */
                                                    __('Add') ?>
                                                    <?= $svgIcons->addToCartHtml("", null, null, ['aria-hidden' => 'true']) ?>
                                                </button>
                                                <form method="post" id="product_addtocart_form_<?= $item->getId() ?>"
                                                      action="<?= $block->getAddToCartUrl($item) ?>">
                                                    <input type="hidden" name="product" value="<?= $item->getId() ?>"/>
                                                    <input type="hidden" name="item" value="<?= $item->getId() ?>"/>
                                                    <?= /** @noEscape */
                                                    $block->getBlockHtml('formkey') ?>
                                                    <input type="hidden" name="product" value="<?= $item->getId() ?>"/>
                                                </form>
                                            <?php else : ?>
                                                <?php if ($item->getIsSalable()) : ?>
                                                    <div class="stock available">
                                                        <span><?= $escaper->escapeHtml(__('In stock')) ?></span>
                                                    </div>
                                                <?php else : ?>
                                                    <div class="stock unavailable">
                                                        <span><?= $escaper->escapeHtml(__('Out of stock')) ?></span>
                                                    </div>
                                                <?php endif; ?>
                                            <?php endif; ?>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    <?= /* @noEscape */
                    ($iterator == count($items) + 1) ? '</' . $tag . '>' : '' ?>
                <?php endforeach ?>
            </<?= /* @noEscape */
            $tagWrap ?>>
        </div>
</div>
<?php if ($isSlider) : ?>
    </div>
<?php else : ?>
    </details>
    </div>
<?php endif; ?>
</div>
<?php if ($isSlider) : ?>
    <script type="text/javascript">
        require([
            "jquery",
            "Amasty_Base/vendor/slick/slick.min"
        ], function ($) {
            $('#amrelated-block-<?= /* @noEscape */ (int)$block->getGroupId();?> [data-amrelated-js="slider"]').slick(
                {
                    dots: true,
                    infinite: true,
                    slidesToShow: 4,
                    slidesToScroll: 1,
                    responsive: [
                        {
                            breakpoint: 1280,
                            settings: {
                                slidesToShow: 3,
                                slidesToScroll: 3
                            }
                        },
                        {
                            breakpoint: 992,
                            settings: {
                                slidesToShow: 2,
                                slidesToScroll: 2
                            }
                        },
                        {
                            breakpoint: 425,
                            settings: {
                                slidesToShow: 1,
                                slidesToScroll: 1
                            }
                        }
                    ]
                }
            );
        });
    </script>
<?php endif; ?>
