<?php

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\SvgIcons;
use Life\ShowroomDeliveryDate\ViewModel\DeliveryDate;
use Life\ShowroomProduct\ViewModel\ProductSerie;
use Magento\Catalog\Block\Product\View;
use Magento\Framework\Escaper;

/** @var ViewModelRegistry $viewModels */
/** @var SvgIcons $svgIcons */
/** @var Escaper $escaper */
$svgIcons = $viewModels->require(SvgIcons::class);
$serieValue = $viewModels->require(ProductSerie::class);

/** @var View $block */

$product = $block->getProduct();

/** @var DeliveryDate $deliveryDateViewModel */
$deliveryDateViewModel = $viewModels->require(DeliveryDate::class);
$deliveryDate = $product->getData('shipping_date') ?
    $deliveryDateViewModel->getLocaleDate($product->getData('shipping_date')) :
    $escaper->escapeHtml(__('No delivery date available'));
?>
<section class="container">
    <div class="bg-primary p-6 rounded-xl">
        <div class="flex flex-wrap items-center gap-x-7 gap-y-4 mb-6 pb-2 border-b border-white/20 text-white text-xs">
            <a href="<?=  $escaper->escapeUrl($block->getBaseUrl()) ?>" class="inline-flex gap-2 items-center">
                <?= $svgIcons->arrowLeftHtml("", 20, 20, ['aria-hidden' => 'true']) ?>
                <span><?= $escaper->escapeHtml(__('Articles')) ?></span>
            </a>
            <span class="text-primary-100"><?= $serieValue->getSubtitle($product) ?></span>
        </div>
        <div class="bg-white p-4 rounded-xl grid grid-rows-auto grid-cols-1 md:gap-x-6 md:grid-cols-[42%_minmax(0,_1fr)] md:grid-rows-[min-content_minmax(0,_1fr)] lg:gap-x-10 lg:grid-cols-2 w-full">
            <?= $block->getChildHtml('product.media') ?>
            <div class="md:grid md:self-center md:items-center md:row-start-1 md:row-span-2">
                <?= $block->getChildHtml('product.title') ?>
                <div
                    x-data="productDeliveryDate()"
                    x-init="getDeliveryDate()"
                    class="flex flex-wrap mt-1.5 text-sm"
                    aria-live="polite"
                >
                    <span><?= $escaper->escapeHtml(__('Expected delivery date')) ?>:&nbsp;</span>
                    <span
                        class="font-bold"
                        x-text="deliveryDate || '<?= $deliveryDate ?>'"
                    ><?= $deliveryDate ?></span>
                </div>
                <script defer>
                    'use strict';
                    function productDeliveryDate() {
                        return {
                            deliveryDate: null,
                            getDeliveryDate() {
                                const relatedProducts = document.querySelectorAll('[data-sku-delivery-date]');
                                const skus = [
                                    {
                                        sku: '<?= $product->getSku(); ?>',
                                        quantity: 1
                                    }
                                ]
                                const token = JSON.parse(localStorage.getItem('mage-cache-storage'))?.customer?.signin_token;
                                const headers = new Headers();
                                
                                headers.append("Content-Type", "application/json");
                                headers.append("Authorization", `Bearer ${token}`);
                                
                                for (const product of relatedProducts) {
                                    skus.push({
                                        sku: product.dataset.skuDeliveryDate,
                                        quantity: 1
                                    })
                                }

                                <?php // TODO: body needs to be checked when real data is available ?>
                                const body = JSON.stringify({
                                    "queryData": '{"products": ' + JSON.stringify(skus) + '}'
                                });

                                const requestOptions = {
                                    method: "POST",
                                    headers,
                                    body,
                                };

                                fetch("<?= $block->getBaseUrl(); ?>rest/V1/showroom/delivery_date/prepare", requestOptions)
                                .then((response) => response.json())
                                .then(([result]) => {
                                    const currentDate = new Date();

                                    for (const [sku, date] of Object.entries(result.products)) {
                                        if (sku != '<?= $product->getSku(); ?>') {
                                            const span = document.querySelector(`[data-sku-delivery-date="${sku}"]`);
                                            span.innerText = date;

                                            if (new Date(date) > currentDate) {
                                                dateNode.classList.remove('text-red-500');
                                                dateNode.classList.add('tertiary');
                                            } else if (new Date(date) === currentDate) {
                                                dateNode.classList.remove('text-red-500');
                                                dateNode.classList.add('primary');
                                            }
                                        } else {
                                            this.deliveryDate = date;
                                        };
                                    }
                                })
                                .catch((error) => console.error(error));
                            }
                        }
                    }
                </script>
                <?= $block->getChildHtml('product.info') ?>
            </div>
        </div>
    </div>
</section>
<section>
    <?= $block->getChildHtml('product_options_wrapper_bottom') ?>
</section>

<?= $block->getChildHtml('product.info.details'); ?>

<section class="mb-2">
    <?= $block->getChildHtml('product.info.details.after') ?>
</section>
