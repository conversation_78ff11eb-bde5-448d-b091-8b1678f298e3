<?php

declare(strict_types=1);

use Magento\Framework\Escaper;
use Magento\Catalog\Helper\Output as CatalogOutputHelper;
use Magento\Catalog\Block\Product\View\Attributes;
use Magento\Catalog\Model\Product;

// phpcs:disable Magento2.Templates.ThisInTemplate.FoundThis
// phpcs:disable Magento2.Templates.ThisInTemplate.FoundHelper

/** @var Escaper $escaper */
/** @var CatalogOutputHelper $output */
/** @var Attributes $block */


/** @var CatalogOutputHelper $catalogOutputHelper */
$catalogOutputHelper = $this->helper(CatalogOutputHelper::class);

/** @var Product $product */
$product = $block->getProduct();
?>

<?php if ($attributes = $block->getAdditionalData()): ?>
    <div class="table-wrapper bg-white rounded-xl p-6 overflow-x-auto" id="product-attributes">
        <table class="additional-attributes w-full">
            <?php foreach ($attributes as $attribute): ?>
                <tr class="group text-sm leading-8 border-b border-secondary-900/50">
                    <th class="col label w-1/2 text-left font-normal product-attribute-label py-3 group-first:pt-0"
                        scope="row"><?= $escaper->escapeHtml($attribute['label']) ?></th>
                    <td class="col data w-1/2 pl-2 text-right opacity-50 product-attribute-value py-3 group-first:pt-0"
                        data-th="<?= $escaper->escapeHtmlAttr($attribute['label']) ?>"
                    ><?= /* @noEscape */
                     $catalogOutputHelper->productAttribute($product, $attribute['value'], $attribute['code']) ?></td>
                </tr>
            <?php endforeach; ?>
        </table>
    </div>
<?php endif;?>
