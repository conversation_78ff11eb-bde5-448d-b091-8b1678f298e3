<?php

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsOutline;
use Hyva\Theme\ViewModel\HeroiconsSolid;
use Hyva\Theme\ViewModel\BlockJsDependencies;
use Hyva\Theme\ViewModel\ProductCompare;
use Hyva\Theme\ViewModel\ProductListItem;
use Hyva\Theme\ViewModel\ProductPage;
use Hyva\Theme\ViewModel\ProductStockItem;
use Hyva\Theme\ViewModel\SvgIcons;
use Hyva\Theme\ViewModel\Wishlist;
use Life\ShowroomDeliveryDate\ViewModel\DeliveryDate;
use Magento\Catalog\Block\Product\AbstractProduct;
use Magento\Catalog\Helper\Output as CatalogOutputHelper;
use Magento\Catalog\ViewModel\Product\OptionsData as ProductOptionsData;
use Magento\Framework\Escaper;

// phpcs:disable Magento2.Templates.ThisInTemplate.FoundThis
// phpcs:disable Magento2.Templates.ThisInTemplate.FoundHelper
// phpcs:disable Generic.Files.LineLength.TooLong

/** @var AbstractProduct $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */

/** @var ProductPage $productViewModel */
$productViewModel = $viewModels->require(ProductPage::class);

/** @var ProductListItem $productListItemViewModel */
$productListItemViewModel = $viewModels->require(ProductListItem::class);

/** @var CatalogOutputHelper $catalogOutputHelper */
$catalogOutputHelper = $this->helper(CatalogOutputHelper::class);

/** @var ProductCompare $compareViewModel */
$compareViewModel = $viewModels->require(ProductCompare::class);
/** @var Wishlist $wishlistViewModel */
$wishlistViewModel = $viewModels->require(Wishlist::class);

/** @var HeroiconsOutline $heroicons */
$heroicons = $viewModels->require(HeroiconsOutline::class);

/** @var HeroiconsSolid $heroiconsSolid */
$heroiconsSolid = $viewModels->require(HeroiconsSolid::class);

$productOptionsViewmodel = $viewModels->require(ProductOptionsData::class);

/** @var SvgIcons $svgIcons */
$svgIcons = $viewModels->require(SvgIcons::class);

/** @var Magento\Catalog\Model\Product $product */
if (! ($product = $block->getData('product'))) {
    return;
}
$imageDisplayArea = $block->getData('image_display_area');
$templateType = $block->getData('template_type');
$viewMode = $block->getData('view_mode');
$showDescription = $block->getData('show_description');
$showAddToWishlist = $wishlistViewModel->isEnabled();
$showAddToCompare = $compareViewModel->showInProductList();
$viewIsGrid = $viewMode === 'grid';
$productType = $product->getTypeId();
$isProductGroupedOrBundle = $productType === 'bundle' || $productType === "grouped";
$productId = $product->getId();
$options   = $productOptionsViewmodel->getOptionsData($product);

$hideDetails       = $block->getData('hide_details') ?: false;
$hideRatingSummary = $block->getData('hide_rating_summary') ?: false;

$imageCustomAttributes = $product->getData('image_custom_attributes')
    ?? $block->getData('image_custom_attributes')
    ?? [];
$productName = $catalogOutputHelper->productAttribute($product, $product->getName(), 'name');

// Ensure the required JS is rendered on the page
$viewModels->require(BlockJsDependencies::class)->setBlockTemplateDependency($block, 'Magento_Catalog::product/list/js/price-box.phtml');

/** @var ProductStockItem $stockItemViewModel */
$stockItemViewModel = $viewModels->require(ProductStockItem::class);
$minSalesQty = $stockItemViewModel->getMinSaleQty($product);
$maxSalesQty = $stockItemViewModel->getMaxSaleQty($product);
$defaultQty = $block->getProductDefaultQty() * 1;

$step = $stockItemViewModel->getQtyIncrements($product) ?: null;

/**
 * sets minimum and maximum values taking into account the values set in the admin,
 * but taking into account the value of Qty Increments
 */
if ($step) {
    $minSalesQty = ceil($minSalesQty / $step) * $step;
    $maxSalesQty = floor($maxSalesQty / $step) * $step;
    $defaultQty = ceil($defaultQty / $step) * $step;
}

$maxSalesQtyLength = ($maxSalesQty ? strlen((string)$maxSalesQty) : 4)
    + (/* add one if decimal for separator */
    (int)$stockItemViewModel->isQtyDecimal($product));

/** @var DeliveryDate $deliveryDateViewModel */
$deliveryDateViewModel = $viewModels->require(DeliveryDate::class);
?>

<?php if ($product->isSaleable()): ?>

<form
    method="post"
    action="<?= $escaper->escapeUrl($productViewModel->getAddToCartUrl($product, ['useUencPlaceholder' => true])) ?>"
    class="item product product-item product_addtocart_form card card-interactive flex flex-col max-w-full grow relative isolate border border-secondary-900/50 rounded-xl overflow-clip bg-white"
    <?php if ($product->getOptions()): ?>
    enctype="multipart/form-data"
    <?php endif; ?>
>
    <?= /** @noEscape */ $block->getBlockHtml('formkey') ?>
    <input type="hidden" name="product" value="<?= (int)$productId ?>"/>
    <?php foreach ($options as $optionItem): ?>
    <input type="hidden"
        name="<?= $escaper->escapeHtml($optionItem['name']) ?>"
        value="<?= $escaper->escapeHtml($optionItem['value']) ?>">
    <?php endforeach; ?>
<?php else: ?>
<div class="item product product-item card card-interactive relative isolate border border-secondary-900/50 rounded-xl overflow-clip bg-white">
<?php endif; ?>

<?php /* Product Image */ ?>
<figure class="product photo product-item-photo flex py-2 justify-center border-b border-secondary-900/50">
    <?=
        /* @phpstan-ignore-next-line */
        $block->getImage($product, $imageDisplayArea)
        ->setTemplate('Magento_Catalog::product/list/image.phtml')
        ->setData('custom_attributes', $imageCustomAttributes)
        ->setProductId($productId)
        ->setClass('w-72 h-32')
        ->setWidth(288)
        ->setHeight(128)
        ->setCustomAttributes(['loading' => 'lazy'])
        ->toHtml();
    ?>
</figure>
<div class="product-info flex flex-col grow p-6">
    <a
        class="product-item-link after:absolute after:inset-0"
        href="<?= $escaper->escapeUrl($product->getProductUrl()) ?>"
        :id="`slide-desc-<?= $escaper->escapeHtmlAttr($product->getId()) ?>-${$id('slider-id')}`"
    >
        <?= /* @noEscape */ $productName ?>
    </a>

    <?php if ($isProductGroupedOrBundle): ?>
        <span class="sr-only">
            <?= $escaper->escapeHtml(__('The price depends on the options chosen on the product page')) ?>
        </span>
    <?php endif; ?>

    <!-- TODO: add (subtitle) attributes -->
    <ul class="flex flex-wrap gap-2 mt-1.5 text-sm text-primary/50">
        <li>Pollywood</li>
        <li>Soltex grey</li>
        <li>L163 x B90 x H72 cm</li>
    </ul>

    <?php // delivery date ?>
    <div
        class="flex flex-wrap mt-auto pt-2 text-sm"
    >
        <span><?= $escaper->escapeHtml(__('Expected delivery date')) ?>:&nbsp;</span>
        <span data-sku-delivery-date="<?= $product->getSku() ?>" class="font-bold text-red-500">
            <?=
            $product->getData('shipping_date') ?
                $deliveryDateViewModel->getLocaleDate($product->getData('shipping_date')) :
                $escaper->escapeHtml(__('No delivery date available'))
            ?>
        </span>
    </div>

    <!-- TODO: missing currency symbol -->
    <div
        class="mt-4 font-semibold"
        x-data="initPriceBox()"
        x-defer="intersect"
        @update-prices-<?= (int)$productId ?>.window="updatePrice($event.detail);"
    >
        <?= /* @noEscape */ $productListItemViewModel->getProductPriceHtml($product) ?>
    </div>

    <div class="mt-5 relative z-10 flex flex-col gap-2 md:flex-row">
        <?php if ($product->isSaleable()): ?>
        <?php if ($isProductGroupedOrBundle): ?>
        <a
            href="<?= $escaper->escapeUrl($product->getProductUrl()) ?>"
            class="btn btn-primary gap-2 grow"
            aria-label="<?= $escaper->escapeHtmlAttr(__('Configure %1 on the product page', $productName)) ?>"
        >
            <span>
                <?= $escaper->escapeHtml(__('Configure')) ?>
            </span>
            <?= $heroicons->pencilAltHtml('', 24, 24, ['aria-hidden' => 'true']) ?>
        </a>
        <?php else: ?>
        <div class="flex" x-data="{ qty: 1 }">
            <?php $btn_base_class = 'inline-flex items-center justify-center w-12 aspect-square border-y border-secondary'; ?>
            <button
                @click.prevent="qty = Math.max(qty - 1, 1)"
                class="<?= $btn_base_class ?> border-l rounded-l-lg"
            >
                <?= $heroicons->minusHtml('', 18, 18, ['aria-hidden' => 'true']); ?>
                <span class="sr-only"><?= $escaper->escapeHtml(__('Subtract 1')) ?></span>
            </button>
            <label class="mb-0">
                <span class="sr-only">
                    <?= $escaper->escapeHtml(__('Quantity')) ?>
                </span>
                <input
                    aria-live="polite"
                    x-model="qty"
                    id="qty_<?= $product->getId() ?>"
                    name="qty"
                    <?php if ($stockItemViewModel->isQtyDecimal($product)): ?>
                    <?php else: ?>
                        type="number"
                        pattern="[0-9]{0,<?= /** @noEscape */
                        $maxSalesQtyLength ?>}"
                        inputmode="numeric"
                    <?php if ($minSalesQty): ?>min="<?= /** @noEscape */
                        $minSalesQty ?>"<?php endif; ?>
                    <?php if ($maxSalesQty): ?>max="<?= /** @noEscape */
                        $maxSalesQty ?>"<?php endif; ?>
                    <?php if ($step): ?>step="<?= /** @noEscape */
                        $step ?>"<?php endif; ?>
                    <?php endif; ?>
                    class="no-spin-buttons input-unstyled h-full w-12 aspect-square border-secondary text-center font-bold invalid:ring-2 invalid:ring-red-500"
                />
            </label>
            <button
                @click.prevent="qty++"
                class="<?= $btn_base_class ?> border-r rounded-r-lg"
            >
                <?= $heroicons->plusHtml('', 18, 18, ['aria-hidden' => 'true']); ?>
                <span class="sr-only"><?= $escaper->escapeHtml(__('Add 1')) ?></span>
            </button>
        </div>
        <button
            aria-label="<?= /* @noEscape */ __('Add to Cart') ?>"
            class="btn btn-primary gap-2 grow"
            data-addto="cart"
        >
            <span>
                <?= $escaper->escapeHtml(__('Add')) ?>
            </span>
            <?= $svgIcons->addToCartHtml("", null, null, ['aria-hidden' => 'true']) ?>
        </button>
        <?php endif; ?>

        <?php else: ?>
        <div class="w-auto justify-center <?= $viewIsGrid ? 'mr-auto' : 'mr-auto md:mr-0' ?>">
            <?= $block->getChildBlock('stockstatus')->setData('product', $product)->toHtml() ?>
        </div>
        <?php endif; ?>
    </div>
</div>
<?php if ($product->isSaleable()): ?>
</form>
<?php else: ?>
</div>
<?php endif; ?>
