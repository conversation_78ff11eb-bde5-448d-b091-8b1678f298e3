<?php

declare(strict_types=1);

/** @var Details $block */
/** @var Escaper $escaper */

use Magento\Catalog\Block\Product\View\Details;
use Magento\Framework\Escaper;

$titleRenderer = $block->getChildBlock('product.section.title.renderer');
$defaultTitleTemplate = $titleRenderer->getTemplate();

?>
<div class="container grid gap-2 py-2">
<?php
foreach ($block->getGroupSortedChildNames('detailed_info', '') as $sectionName) {
    $sectionBlock  = $block->getLayout()->getBlock($sectionName);
    $sectionHtml   = (string) $sectionBlock->toHtml();
    $titleTemplate = $sectionBlock->getData('title_template') ?? $defaultTitleTemplate;

    if (empty(trim($sectionHtml))) {
        continue;
    }
    ?>
    <section id="<?= $escaper->escapeHtmlAttr($sectionBlock->getNameInLayout()) ?>">
        <h2 class="sr-only">
            <?= $escaper->escapeHtml($sectionBlock->getTitle()) ?>
        </h2>
        <details open>
            <summary>
                <?=
                    $titleRenderer->setTemplate($titleTemplate)
                        ->assign('sectionBlock', $sectionBlock)
                        ->toHtml()
                ?>
            </summary>
            <div>
                <?= /** @noEscape  */ $sectionHtml ?>
            </div>
        </details>
    </section>
<?php } ?>
</div>
