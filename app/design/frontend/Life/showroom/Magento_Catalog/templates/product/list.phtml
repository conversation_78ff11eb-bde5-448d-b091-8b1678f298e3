<?php

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\CurrentCategory;
use Hyva\Theme\ViewModel\ProductListItem;
use Hyva\Theme\ViewModel\ProductPage;
use Hyva\Theme\ViewModel\SvgIcons;
use Life\ShowroomCatalog\ViewModel\CategoriesDoorway;
use Life\ShowroomCatalog\ViewModel\CustomerInfo;
use Magento\Catalog\Block\Product\ListProduct;
use Magento\Framework\Escaper;

/** @var ListProduct $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */
/** @var ProductPage $productViewModel */
/** @var CurrentCategory $currentCategoryViewModel */
/** @var SvgIcons $svgIcons */

$svgIcons = $viewModels->require(SvgIcons::class);

$productViewModel = $viewModels->require(ProductPage::class);
$productListItemViewModel = $viewModels->require(ProductListItem::class);
$currentCategoryViewModel = $viewModels->require(CurrentCategory::class);
$currentCustomerInfo = $viewModels->require(CustomerInfo::class);
$categoriesDoorway = $viewModels->require(CategoriesDoorway::class);
$categoriesDoorwayData = [];
$categoryName = '';
$categoryPath = '';
try {
    $categoriesDoorwayData = $categoriesDoorway->getCategoriesData($currentCategoryViewModel->get());
    $categoryName = $currentCategoryViewModel->get()->getName();
    $categoryPath = $currentCategoryViewModel->get()->getUrlPath();
} catch(Exception $e){
    //do nothing since exception happens on search page inside CurrentCategory viewModel
}

$eagerLoadImagesCount = (int)($block->getData('eager_load_images_count') ?? 3);
$productCollection = $block->getLoadedProductCollection();

?>

<header class="container mb-2">
    <div class="bg-primary text-white rounded-xl px-6 py-4">
        <?php if ($categoryPath) : ?>
            <div class="flex flex-wrap items-center gap-x-7 gap-y-4 mb-6 pb-2 border-b border-white/20 text-white text-xs">
                <a
                    href="<?= $escaper->escapeUrl($block->getBaseUrl()) ?>"
                    class="inline-flex items-center gap-x-2"
                >
                    <?= $svgIcons->arrowLeftHtml("", 20, 20, ['aria-hidden' => 'true']) ?>
                    <?= $escaper->escapeHtml(__('Back')) ?>
                </a>
                <span class="text-primary-100">
                    <?= $escaper->escapeHtml(__('Products')) ?>
                </span>
            </div>
        <?php else: ?>
            <div class="flex items-center gap-2 pb-2 border-b border-white/20 text-primary-100 text-xs">
                <span class="inline-flex gap-2">
                    <?= $escaper->escapeHtml(__('Logged in as')) ?>
                    <span class="text-white">
                        <?= $currentCustomerInfo->getCurrentUserName() ?>
                    </span>
                </span>
                <?= $block->getChildHtml('store_language_showroom') ?>
                <a href="<?= $block->getBaseUrl() . 'customer/account/logout' ?>">
                    <?= $svgIcons->signoutHtml("", 24, 24, ['aria-hidden' => 'true']) ?>
                </a>
            </div>
        <?php endif; ?>
        <div class="max-w-lg mx-auto py-14">
            <h1 class="heading-5 mb-6">
                <?=
                    $categoryPath ?
                    $categoryName :
                    $escaper->escapeHtml(__('Search a product'))
                ?>
            </h1>
            <?php if (!$categoryPath) : ?>
                <script>
                    function initSearchForm() {
                        return {
                            searchVal: '',
                            searching: false,
                            search() {
                                if (this.searchVal.trim().length < 3) return;
                                window.location.href = '<?= $block->getBaseUrl() ?>catalogsearch/result/?q=' + encodeURIComponent(this.searchVal);
                                this.searching = true;
                            },
                        }
                    }
                </script>
                <search x-data="initSearchForm()">
                    <form
                        @submit.prevent="search"
                        @input="searchVal = $event.target.value"
                        class="relative"
                    >
                        <label>
                            <span class="sr-only">
                                <?= $escaper->escapeHtml(__('Search for serie name or article number')) ?>
                            </span>
                            <input
                                id="product-search"
                                type="search"
                                inputmode="search"
                                required
                                minlength="3"
                                placeholder="<?= $escaper->escapeHtml(__('Search for serie name or article number')) ?>"
                                class="w-full !min-h-[48px] !pr-16 !rounded-[18.75rem]"
                                :disabled="searching"
                            >
                        </label>
                        <button
                            role="button"
                            class="inline-flex pr-6 pl-4 items-center justify-center absolute right-0 top-1/2 -translate-y-1/2 h-12 text-black"
                            :disabled="searching"
                        >
                            <?= $svgIcons->searchHtml("", 24, 24, ['aria-hidden' => 'true']) ?>
                            <span class="sr-only">
                                <?= $escaper->escapeHtml(__('Search')) ?>
                            </span>
                        </button>
                    </form>
                </search>
            <?php endif; ?>
        </div>
    </div>
</header>
<?php if (!empty($categoriesDoorwayData)): ?>
    <section class="container mb-2">
        <details open>
            <summary>
                <span id="product-categories">
                    <?= $escaper->escapeHtmlAttr(__('Categories')) ?>
                </span>
            </summary>
            <nav aria-labelledby="product-categories">
                <ul class="grid gap-2 md:grid-cols-3">
                    <?php foreach ($categoriesDoorwayData as $categoryData): ?>
                        <li class="grid gap-2 p-4 relative justify-items-center bg-white border border-secondary-900/50 rounded-xl text-center">
                            <img src="<?= $categoryData['image'] ?>" alt="<?= $categoryData['title'] ?>" width="151"
                                 height="74" class="object-contain"/>
                            <a href="<?= $categoryData['link'] ?>" class="after:absolute after:inset-0 block">
                                <?= $categoryData['title'] ?>
                            </a>
                        </li>
                    <?php endforeach; ?>
                </ul>
            </nav>
        </details>
    </section>
<?php endif; ?>

<!-- product list -->
<section
    id="product-list"
    class="container"
    aria-labelledby="product-list-title"
>
    <details open>
        <summary>
            <span id="product-list-title">
                <?= $escaper->escapeHtmlAttr(__('Products')) ?>
            </span>
        </summary>
        <?php if (!$productCollection->count()): ?>
            <p><?= $escaper->escapeHtml(__('We can\'t find products matching the selection.')) ?></p>
        <?php else: ?>
            <div x-data>
                <?= $block->getChildHtml('catalog.leftnav'); // Category filters
                ?>
                <?= $block->getChildHtml('catalogsearch.leftnav'); // Search filters
                ?>
                <?= $block->getAdditionalHtml() ?>
                <?php
                if ($block->getMode() == 'grid') {
                    $viewMode = 'grid';
                    $imageDisplayArea = 'category_page_grid';
                    $showDescription = false;
                    $templateType = \Magento\Catalog\Block\Product\ReviewRendererInterface::SHORT_VIEW;
                } else {
                    $viewMode = 'list';
                    $imageDisplayArea = 'category_page_list';
                    $showDescription = true;
                    $templateType = \Magento\Catalog\Block\Product\ReviewRendererInterface::FULL_VIEW;
                }
                /**
                 * Position for actions regarding image size changing in vde if needed
                 */
                $pos = $block->getPositioned();
                ?>
                <div
                    class="products wrapper mode-<?= /* @noEscape */ $viewMode ?> products-<?= /* @noEscape */ $viewMode ?>"
                    x-data="productList()"
                    x-init="initProductList()"
                >
                    <ul
                        class="mx-auto py-6 grid gap-4 grid-cols-1 <?= /* @noEscape */
                        $viewMode === 'grid'
                            ? 'md:grid-cols-2'
                            : '' ?>"
                    >
                        <?php
                        /** @var \Magento\Catalog\Model\Product $product */
                        foreach (array_values($productCollection->getItems()) as $i => $product):
                            if ($i < $eagerLoadImagesCount) {
                                $product->setData('image_custom_attributes', ['loading' => 'eager', 'fetchpriority' => 'high']);
                            }
                            ?>
                            <li class="flex">
                                <?= $productListItemViewModel->getItemHtml(
                                    $product,
                                    $block,
                                    $viewMode,
                                    $templateType,
                                    $imageDisplayArea,
                                    $showDescription
                                ); ?>
                            </li>
                        <?php endforeach; ?>
                    </ul>
                    <script defer>
                        'use strict';
                        function productList() {
                            return {
                                async initProductList() {

                                    const token = JSON.parse(localStorage.getItem('mage-cache-storage'))?.customer?.signin_token;
                                    const headers = new Headers();

                                    headers.append("Content-Type", "application/json");
                                    headers.append("Authorization", `Bearer ${token}`);

                                    <?php
                                    $productsForDelivery = [];
                                    foreach ($productCollection as $product) {
                                        $productsForDelivery[] = [
                                            'sku' => $product->getSku(),
                                            'quantity' => 1,
                                        ];
                                    }
                                    $productsForDelivery = json_encode(['products' => $productsForDelivery]);
                                    ?>

                                    <?php // TODO: body needs to be checked when real data is available ?>
                                    const body = JSON.stringify(
                                        {
                                            "queryData": '<?= $productsForDelivery ?>'
                                        }
                                    );

                                    const requestOptions = {
                                        method: "POST",
                                        headers,
                                        body
                                    };

                                    fetch("<?= $block->getBaseUrl(); ?>rest/V1/showroom/delivery_date/prepare", requestOptions)
                                    .then((response) => response.json())
                                    .then(([result]) => {
                                        const currentDate = new Date();

                                        for (const [sku, date] of Object.entries(result.products)) {
                                            const dateNode = document.querySelector(`[data-sku-delivery-date='${sku}']`);
                                            if (!dateNode || !date) return;
                                            dateNode.innerText = date;

                                            if (new Date(date) > currentDate) {
                                                dateNode.classList.remove('text-red-500');
                                                dateNode.classList.add('tertiary');
                                            } else if (new Date(date) === currentDate) {
                                                dateNode.classList.remove('text-red-500');
                                                dateNode.classList.add('primary');
                                            }
                                        }
                                    })
                                    .catch((error) => console.error(error));
                                }
                            }
                        }
                    </script>
                    <?= $block->getChildHtml('product.list.active.filters') ?>
                </div>
                <?= $block->getChildBlock('toolbar')->setIsBottom(true)->toHtml() ?>
            </div>
        <?= $block->fetchView($block->getTemplateFile('Hyva_SmileElasticsuite::catalog/layer/filter/js/attribute-filter-js.phtml')) ?>
        <?= $block->fetchView($block->getTemplateFile('Hyva_SmileElasticsuite::catalog/layer/filter/js/slider-filter-js.phtml')) ?>

            <script defer="defer">
                function initLayeredSwatch() {
                    return {
                        getSwatchType(typeNumber) {
                            switch (typeNumber) {
                                case "1":
                                    return "color";
                                case "2":
                                    return "image";
                                default:
                                    return "text";
                            }
                        },
                        getSwatchBackgroundStyle(type, value, image) {
                            if (this.getSwatchType(type) === "color") {
                                return 'background-color:' + value;
                            } else if (this.getSwatchType(type) === "image") {
                                return "background: #ffffff url('" + image + "') no-repeat center";
                            } else {
                                return '';
                            }
                        },
                        activeTooltipItem: false,
                        tooltipPositionElement: false,
                        isVisualSwatch() {
                            return this.getSwatchType(this.activeTooltipItem.type) !== 'text'
                        }
                    }
                }

            </script>

            <script defer="defer">
                function initToolbar(options) {
                    return {
                        options: options.productListToolbarForm || {},
                        getUrlParams: function () {
                            let decode = window.decodeURIComponent,
                                urlPaths = this.options.url.split('?'),
                                urlParams = urlPaths[1] ? urlPaths[1].split('&') : [],
                                params = {},
                                parameters, i;

                            for (i = 0; i < urlParams.length; i++) {
                                parameters = urlParams[i].split('=');
                                params[decode(parameters[0])] = parameters[1] !== undefined ?
                                    decode(parameters[1].replace(/\+/g, '%20')) :
                                    '';
                            }

                            return params;
                        },
                        getCurrentLimit: function () {
                            return this.getUrlParams()[this.options.limit] || this.options.limitDefault;
                        },
                        getCurrentPage: function () {
                            return this.getUrlParams()[this.options.page] || 1;
                        },
                        changeUrl(paramName, paramValue, defaultValue, sorting = '') {
                            let urlPaths = this.options.url.split('?'),
                                baseUrl = urlPaths[0],
                                paramData = this.getUrlParams(),
                                currentPage = this.getCurrentPage(),
                                newPage;

                            /**
                             * calculates the page on which the first item of the current page will
                             * be with the new limit and sets that number as the new page
                             */
                            if (currentPage > 1 && paramName === this.options.limit) {
                                newPage = Math.floor(this.getCurrentLimit() * (currentPage - 1) / paramValue) + 1;

                                if (newPage > 1) {
                                    paramData[this.options.page] = newPage;
                                } else {
                                    delete paramData[this.options.page];
                                }
                            }

                            paramData[paramName] = paramValue;

                            if (sorting) {
                                paramData['product_list_dir'] = sorting;
                            }

                            if (this.options.post) {
                                hyva.postForm({
                                    action: baseUrl,
                                    data: paramData,
                                    skipUenc: true
                                });
                            } else {
                                if (paramValue === defaultValue.toString()) {
                                    delete paramData[paramName];
                                    if (sorting) {
                                        delete paramData['product_list_dir'];
                                    }
                                }
                                paramData = Object.keys(paramData).length === 0 ?
                                    '' :
                                    '?' + (new URLSearchParams(paramData));
                                location.href = baseUrl + paramData
                            }
                        }
                    }
                }

            </script>
        <?php endif; ?>
    </details>
</section>
