<?php

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsOutline;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;
use Hyva\Theme\ViewModel\SvgIcons;

/** @var Template $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */
/** @var SvgIcons $svgIcons */
$svgIcons = $viewModels->require(SvgIcons::class);

/** @var HeroiconsOutline $heroicons */
$heroicons = $viewModels->require(HeroiconsOutline::class);
?>

<button
    type="submit"
    form="product_addtocart_form"
    class="btn btn-primary gap-2"
    id="product-addtocart-button"
    data-addto="cart"
>
    <span>
        <?= $block->getData('is_cart_configure') ?
            $escaper->escapeHtml(__('Update item')) :
            $escaper->escapeHtml(__('Add to Cart')) 
        ?>
    </span>
    <?= $svgIcons->addToCartHtml("", null, null, ['aria-hidden' => 'true']) ?>
</button>

<?= $block->getChildHtml('', true) ?>
