<?php

declare(strict_types=1);

use Magento\Catalog\Block\Product\Image;
use Magento\Framework\Escaper;

/** @var Image $block */
/** @var Escaper $escaper */

$hasLoadingAttribute = ($block->getCustomAttributes() ?: [])['loading'] ?? false;

?>
<img
    class="object-contain <?= $escaper->escapeHtmlAttr($block->getClass()) ?>"
    x-data=""
    @update-gallery-<?= (int)$block->getProductId() ?>.window="$root.src = $event.detail"
    <?php foreach ($block->getCustomAttributes() as $name => $value): ?>
        <?= $escaper->escapeHtmlAttr($name) ?>="<?= $escaper->escapeHtmlAttr($value) ?>"
    <?php endforeach; ?>
    src="<?= $escaper->escapeUrl($block->getImageUrl()) ?>"
    <?php if (! $hasLoadingAttribute): ?>
    loading="lazy"
    <?php endif; ?>
    width="<?= $escaper->escapeHtmlAttr($block->getWidth()) ?>"
    height="<?= $escaper->escapeHtmlAttr($block->getHeight()) ?>"
    alt="<?= $escaper->escapeHtmlAttr($block->getLabel()) ?>"
/>
