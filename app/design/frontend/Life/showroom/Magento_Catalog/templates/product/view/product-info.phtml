<?php

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\ProductAttributes;
use Hyva\Theme\ViewModel\ProductPage;
use Life\ShowroomDealer\ViewModel\DealerInfo;
use Magento\Catalog\Model\Product;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;

/** @var Template $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */

/** @var ProductPage $productViewModel */
$productViewModel = $viewModels->require(ProductPage::class);
/** @var ProductAttributes $attributesViewModel */
$attributesViewModel = $viewModels->require(ProductAttributes::class);
$dealerViewModel = $viewModels->require(DealerInfo::class);
/** @var Product $product */
$product = $productViewModel->getProduct();
$productType = $product->getTypeId();

?>
<div class="w-full">
    <div class="mt-4 grid gap-2">
        <?php if (!$dealerViewModel->isDealer()): ?>
            <div role="group" aria-label="<?= $escaper->escapeHtmlAttr('Price') ?>">
                <?= $block->getChildHtml("product.info.price") ?>
            </div>
        <?php endif; ?>

        <div class="flex flex-wrap gap-2 mt-4">
            <?php if ($product->isSaleable()): ?>
                <?= $block->getChildHtml("product.info.quantity") ?>
                <?= $block->getChildHtml("product.info.addtocart") ?>
            <?php endif; ?>
        </div>

    </div>

    <?= $block->getChildHtml("product.info.additional") ?>
    <?= $block->getChildHtml('product.info.form') ?>
</div>
<?= $block->getChildHtml('product.info.form') ?>