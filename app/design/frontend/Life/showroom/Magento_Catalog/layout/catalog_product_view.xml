<?xml version="1.0" ?>
<page
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd"
>
    <update handle="hyva_modal" />
    <body>

        <!-- adds custom classes to main title -->
        <referenceBlock name="page.main.title">
            <arguments>
                <argument name="css_class" xsi:type="string">mt-0 product</argument>
            </arguments>
        </referenceBlock>

        <!-- removes the product description block -->
        <referenceBlock name="description" remove="true" />

        <referenceBlock name="product.detail.page">
            <container name="product.info.details.after"/>
        </referenceBlock>
        <referenceBlock name="page.main.title">
            <block
                name="product.info.subtitle"
                class="Magento\Catalog\Block\Product\View"
                template="Life_ShowroomProduct::product/view/subtitle.phtml"
            />
        </referenceBlock>

        <!-- updates section title -->
        <referenceBlock name="product.attributes">
            <arguments>
                <argument name="title" xsi:type="string" translate="true">Product specifications</argument>
            </arguments>
        </referenceBlock>
    </body>
</page>
