<?php

declare(strict_types=1);

use Magento\Framework\Escaper;
use Magento\Theme\Block\Html\Title;

/** @var Escaper $escaper */
/** @var Title $block */

$textSizeCssClass = $block->getCssClass() ? ' ' . $block->getCssClass() : ' text-3xl';
$titleContainerClasses = $block->getData('maybe_purged_tailwind_section_classes') ?? '';
$titleHtml = '';
if (trim((string)$block->getPageHeading())) {
    $titleHtml = '<span class="base" data-ui-id="page-title-wrapper" '
        . $block->getAddBaseAttribute()
        . '>'
        . $escaper->escapeHtml($block->getPageHeading())
        . '</span>';
}
?>
<?php if ($titleHtml): ?>
<header class="<?= $escaper->escapeHtmlAttr($titleContainerClasses . $textSizeCssClass) ?>">
    <h1 class="text-base font-sans font-normal leading-[1.875]"
        <?php if ($block->getId()): ?> id="<?= $escaper->escapeHtmlAttr($block->getId()) ?>" <?php endif; ?>>
        <?= /* @noEscape */ $titleHtml ?>
    </h1>
</header>
<?= $block->getChildHtml() ?>
<?php endif; ?>
