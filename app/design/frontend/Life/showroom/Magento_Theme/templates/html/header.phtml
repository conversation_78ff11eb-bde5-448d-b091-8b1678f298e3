<?php

declare(strict_types=1);

use Hyva\Theme\ViewModel\SvgIcons;
use Hyva\Theme\Model\ViewModelRegistry;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;

/** @var ViewModelRegistry $viewModels */
/** @var SvgIcons $svgIcons */
/** @var Escaper $escaper */
/** @var Template $block */

$svgIcons = $viewModels->require(SvgIcons::class);
?>
<div
    id="header"
    class="container grid items-center h-full isolate"
>
    <nav>
        <ul class="flex gap-4 justify-around">

            <?php // products ?>
            <li>
                <a href="<?= $escaper->escapeUrl($block->getBaseUrl())?>" class="inline-block min-w-[48px] min-h-[48px] text-sm
                text-center">
                    <?= $svgIcons->bookOpenHtml("mb-2 mx-auto", 24, 24, ['aria-hidden' => 'true']) ?>
                    <?= $escaper->escapeHtml(__('Articles')) ?>
                </a>
            </li>

            <?php // customers ?>
            <li>
                <a
                    href="<?= $escaper->escapeUrl($block->getUrl('showroom_customer/selection/index')) ?>"
                    class="inline-block min-w-[48px] min-h-[48px] text-sm text-center"
                >
                    <?= $svgIcons->userHtml("mb-2 mx-auto", 24, 24, ['aria-hidden' => 'true']) ?>
                    <?= $escaper->escapeHtml(__('Customer')) ?>
                </a>
            </li>

            <?php // cart ?>
            <li>
                <a
                    href="<?= $escaper->escapeUrl($block->getUrl('checkout/cart/index')) ?>"
                    class="inline-grid justify-items-center min-w-[48px] min-h-[48px] text-sm text-center"
                    x-data="cart()"
                    @private-content-loaded.window="getData(event.detail.data)"
                >
                    <div class="relative" aria-hidden="true">
                        <span
                            x-cloak
                            x-show="cartSummaryCount"
                            x-text="cartSummaryCount"
                            class="inline-flex min-w-[1.25rem] aspect-square absolute -top-2 -right-4 text-xs font-bold bg-primary-100 rounded-full justify-center items-center"
                        ></span>
                        <?= $svgIcons->cartHtml("mb-2 mx-auto", 24, 24) ?>
                    </div>
                    <?= $escaper->escapeHtml(__('Cart')) ?>
                </a>
                <script defer>
                    'use strict';
                    function cart() {
                        return {
                            cartSummaryCount: 0,
                            getData(data) {
                                if (data.cart?.summary_count) {
                                    this.cartSummaryCount = data.cart.summary_count;
                                }
                            },
                        }
                    };
                </script>
            </li>

            <?php // quotes ?>
            <li>
                <a
                    href="<?= $escaper->escapeUrl($block->getUrl('showroom_quote/selection/index')) ?>"
                    class="inline-block min-w-[48px] min-h-[48px] text-sm text-center"
                >
                    <?= $svgIcons->fileHtml("mb-2 mx-auto", 24, 24, ['aria-hidden' => 'true']) ?>
                    <?= $escaper->escapeHtml(__('Offers')) ?>
                </a>
            </li>
        </ul>
    </nav>


</div>

