<?php

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\SvgIcons;
use Magento\CatalogSearch\Block\Result;
use Magento\Framework\Escaper;

/** @var $block  Result*/
/** @var ViewModelRegistry $viewModels */
/** @var Escaper $escaper */
$productList = $block->getProductListHtml();

/** @var SvgIcons $svgIcons */
$svgIcons = $viewModels->require(SvgIcons::class);

?>
<?php if ($block->getResultCount()) : ?>
    <?= /* @noEscape */ $block->getChildHtml('tagged_product_list_rss_link') ?>
<div class="search results">
    <?= /* @noEscape */ $productList ?>
</div>
<?php else : ?>
<header class="container mb-2">
    <div class="bg-primary text-white rounded-xl px-6 py-4">
        <div class="flex pb-2 border-b border-white/20 text-primary-100 text-xs">
            <a href="<?= $block->getBaseUrl() ?>" class="inline-flex gap-2 items-center text-white">
                <?= $svgIcons->arrowLeftHtml("", 24, 24, ['aria-hidden' => 'true']) ?>
                <?= $escaper->escapeHtml(__('Products')) ?>
            </a>
        </div>
        <div class="max-w-lg mx-auto py-14">
            <h1 class="heading-5 mb-6">
                <?= $block->escapeHtml($block->getNoResultText() ? $block->getNoResultText() : __('Your search returned no results.')) ?>
            </h1>
            <?= /* @noEscape */ $block->getAdditionalHtml() ?>
            <script>
                function initSearchForm() {
                    return {
                        searchVal: '',
                        searching: false,
                        search() {
                            if (this.searchVal.trim().length < 3) return;
                            window.location.href = '<?= $block->getBaseUrl() ?>catalogsearch/result/?q=' + encodeURIComponent(this.searchVal);
                            this.searching = true;
                        },
                    }
                }
            </script>
            <search x-data="initSearchForm()">
                <form
                    @submit.prevent="search"
                    @input="searchVal = $event.target.value"
                    class="relative"
                >
                    <label>
                        <span class="sr-only">
                            <?= $escaper->escapeHtml(__('Search for serie name or article number')) ?>
                        </span>
                        <input
                            id="product-search"
                            type="search"
                            inputmode="search"
                            required
                            minlength="3"
                            placeholder="<?= $escaper->escapeHtml(__('Search for serie name or article number')) ?>"
                            class="w-full !min-h-[48px] !pr-16 !rounded-[18.75rem]"
                            :disabled="searching"
                        >
                    </label>
                    <button
                        role="button"
                        class="inline-flex pr-6 pl-4 items-center justify-center absolute right-0 top-1/2 -translate-y-1/2 h-12 text-black"
                        :disabled="searching"
                    >
                        <?= $svgIcons->searchHtml("", 24, 24, ['aria-hidden' => 'true']) ?>
                        <span class="sr-only">
                            <?= $escaper->escapeHtml(__('Search')) ?>
                        </span>
                    </button>
                </form>
            </search>
        </div>
    </div>
</header>
<?php endif; ?>
