<?php

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\Store;
use Hyva\Theme\ViewModel\StoreSwitcher;
use Life\ThemeConfigurations\ViewModel\StoreLanguage;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;
use Magento\Store\ViewModel\SwitcherUrlProvider;

/** @var Template $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */

/** @var SwitcherUrlProvider $switcherUrlProvider */
$switcherUrlProvider = $viewModels->require(SwitcherUrlProvider::class);

/** @var Store $storeViewModel */
$storeViewModel = $viewModels->require(Store::class);

/** @var StoreSwitcher $storeSwitcherViewModel */
$storeSwitcherViewModel = $viewModels->require(StoreSwitcher::class);

/** @var StoreLanguage $storeLanguageViewModel */
$storeLanguageViewModel = $viewModels->require(StoreLanguage::class);
?>
<?php if (count($storeSwitcherViewModel->getStores()) > 1): ?>
    <div
        x-data="{ open: false }"
        class="ml-auto"
    >
        <div class="relative inline-block text-left">
            <div>
                <button
                    @click.prevent="open = !open"
                    @click.outside="open = false"
                    @keydown.window.escape="open=false"
                    type="button"
                    class="inline-flex relative justify-center w-full px-4 py-2 text-inherit font-semibold after:absolute after:w-12 after:h-12 after:top-1/2 after:left-1/2 after:translate-x-1/2 after:translate-y-1/2"
                    aria-haspopup="true"
                    aria-expanded="true"
                >
                    <?= strtoupper($escaper->escapeHtml($storeLanguageViewModel->getCurrentLanguage())) ?>
                </button>
            </div>
            <nav
                aria-label="<?= $escaper->escapeHtml(__('Language')) ?>"
                x-cloak
                x-show="open"
                x-transition
                class="absolute right-0 top-full z-20 py-2 overflow-auto origin-top-left lg:mt-3 bg-secondary rounded-xl text-black"
            >
                <ul aria-orientation="vertical">
                    <?php foreach ($storeSwitcherViewModel->getStores() as $lang): ?>
                        <?php if ($lang->getId() != $storeViewModel->getStoreId()): ?>
                            <li>
                                <a
                                    href="<?= $escaper->escapeUrl($switcherUrlProvider->getTargetStoreRedirectUrl($lang)) ?>"
                                    class="block px-4 py-1"
                                >
                                    <?= strtoupper($escaper->escapeHtml($storeLanguageViewModel->getStoreLanguage($lang)))?>
                                </a>
                            </li>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </ul>
            </nav>
        </div>
    </div>
<?php endif; ?>
