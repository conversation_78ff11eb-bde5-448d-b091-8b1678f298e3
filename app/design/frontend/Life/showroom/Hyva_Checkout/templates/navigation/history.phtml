<?php

declare(strict_types=1);

use Hyva\Checkout\ViewModel\Navigation;
use Hyva\Theme\Model\ViewModelRegistry;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;

/** @var Template $block */
/** @var ViewModelRegistry $viewModels */
/** @var Escaper $escaper */
/** @var Navigation $viewModel */

$viewModel = $viewModels->require(Navigation::class);
$config = $viewModel->getConfig();
?>
<?php if ($config->hasSteps() && $config->isMultiStepper()): ?>
    <?php $previous = $config->getStepBefore() ?>
    <?php $next = $config->getStepAfter() ?>

    <?php if (is_array($previous) && $viewModel->getSystemConfig()->showNavigationBackButton()): ?>
        <button type="button"
                rel="prev"
                class="btn btn-secondary"
                x-spread="buttonPrevious('<?= $escaper->escapeJs($previous['route']) ?>')"
                x-bind="buttonPrevious('<?= $escaper->escapeJs($previous['route']) ?>')"
        >
            <?= $escaper->escapeHtml(__('Back to %1', [mb_strtolower((string) __($previous['label'] ?? 'previous step'), 'UTF-8')])) ?>
        </button>
    <?php endif ?>

    <?php if (is_array($next)): ?>
        <button type="button"
                rel="next"
                class="btn btn-primary !font-inherit"
                x-spread="buttonNext('<?= $escaper->escapeJs($next['route']) ?>')"
                x-bind="buttonNext('<?= $escaper->escapeJs($next['route']) ?>')"
        >
            <?= $escaper->escapeHtml(__('Proceed')) ?>
        </button>
    <?php endif ?>
<?php endif ?>
