<?php

declare(strict_types=1);

use Hyva\Checkout\ViewModel\Navigation;
use Hyva\Theme\Model\ViewModelRegistry;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;

/** @var Template $block */
/** @var ViewModelRegistry $viewModels */
/** @var Escaper $escaper */
/** @var Navigation $viewModel */

$viewModel = $viewModels->require(Navigation::class);
$next = $viewModel->getConfig()->getStepAfter();
?>
<?php if ($next === null): ?>
    <button type="button"
            class="btn btn-primary !font-inherit"
            x-spread="buttonPlaceOrder()"
            x-bind="buttonPlaceOrder()"
    >
        <?= $escaper->escapeHtml(__('Proceed')) ?>
    </button>
<?php endif ?>
