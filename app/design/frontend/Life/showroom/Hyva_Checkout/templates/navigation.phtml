<?php

declare(strict_types=1);

use Magento\Framework\View\Element\Template;

/** @var Template $block */

$navHtmlLeft = $block->getChildHtml('hyva.checkout.navigation.left');
$navHtmlRight = $block->getChildHtml('hyva.checkout.navigation.right');
?>
<nav
    x-data="Object.assign(initCheckoutNavigation(), initNavigation())"
    x-init="initialize()"
    class="nav-main rounded-xl bg-secondary-300 p-6"
>
    <div class="grid gap-2">
        <?php if (! empty($navHtmlLeft)): ?>
            <?= /* @noEscape */ $navHtmlLeft ?>
        <?php endif ?>
        <?= /* @noEscape */ $navHtmlRight ?>
    </div>
</nav>
