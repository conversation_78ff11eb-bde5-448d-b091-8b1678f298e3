<?php

declare(strict_types=1);

use Hyva\Checkout\Model\MethodMetaDataInterface;
use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Checkout\ViewModel\Checkout\Payment\MethodList as ViewModel;
use Hyva\Checkout\Magewire\Checkout\Payment\MethodList as Magewire;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;
use Magento\Quote\Api\Data\PaymentMethodInterface;

/** @var Template $block */
/** @var ViewModelRegistry $viewModels */
/** @var ViewModel $viewModel */
/** @var PaymentMethodInterface $method */
/** @var Magewire $magewire */
/** @var Escaper $escaper */
/** @var MethodMetaDataInterface $methodMetaData */

$viewModel = $viewModels->require(ViewModel::class);
$methods = $viewModel->getList();
?>
<details id="payment-methods" open>
    <summary>
        <span><?= $escaper->escapeHtml(__('Payment Method')) ?></span>
    </summary>
    <div>
        <?php if ($methods): ?>
            <script>
                window.addEventListener('checkout:step:loaded', () => {
                    if('<?= $escaper->escapeJs($magewire->method) ?>' && document.getElementById('payment-method-list')) {
                        window.dispatchEvent(new CustomEvent('checkout:payment:method-activate', { detail: { method: '<?= $escaper->escapeJs($magewire->method) ?>'} }))
                    }
                }, { once: true })
            </script>
    
            <ol id="payment-method-list" class="grid gap-3">
                <?php foreach ($methods as $method): ?>
                    <?php $methodCodeAttr = $escaper->escapeHtmlAttr($method->getCode()) ?>
                    <?php $methodMetaData = $viewModel->getMethodMetaData($block, $method) ?>
    
                    <li id="payment-method-option-<?= /* @noEscape */ $methodCodeAttr ?>"
                        class="<?= $magewire->method === $method->getCode() ? 'active' : 'inactive' ?> flex items-center rounded-xl bg-white border border-secondary-900/50 p-4"
                        wire:key="<?= /* @noEscape */ $methodCodeAttr ?>"
                    >
                        <div class="flex gap-4 items-center">
                            <div class="flex items-center">
                                <input type="radio"
                                       class="flex-none disabled:opacity-25"
                                       id="payment-method-<?= /* @noEscape */ $methodCodeAttr ?>"
                                       name="payment-method-option"
                                       value="<?= /* @noEscape */ $methodCodeAttr ?>"
                                       wire:model="method"
                                />
                            </div>
    
                            <label class="flex items-center gap-2 cursor-pointer w-full relative"
                                   for="payment-method-<?= /* @noEscape */ $methodCodeAttr ?>"
                            >
                                <div class="flex w-12 items-center">
                                    <?php if ($methodMetaData->canRenderIcon()): ?>
                                        <?= /* @noEscape */ $methodMetaData->renderIcon() ?>
                                    <?php endif ?>
                                </div>
                                <div class="text-sm flex flex-col">
                                    <?= $escaper->escapeHtml($method->getTitle()) ?>
    
                                    <?php if ($methodMetaData->hasSubTitle()): ?>
                                        <span class="text-gray-500 font-medium text-sm">
                                            <?= $escaper->escapeHtml($methodMetaData->getSubTitle()) ?>
                                        </span>
                                    <?php endif ?>
                                </div>
                            </label>
                        </div>
                        <?php if ($viewModel->canShowMethod($block, $method, $magewire->method)): ?>
                            <?php $html = $viewModel->getMethodBlock($block, $method)->toHtml() ?>
    
                            <?php if (! empty($html)): ?>
                                <div id="<?= 'payment-method-view-' . /* @noEscape */ $methodCodeAttr ?>" class="w-full pl-8 mt-4">
                                    <?= /* @noEscape */ $html ?>
                                </div>
                            <?php endif ?>
                        <?php endif ?>
                    </li>
                <?php endforeach ?>
            </ol>
        <?php else: ?>
            <div class="message warning">
                <?= $escaper->escapeHtml(__('No Payment method available.')) ?>
            </div>
        <?php endif ?>
    </div>
</div>
