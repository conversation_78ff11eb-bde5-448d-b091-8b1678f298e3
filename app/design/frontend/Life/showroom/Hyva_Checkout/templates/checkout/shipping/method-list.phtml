<?php

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Checkout\ViewModel\Checkout\Formatter as FormatterViewModel;
use Hyva\Checkout\ViewModel\Checkout\Shipping\MethodList as ViewModel;
use Hyva\Checkout\Magewire\Checkout\Shipping\MethodList as Magewire;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;
use Magento\Quote\Api\Data\ShippingMethodInterface;
use Magento\Tax\Helper\Data as TaxHelper;
use Hyva\Theme\ViewModel\Store;

// phpcs:disable Magento2.Templates.ThisInTemplate.FoundHelper
/** @var Template $block */
/** @var ViewModelRegistry $viewModels */
/** @var ViewModel $viewModel */
/** @var ShippingMethodInterface $method */
/** @var Magewire $magewire */
/** @var Escaper $escaper */
/** @var TaxHelper $taxHelper */
/** @var Store $storeViewModel */

$viewModel = $viewModels->require(ViewModel::class);
$formatterViewModel = $viewModels->require(FormatterViewModel::class);
$taxHelper = $this->helper(TaxHelper::class);
$storeViewModel = $viewModels->require(Store::class);
$storeId = $storeViewModel->getStoreId();
$methods = $viewModel->getList();
?>
<details id="shipping-methods" open>
    <summary>
        <span><?= $escaper->escapeHtml(__('Shipping method')) ?></span>
    </summary>

    <?php if ($methods): ?>
        <script>
            window.addEventListener('checkout:step:loaded', () => {
                if('<?= $escaper->escapeJs($magewire->method) ?>' && document.getElementById('shipping-method-list')) {
                    window.dispatchEvent(
                        new CustomEvent('checkout:shipping:method-activate', {
                            detail: {
                                method: '<?= $escaper->escapeJs($magewire->method) ?>'
                            }
                        })
                    )
                }
            }, { once: true })
        </script>

        <ol id="shipping-method-list" class="grid gap-2">
            <?php foreach ($methods as $method): ?>
                <?php if ($method->getAvailable()): ?>
                    <?php $methodCode = $escaper->escapeHtml($method->getMethodCode()) ?>
                    <?php $methodCodeAttr = $escaper->escapeHtmlAttr($method->getMethodCode()) ?>
                    <?php $methodCarrierCode = $escaper->escapeHtmlAttr($method->getCarrierCode()) ?>
                    <?php $methodTitle = $escaper->escapeHtml($method->getMethodTitle() ?: __('Unknown')) ?>

                    <li id="shipping-method-option-<?= /* @noEscape */ $methodCode ?>"
                         class="flex gap-4 bg-white border border-secondary-900/50 rounded-xl p-6 <?= $magewire->method === $methodCarrierCode . '_' . $methodCodeAttr ? 'active' : 'inactive' ?>"
                         wire:key="<?= /* @noEscape */ $methodCodeAttr ?>"
                         x-data="{ disabled: <?= ! $method->getErrorMessage() ? 'false' : 'true' ?> }"
                    >
                        <input type="radio"
                                class="flex-none mt-1.5 disabled:opacity-25"
                                id="shipping-method-<?= /* @noEscape */ $methodCodeAttr ?>"
                                name="shipping-method-option"
                                value="<?= $escaper->escapeHtmlAttr($methodCarrierCode . '_' . $methodCodeAttr) ?>"
                                wire:model="method"
                                x-bind:disabled="disabled"
                        />

                        <div class="flex-1 space-y-2">
                            <label class="flex justify-between gap-x-4 text-sm"
                                   for="shipping-method-<?= /* @noEscape */ $methodCodeAttr ?>"
                            >
                                <div>
                                    <?= /** @noEscape */ $methodTitle ?>
                                </div>

                                <div class="product-price text-right font-medium text-primary">
                                    <?php if ($taxHelper->displayShippingPriceIncludingTax() || $taxHelper->displayShippingBothPrices()): ?>
                                        <span class="price-including-tax" data-label="<?= $escaper->escapeHtmlAttr(__('Incl. Tax')) ?>">
                                            <?= /* @noEscape */ $formatterViewModel->currency($method->getPriceInclTax()) ?>
                                        </span>
                                    <?php endif ?>

                                    <?php if (($taxHelper->displayShippingPriceExcludingTax() || $taxHelper->displayShippingBothPrices()) && $taxHelper->getShippingTaxClass($storeId) !== 0): ?>
                                        <span class="price-excluding-tax" data-label="<?= $escaper->escapeHtmlAttr(__('Excl. Tax')) ?>">
                                            <?= /* @noEscape */ $formatterViewModel->currency($method->getPriceExclTax()) ?>
                                        </span>
                                    <?php endif ?>
                                </div>
                            </label>

                            <?php if ($method->getErrorMessage()): ?>
                                <div role="alert" class="messages w-full">
                                    <p class="message error mb-0">
                                        <?= $escaper->escapeHtml(__($method->getErrorMessage())) ?>
                                    </p>
                                </div>
                            <?php endif ?>

                            <?php if ($viewModel->isCurrentShippingMethod($method, $magewire->method)): ?>
                                <?php if ($viewModel->hasAdditionalView($block, $method)): ?>
                                    <?php $html = $viewModel->getAdditionalViewBlock($block, $method)->toHtml() ?>
                                    <?php if (! empty($html)): ?>
                                        <div id="<?= /* @noEscape */ 'shipping-method-view-' . $methodCodeAttr ?>"
                                             class="w-full mt-4"
                                        >
                                            <?= /* @noEscape */ $html ?>
                                        </div>
                                    <?php endif ?>
                                <?php endif ?>
                            <?php endif ?>
                        </div>
                    </li>
                <?php endif ?>
            <?php endforeach ?>
        </ol>
    <?php endif ?>
</details>
