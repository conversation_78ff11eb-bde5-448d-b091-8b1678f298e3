<?php

declare(strict_types=1);

use Hyva\Checkout\ViewModel\Main as ViewModel;
use Hyva\Theme\ViewModel\SvgIcons;
use Life\ShowroomQuoteHyva\ViewModel\QuoteCustomer;

/** @var \Magento\Framework\View\Element\Template $block */
/** @var \Hyva\Checkout\Magewire\Main $magewire */
/** @var \Magento\Framework\Escaper $escaper */
/** @var \Hyva\Theme\Model\ViewModelRegistry $viewModels */

/** @var ViewModel $viewModel */
$viewModel = $viewModels->require(ViewModel::class);

/** @var SvgIcons $svgIcons */
$svgIcons = $viewModels->require(SvgIcons::class);
/** @var QuoteCustomer $quoteViewModel */
$quoteViewModel = $viewModels->require(QuoteCustomer::class);
?>
<div
    id="hyva-checkout-main"
    class="<?= $escaper->escapeHtmlAttr($viewModel->getStepClassesAsString()) ?> relative container grid gap-2"
    role="main"
>
    <header>
        <div class="grid bg-primary rounded-xl px-4 text-white">
            <div class="flex items-center gap-10 text-xs pt-4 pb-2 border-b border-white/20">
                <a
                    href="<?= $escaper->escapeUrl($block->getBaseUrl()) ?>checkout/cart/"
                    class="inline-flex items-center gap-x-2"
                >
                    <?= $svgIcons->arrowLeftHtml("", 20, 20, ['aria-hidden' => 'true']) ?>
                    <?= $escaper->escapeHtml(__('Back')) ?>
                </a>
                <span class="text-primary-100">
                    <?= $escaper->escapeHtml(__('Cart')) ?>
                </span>
            </div>

            <div class="inline-grid w-full max-w-lg mx-auto gap-6 py-14">
                <h1 class="heading-5 mb-2">
                    <?= $escaper->escapeHtml(__('Complete order')) ?>
                </h1>
                <p class="font-semibold text-primary-100">
                    <?= __('Quote #%1', $quoteViewModel->getQuote()->getId()??'') ?>
                </p>
            </div>
        </div>
    </header>

    <a
        href="<?= $block->getBaseUrl() ?>checkout/cart"
        class="flex rounded-xl bg-primary-100 p-6 text-primary"
    >
        <span class="font-semibold leading-8"><?= $escaper->escapeHtml(__('Shipment details')) ?></span>
        <?= $svgIcons->checkCircleHtml("mt-1 ml-auto", 24, 24, ['aria-hidden' => 'true']) ?>
    </a>



    <?php /* Change the Frontend API main.container when you change the id attribute value. */ ?>
    <div id="hyva-checkout-container">
        <?= $block->getChildHtml('hyva.checkout.container') ?>
    </div>

    <?php /* Render if it is still is a child of "hyva.checkout.main", usually it will be moved into a column. */ ?>
    <?= $block->getChildHtml('hyva.checkout.navigation') ?>
</div>
