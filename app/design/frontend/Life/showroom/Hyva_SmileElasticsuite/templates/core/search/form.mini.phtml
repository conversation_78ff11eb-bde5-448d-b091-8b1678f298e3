<?php

declare(strict_types=1);

use Hyva\Theme\ViewModel\HeroiconsOutline;
use Magento\Search\Helper\Data as SearchHelper;
use Hyva\Theme\ViewModel\SvgIcons;

/** @var \Smile\ElasticsuiteCore\Block\Search\Form\Autocomplete $block */
/** @var SearchHelper $helper */
/** @var \Magento\Framework\Escaper $escaper */
/** @var \Hyva\Theme\Model\ViewModelRegistry $viewModels */
/** @var HeroiconsOutline $heroicons */

/** @var SvgIcons $svgIcons */
$svgIcons = $viewModels->require(SvgIcons::class);

$helper = $this->helper(SearchHelper::class);
$suggestionUrl = $helper->getResultUrl() . '?' . $helper->getQueryParamName() . '=';
$heroicons = $viewModels->require(HeroiconsOutline::class);
$templates = json_decode($block->getJsonSuggestRenderers(), true);
?>

<script defer="defer">
    function initMiniSearchComponent() {
        "use strict";

        const headerElement = document.querySelector('header');
        const bodyElement = document.querySelector('body');
        let contentElement = document.querySelector('.top-container');
        if (!contentElement) {
            contentElement = document.querySelector('main');
        }
        const mainMenu = document.querySelector('.desktop-nav');
        const headerTop = headerElement.getBoundingClientRect().top;

        return {
            show: false,
            formSelector: "#search_mini_form",
            url: "<?= /* @escapeNotVerified */ $block->getUrl('search/ajax/suggest') ?>",
            destinationSelector: "#search_autocomplete",
            templates: <?= /* @noEscape */ $block->getJsonSuggestRenderers() ?>,
            priceFormat: <?= /* @noEscape */ $block->getJsonPriceFormat() /** @phpstan-ignore-line */ ?>,
            minSearchLength: <?= /* @escapeNotVerified */ $helper->getMinQueryLength() ?>,
            searchResultsByType: {},
            currentRequest: null,
            searchLink: "",
            isLoading: false,
            isMobile: false,
            headerHeight: headerElement.getBoundingClientRect().height,

            <?php
            /**
             * Fix for: https://bugs.chromium.org/p/chromium/issues/detail?id=1334207
             */
            ?>
            preventStickyScrollOnKeydown: function () {
                let self = this;
                const scrollDetect = document.getElementById('scrolled');

                const observerOptions = {
                    threshold: 0,
                    rootMargin: '0px'
                };

                const observer = new IntersectionObserver(entries => {
                    const [entry] = entries;
                    if (entry.isIntersecting) {
                        headerElement.style.position = 'sticky';
                        contentElement.style.paddingTop = 0;
                    } else {
                        headerElement.style.position = 'fixed';
                        contentElement.style.paddingTop = self.headerHeight + 'px';
                    }
                }, observerOptions);

                observer.observe(scrollDetect);
            },

            checkIsMobileResolution: function () {
                this.isMobile = window.outerWidth < 768;
                this.headerHeight = headerElement.getBoundingClientRect().height;
            },

            /**
             * Get search results.
             */
            getSearchResults: function (value) {
                if (value.length < 1) {
                    this.show = false;
                }

                if (value.length < parseInt(this.minSearchLength, 10)) {
                    this.searchResultsByType = [];

                    return false;
                }

                let url = this.url + '?' + new URLSearchParams({
                    q: value,
                    _: Date.now()
                }).toString();

                if (this.currentRequest !== null) {
                    this.currentRequest.abort();
                }
                this.currentRequest = new AbortController();
                this.searchLink = "<?= $escaper->escapeUrl($block->getUrl('catalogsearch/result')) ?>?q=" + value;
                this.isLoading = true;

                fetch(url, {
                    method: 'GET',
                    signal: this.currentRequest.signal,
                }).then((response) => {
                    if (response.ok) {
                        return response.json();
                    }
                }).then((data)  => {
                    this.show = data.length > 0;

                    this.searchResultsByType = data.reduce((acc, result) => {
                        let column = (result.type === 'product') ? 'main' : 'sidebar';
                        // Init columns, first sidebar and then main
                        if (! acc['sidebar']) acc['sidebar'] = [];
                        if (! acc['main']) acc['main'] = [];

                        if (! acc[column][result.type]) acc[column][result.type] = [];
                        acc[column][result.type].push(result);
                        return acc;
                    }, {});

                    this.isLoading = false;
                }).catch((error) => {
                });
            },
        }
    }
</script>
<div id="search-content" x-show="true">
    <div x-data="initMiniSearchComponent()">
        <template x-if="isLoading">
            <?= $block->fetchView((string) $block->getTemplateFile('Hyva_Theme::ui/loading.phtml')) ?>
        </template>
        <form
            class="form minisearch"
            :class="{'bg-transparent z-10 fixed top-0 bottom-0 left-0 right-0 lg:static': show}"
            action="<?= $escaper->escapeUrl($helper->getResultUrl()) ?>" method="get"
        >
            <div class="relative z-50 max-md:pt-2" :class="{'p-3 lg:p-0': show}">
                <label class="relative mb-0">
                    <span class="sr-only" ><?= $escaper->escapeHtmlAttr(__('Search entire store here...')) ?></span>
                    <input
                        x-on:input.debounce="getSearchResults($event.target.value)"
                        x-init="preventStickyScrollOnKeydown()"
                        @resize.window.debounce="checkIsMobileResolution()"
                        x-ref="searchInput"
                        type="search"
                        class="w-full p-2 text-lg leading-normal transition !min-h-a11y !rounded-full appearance-none text-grey-800 focus:outline-none focus:border-transparent lg:text-xl"
                        autocapitalize="off"
                        autocomplete="off"
                        autocorrect="off"
                        name="<?= $escaper->escapeHtmlAttr($helper->getQueryParamName()) ?>"
                        value="<?= $escaper->escapeHtmlAttr($helper->getEscapedQueryText()) ?>"
                        placeholder="<?= $escaper->escapeHtmlAttr(__('Search entire store here...')) ?>"
                        maxlength="<?= $escaper->escapeHtmlAttr((string)$helper->getMaxQueryLength()) ?>"
                    />
                    <span
                        x-show="show && !isLoading"
                        class="absolute -translate-y-1/2 right-10 top-1/2"
                        @click="show = false"
                    >
                        <?= $heroicons->xHtml('p-4 text-primary/50', 48, 48); ?>
                    </span>
                    <button
                        type="submit"
                        title="<?= $escaper->escapeHtml(__('Search')) ?>"
                        class="absolute -translate-y-1/2 action search top-1/2 right-2"
                        aria-label="Search"
                        data-testid="search-icon">
                        <?= $svgIcons->searchHtml('text-primary p-3.5', 48, 48) ?>
                    </button>
                </label>
            </div>
            <div
                id="search_autocomplete"
                class="relative z-40 w-full px-3 overflow-y-auto search-autocomplete md:overflow-visible lg:px-0 h-[calc(100vh-5rem)] md:h-auto"
                x-show="show && !isLoading"
                style="display:none;"
            >
                <div
                    class="fixed top-0 left-0 w-full h-screen bg-black/30"
                    @click="show = false"
                    x-show="show && !isLoading"
                ></div>
                <div class="relative z-10 w-full bg-white rounded-xl lg:absolute lg:top-3 lg:flex sm-max:flex-col lg:w-screen lg:max-w-4xl xl:max-w-6xl lg:left-1/2 lg:-translate-x-1/2">
                    <template
                        x-for="(searchResultsByColumn, index) in searchResultsByType"
                        :key="index"
                    >
                        <div :class="{'flex-grow': (index === 'main'), 'max-lg:border-b max-lg:border-secondary-900/50 sm:grid sm:grid-cols-2 lg:block lg:border-r lg:border-secondary-900/50 lg:w-60 xl:w-52 md:basis-80 lg:basis-auto lg:flex-shrink-0': (index === 'sidebar')}">
                            <template x-for="searchResultByType in Object.values(searchResultsByColumn)">
                                <div :class="{'h-full flex flex-col': (index === 'main'), '': (index === 'sidebar')}">
                                    <template x-if="index !== 'main' && searchResultByType.hasOwnProperty(0) && templates[searchResultByType[0].type].title && templates[searchResultByType[0].type].titleRenderer === undefined">
                                        <div class="px-4 pt-4 mb-2 text-sm font-semibold" x-text="templates[searchResultByType[0].type].title"></div>
                                    </template>
                                    <template x-if="index !== 'main' && searchResultByType.hasOwnProperty(0) && templates[searchResultByType[0].type].titleRenderer !== undefined">
                                        <div class="px-4 pt-4 mb-2 text-sm font-semibold" x-text="window[templates[searchResultByType[0].type].titleRenderer](searchResultByType)"></div>
                                    </template>

                                    <div :class="{'grid md:pb-0 grid-cols-1 sm:grid-cols-2 sm:[&>*:nth-child(2n)]:border-r sm:[&>*:nth-child(2n)]:border-secondary-900/50 flex-grow': (index === 'main'), 'flex flex-col': (index === 'sidebar')}">
                                        <template x-for="searchResult in searchResultByType">
                                            <div
                                                :class="{'border-b border-secondary-900/50 lg:hover:bg-secondary-300 transition duration-300 ease-in-out': (index === 'main'), '': (index === 'sidebar')}"
                                            >
                                                <?php foreach(json_decode($block->getJsonSuggestRenderers(), true) as $renderer): ?>
                                                    <?= /** @phpstan-ignore-next-line */
                                                    $block->getLayout()
                                                        ->createBlock(\Magento\Framework\View\Element\Template::class)
                                                        ->setTemplate($renderer['template'])
                                                        ->setData('suggestion_url', $suggestionUrl)
                                                        ->toHtml()
                                                    ?>
                                                <?php endforeach; ?>
                                            </div>
                                        </template>
                                    </div>

                                    <template x-if="index === 'main'">
                                        <div class="flex justify-center w-full p-4 -mt-px bg-secondary lg:p-6 rounded-b-xl lg:rounded-bl-none lg:rounded-br-xl">
                                            <a :href="searchLink" class="text-link after:hidden before:hover:delay-0">
                                                <?= $escaper->escapeHtml(__('See products in')); /** @phpstan-ignore-line */ ?>
                                                <span class="ml-1 font-semibold"><?= $escaper->escapeHtml(__('all categories')); /** @phpstan-ignore-line */ ?></span>
                                            </a>
                                        </div>
                                    </template>
                                </div>
                            </template>
                        </div>
                    </template>
                </div>
            </div>
            <?= $block->getChildHtml() ?>
        </form>
    </div>
</div>
