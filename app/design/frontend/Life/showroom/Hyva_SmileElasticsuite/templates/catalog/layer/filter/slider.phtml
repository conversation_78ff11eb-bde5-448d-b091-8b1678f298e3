<?php

declare(strict_types=1);

use Life\ThemeConfigurations\ViewModel\HtmxUpdate;

/** @var \Smile\ElasticsuiteCatalog\Block\Navigation\Renderer\Slider $block */
/** @var \Magento\Framework\Escaper $escaper */

$filter = $block->getFilter();
$item = current($filter->getItems());
if (!$item) {
    return '';
}

$uniqueId = '_' . uniqid();
$jsConfig = $block->getJsonConfig();
$dataRole = (string)$block->getDataRole();
$isPriceSlider = (strpos($dataRole, 'price') !== false)
?>

<style>
    .es-range-slider input[type=range] {
        pointer-events: none;
    }

    .es-range-slider .thumb {
        margin-top: -0.5rem;
        width: 1.5rem;
        height: 1.5rem;
    }

    .es-range-slider input[type=range] {
        margin-top: -0.5rem;
        height: 1.5rem;
    }

    .es-range-slider input[type=range]::-webkit-slider-thumb {
        pointer-events: all;
        width: 1.5rem;
        height: 1.5rem;
        border-radius: 0px;
        border: 0 none;
        -webkit-appearance: none;
    }

    .es-range-slider input[type=range]::-moz-range-thumb {
        pointer-events: all;
        width: 1.5rem;
        height: 1.5rem;
        border-radius: 0px;
        border: 0 none;
        -webkit-appearance: none;
    }

    .es-range-slider input[type=range]::-ms-thumb {
        pointer-events: all;
        width: 1.5rem;
        height: 1.5rem;
        border-radius: 0px;
        border: 0 none;
        -webkit-appearance: none;
    }

</style>
<div
    class="smile-es-range-slider"
    data-role="<?= $block->getDataRole(); ?>"
>
    <div
        x-data='rangeSlider({}, <?= $jsConfig ?>, <?= $isPriceSlider ? 'true' : 'false' ?>)'
        x-init="initialize()"
        class="relative w-full max-w-xl pt-2"
    >
        <div class="flex items-center justify-between mb-8 gap-x-2">
            <div>
                <span class="sr-only"><?= $escaper->escapeHtml(__('Minimum value')) ?></span>
                <span
                    x-model="minprice"
                    x-text="fromLabel"
                    class="w-24 px-3 py-2 text-center border border-gray-200 rounded"
                ></span>
            </div>
            <div>
                <span class="sr-only"><?= $escaper->escapeHtml(__('Maximum value')) ?></span>
                <span
                    x-model="maxprice"
                    x-text="toLabel"
                    class="w-24 px-3 py-2 text-center border border-gray-200 rounded"
                ></span>
            </div>
        </div>


        <div
            class="relative bg-gray-200 rounded-md es-range-slider"
            role="group"
            aria-label="<?= $escaper->escapeHtmlAttr(__('Range slider')) ?>"
            aria-describedby="range-handler"
        >
            <input
                type="range"
                step="1"
                x-bind:min="min"
                x-bind:max="max"
                x-on:input="mintrigger"
                @focus="() => updateFocusState('min', true)"
                @blur="() => updateFocusState('min', false)"
                x-bind:style="'z-index: ' + minZindex"
                x-model="minprice"
                class="absolute w-full h-2 opacity-0 appearance-none cursor-pointer pointer-events-none"
                aria-label="<?= $escaper->escapeHtmlAttr(__('Minimum price value, use left and right arrows to adjust.')) ?>"
            />
            <input
                type="range"
                step="1"
                x-bind:min="min"
                x-bind:max="max"
                x-on:input="maxtrigger"
                @focus="() => updateFocusState('max', true)"
                @blur="() => updateFocusState('max', false)"
                x-bind:style="'z-index: ' + maxZindex"
                x-model="maxprice"
                class="absolute w-full h-2 opacity-0 appearance-none cursor-pointer pointer-events-none"
                aria-label="<?= $escaper->escapeHtmlAttr(__('Maximum price value, use left and right arrows to adjust.')) ?>"
            />

            <div
                class="relative z-10 h-2"
                style="height: 0.5rem; margin-right: 1.5rem;"
            >
                <div class="absolute top-0 bottom-0 left-0 right-0 z-10"></div>
                <div
                    class="absolute top-0 bottom-0 z-20 rounded-md bg-primary"
                    x-bind:style="'right:'+ (100 - 1 - maxthumb)+'%; left:'+ minthumb+'%'"
                ></div>
                <div
                    :class="{ 'ring': minfocused }"
                    class="absolute top-0 left-0 z-30 w-6 h-6 -mt-2 rounded-full bg-primary thumb"
                    x-bind:style="'left: '+minthumb+'%'"
                ></div>
                <div
                    :class="{ 'ring': maxfocused }"
                    class="absolute top-0 right-0 z-30 w-6 h-6 -mt-2 rounded-full bg-primary thumb"
                    x-bind:style="'left: '+maxthumb+'%'"
                ></div>
            </div>
        </div>

        <div class="flex items-center justify-between h-6 mt-8 mb-2.5">
            <div x-html="message"></div>
            <div class="">
                <button
                    class="justify-center w-auto px-4 mr-auto text-sm btn btn-primary md:px-2 lg:px-4 min-w-[5rem]"
                    id="apply-btn"
                    x-ref="button"
                    x-model="url"
                    :hx-get="url"
                    hx-indicator="#products-loader, #products-loader-small"
                    hx-select=".products-grid"
                    hx-select-oob="<?= HtmxUpdate::CATEGORY_ELEMENTS_ID; ?>"
                    hx-target=".products-grid"
                    hx-swap="outerHTML"
                    hx-replace-url="true"
                    :aria-label="
                        hyva.str('<?= $escaper->escapeJs(__('Apply pricing filtering, minimum price %1, maximum price %2')) ?>', minprice, maxprice) + ', ' +
                        hyva.str('<?= $escaper->escapeJs(__('%1 products available')) ?>', count)
                    "
                >
                    <span><?= __('OK'); ?></span>
                </button>
            </div>
        </div>
    </div>
</div>
