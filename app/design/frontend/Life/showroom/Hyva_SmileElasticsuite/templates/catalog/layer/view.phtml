<?php

declare(strict_types=1);

use Hyva\Theme\ViewModel\SvgIcons;
use Life\CategoryLayeredNavigationSpecialPage\ViewModel\SpecialCategoryPage;
use Life\ThemeConfigurations\ViewModel\HtmxUpdate;
use Magento\Catalog\Helper\Data;

/** @var \Magento\LayeredNavigation\Block\Navigation $block */
/** @var \Magento\Framework\Escaper $escaper */
/** @var ViewModelRegistry $viewModels */

/** @var Data $catalogHelper */
$catalogHelper = $this->helper(Data::class);

/** @var SvgIcons $svgIcons */
$svgIcons = $viewModels->require(SvgIcons::class);

/** @var SpecialCategoryPage $specialCategoryPageViewModel */
$specialCategoryPageViewModel = $viewModels->require(SpecialCategoryPage::class);

$activeFiltersName = [];
$activeFiltersCount = 0;
$activeFiltersByAttribute = [];
foreach ($block->getLayer()->getState()->getFilters() as $activeFilter) {
    $filterName = (string)$activeFilter->getName();

    // Category is pre-selected in the background for special category page, so it should not appear as active
    if (
        $filterName === (string)__('Category')
        && $specialCategoryPageViewModel->isSpecialCategoryPage()
    ) {
        continue;
    }

    $activeFiltersName[] = $filterName;
    if (isset($activeFiltersByAttribute[$filterName])) {
        $activeFiltersByAttribute[$filterName] = $activeFiltersByAttribute[$filterName] + 1;
    } else {
        $activeFiltersByAttribute[$filterName] = 1;
    }
    $activeFiltersCount++;
}
$activeFilters = json_decode($block->getActiveFilters());

$quickViewLimit = 3;
?>
<?php if ($block->canShowBlock()) : ?>
    <div class="relative product-filter">
        <!-- Layered navigation quick view -->
        <?php $filterIndex = 0; ?>
        <div class="flex flex-wrap justify-between gap-2 mb-2 md:gap-y-4">
            <div
                class="flex gap-2"
                id="quick-filters"
            >
                <div
                    class="flex-wrap gap-2 flex"
                    x-data="{ openedIndex: -1 }"
                >
                    <?php foreach ($block->getFilters() as $filter) : ?>
                        <?php
                        // For category pages there is no need to have category filter
                        if ($catalogHelper->getCategory() && ((string)$filter->getName() === (string)__('Category'))) {
                            continue;
                        }

                        if ($filterIndex > 2) {
                            break;
                        }
                        ?>
                        <?php if ($filter->getItemsCount() || in_array($filter->getName(), $activeFiltersName)) : ?>
                            <?php
                            $filterRenderedContent = $block->getChildBlock('renderer')->render($filter);
                            if (empty(trim($filterRenderedContent))) {
                                continue;
                            }
                            ?>

                            <div
                                role="group"
                                aria-labelledby="filter-option-<?= $filterIndex ?>-title"
                                class="filter-option relative hidden md:block"
                                id="<?= $filter->getData('attribute_model')?->getData('attribute_code') ?>"
                            >
                                <div
                                    id="filter-option-<?= $filterIndex ?>-title"
                                    class="filter-options-title"
                                >
                                    <button
                                        class="btn btn-secondary flex items-center justify-between w-full cursor-pointer gap-x-2"
                                        @click="openedIndex == <?= $filterIndex ?> ? openedIndex = -1 : openedIndex = <?= $filterIndex ?>"
                                        aria-controls="filter-option-<?= $filterIndex ?>-content"
                                        :aria-expanded="openedIndex == <?= $filterIndex ?>"
                                    >
                                        <span class="flex items-center text-base leading-7 title gap-x-2">
                                            <?= $escaper->escapeHtml(__($filter->getName())) ?>
                                            <?php if (
                                                $activeFiltersByAttribute
                                                && !empty($activeFiltersByAttribute[(string)$filter->getName()])
                                            ) : ?>
                                                <span
                                                    class="flex items-center justify-center w-6 h-6 bg-white border rounded-full border-secondary-900/50 text-[0.6875rem]"
                                                >
                                                    <?= $activeFiltersByAttribute[(string)$filter->getName()] ?>
                                                </span>
                                            <?php endif; ?>
                                            <span class="sr-only"><?= $escaper->escapeHtml(__(' filter')) ?></span>
                                        </span>
                                    </button>
                                </div>
                                <div
                                    id="filter-option-<?= $filterIndex ?>-content"
                                    class="absolute left-0 z-[25] p-5 bg-white shadow-lg rounded-xl min-w-max top-[calc(100%+0.5rem)]"
                                    x-show="openedIndex == <?= $filterIndex ?>"
                                    style="display: none;"
                                >
                                    <?= /* @noEscape */ $filterRenderedContent ?>
                                </div>
                            </div>
                            <?php
                            $filterIndex++;
                            ?>
                        <?php endif; ?>
                    <?php endforeach; ?>
                    <div x-data="{ defaultBtnVisible: null, productsVisible: null }" @products-visible.window="productsVisible = $event.detail.visible">
                        <div
                            role="group"
                            class="btn btn-primary gap-2 all-filters<?php if ($filterIndex < $quickViewLimit) : ?> md:hidden<?php endif; ?>"
                            x-intersect.full="defaultBtnVisible = true"
                            x-intersect:leave.full="defaultBtnVisible = false"
                            @click.prevent.stop="$dispatch('toggle-all-filters')"
                        >
                            <span><?= $escaper->escapeHtml(__('All filters')); ?></span>
                            <?php if ($activeFiltersCount) : ?>
                                <span
                                    class="flex items-center justify-center w-6 h-6 bg-white border rounded-full border-secondary-900/50 text-[0.6875rem] text-black"
                                ><?= $escaper->escapeHtml($activeFiltersCount) ?></span>
                            <?php endif; ?>
                            <?= $svgIcons->filterHtml('', 24, 24, ["aria-hidden" => "true"]); ?>
                        </div>

                        <!-- Filter button copy -->
                        <div
                            role="group"
                            class="btn btn-primary gap-2 fixed z-50 motion-safe:transition-all ease-in-out transform -translate-x-1/2 all-filters md:!hidden duration-250 left-1/2"
                            @click.prevent.stop="$dispatch('toggle-all-filters')"
                            :class="{ '-bottom-12': defaultBtnVisible || !productsVisible, 'bottom-6': !defaultBtnVisible && productsVisible }"
                        >
                            <span><?= $escaper->escapeHtml(__('All filters')); ?></span>
                            <?php if ($activeFiltersCount) : ?>
                                <span
                                    class="flex items-center justify-center w-6 h-6 bg-white border rounded-full border-secondary-900/50 text-[0.6875rem] text-black"
                                ><?= $escaper->escapeHtml($activeFiltersCount) ?></span>
                            <?php endif; ?>
                            <?= $svgIcons->filterHtml('', 24, 24, ["aria-hidden" => "true"]); ?>
                        </div>
                    </div>
                </div>

                
            </div>
            <?= $block->getChildHtml('state') ?>
        </div>

        <div
            role="region"
            aria-label="<?= $escaper->escapeHtmlAttr(__('Product filters')) ?>"
            class="
                fixed top-0 bottom-0 left-0 w-full z-product-filter block
                motion-safe:transition motion-safe:duration-500 motion-safe:ease-in-out
            "
            x-data="initLayeredNavigation()"
            @toggle-all-filters.window="visible = !visible"
            x-show="visible"
            x-cloak
            x-transition:enter-start="opacity-0"
            x-transition:enter-end="opacity-100"
            x-transition:leave-start="opacity-100"
            x-transition:leave-end="opacity-0"
        >
            <div
                @click="visible = false"
                class="absolute inset-0 bg-black/30"
            ></div>

            <div
                class="fixed right-0 z-10 h-full px-10 bg-white w-[30rem] max-w-[90%] overflow-y-auto filter-modal"
            >
                <header class="text-center py-6 sticky top-0 z-10 bg-white">
                    <h2 class="font-normal text-base">
                        <?= $escaper->escapeHtml(__('All filters')) ?>
                    </h2>
                    <button 
                        @click.prevent="visible = false"
                        class="inline-flex min-w-[44px] min-h-[44px] justify-center items-center absolute right-0 top-1/2 -translate-y-1/2 translate-x-4"
                    >
                        <?= $svgIcons->closeHtml("", 24, 24, ['aria-hidden' => 'true']) ?>
                        <span class="sr-only"><?= $escaper->escapeHtml(__('Close filter')) ?></span>    
                    </button>
                </header>
                <div
                    id="filters-content"
                    class="block-content filter-content h-[calc(100vh-5.625rem)] pb-20"
                >
                    <?php $filterIndex = 0; ?>
                    <a
                        href="#products-list"
                        class="block px-12 py-4 text-center text-white bg-blue-600 rounded-sm sr-only focus:not-sr-only"
                    >
                        <?= $escaper->escapeHtml(__('Skip to product list')) ?>
                    </a>

                    <?php foreach ($block->getFilters() as $filter) : ?>
                        <?php $showOpen = in_array($filterIndex, $activeFilters) ?>
                        <?php
                        // For category pages there is no need to have category filter
                        if ($catalogHelper->getCategory() && ((string)$filter->getName() === (string)__('Category'))) {
                            continue;
                        }
                        ?>
                        <?php if ($filter->getItemsCount() || in_array($filter->getName(), $activeFiltersName)) : ?>
                            <?php
                            $filterRenderedContent = $block->getChildBlock('renderer')->render($filter);
                            if (empty(trim($filterRenderedContent))) {
                                continue;
                            }

                            $showOpen = in_array($filterIndex, $activeFilters);
                            ?>
                            <div
                                role="group"
                                aria-labelledby="filter-option-<?= $filterIndex ?>-title"
                                class="filter-option"
                                id="<?= $filter->getData('attribute_model')?->getData('attribute_code') ?>"
                                x-data="{ open: false }"
                            >
                                <h3
                                    id="filter-option-<?= $filterIndex ?>-title"
                                    class="filter-options-title font-semibold text-base"
                                >
                                    <button
                                        class="flex justify-between items-center py-3 w-full"
                                        :class="{ 'border-b': open }"
                                        @click="open = !open"
                                        aria-controls="filter-option-<?= $filterIndex ?>-content"
                                        :aria-expanded="false"
                                    >
                                        <span class="title">
                                            <?= $escaper->escapeHtml(__($filter->getName())) ?>
                                            <span class="sr-only"><?= $escaper->escapeHtml(__(' filter')) ?></span>
                                        </span>
                                        <svg
                                            width="24"
                                            height="24"
                                            viewBox="0 0 24 24"
                                            fill="none"
                                            xmlns="http://www.w3.org/2000/svg"
                                            class="transition-transform translate-x-2 transform duration-300 ease-in-out opacity-50"
                                            :class="open ? '-rotate-180' : '-rotate-90'"
                                            aria-hidden="true"
                                        >
                                            <path
                                                d="M19 9L12 16L5 9"
                                                stroke="#4A5568"
                                                stroke-width="2"
                                                stroke-linecap="round"
                                                stroke-linejoin="round"
                                            />
                                        </svg>
                                    </button>
                                </h3>
                                <div
                                    id="filter-option-<?= $filterIndex ?>-content"
                                    class="filter-options-content mt-4 mb-8"
                                    x-show="open"
                                >
                                    <?= /* @noEscape */ $filterRenderedContent ?>
                                </div>
                            </div>
                            <?php $filterIndex++ ?>
                        <?php endif; ?>
                    <?php endforeach; ?>

                    <?php if ($block->getLayer()->getState()->getFilters()) : ?>
                        <div class="absolute bottom-0 left-0 flex justify-center w-full gap-3 px-3 py-6 bg-gradient-to-b from-white/0 to-white">
                            <button
                                hx-get="<?= $escaper->escapeUrl($block->getClearUrl()) ?>"
                                hx-indicator="#products-loader, #products-loader-small"
                                hx-select=".products-grid"
                                hx-select-oob="<?= HtmxUpdate::CATEGORY_ELEMENTS_ID; ?>"
                                hx-target=".products-grid"
                                hx-swap="outerHTML"
                                hx-replace-url="true"
                                class="text-sm btn btn-secondary"
                            >
                                <?= $escaper->escapeHtml(__('Clear all')) ?>
                            </button>
                            <button
                                class="text-sm btn btn-primary grow"
                                @click="visible = false"
                            >
                                <?= $escaper->escapeHtml(__("Show %1 product(s)", $block->getLayer()->getProductCollection()->getSize())) ?>
                            </button>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <script defer="defer">
        function initLayeredNavigation() {
            return {
                visible: false,
            }
        }

    </script>
<?php endif; ?>
