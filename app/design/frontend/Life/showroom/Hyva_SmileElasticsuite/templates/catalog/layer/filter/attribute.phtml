<?php

declare(strict_types=1);

use Life\ThemeConfigurations\ViewModel\HtmxUpdate;

/** @var \Smile\ElasticsuiteCatalog\Block\Navigation\FilterRenderer $block */
/** @var \Smile\ElasticsuiteCatalog\Block\Navigation\Renderer\Attribute $this */
/** @var \Magento\Framework\Escaper $escaper */

try {
    $this->getFilter()->getAttributeModel();
} catch (\Exception $e) {
    return '';
}

$jsLayout = $this->getJsLayout();
$attributeData = json_decode($jsLayout);
$attributeOptions = $attributeData->items ?? [];
$datascope = $this->getFilter()->getRequestVar() . 'Filter';

?>
<?php if (!empty($filterItems)) : ?>
    <div
        class="<?= $escaper->escapeHtmlAttr($datascope); ?>"
        x-data='initSmileAttibute(<?= $jsLayout ?>)'
        x-init="initialize();"
    >
        <div class="field search"  x-show="displaySearch">
            <div class="control">
                <input
                    class="w-full mb-2 filter-search"
                    type="text"
                    autocomplete="off"
                    :placeholder="searchPlaceholder"
                    @keyup="onSearchChange"
                    @focusout="onSearchFocusOut"
                />
            </div>
        </div>
        <div class="items">
            <?php foreach ($attributeOptions as $option): ?>
                <div class="my-1.5 item pl-[0.1875rem]">
                    <button
                        hx-get="<?= $escaper->escapeUrl($option->url ?? ''); ?>"
                        hx-indicator="#products-loader, #products-loader-small"
                        hx-select=".products-grid"
                        hx-select-oob="<?= HtmxUpdate::CATEGORY_ELEMENTS_ID; ?>"
                        hx-target=".products-grid"
                        hx-swap="outerHTML"
                        hx-replace-url="true"
                        x-show="<?= $option->count ?? 0; ?> >= 1"
                        :rel="options.displayRelNofollow"
                        class="flex items-center"
                    >
                        <span class="checkbox <?= $option->is_selected ? 'checked' : ''; ?>"></span>
                        <span class="pl-2 cursor-pointer">
                            <span><?= $escaper->escapeHtml($option->label ?? ''); ?></span>
                            <span
                                class="count"
                                x-show ="options.displayProductCount"
                            >(<?= $escaper->escapeHtml($option->count ?? ''); ?>)</span>
                        </span>
                    </button>

                    <?php if ($option->count < 1): ?>
                        <div>
                            <span><?= $escaper->escapeHtml($option->label ?? ''); ?></span>
                            <span class="count">0</span>
                        </div>
                    <?php endif; ?>
                </div>
            <?php endforeach; ?>
        </div>

        <div
            class="no-results-message"
            x-model="result"
            x-show="getFulltextSearch() && !hasSearchResult()"
            class="empty"
        >
            <p x-html="getSearchResultMessage()"></p>
        </div>

        <div class="actions" x-show="enableExpansion()">
            <div class="text-right secondary">
                <a
                    class="underline cursor-pointer action show-more text-secondary"
                    x-on:click="onShowMore()"
                    x-show="displayShowMore()"
                >
                    <span x-text="showMoreLabel + '+'"></span>
                </a>
                <a
                    class="underline cursor-pointer action show-less text-secondary"
                    x-on:click="onShowLess()"
                    x-show="displayShowLess()"
                >
                    <span x-text="showLessLabel + '-'"></span>
                </a>
            </div>
        </div>
    </div>
<?php endif;
