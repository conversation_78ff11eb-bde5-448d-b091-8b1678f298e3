<?php

declare(strict_types=1);

/** @var \Magento\Framework\View\Element\Template $block */
/** @var \Magento\Framework\Escaper $escaper */
?>
<script defer="defer">
    function initResultsCounter () {
        return {
            label: '',
            show: false,
            showResults(event) {
                if (event.detail && event.detail.label) {
                    this.label = event.detail.label;
                    this.show = true;
                }
            }
        }
    }
</script>
<div
    x-data="initResultsCounter()"
    @show-results-count.window="showResults($event)"
    class="order-3 col-span-2 text-sm leading-5 text-black sm:order-2 md:order-3 lg:order-2 gap-x-1"
    x-show="show"
    x-cloak
    x-html="label"
>
</div>
