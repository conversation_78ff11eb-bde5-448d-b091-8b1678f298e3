<?php

declare(strict_types=1);

?>
<template x-if="searchResult.type == 'product'">
    <div class="relative flex items-center h-full px-4 py-2 md:py-4">
        <div class="w-2/12 mr-4 overflow-hidden image rounded-xl">
            <img :src="searchResult.image" :alt="searchResult.title" class="inline-block w-full" />
        </div>
        <div class="w-10/12">
            <a :href="searchResult.url" class="block after:absolute after:inset-0">
                <span class="text-sm" x-text="searchResult.title"></span>
            </a>
        </div>
    </div>
</template>
