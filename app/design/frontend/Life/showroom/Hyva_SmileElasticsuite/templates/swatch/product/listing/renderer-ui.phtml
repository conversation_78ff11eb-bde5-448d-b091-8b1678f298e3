<script defer="defer">
    function initListConfigurableSwatchOptions(element, data) {
        const configurableOptionsComponent = initConfigurableOptions(
            data.productId ?? null,
            data.config ?? []
        );
        const swatchOptionsComponent = initSwatchOptions(data.swatchConfig ?? []);
        const preselectedSwatchData = data.preselectedSwatchData ?? [];

        return Object.assign(
            configurableOptionsComponent,
            swatchOptionsComponent,
            preselectedSwatchData,
            {
                mouseDown: false,
                startX: 0,
                maxScroll: 0,
                scrollLeft: null,
                slider: null,
                show: false,
                productLink: data.productLink ?? '',

                init() {
                    let self = this;

                    setTimeout(function() {
                        window.dispatchEvent(new CustomEvent('has-swatch-colors-' + data.productId, {}))
                    }, 300);

                    setTimeout(function() {
                        for (let key in preselectedSwatchData) {
                            self.changeOption(key, preselectedSwatchData[key]);
                        }
                    }, 300);

                    window.addEventListener('toggle-swatch-colors', function(event) {
                        let productId = event?.detail?.productId;

                        if (productId == data.productId) {
                            self.show = !self.show;
                        }
                    });
                },

                changeOption(optionId, value, skipUpdateGallery) {
                    this.selectedValues[optionId] = value;
                    this.findSimpleIndex();
                    this.findAllowedAttributeOptions();
                    this.updatePrices();
                    this.updateLinks();
                    !skipUpdateGallery && this.updateGallery();
                },

                updateLinks() {
                    if (!this.productId || !this.productLink) {
                        return;
                    }

                    let urlQueryParams = new URLSearchParams();
                    this.selectedValues.forEach((value, key) => {
                        urlQueryParams.set(key, value);
                    });

                    if (!urlQueryParams.toString().length) {
                        return;
                    }

                    Array.from(document.querySelectorAll('[data-link-product-id="' + this.productId + '"]')).map(link => {
                        link.href = this.productLink + '?' + urlQueryParams.toString();
                    });
                },

                updateGallery() {
                    if (!this.productIndex) {
                        return;
                    }

                    window.dispatchEvent(
                        new CustomEvent(
                            "update-list-images-" + data.productId,
                            {detail: this.productIndex}
                        )
                    );
                },

                preselectQuerystringItems() {
                    // pre-select option like ?size=167
                    const urlQueryParams = new URLSearchParams(window.location.search.replace('?', ''));
                    Object.values(this.optionConfig.attributes).map(attribute => {
                        const skipUpdateGallery = false;
                        if (urlQueryParams.get(attribute.code) !== null) {
                            let optionId = urlQueryParams.get(attribute.code);
                            let found = false;
                            Object.values(attribute.options).map(option => {
                                if (found === false && option.label === optionId) {
                                    optionId = option.id;
                                }
                            });
                            this.changeOption(attribute.id, optionId, skipUpdateGallery);
                        }
                    });
                },

                scrollEvents: {
                    ['@mousedown'](e) {
                        this.slider = e.target.closest('.snap');
                        if (!this.slider) {
                            return;
                        }
                        this.maxScroll = this.slider.scrollWidth - this.slider.offsetWidth;
                        this.startX = e.pageX - this.slider.offsetLeft;
                        this.scrollLeft = this.slider.scrollLeft;
                        this.mouseDown = true;
                    },
                    ['@mouseout.self']() {
                        this.mouseDown = false;
                    },
                    ['@mouseup']() {
                        this.mouseDown = false;
                    },
                    ['@mousemove'](e) {
                        e.preventDefault();
                        if (!this.mouseDown) {
                            return;
                        }
                        const x = e.pageX - this.slider.offsetLeft;
                        const scroll = x - this.startX;
                        const scrollLeft = this.scrollLeft - scroll;

                        if (scrollLeft > this.maxScroll) {
                            this.slider.scrollLeft = this.maxScroll;
                            return
                        }
                        this.slider.scrollLeft = this.scrollLeft - scroll;
                    },
                    ['@onselectstart']() {
                        return false;
                    }
                },

                resizeEvent() {
                    Array.from(this.$el.querySelectorAll('.snap')).forEach(slider => {
                        slider.scrollLeft = 0;
                    })
                }
            }
        );
    }
</script>
