<?xml version="1.0"?>
<page
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd"
    layout="1column"
>
    <body>
        <referenceBlock name="head.additional">
            <block template="Magento_Theme::head/htmx.phtml" />
        </referenceBlock>
        <referenceContainer name="content">
            <block
                name="search.results.count"
                template="Hyva_SmileElasticsuite::catalog/list/results-count.phtml"
                before="-"
            />
        </referenceContainer>

        <referenceBlock name="category.product.type.details.renderers">
            <block
                class="Magento\Swatches\Block\Product\Renderer\Listing\Configurable"
                name="category.product.type.details.renderers.configurable"
                as="configurable"
                template="Hyva_SmileElasticsuite::swatch/product/listing/renderer.phtml"
                ifconfig="catalog/frontend/show_swatches_in_product_list"
            />
        </referenceBlock>

        <referenceBlock name="search_result_list">
            <block
                class="Magento\LayeredNavigation\Block\Navigation\State"
                name="product.list.active.filters"
                template="Hyva_SmileElasticsuite::swatch/product/listing/filter.phtml"
            />
        </referenceBlock>

        <referenceContainer name="before.body.end">
            <block
                name="product.list.active.filters.ui"
                template="Hyva_SmileElasticsuite::swatch/product/listing/filter-ui.phtml"
            />
        </referenceContainer>

        <move element="catalogsearch.leftnav" destination="search_result_list" />
        <move element="search.results.count" destination="page.main.title" />
        <referenceBlock name="page.main.title" remove="true" />
        <referenceBlock name="catalogsearch.navigation.renderer.category" remove="true" />
        <referenceContainer name="page.messages" remove="true" />
    </body>
</page>
