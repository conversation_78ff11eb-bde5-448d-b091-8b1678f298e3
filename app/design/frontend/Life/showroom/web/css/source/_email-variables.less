//
//  Email variable overrides
//  _____________________________________________

//  This file should be used to set theme-specific variables for emails.
//  By default, emails will inherit variables from the Magento UI Library and the _theme.less and _variables.less files
//  in a custom theme. Use this file if you want to set specific variables for emails.
//  For example, you can make the email font, typography, colors, etc unique from the frontend theme.

//  Example usage:
//  @link__color: @color-dark-green1;
//  @link__text-decoration: underline;
//
//  @email__background-color: @color-heathered-grey; // Change background to darker grey color

@font-family__base: 'Verdana', sans-serif;
@text__color: #000;
@h1__font-weight: bold;
@h1__font-color: @text__color;
@h2__font-size: 16px;
@h2__line-height: 24px;
@h3__font-weight: bold;
@h3__font-color: @text__color;
@h3__font-family: @font-family__base;
@h3__font-size: 14px;
@h3__line-height: 19px;
@font-size__base: 13px;
@link__color: @color-primary;
@color-primary: #00281E;
@color-primary-light: #033B2C;
@color-secondary: #E6DFD4;
@color-secondary-dark: #D5C7B3;
@color-white: #ffffff;