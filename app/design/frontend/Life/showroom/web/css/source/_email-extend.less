//
//  Email extend styles
//  _____________________________________________

//  This file should be used for theme-specific styles for emails that extend or modify the styles in _email.less
//  This will allow you to edit email styles without copying and editing the _email.less file

//  Custom fonts are declared here, rather than in _email-base.less, as it allows for custom fonts to be changed/removed
//  in custom themes.

//  Importing fonts from an external CSS file, rather than embedding @font-face declarations inside the <style> tag,
//  as the latter will cause font rendering issues if the web fonts are inaccessible.

@import url("@{baseUrl}css/email-fonts.css");

// Remove gray background color from table
.shipment-track {
    thead,
    tfoot {
        > tr {
            > th,
            > td {
                background-color: @color-white !important;
            }

            > th {
                &:first-child {
                    padding: 12px 0 !important;
                }
            }
        }
    }
}
.email-items {
    thead,
    tfoot {
        > tr {
            > th,
            > td {
                background-color: @color-white !important;
            }
        }
    }

    tbody {
        tr {
            td {
                dt,
                dd {
                    line-height: 20px !important;
                }
            }
        }
    }
}

.main {
    max-width: 600px !important;
    width: 100% !important;
}

.main-content {
    padding: 40px 12px !important;

    p {
        line-height: 18px !important;
    }

    a {
        color: @color-primary !important;
        text-decoration: underline !important;

        &:hover {
            color: @color-primary !important;
            text-decoration: none !important;
        }
    }

    ul {
        margin: 0 !important;
        padding-left: 24px !important;

        li {
            margin-bottom: 4px !important;
        }
    }
}

table.button {
    .inner-wrapper {
        margin: 0 !important;

        tr {
            td {
                background: none !important;

                a {
                    background: @color-primary !important;
                    border: none !important;
                    border-radius: 999px !important;
                    color: @color-white !important;
                    font-size: 14px !important;
                    line-height: 20px !important;
                    margin: 16px 0 !important;
                    padding: 12px 16px !important;
                    text-decoration: none !important;

                    &:hover {
                        background: @color-primary-light !important;
                        color: @color-white !important;
                    }
                }

                &:hover {
                    background: none !important;
                }
            }
        }
    }
}

.email-button-primary {
    &:visited {
        color: @color-white !important;
    }
}

.header {
    padding: 16px 12px 0 !important;
}

.footer {
    background: @color-primary;
    color: @color-white;
    max-width: 600px;
    width: 100%;

    > tr {
        > td {
            padding: 20px 12px 40px !important;

            &.footer-logo {
                padding: 40px 12px 20px !important;
            }
        }
    }

    table {
        tr {
            td {
                padding: 0 !important;
            }
        }
    }

    p {
        font-size: 16px;
        line-height: 24px;
        margin-bottom: 0 !important;
    }

    .spacing {
        height: 16px;
        width: 16px;
    }

    table.button {
        width: 120px;

        .inner-wrapper {
            tr {
                td {
                    a {
                        background: @color-secondary !important;

                        &:hover {
                            background: @color-secondary-dark !important;
                        }
                    }
                }
            }
        }
    }
}

@media only screen and (max-width: 479px) {
    .footer table tr td {
        display: block !important;
    }
}