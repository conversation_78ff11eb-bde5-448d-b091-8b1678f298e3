:root {
    /* makes sure iOS native controls do not overlay the header */
    @apply max-h-[100dvh];

    /* custom CSS properties */
    --max-w-container: 1024px;
    --p-container: 1rem;
    --h-header: 5.75rem;
}

html {
    @apply scroll-pt-48 md:scroll-pt-40 h-full motion-safe:scroll-smooth;
}

body {
    @apply pb-header min-h-full;
    text-decoration-skip-ink: none;
}

button,
[type="button"],
[type="reset"],
[type="submit"],
[type="image"],
[type="checkbox"],
[type="radio"],
summary,
select {
	@apply cursor-pointer;
}

[disabled],
[readonly] {
    @apply cursor-not-allowed opacity-50;
}

.container {
    @apply w-full max-w-container mx-auto px-container;
}

.page-wrapper {
    @apply flex flex-col grow py-container;
}

.page-footer {
    @apply mt-auto;
}
