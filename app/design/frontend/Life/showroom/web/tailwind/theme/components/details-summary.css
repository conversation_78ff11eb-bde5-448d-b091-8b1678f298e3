
details {
    @apply bg-secondary rounded-xl px-6;

    > *:not(summary) {
        @apply pb-6;
    }
  
    summary {
        @apply flex py-6 pr-8 relative items-center cursor-pointer list-none font-semibold leading-[1.875];

        &::-webkit-details-marker {
            @apply hidden;
        }

        &::before,
        &::after {
            @apply content-[''] w-4 h-[2px] absolute top-1/2 right-0 -translate-y-1/2 inline-block bg-primary text-4xl transition-transform;
        }

        &::after {
            @apply rotate-90;
        }
    }
  
    /* open state */
    &[open] {
        @apply bg-secondary-300;
        
        summary {
            &::before {
                @apply rotate-90 opacity-0;
            }
            &::after {
                @apply rotate-180;
            }
        }
    }
}