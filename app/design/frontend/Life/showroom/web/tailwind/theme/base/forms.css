/**
 * different styles can be found at https://tailwindcss-forms.vercel.app/
 **/
 .form-input,
 .form-email,
 .form-select,
 .form-multiselect,
 .form-textarea,
 input[type="text"],
 input[type="password"],
 input[type="url"],
 input[type="tel"],
 input[type="search"],
 input[type="number"],
 input[type="date"],
 input[type="datetime"],
 input[type="email"],
 textarea,
 select {
    &:not(.input-unstyled) {
        @apply px-4 text-base border rounded-md appearance-none border-[#DCDCDC] text-primary placeholder:text-black/50 min-h-[2.5rem] bg-none focus:ring-0;

        &:not([readonly]):focus {
            @apply border-primary;
        }

        &::-webkit-search-decoration,
        &::-webkit-search-cancel-button,
        &::-webkit-search-results-button,
        &::-webkit-search-results-decoration {
            @apply hidden w-0 h-0;
        }
    }

    .field-error & {
        @apply border-error;
    }
}

textarea {
    @apply resize-y;
}

span.checkbox,
input[type="checkbox"] {
    @apply rounded-sm appearance-none border-white !ring-1 !ring-primary !ring-offset-2 w-2.5 h-2.5 transition-all duration-300;

    /* hover */
    @apply hover:bg-none hover:bg-primary/50 hover:text-primary/50;

    /* focus */
    &:focus-visible {
        @apply bg-none outline-2 outline-primary;
    }

    /* checked */
    &.checked,
    &:checked {
        @apply bg-none bg-primary text-primary;

        /* hover */
        @apply hover:bg-none hover:bg-primary hover:text-primary;

    }
}

input[type="radio"] {
    @apply rounded-full appearance-none border-white !ring-1 !ring-primary !ring-offset-2 w-2.5 h-2.5 transition-all duration-300;

     /* hover */
     @apply hover:bg-none hover:bg-primary/50 hover:text-primary/50;

     /* focus */
     &:focus-visible {
        @apply bg-none outline-2 outline-primary;
    }


    &:checked {
        @apply bg-none bg-primary text-primary;

        /* hover */
        @apply hover:bg-none hover:bg-primary hover:border-primary;
    }
}

form,
fieldset {
    .field.field-reserved {
        @apply mb-3;
    }

    .field.field-reserved ul {
        @apply text-sm;
    }

    label {
        @apply block mb-1 text-primary;
    }

    .field.choice {
        @apply flex items-center;
    }

    .field.choice input {
        @apply mr-4;
    }

    .field.choice label {
        @apply mb-0;
    }

    .cart.message.item.error,
    .field.field-error .messages {
        @apply pb-0 mt-1 text-red-500;
        max-width: fit-content;
    }

    legend {
        @apply mb-3 text-xl text-primary;
    }

    legend + br {
        @apply hidden;
    }
}

fieldset ~ fieldset {
    @apply mt-8;
}

.label {
    @apply text-xs;
}