@layer components {
    .btn {
        @apply relative inline-flex items-center justify-center px-5 py-3 overflow-hidden text-base text-white text-center transition-all duration-500 ease-in-out !rounded-full bg-primary;
        

        svg {
            @apply inline-flex;
        }

        span {
            @apply relative z-10 align-middle;
        }

        svg {
            @apply relative z-10;
        }

        &:focus-visible {
             @apply border-transparent outline-none ring-4 ring-primary/50;
        }
    }

    .btn-primary {
        @apply text-white bg-primary;
    }

    .btn-secondary {
        @apply text-primary bg-secondary;

        &:focus-visible {
            @apply ring-secondary-700/50;
       }
    }

    .btn-secondary-outline {
        @apply inline-flex gap-2 bg-transparent border border-secondary-900 text-primary;

        &:active {
            @apply bg-secondary-900/20;
        }
    }

    .btn-size-lg {
        @apply px-10 py-4 text-lg;
    }

    .btn-size-sm {
        @apply px-2 py-2 text-sm;
    }

    .text-link {
        &:focus-visible {
            @apply rounded-sm outline outline-1 outline-primary/50 outline-offset-2;
        }
    }
}

@layer utilities {
    .a11y-pointer {
        @apply relative isolate;

        &::after {
            @apply content-[""] inline-block absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 -z-10 min-w-[44px] min-h-[44px];

            @media (pointer: fine) {
                @apply min-w-[24px] min-h-[24px];
            }
        }
    }
}

