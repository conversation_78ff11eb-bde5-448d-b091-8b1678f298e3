/* purgecss start ignore */
/* ! tailwindcss v2.2.9 | MIT License | https://tailwindcss.com */
/*! modern-normalize v1.1.0 | MIT License | https://github.com/sindresorhus/modern-normalize */
/*
Document
========
*/
/**
Use a better box model (opinionated).
*/
*,
::before,
::after {
	box-sizing: border-box;
}
/**
Use a more readable tab size (opinionated).
*/
html {
	-moz-tab-size: 4;
	-o-tab-size: 4;
	   tab-size: 4;
}
/**
1. Correct the line height in all browsers.
2. Prevent adjustments of font size after orientation changes in iOS.
*/
html {
	line-height: 1.15; /* 1 */
	-webkit-text-size-adjust: 100%; /* 2 */
}
/*
Sections
========
*/
/**
Remove the margin in all browsers.
*/
body {
	margin: 0;
}
/**
Improve consistency of default fonts in all browsers. (https://github.com/sindresorhus/modern-normalize/issues/3)
*/
body {
	font-family:
		system-ui,
		-apple-system, /* Firefox supports this but not yet `system-ui` */
		'Segoe UI',
		Roboto,
		Helvetica,
		Arial,
		sans-serif,
		'Apple Color Emoji',
		'Segoe UI Emoji';
}
/*
Grouping content
================
*/
/**
1. Add the correct height in Firefox.
2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)
*/
hr {
	height: 0; /* 1 */
	color: inherit; /* 2 */
}
/*
Text-level semantics
====================
*/
/**
Add the correct text decoration in Chrome, Edge, and Safari.
*/
abbr[title] {
	-webkit-text-decoration: underline dotted;
	        text-decoration: underline dotted;
}
/**
Add the correct font weight in Edge and Safari.
*/
b,
strong {
	font-weight: bolder;
}
/**
1. Improve consistency of default fonts in all browsers. (https://github.com/sindresorhus/modern-normalize/issues/3)
2. Correct the odd 'em' font sizing in all browsers.
*/
code,
kbd,
samp,
pre {
	font-family:
		ui-monospace,
		SFMono-Regular,
		Consolas,
		'Liberation Mono',
		Menlo,
		monospace; /* 1 */
	font-size: 1em; /* 2 */
}
/**
Add the correct font size in all browsers.
*/
small {
	font-size: 80%;
}
/**
Prevent 'sub' and 'sup' elements from affecting the line height in all browsers.
*/
sub,
sup {
	font-size: 75%;
	line-height: 0;
	position: relative;
	vertical-align: baseline;
}
sub {
	bottom: -0.25em;
}
sup {
	top: -0.5em;
}
/*
Tabular data
============
*/
/**
1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)
2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)
*/
table {
	text-indent: 0; /* 1 */
	border-color: inherit; /* 2 */
}
/*
Forms
=====
*/
/**
1. Change the font styles in all browsers.
2. Remove the margin in Firefox and Safari.
*/
button,
input,
optgroup,
select,
textarea {
	font-family: inherit; /* 1 */
	font-size: 100%; /* 1 */
	line-height: 1.15; /* 1 */
	margin: 0; /* 2 */
}
/**
Remove the inheritance of text transform in Edge and Firefox.
1. Remove the inheritance of text transform in Firefox.
*/
button,
select { /* 1 */
	text-transform: none;
}
/**
Correct the inability to style clickable types in iOS and Safari.
*/
button,
[type='button'],
[type='reset'],
[type='submit'] {
	-webkit-appearance: button;
}
/**
Remove the inner border and padding in Firefox.
*/
::-moz-focus-inner {
	border-style: none;
	padding: 0;
}
/**
Restore the focus styles unset by the previous rule.
*/
:-moz-focusring {
	outline: 1px dotted ButtonText;
}
/**
Remove the additional ':invalid' styles in Firefox.
See: https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737
*/
:-moz-ui-invalid {
	box-shadow: none;
}
/**
Remove the padding so developers are not caught out when they zero out 'fieldset' elements in all browsers.
*/
legend {
	padding: 0;
}
/**
Add the correct vertical alignment in Chrome and Firefox.
*/
progress {
	vertical-align: baseline;
}
/**
Correct the cursor style of increment and decrement buttons in Safari.
*/
::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
	height: auto;
}
/**
1. Correct the odd appearance in Chrome and Safari.
2. Correct the outline style in Safari.
*/
[type='search'] {
	-webkit-appearance: textfield; /* 1 */
	outline-offset: -2px; /* 2 */
}
/**
Remove the inner padding in Chrome and Safari on macOS.
*/
::-webkit-search-decoration {
	-webkit-appearance: none;
}
/**
1. Correct the inability to style clickable types in iOS and Safari.
2. Change font properties to 'inherit' in Safari.
*/
::-webkit-file-upload-button {
	-webkit-appearance: button; /* 1 */
	font: inherit; /* 2 */
}
/*
Interactive
===========
*/
/*
Add the correct display in Chrome and Safari.
*/
summary {
	display: list-item;
}
/**
 * Manually forked from SUIT CSS Base: https://github.com/suitcss/base
 * A thin layer on top of normalize.css that provides a starting point more
 * suitable for web applications.
 */
/**
 * Removes the default spacing and border for appropriate elements.
 */
blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
  margin: 0;
}
button {
  background-color: transparent;
  background-image: none;
}
fieldset {
  margin: 0;
  padding: 0;
}
ol,
ul {
  list-style: none;
  margin: 0;
  padding: 0;
}
/**
 * Tailwind custom reset styles
 */
/**
 * 1. Use the user's configured `sans` font-family (with Tailwind's default
 *    sans-serif font stack as a fallback) as a sane default.
 * 2. Use Tailwind's default "normal" line-height so the user isn't forced
 *    to override it to ensure consistency even when using the default theme.
 */
html {
  font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"; /* 1 */
  line-height: 1.5; /* 2 */
}
/**
 * Inherit font-family and line-height from `html` so users can set them as
 * a class directly on the `html` element.
 */
body {
  font-family: inherit;
  line-height: inherit;
}
/**
 * 1. Prevent padding and border from affecting element width.
 *
 *    We used to set this in the html element and inherit from
 *    the parent element for everything else. This caused issues
 *    in shadow-dom-enhanced elements like <details> where the content
 *    is wrapped by a div with box-sizing set to `content-box`.
 *
 *    https://github.com/mozdevs/cssremedy/issues/4
 *
 *
 * 2. Allow adding a border to an element by just adding a border-width.
 *
 *    By default, the way the browser specifies that an element should have no
 *    border is by setting it's border-style to `none` in the user-agent
 *    stylesheet.
 *
 *    In order to easily add borders to elements by just setting the `border-width`
 *    property, we change the default border-style for all elements to `solid`, and
 *    use border-width to hide them instead. This way our `border` utilities only
 *    need to set the `border-width` property instead of the entire `border`
 *    shorthand, making our border utilities much more straightforward to compose.
 *
 *    https://github.com/tailwindcss/tailwindcss/pull/116
 */
*,
::before,
::after {
  box-sizing: border-box; /* 1 */
  border-width: 0; /* 2 */
  border-style: solid; /* 2 */
  border-color: currentColor; /* 2 */
}
/*
 * Ensure horizontal rules are visible by default
 */
hr {
  border-top-width: 1px;
}
/**
 * Undo the `border-style: none` reset that Normalize applies to images so that
 * our `border-{width}` utilities have the expected effect.
 *
 * The Normalize reset is unnecessary for us since we default the border-width
 * to 0 on all elements.
 *
 * https://github.com/tailwindcss/tailwindcss/issues/362
 */
img {
  border-style: solid;
}
textarea {
  resize: vertical;
}
input::-moz-placeholder, textarea::-moz-placeholder {
  opacity: 1;
  color: #9ca3af;
}
input:-ms-input-placeholder, textarea:-ms-input-placeholder {
  opacity: 1;
  color: #9ca3af;
}
input::placeholder,
textarea::placeholder {
  opacity: 1;
  color: #9ca3af;
}
button,
[role="button"] {
  cursor: pointer;
}
/**
 * Override legacy focus reset from Normalize with modern Firefox focus styles.
 *
 * This is actually an improvement over the new defaults in Firefox in our testing,
 * as it triggers the better focus styles even for links, which still use a dotted
 * outline in Firefox by default.
 */
:-moz-focusring {
	outline: auto;
}
table {
  border-collapse: collapse;
}
h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: inherit;
  font-weight: inherit;
}
/**
 * Reset links to optimize for opt-in styling instead of
 * opt-out.
 */
a {
  color: inherit;
  text-decoration: inherit;
}
/**
 * Reset form element properties that are easy to forget to
 * style explicitly so you don't inadvertently introduce
 * styles that deviate from your design system. These styles
 * supplement a partial reset that is already applied by
 * normalize.css.
 */
button,
input,
optgroup,
select,
textarea {
  padding: 0;
  line-height: inherit;
  color: inherit;
}
/**
 * Use the configured 'mono' font family for elements that
 * are expected to be rendered with a monospace font, falling
 * back to the system monospace stack if there is no configured
 * 'mono' font family.
 */
pre,
code,
kbd,
samp {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
}
/**
 * 1. Make replaced elements `display: block` by default as that's
 *    the behavior you want almost all of the time. Inspired by
 *    CSS Remedy, with `svg` added as well.
 *
 *    https://github.com/mozdevs/cssremedy/issues/14
 * 
 * 2. Add `vertical-align: middle` to align replaced elements more
 *    sensibly by default when overriding `display` by adding a
 *    utility like `inline`.
 *
 *    This can trigger a poorly considered linting error in some
 *    tools but is included by design.
 * 
 *    https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210
 */
img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
  display: block; /* 1 */
  vertical-align: middle; /* 2 */
}
/**
 * Constrain images and videos to the parent width and preserve
 * their intrinsic aspect ratio.
 *
 * https://github.com/mozdevs/cssremedy/issues/14
 */
img,
video {
  max-width: 100%;
  height: auto;
}
/**
 * Ensure the default browser behavior of the `hidden` attribute.
 */
[hidden] {
  display: none;
}
*, ::before, ::after {
	--tw-translate-x: 0;
	--tw-translate-y: 0;
	--tw-rotate: 0;
	--tw-skew-x: 0;
	--tw-skew-y: 0;
	--tw-scale-x: 1;
	--tw-scale-y: 1;
	--tw-transform: translateX(var(--tw-translate-x)) translateY(var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
	--tw-border-opacity: 1;
	border-color: rgba(229, 231, 235, var(--tw-border-opacity));
	--tw-ring-offset-shadow: 0 0 #0000;
	--tw-ring-shadow: 0 0 #0000;
	--tw-shadow: 0 0 #0000;
	--tw-ring-inset: var(--tw-empty,/*!*/ /*!*/);
	--tw-ring-offset-width: 0px;
	--tw-ring-offset-color: #fff;
	--tw-ring-color: rgba(59, 130, 246, 0.5);
	--tw-ring-offset-shadow: 0 0 #0000;
	--tw-ring-shadow: 0 0 #0000;
	--tw-shadow: 0 0 #0000;
	--tw-blur: var(--tw-empty,/*!*/ /*!*/);
	--tw-brightness: var(--tw-empty,/*!*/ /*!*/);
	--tw-contrast: var(--tw-empty,/*!*/ /*!*/);
	--tw-grayscale: var(--tw-empty,/*!*/ /*!*/);
	--tw-hue-rotate: var(--tw-empty,/*!*/ /*!*/);
	--tw-invert: var(--tw-empty,/*!*/ /*!*/);
	--tw-saturate: var(--tw-empty,/*!*/ /*!*/);
	--tw-sepia: var(--tw-empty,/*!*/ /*!*/);
	--tw-drop-shadow: var(--tw-empty,/*!*/ /*!*/);
	--tw-filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

      [type='text'],
      [type='email'],
      [type='url'],
      [type='password'],
      [type='number'],
      [type='date'],
      [type='datetime-local'],
      [type='month'],
      [type='search'],
      [type='tel'],
      [type='time'],
      [type='week'],
      [multiple],
      textarea,
      select
     {
	-webkit-appearance: none;
	   -moz-appearance: none;
	        appearance: none;
	background-color: #fff;
	border-color: #6b7280;
	border-width: 1px;
	border-radius: 0px;
	padding-top: 0.5rem;
	padding-right: 0.75rem;
	padding-bottom: 0.5rem;
	padding-left: 0.75rem;
	font-size: 1rem;
	line-height: 1.5rem;
}
[type='text']:focus, [type='email']:focus, [type='url']:focus, [type='password']:focus, [type='number']:focus, [type='date']:focus, [type='datetime-local']:focus, [type='month']:focus, [type='search']:focus, [type='tel']:focus, [type='time']:focus, [type='week']:focus, [multiple]:focus, textarea:focus, select:focus {
	outline: 2px solid transparent;
	outline-offset: 2px;
	--tw-ring-inset: var(--tw-empty,/*!*/ /*!*/);
	--tw-ring-offset-width: 0px;
	--tw-ring-offset-color: #fff;
	--tw-ring-color: #2563eb;
	--tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
	--tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
	box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
	border-color: #2563eb;
}
input::-moz-placeholder, textarea::-moz-placeholder {
	color: #6b7280;
	opacity: 1;
}
input:-ms-input-placeholder, textarea:-ms-input-placeholder {
	color: #6b7280;
	opacity: 1;
}
input::placeholder, textarea::placeholder {
	color: #6b7280;
	opacity: 1;
}
::-webkit-datetime-edit-fields-wrapper {
	padding: 0;
}
::-webkit-date-and-time-value {
	min-height: 1.5em;
}
select {
	background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
	background-position: right 0.5rem center;
	background-repeat: no-repeat;
	background-size: 1.5em 1.5em;
	padding-right: 2.5rem;
	-webkit-print-color-adjust: exact;
	        color-adjust: exact;
}
[multiple] {
	background-image: initial;
	background-position: initial;
	background-repeat: unset;
	background-size: initial;
	padding-right: 0.75rem;
	-webkit-print-color-adjust: unset;
	        color-adjust: unset;
}

      [type='checkbox'],
      [type='radio']
     {
	-webkit-appearance: none;
	   -moz-appearance: none;
	        appearance: none;
	padding: 0;
	-webkit-print-color-adjust: exact;
	        color-adjust: exact;
	display: inline-block;
	vertical-align: middle;
	background-origin: border-box;
	-webkit-user-select: none;
	   -moz-user-select: none;
	    -ms-user-select: none;
	        user-select: none;
	flex-shrink: 0;
	height: 1rem;
	width: 1rem;
	color: #2563eb;
	background-color: #fff;
	border-color: #6b7280;
	border-width: 1px;
}
[type='checkbox'] {
	border-radius: 0px;
}
[type='radio'] {
	border-radius: 100%;
}

      [type='checkbox']:focus,
      [type='radio']:focus
     {
	outline: 2px solid transparent;
	outline-offset: 2px;
	--tw-ring-inset: var(--tw-empty,/*!*/ /*!*/);
	--tw-ring-offset-width: 2px;
	--tw-ring-offset-color: #fff;
	--tw-ring-color: #2563eb;
	--tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
	--tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
	box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

      [type='checkbox']:checked,
      [type='radio']:checked
     {
	border-color: transparent;
	background-color: currentColor;
	background-size: 100% 100%;
	background-position: center;
	background-repeat: no-repeat;
}
[type='checkbox']:checked {
	background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M12.207 4.793a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0l-2-2a1 1 0 011.414-1.414L6.5 9.086l4.293-4.293a1 1 0 011.414 0z'/%3e%3c/svg%3e");
}
[type='radio']:checked {
	background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3ccircle cx='8' cy='8' r='3'/%3e%3c/svg%3e");
}

      [type='checkbox']:checked:hover,
      [type='checkbox']:checked:focus,
      [type='radio']:checked:hover,
      [type='radio']:checked:focus
     {
	border-color: transparent;
	background-color: currentColor;
}
[type='checkbox']:indeterminate {
	background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 16 16'%3e%3cpath stroke='white' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M4 8h8'/%3e%3c/svg%3e");
	border-color: transparent;
	background-color: currentColor;
	background-size: 100% 100%;
	background-position: center;
	background-repeat: no-repeat;
}

      [type='checkbox']:indeterminate:hover,
      [type='checkbox']:indeterminate:focus
     {
	border-color: transparent;
	background-color: currentColor;
}
[type='file'] {
	background: unset;
	border-color: inherit;
	border-width: 0;
	border-radius: 0;
	padding: 0;
	font-size: unset;
	line-height: inherit;
}
[type='file']:focus {
	outline: 1px auto -webkit-focus-ring-color;
}
.container {
	width: 100%;
	margin-right: auto;
	margin-left: auto;
	padding-right: 1.5rem;
	padding-left: 1.5rem;
}
@media (min-width: 640px) {
	.container {
		max-width: 640px;
	}
}
@media (min-width: 768px) {
	.container {
		max-width: 768px;
	}
}
@media (min-width: 1024px) {
	.container {
		max-width: 1024px;
	}
}
@media (min-width: 1280px) {
	.container {
		max-width: 1280px;
	}
}
@media (min-width: 1536px) {
	.container {
		max-width: 1536px;
	}
}
.prose {
	color: #374151;
	max-width: 65ch;
}
.prose [class~="lead"] {
	color: #4b5563;
	font-size: 1.25em;
	line-height: 1.6;
	margin-top: 1.2em;
	margin-bottom: 1.2em;
}
.prose a {
	color: #111827;
	text-decoration: underline;
	font-weight: 500;
}
.prose strong {
	color: #111827;
	font-weight: 600;
}
.prose ol {
	counter-reset: list-counter;
	margin-top: 1.25em;
	margin-bottom: 1.25em;
}
.prose ol > li {
	position: relative;
	counter-increment: list-counter;
	padding-left: 1.75em;
}
.prose ol > li::before {
	content: counter(list-counter) ".";
	position: absolute;
	font-weight: 400;
	color: #6b7280;
	left: 0;
}
.prose ul > li {
	position: relative;
	padding-left: 1.75em;
}
.prose ul > li::before {
	content: "";
	position: absolute;
	background-color: #d1d5db;
	border-radius: 50%;
	width: 0.375em;
	height: 0.375em;
	top: calc(0.875em - 0.1875em);
	left: 0.25em;
}
.prose hr {
	border-color: #e5e7eb;
	border-top-width: 1px;
	margin-top: 3em;
	margin-bottom: 3em;
}
.prose blockquote {
	font-weight: 500;
	font-style: italic;
	color: #111827;
	border-left-width: 0.25rem;
	border-left-color: #e5e7eb;
	quotes: "\201C""\201D""\2018""\2019";
	margin-top: 1.6em;
	margin-bottom: 1.6em;
	padding-left: 1em;
}
.prose blockquote p:first-of-type::before {
	content: open-quote;
}
.prose blockquote p:last-of-type::after {
	content: close-quote;
}
.prose h1 {
	color: #111827;
	font-weight: 800;
	font-size: 2.25em;
	margin-top: 0;
	margin-bottom: 0.8888889em;
	line-height: 1.1111111;
}
.prose h2 {
	color: #111827;
	font-weight: 700;
	font-size: 1.5em;
	margin-top: 2em;
	margin-bottom: 1em;
	line-height: 1.3333333;
}
.prose h3 {
	color: #111827;
	font-weight: 600;
	font-size: 1.25em;
	margin-top: 1.6em;
	margin-bottom: 0.6em;
	line-height: 1.6;
}
.prose h4 {
	color: #111827;
	font-weight: 600;
	margin-top: 1.5em;
	margin-bottom: 0.5em;
	line-height: 1.5;
}
.prose figure figcaption {
	color: #6b7280;
	font-size: 0.875em;
	line-height: 1.4285714;
	margin-top: 0.8571429em;
}
.prose code {
	color: #111827;
	font-weight: 600;
	font-size: 0.875em;
}
.prose code::before {
	content: "`";
}
.prose code::after {
	content: "`";
}
.prose a code {
	color: #111827;
}
.prose pre {
	color: #e5e7eb;
	background-color: #1f2937;
	overflow-x: auto;
	font-size: 0.875em;
	line-height: 1.7142857;
	margin-top: 1.7142857em;
	margin-bottom: 1.7142857em;
	border-radius: 0.375rem;
	padding-top: 0.8571429em;
	padding-right: 1.1428571em;
	padding-bottom: 0.8571429em;
	padding-left: 1.1428571em;
}
.prose pre code {
	background-color: transparent;
	border-width: 0;
	border-radius: 0;
	padding: 0;
	font-weight: 400;
	color: inherit;
	font-size: inherit;
	font-family: inherit;
	line-height: inherit;
}
.prose pre code::before {
	content: "";
}
.prose pre code::after {
	content: "";
}
.prose table {
	width: 100%;
	table-layout: auto;
	text-align: left;
	margin-top: 2em;
	margin-bottom: 2em;
	font-size: 0.875em;
	line-height: 1.7142857;
}
.prose thead {
	color: #111827;
	font-weight: 600;
	border-bottom-width: 1px;
	border-bottom-color: #d1d5db;
}
.prose thead th {
	vertical-align: bottom;
	padding-right: 0.5714286em;
	padding-bottom: 0.5714286em;
	padding-left: 0.5714286em;
}
.prose tbody tr {
	border-bottom-width: 1px;
	border-bottom-color: #e5e7eb;
}
.prose tbody tr:last-child {
	border-bottom-width: 0;
}
.prose tbody td {
	vertical-align: top;
	padding-top: 0.5714286em;
	padding-right: 0.5714286em;
	padding-bottom: 0.5714286em;
	padding-left: 0.5714286em;
}
.prose {
	font-size: 1rem;
	line-height: 1.75;
}
.prose p {
	margin-top: 1.25em;
	margin-bottom: 1.25em;
}
.prose img {
	margin-top: 2em;
	margin-bottom: 2em;
}
.prose video {
	margin-top: 2em;
	margin-bottom: 2em;
}
.prose figure {
	margin-top: 2em;
	margin-bottom: 2em;
}
.prose figure > * {
	margin-top: 0;
	margin-bottom: 0;
}
.prose h2 code {
	font-size: 0.875em;
}
.prose h3 code {
	font-size: 0.9em;
}
.prose ul {
	margin-top: 1.25em;
	margin-bottom: 1.25em;
}
.prose li {
	margin-top: 0.5em;
	margin-bottom: 0.5em;
}
.prose > ul > li p {
	margin-top: 0.75em;
	margin-bottom: 0.75em;
}
.prose > ul > li > *:first-child {
	margin-top: 1.25em;
}
.prose > ul > li > *:last-child {
	margin-bottom: 1.25em;
}
.prose > ol > li > *:first-child {
	margin-top: 1.25em;
}
.prose > ol > li > *:last-child {
	margin-bottom: 1.25em;
}
.prose ul ul, .prose ul ol, .prose ol ul, .prose ol ol {
	margin-top: 0.75em;
	margin-bottom: 0.75em;
}
.prose hr + * {
	margin-top: 0;
}
.prose h2 + * {
	margin-top: 0;
}
.prose h3 + * {
	margin-top: 0;
}
.prose h4 + * {
	margin-top: 0;
}
.prose thead th:first-child {
	padding-left: 0;
}
.prose thead th:last-child {
	padding-right: 0;
}
.prose tbody td:first-child {
	padding-left: 0;
}
.prose tbody td:last-child {
	padding-right: 0;
}
.prose > :first-child {
	margin-top: 0;
}
.prose > :last-child {
	margin-bottom: 0;
}
.btn {
	display: flex;
	align-items: center;
	border-radius: 0.5rem;
	--tw-bg-opacity: 1;
	background-color: rgba(107, 114, 128, var(--tw-bg-opacity));
	padding-left: 1rem;
	padding-right: 1rem;
	padding-top: 0.5rem;
	padding-bottom: 0.5rem;
	font-size: 1rem;
	line-height: 1.5rem;
	font-weight: 500;
	--tw-text-opacity: 1;
	color: rgba(255, 255, 255, var(--tw-text-opacity));
	--tw-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
	box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
@media (min-width: 768px) {
    .btn {
		padding-left: 1.5rem;
		padding-right: 1.5rem;
	}
    .btn {
		font-size: 1.125rem;
		line-height: 1.75rem;
	}
        }
@media (min-width: 1280px) {
    .btn {
		font-size: 1rem;
		line-height: 1.5rem;
	}
        }
.btn svg {
	display: inline-flex;
}
.btn span {
            vertical-align: middle;
        }
.btn:hover {
	--tw-bg-opacity: 1;
	background-color: rgba(75, 85, 99, var(--tw-bg-opacity));
}
.btn:focus {
	border-color: transparent;
	outline: 2px solid transparent;
	outline-offset: 2px;
	--tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
	--tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);
	box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
	--tw-ring-opacity: 1;
	--tw-ring-color: rgba(30, 64, 175, var(--tw-ring-opacity));
	--tw-ring-opacity: 0.5;
}
.btn-primary {
	--tw-bg-opacity: 1;
	background-color: rgba(29, 78, 216, var(--tw-bg-opacity));
	--tw-text-opacity: 1;
	color: rgba(255, 255, 255, var(--tw-text-opacity));
	--tw-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
	box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.btn-primary:hover {
	--tw-bg-opacity: 1;
	background-color: rgba(37, 99, 235, var(--tw-bg-opacity));
	--tw-text-opacity: 1;
	color: rgba(255, 255, 255, var(--tw-text-opacity));
	--tw-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
	box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.btn-secondary {
	border-width: 2px;
	--tw-border-opacity: 1;
	border-color: rgba(37, 99, 235, var(--tw-border-opacity));
	--tw-bg-opacity: 1;
	background-color: rgba(255, 255, 255, var(--tw-bg-opacity));
	--tw-text-opacity: 1;
	color: rgba(0, 0, 0, var(--tw-text-opacity));
	--tw-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
	box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.btn-secondary:hover {
	--tw-border-opacity: 1;
	border-color: rgba(30, 64, 175, var(--tw-border-opacity));
	--tw-bg-opacity: 1;
	background-color: rgba(255, 255, 255, var(--tw-bg-opacity));
	--tw-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
	box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.btn-secondary:focus {
	border-width: 2px;
	border-color: transparent;
}
.actions-toolbar .primary button {
	display: flex;
	align-items: center;
	border-radius: 0.5rem;
	--tw-bg-opacity: 1;
	background-color: rgba(107, 114, 128, var(--tw-bg-opacity));
	padding-left: 1rem;
	padding-right: 1rem;
	padding-top: 0.5rem;
	padding-bottom: 0.5rem;
	font-size: 1rem;
	line-height: 1.5rem;
	font-weight: 500;
	--tw-text-opacity: 1;
	color: rgba(255, 255, 255, var(--tw-text-opacity));
	--tw-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
	box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
@media (min-width: 768px) {
    .actions-toolbar .primary button {
		padding-left: 1.5rem;
		padding-right: 1.5rem;
	}
    .actions-toolbar .primary button {
		font-size: 1.125rem;
		line-height: 1.75rem;
	}
        }
@media (min-width: 1280px) {
    .actions-toolbar .primary button {
		font-size: 1rem;
		line-height: 1.5rem;
	}
        }
.actions-toolbar .primary button svg {
	display: inline-flex;
}
.actions-toolbar .primary button span {
            vertical-align: middle;
        }
.actions-toolbar .primary button:hover {
	--tw-bg-opacity: 1;
	background-color: rgba(75, 85, 99, var(--tw-bg-opacity));
}
.actions-toolbar .primary button:focus {
	border-color: transparent;
	outline: 2px solid transparent;
	outline-offset: 2px;
	--tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
	--tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);
	box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
	--tw-ring-opacity: 1;
	--tw-ring-color: rgba(30, 64, 175, var(--tw-ring-opacity));
	--tw-ring-opacity: 0.5;
}
.actions-toolbar .primary button {
	--tw-bg-opacity: 1;
	background-color: rgba(29, 78, 216, var(--tw-bg-opacity));
	--tw-text-opacity: 1;
	color: rgba(255, 255, 255, var(--tw-text-opacity));
	--tw-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
	box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.actions-toolbar .primary button:hover {
	--tw-bg-opacity: 1;
	background-color: rgba(37, 99, 235, var(--tw-bg-opacity));
	--tw-text-opacity: 1;
	color: rgba(255, 255, 255, var(--tw-text-opacity));
	--tw-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
	box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
#category-view-container {
	margin-left: auto;
	margin-right: auto;
	display: flex;
	flex-direction: column;
	align-items: center;
	padding-left: 0px;
	padding-right: 0px;
	padding-top: 1rem;
	padding-bottom: 1rem;
}
.category-description {
	max-width: 56rem;
}
.toolbar-products .modes-mode {
	height: 1.5rem;
	width: 1.5rem;
}
.toolbar-products .modes-mode span {
	position: absolute;
	width: 1px;
	height: 1px;
	padding: 0;
	margin: -1px;
	overflow: hidden;
	clip: rect(0, 0, 0, 0);
	white-space: nowrap;
	border-width: 0;
}
.toolbar-products .modes-mode.mode-grid {
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" /></svg>');
        }
.toolbar-products .modes-mode.mode-list {
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16" /></svg>');
        }
.price-including-tax + .price-excluding-tax {
	font-size: 0.875rem;
	line-height: 1.25rem;
}
.price-including-tax + .price-excluding-tax:before {
        content: attr(data-label) ': ';
    }
.price-excluding-tax, .price-including-tax {
	display: block;
	white-space: nowrap;
}
.price-excluding-tax .price, .price-including-tax .price {
	font-weight: 600;
	line-height: 1.625;
}
#customer-login-container {
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	padding-top: 2rem;
	padding-bottom: 2rem;
}
@media (min-width: 768px) {
#customer-login-container {
		flex-direction: row;
	}
    }
/**
 * different styles can be found at https://tailwindcss-forms.vercel.app/
 **/
.form-input, .form-email, .form-select, .form-multiselect, .form-textarea {
	border-radius: 0.375rem;
	border-width: 1px;
	--tw-border-opacity: 1;
	border-color: rgba(209, 213, 219, var(--tw-border-opacity));
	--tw-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
	box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.form-input:focus, .form-email:focus, .form-select:focus, .form-multiselect:focus, .form-textarea:focus {
	--tw-border-opacity: 1;
	border-color: rgba(37, 99, 235, var(--tw-border-opacity));
	--tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
	--tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color);
	box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
	--tw-ring-opacity: 1;
	--tw-ring-color: rgba(30, 64, 175, var(--tw-ring-opacity));
	--tw-ring-opacity: 0.5;
}
.page.messages {
	position: -webkit-sticky;
	position: sticky;
	z-index: 20;
    top: 0px;
}
.page.messages .messages
    section#messages {
	width: 100%;
	margin-right: auto;
	margin-left: auto;
	padding-right: 1.5rem;
	padding-left: 1.5rem;
}
@media (min-width: 640px) {
	.page.messages .messages
    section#messages {
		max-width: 640px;
	}
}
@media (min-width: 768px) {
	.page.messages .messages
    section#messages {
		max-width: 768px;
	}
}
@media (min-width: 1024px) {
	.page.messages .messages
    section#messages {
		max-width: 1024px;
	}
}
@media (min-width: 1280px) {
	.page.messages .messages
    section#messages {
		max-width: 1280px;
	}
}
@media (min-width: 1536px) {
	.page.messages .messages
    section#messages {
		max-width: 1536px;
	}
}
.page.messages .messages
    section#messages {
	margin-left: auto;
	margin-right: auto;
	padding-top: 0.75rem;
	padding-bottom: 0.75rem;
}
.message {
	margin-bottom: 0.5rem;
	display: flex;
	width: 100%;
	align-items: center;
	justify-content: space-between;
	border-radius: 0.25rem;
	--tw-bg-opacity: 1;
	background-color: rgba(75, 85, 99, var(--tw-bg-opacity));
	padding: 0.5rem;
	--tw-text-opacity: 1;
	color: rgba(255, 255, 255, var(--tw-text-opacity));
	--tw-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
	box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.message.error {
	--tw-bg-opacity: 1;
	background-color: rgba(239, 68, 68, var(--tw-bg-opacity));
}
.message.success {
	--tw-bg-opacity: 1;
	background-color: rgba(16, 185, 129, var(--tw-bg-opacity));
}
.message.info, .message.warning, .message.notice {
	--tw-bg-opacity: 1;
	background-color: rgba(245, 158, 11, var(--tw-bg-opacity));
}
.message a {
	text-decoration: underline;
}
.product-item .price-container {
	display: block;
}
.product-item .price-container .price {
	font-size: 1.125rem;
	line-height: 1.75rem;
	font-weight: 600;
}
.product-item .price-container .price-label {
	font-size: 0.875rem;
	line-height: 1.25rem;
}
.product-item .special-price .price-container .price-label {
	position: absolute;
	width: 1px;
	height: 1px;
	padding: 0;
	margin: -1px;
	overflow: hidden;
	clip: rect(0, 0, 0, 0);
	white-space: nowrap;
	border-width: 0;
}
.product-item .old-price .price-container {
	--tw-text-opacity: 1;
	color: rgba(107, 114, 128, var(--tw-text-opacity));
}
.product-item .old-price .price-container .price {
	font-size: 1rem;
	line-height: 1.5rem;
	font-weight: 400;
}
.page-product-bundle .price-final_price .price-from .price-container, .page-product-bundle .price-final_price .price-to .price-container {
	margin-bottom: 1rem;
	display: block;
	--tw-text-opacity: 1;
	color: rgba(55, 65, 81, var(--tw-text-opacity));
}
.page-product-bundle .price-final_price .price-from .price-container .price-label, .page-product-bundle .price-final_price .price-to .price-container .price-label {
	display: block;
	font-size: 1.125rem;
	line-height: 1.75rem;
	font-weight: 500;
}
.page-product-bundle .price-final_price .price-from .price-container .price, .page-product-bundle .price-final_price .price-to .price-container .price {
	display: block;
	font-size: 1.5rem;
	line-height: 2rem;
	font-weight: 600;
	line-height: 1.25;
	--tw-text-opacity: 1;
	color: rgba(31, 41, 55, var(--tw-text-opacity));
}
.page-product-bundle .price-final_price .price-from .old-price .price-container .price, .page-product-bundle .price-final_price .price-from .old-price .price-container .price-label, .page-product-bundle .price-final_price .price-to .old-price .price-container .price, .page-product-bundle .price-final_price .price-to .old-price .price-container .price-label {
	display: inline;
	font-size: 1rem;
	line-height: 1.5rem;
	font-weight: 400;
	--tw-text-opacity: 1;
	color: rgba(107, 114, 128, var(--tw-text-opacity));
}
.wishlist-widget .price-box .price-label {
	position: absolute;
	width: 1px;
	height: 1px;
	padding: 0;
	margin: -1px;
	overflow: hidden;
	clip: rect(0, 0, 0, 0);
	white-space: nowrap;
	border-width: 0;
}
.wishlist-widget .price-box .old-price {
	position: absolute;
	width: 1px;
	height: 1px;
	padding: 0;
	margin: -1px;
	overflow: hidden;
	clip: rect(0, 0, 0, 0);
	white-space: nowrap;
	border-width: 0;
}
.modal {
  /*
   * TODO: add tailwind classes used for the cart and modal styles.
   * This will make the modal and off-canvas styles theme specific and more adjustable.
   */
}
.backdrop {
	position: fixed;
	top: 0px;
	right: 0px;
	bottom: 0px;
	left: 0px;
	display: flex;
	--tw-bg-opacity: 1;
	background-color: rgba(0, 0, 0, var(--tw-bg-opacity));
	--tw-bg-opacity: 0.25;
}
.snap {
    -ms-scroll-snap-type: x mandatory;
    scroll-snap-type: x mandatory;
    -ms-overflow-style: none;
    scroll-behavior: smooth;
    scrollbar-width: none;
}
.snap::-webkit-scrollbar {
        display: none;
    }
.snap > div {
        scroll-snap-align: start;
    }
body {
    overflow-y: scroll;
}
.clearfix::after {
    content: "";
    display: block;
    clear: both;
}
.page-main {
	margin-top: 2rem;
	margin-bottom: 2rem;
}
.flex-columns-wrapper {
	display: flex;
	flex-direction: column;
}
@media (min-width: 768px) {
.flex-columns-wrapper {
		flex-direction: row;
	}
    }
.columns {
	width: 100%;
	margin-right: auto;
	margin-left: auto;
	padding-right: 1.5rem;
	padding-left: 1.5rem;
}
@media (min-width: 640px) {
	.columns {
		max-width: 640px;
	}
}
@media (min-width: 768px) {
	.columns {
		max-width: 768px;
	}
}
@media (min-width: 1024px) {
	.columns {
		max-width: 1024px;
	}
}
@media (min-width: 1280px) {
	.columns {
		max-width: 1280px;
	}
}
@media (min-width: 1536px) {
	.columns {
		max-width: 1536px;
	}
}
.columns {
	display: grid;
	grid-template-columns: repeat(1, minmax(0, 1fr));
	-moz-column-gap: 2rem;
	     column-gap: 2rem;
	row-gap: 1rem;
    grid-template-rows: auto minmax(0,1fr);
}
.columns .main {
	order: 2;
}
.columns .sidebar {
	order: 3;
}
.page-main-full-width .columns {
	max-width: none;
	padding-left: 0px;
	padding-right: 0px;
}
.page-with-filter .columns .sidebar-main {
	order: 1;
}
@media (min-width: 640px) {
        .page-layout-2columns-left .columns, .page-layout-2columns-right .columns, .page-layout-3columns .columns {
		grid-template-columns: repeat(2, minmax(0, 1fr));
	}

            .page-layout-2columns-left .columns .main, .page-layout-2columns-right .columns .main, .page-layout-3columns .columns .main {
		grid-column: span 2 / span 2;
	}

            .page-layout-2columns-left .columns .sidebar, .page-layout-2columns-right .columns .sidebar, .page-layout-3columns .columns .sidebar {
		order: 3;
	}
            .page-with-filter .columns .sidebar-main {
		order: 1;
	}
            .page-with-filter .columns .sidebar-main {
		grid-column: span 2 / span 2;
	}
}
@media (min-width: 768px) {
        .page-layout-2columns-left .columns, .page-layout-2columns-right .columns, .page-layout-3columns .columns {
		grid-template-columns: repeat(3, minmax(0, 1fr));
	}

            .page-layout-2columns-left .columns .main, .page-layout-2columns-right .columns .main, .page-layout-3columns .columns .main {
		grid-row: span 2 / span 2;
	}

            .page-layout-2columns-left .columns .sidebar, .page-layout-2columns-right .columns .sidebar, .page-layout-3columns .columns .sidebar {
		grid-column: span 1 / span 1;
	}
            .page-layout-2columns-left .columns .main, .page-layout-3columns .columns .main {
		grid-column-start: 2 !important;
	}
            .page-layout-2columns-left .columns .sidebar, .page-layout-3columns .columns .sidebar {
		order: 1;
	}

            .page-layout-2columns-left .columns .sidebar ~ .sidebar-additional, .page-layout-3columns .columns .sidebar ~ .sidebar-additional {
		order: 3;
	}
        .page-layout-2columns-right .sidebar-main, .page-layout-2columns-right.page-with-filter .sidebar-main {
		order: 3;
	}
}
@media (min-width: 1024px) {
        .page-layout-2columns-left .columns, .page-layout-2columns-right .columns, .page-layout-3columns .columns {
		grid-template-columns: repeat(4, minmax(0, 1fr));
	}
            .page-layout-2columns-left .columns .main, .page-layout-2columns-right .columns .main {
		grid-column: span 3 / span 3;
	}
            .page-layout-3columns .columns .sidebar-additional {
		grid-column-start: 4;
	}
}
.product-image-container {
    width: 100% !important;
}
.product-image-container img {
        width: 100%;
    }
.swatch-attribute .swatch-attribute-options {
	display: flex;
	flex-direction: row;
	flex-wrap: wrap;
}
.swatch-attribute .swatch-attribute-options .swatch-option {
	margin: 0.25rem;
	display: flex;
	justify-content: center;
	border-width: 1px;
	padding-left: 0.5rem;
	padding-right: 0.5rem;
	padding-top: 0.25rem;
	padding-bottom: 0.25rem;
            min-width: 40px;
}
body {
	--tw-bg-opacity: 1;
	background-color: rgba(250, 250, 250, var(--tw-bg-opacity));
}
#cart-drawer {
	--tw-text-opacity: 1;
	color: rgba(0, 0, 0, var(--tw-text-opacity));
}
[x-cloak] {
    display: none !important;
}
.input {
	margin-right: 0.5rem;
	width: auto;
	border-radius: 0.25rem;
	border-width: 1px;
	padding-top: 0.5rem;
	padding-bottom: 0.5rem;
	padding-left: 1rem;
	padding-right: 1rem;
	font-size: 1rem;
	line-height: 1.5rem;
}
@media (min-width: 768px) {
.input {
		margin-right: 1rem;
	}
    }
@media (min-width: 1024px) {
.input {
		margin-right: 0px;
	}
    }
@media (min-width: 1280px) {
.input {
		margin-right: 1rem;
	}
    }
.input-light {
	--tw-border-opacity: 1;
	border-color: rgba(156, 163, 175, var(--tw-border-opacity));
	--tw-bg-opacity: 1;
	background-color: rgba(255, 255, 255, var(--tw-bg-opacity));
}
.input-light:focus {
	--tw-border-opacity: 1;
	border-color: rgba(29, 78, 216, var(--tw-border-opacity));
	outline: 2px solid transparent;
	outline-offset: 2px;
}
.card {
	border-radius: 0.125rem;
	--tw-bg-opacity: 1;
	background-color: rgba(255, 255, 255, var(--tw-bg-opacity));
	padding: 1rem;
	--tw-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
	box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.card-interactive:hover {
	--tw-bg-opacity: 1;
	background-color: rgba(250, 250, 250, var(--tw-bg-opacity));
}
.duration-200 {
    transition-duration: 200ms;
}
.ease-in {
    transition-timing-function: cubic-bezier(0.4, 0, 1, 1);
}
.ease-out {
    transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
}
.transition {
    transition: transform 250ms ease, color 250ms ease;
}
.transform-180 {
    transform: rotate(-180deg);
}
html {
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}
body {
	font-size: 1rem;
	line-height: 1.5rem;
	line-height: 1.5;
	letter-spacing: 0em;
	--tw-text-opacity: 1;
	color: rgba(0, 0, 0, var(--tw-text-opacity));
        font-family: "Segoe UI", "Helvetica Neue", Arial, sans-serif;
}
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */
/**
 * Layout
 */
/* Rows */
[data-content-type='row'] {
	box-sizing: border-box;
}
[data-content-type='row'] > div {
	margin-bottom: 0.625rem;
	padding: 0.625rem;
}
[data-content-type='row'][data-appearance='contained'] {
	width: 100%;
	margin-right: auto;
	margin-left: auto;
	padding-right: 1.5rem;
	padding-left: 1.5rem;
}
@media (min-width: 640px) {
	[data-content-type='row'][data-appearance='contained'] {
		max-width: 640px;
	}
}
@media (min-width: 768px) {
	[data-content-type='row'][data-appearance='contained'] {
		max-width: 768px;
	}
}
@media (min-width: 1024px) {
	[data-content-type='row'][data-appearance='contained'] {
		max-width: 1024px;
	}
}
@media (min-width: 1280px) {
	[data-content-type='row'][data-appearance='contained'] {
		max-width: 1280px;
	}
}
@media (min-width: 1536px) {
	[data-content-type='row'][data-appearance='contained'] {
		max-width: 1536px;
	}
}
[data-content-type='row'][data-appearance='contained'] {
	margin-left: auto;
	margin-right: auto;
	box-sizing: border-box;
}
[data-content-type='row'][data-appearance='contained'] [data-element='inner'] {
	box-sizing: border-box;
            background-attachment: scroll !important;
}
[data-content-type='row'][data-appearance='full-bleed'] {
        background-attachment: scroll !important;
    }
[data-content-type='row'][data-appearance='full-width'] {
        background-attachment: scroll !important;
    }
[data-content-type='row'][data-appearance='full-width'] > .row-full-width-inner {
	width: 100%;
	margin-right: auto;
	margin-left: auto;
	padding-right: 1.5rem;
	padding-left: 1.5rem;
}
@media (min-width: 640px) {
	[data-content-type='row'][data-appearance='full-width'] > .row-full-width-inner {
		max-width: 640px;
	}
}
@media (min-width: 768px) {
	[data-content-type='row'][data-appearance='full-width'] > .row-full-width-inner {
		max-width: 768px;
	}
}
@media (min-width: 1024px) {
	[data-content-type='row'][data-appearance='full-width'] > .row-full-width-inner {
		max-width: 1024px;
	}
}
@media (min-width: 1280px) {
	[data-content-type='row'][data-appearance='full-width'] > .row-full-width-inner {
		max-width: 1280px;
	}
}
@media (min-width: 1536px) {
	[data-content-type='row'][data-appearance='full-width'] > .row-full-width-inner {
		max-width: 1536px;
	}
}
/* Column Groups */
[data-content-type='column-group'] {
	flex-wrap: wrap;
}
@media (min-width: 768px) {
	[data-content-type='column-group'] {
		flex-wrap: nowrap;
	}
}
/* Columns */
[data-content-type='column'] {
	box-sizing: border-box;
	width: 100%;
	max-width: 100%;
    background-attachment: scroll !important;
    flex-basis: 100%;
}
@media (min-width: 768px) {
[data-content-type='column'] {
        flex-basis: auto
}
    }
/* Tabs/Tab Item */
[data-content-type='tabs'] .tabs-navigation {
	margin-bottom: -1px;
	display: block;
	padding: 0px;
	--tw-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
	box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
[data-content-type='tabs'] .tabs-navigation li.tab-header {
	position: relative;
	margin-top: 0px;
	margin-bottom: 0px;
	margin-right: 0px;
	margin-left: -1px;
	display: inline-block;
	max-width: 100%;
	overflow-wrap: break-word;
	border-bottom-left-radius: 0px;
	border-bottom-right-radius: 0px;
	border-width: 1px;
	border-bottom-width: 0px;
	border-style: solid;
	--tw-border-opacity: 1;
	border-color: rgba(209, 213, 219, var(--tw-border-opacity));
	--tw-bg-opacity: 1;
	background-color: rgba(243, 244, 246, var(--tw-bg-opacity));
}
[data-content-type='tabs'] .tabs-navigation li.tab-header:first-child {
	margin-left: 0px;
}
[data-content-type='tabs'] .tabs-navigation li.tab-header.active {
	z-index: 20;
	--tw-bg-opacity: 1;
	background-color: rgba(255, 255, 255, var(--tw-bg-opacity));
                border-bottom: 1px solid white;
}
[data-content-type='tabs'] .tabs-navigation li.tab-header a.tab-title {
	position: relative;
	display: block;
	cursor: pointer;
	white-space: normal;
	padding-top: 0.75rem;
	padding-bottom: 0.75rem;
	padding-left: 1.25rem;
	padding-right: 1.25rem;
	vertical-align: middle;
	font-size: 0.875rem;
	line-height: 1.25rem;
	font-weight: 600;
}
[data-content-type='tabs'] .tabs-navigation li.tab-header a.tab-title span {
	font-weight: 600;
	line-height: 1.25rem;
	--tw-text-opacity: 1;
	color: rgba(0, 0, 0, var(--tw-text-opacity));
}
[data-content-type='tabs'] .tabs-content {
	position: relative;
	z-index: 10;
	box-sizing: border-box;
	overflow: hidden;
	border-radius: 0.125rem;
	border-width: 1px;
	border-style: solid;
	--tw-border-opacity: 1;
	border-color: rgba(209, 213, 219, var(--tw-border-opacity));
	--tw-bg-opacity: 1;
	background-color: rgba(255, 255, 255, var(--tw-bg-opacity));
	--tw-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
	box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
[data-content-type='tabs'] .tabs-content [data-content-type='tab-item'] {
	box-sizing: border-box;
	padding: 2rem;
            min-height: inherit;
            background-attachment: scroll !important;
}
[data-content-type='tabs'].tab-align-left .tabs-content {
            border-top-left-radius: 0 !important;
        }
[data-content-type='tabs'].tab-align-right .tabs-content {
            border-top-right-radius: 0 !important;
        }
/**
 * Elements
 */
/* Text */
[data-content-type='text'] {
	overflow-wrap: break-word;
}
/* Heading */
[data-content-type='heading'] {
	overflow-wrap: break-word;
}
/* Buttons/Button Item */
[data-content-type='buttons'] {
	max-width: 100%;
}
[data-content-type='buttons'] [data-content-type='button-item'] {
	margin-right: 0.5rem;
	margin-bottom: 0.5rem;
	max-width: 100%;
}
[data-content-type='buttons'] [data-content-type='button-item'] [data-element='link'], [data-content-type='buttons'] [data-content-type='button-item'] [data-element='empty_link'] {
	max-width: 100%;
	overflow-wrap: break-word;
}
[data-content-type='buttons'] [data-content-type='button-item'] [data-element='empty_link'] {
	cursor: default;
}
[data-content-type='buttons'] [data-content-type='button-item'] a, [data-content-type='buttons'] [data-content-type='button-item'] button, [data-content-type='buttons'] [data-content-type='button-item'] div {
	display: inline-block;
	--tw-shadow: 0 0 #0000;
	box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
[data-content-type='buttons'] [data-content-type='button-item'] a.pagebuilder-button-link, [data-content-type='buttons'] [data-content-type='button-item'] button.pagebuilder-button-link, [data-content-type='buttons'] [data-content-type='button-item'] div.pagebuilder-button-link {
	box-sizing: border-box;
}
a.pagebuilder-button-primary, button.pagebuilder-button-primary, div.pagebuilder-button-primary {
	display: flex;
	align-items: center;
	border-radius: 0.5rem;
	--tw-bg-opacity: 1;
	background-color: rgba(107, 114, 128, var(--tw-bg-opacity));
	padding-left: 1rem;
	padding-right: 1rem;
	padding-top: 0.5rem;
	padding-bottom: 0.5rem;
	font-size: 1rem;
	line-height: 1.5rem;
	font-weight: 500;
	--tw-text-opacity: 1;
	color: rgba(255, 255, 255, var(--tw-text-opacity));
	--tw-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
	box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
@media (min-width: 768px) {
    a.pagebuilder-button-primary, button.pagebuilder-button-primary, div.pagebuilder-button-primary {
		padding-left: 1.5rem;
		padding-right: 1.5rem;
	}
    a.pagebuilder-button-primary, button.pagebuilder-button-primary, div.pagebuilder-button-primary {
		font-size: 1.125rem;
		line-height: 1.75rem;
	}
        }
@media (min-width: 1280px) {
    a.pagebuilder-button-primary, button.pagebuilder-button-primary, div.pagebuilder-button-primary {
		font-size: 1rem;
		line-height: 1.5rem;
	}
        }
a.pagebuilder-button-primary svg, button.pagebuilder-button-primary svg, div.pagebuilder-button-primary svg {
	display: inline-flex;
}
a.pagebuilder-button-primary span, button.pagebuilder-button-primary span, div.pagebuilder-button-primary span {
            vertical-align: middle;
        }
a.pagebuilder-button-primary:hover, button.pagebuilder-button-primary:hover, div.pagebuilder-button-primary:hover {
	--tw-bg-opacity: 1;
	background-color: rgba(75, 85, 99, var(--tw-bg-opacity));
}
a.pagebuilder-button-primary:focus, button.pagebuilder-button-primary:focus, div.pagebuilder-button-primary:focus {
	border-color: transparent;
	outline: 2px solid transparent;
	outline-offset: 2px;
	--tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
	--tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);
	box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
	--tw-ring-opacity: 1;
	--tw-ring-color: rgba(30, 64, 175, var(--tw-ring-opacity));
	--tw-ring-opacity: 0.5;
}
a.pagebuilder-button-primary, button.pagebuilder-button-primary, div.pagebuilder-button-primary {
	--tw-bg-opacity: 1;
	background-color: rgba(29, 78, 216, var(--tw-bg-opacity));
	--tw-text-opacity: 1;
	color: rgba(255, 255, 255, var(--tw-text-opacity));
	--tw-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
	box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
a.pagebuilder-button-primary:hover, button.pagebuilder-button-primary:hover, div.pagebuilder-button-primary:hover {
	--tw-bg-opacity: 1;
	background-color: rgba(37, 99, 235, var(--tw-bg-opacity));
	--tw-text-opacity: 1;
	color: rgba(255, 255, 255, var(--tw-text-opacity));
	--tw-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
	box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
a.pagebuilder-button-secondary, button.pagebuilder-button-secondary, div.pagebuilder-button-secondary {
	display: flex;
	align-items: center;
	border-radius: 0.5rem;
	--tw-bg-opacity: 1;
	background-color: rgba(107, 114, 128, var(--tw-bg-opacity));
	padding-left: 1rem;
	padding-right: 1rem;
	padding-top: 0.5rem;
	padding-bottom: 0.5rem;
	font-size: 1rem;
	line-height: 1.5rem;
	font-weight: 500;
	--tw-text-opacity: 1;
	color: rgba(255, 255, 255, var(--tw-text-opacity));
	--tw-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
	box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
@media (min-width: 768px) {
    a.pagebuilder-button-secondary, button.pagebuilder-button-secondary, div.pagebuilder-button-secondary {
		padding-left: 1.5rem;
		padding-right: 1.5rem;
	}
    a.pagebuilder-button-secondary, button.pagebuilder-button-secondary, div.pagebuilder-button-secondary {
		font-size: 1.125rem;
		line-height: 1.75rem;
	}
        }
@media (min-width: 1280px) {
    a.pagebuilder-button-secondary, button.pagebuilder-button-secondary, div.pagebuilder-button-secondary {
		font-size: 1rem;
		line-height: 1.5rem;
	}
        }
a.pagebuilder-button-secondary svg, button.pagebuilder-button-secondary svg, div.pagebuilder-button-secondary svg {
	display: inline-flex;
}
a.pagebuilder-button-secondary span, button.pagebuilder-button-secondary span, div.pagebuilder-button-secondary span {
            vertical-align: middle;
        }
a.pagebuilder-button-secondary:hover, button.pagebuilder-button-secondary:hover, div.pagebuilder-button-secondary:hover {
	--tw-bg-opacity: 1;
	background-color: rgba(75, 85, 99, var(--tw-bg-opacity));
}
a.pagebuilder-button-secondary:focus, button.pagebuilder-button-secondary:focus, div.pagebuilder-button-secondary:focus {
	border-color: transparent;
	outline: 2px solid transparent;
	outline-offset: 2px;
	--tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
	--tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);
	box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
	--tw-ring-opacity: 1;
	--tw-ring-color: rgba(30, 64, 175, var(--tw-ring-opacity));
	--tw-ring-opacity: 0.5;
}
a.pagebuilder-button-secondary, button.pagebuilder-button-secondary, div.pagebuilder-button-secondary {
	border-width: 2px;
	--tw-border-opacity: 1;
	border-color: rgba(37, 99, 235, var(--tw-border-opacity));
	--tw-bg-opacity: 1;
	background-color: rgba(255, 255, 255, var(--tw-bg-opacity));
	--tw-text-opacity: 1;
	color: rgba(0, 0, 0, var(--tw-text-opacity));
	--tw-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
	box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
a.pagebuilder-button-secondary:hover, button.pagebuilder-button-secondary:hover, div.pagebuilder-button-secondary:hover {
	--tw-border-opacity: 1;
	border-color: rgba(30, 64, 175, var(--tw-border-opacity));
	--tw-bg-opacity: 1;
	background-color: rgba(255, 255, 255, var(--tw-bg-opacity));
	--tw-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
	box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
a.pagebuilder-button-secondary:focus, button.pagebuilder-button-secondary:focus, div.pagebuilder-button-secondary:focus {
	border-width: 2px;
	border-color: transparent;
}
/* HTML Code */
[data-content-type='html'] {
	overflow-wrap: break-word;
}
/**
 * Media
 */
/* Image */
[data-content-type='image'] {
	box-sizing: border-box;
}
[data-content-type='image'] > [data-element='link'],
    [data-content-type='image'] > [data-element='link'] img {
        border-radius: inherit;
    }
[data-content-type='image'] .pagebuilder-mobile-hidden {
	display: none;
}
@media (min-width: 768px) {
	[data-content-type='image'] .pagebuilder-mobile-hidden {
		display: block;
	}
	[data-content-type='image'] .pagebuilder-mobile-only {
		display: none;
	}
}
[data-content-type='image'] figcaption {
	overflow-wrap: break-word;
}
/* Video */
[data-content-type='video'] {
    font-size: 0;
}
[data-content-type='video'] .pagebuilder-video-inner {
	box-sizing: border-box;
	display: inline-block;
	width: 100%;
}
[data-content-type='video'] .pagebuilder-video-container {
	position: relative;
	overflow: hidden;
        border-radius: inherit;
        padding-top: 56.25%;
}
[data-content-type='video'] iframe, [data-content-type='video'] video {
	position: absolute;
	left: 0px;
	top: 0px;
	height: 100%;
	width: 100%;
}
/* Banner */
[data-content-type='banner'] > [data-element='link'], [data-content-type='banner'] > [data-element='empty_link'] {
        color: inherit;
        text-decoration: inherit;
    }
[data-content-type='banner'] > [data-element='link']:hover, [data-content-type='banner'] > [data-element='empty_link']:hover {
            color: inherit;
            text-decoration: inherit;
        }
[data-content-type='banner'] .pagebuilder-banner-wrapper {
	box-sizing: border-box;
	overflow-wrap: break-word;
        background-clip: padding-box;
        border-radius: inherit;
        background-attachment: scroll !important;
}
[data-content-type='banner'] .pagebuilder-banner-wrapper .pagebuilder-overlay {
	position: relative;
	box-sizing: border-box;
	padding: 2rem;
	transition-property: background-color, border-color, color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;
	transition-property: background-color, border-color, color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
	transition-property: background-color, border-color, color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;
	transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
	transition-duration: 150ms;
	transition-duration: 500ms;
	transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
[data-content-type='banner'] .pagebuilder-banner-wrapper .pagebuilder-overlay.pagebuilder-poster-overlay {
	display: flex;
	align-items: center;
	justify-content: center;
}
[data-content-type='banner'] .pagebuilder-banner-wrapper .pagebuilder-overlay:not(.pagebuilder-poster-overlay) {
	max-width: none;
}
@media (min-width: 768px) {
	[data-content-type='banner'] .pagebuilder-banner-wrapper .pagebuilder-overlay:not(.pagebuilder-poster-overlay) {
		max-width: 36rem;
	}
}
[data-content-type='banner'] .pagebuilder-banner-wrapper.jarallax .video-overlay {
	z-index: 0;
}
[data-content-type='banner'] .pagebuilder-banner-wrapper [data-element='content'] {
	overflow: auto;
            min-height: 50px;
}
[data-content-type='banner'] .pagebuilder-banner-wrapper .pagebuilder-banner-button {
	margin: 0px;
	margin-top: 1.25rem;
	max-width: 100%;
	overflow-wrap: break-word;
	transition-property: opacity;
	transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
	transition-duration: 150ms;
	transition-duration: 500ms;
	transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
            text-align: inherit;
}
[data-content-type='banner'] .pagebuilder-banner-wrapper .pagebuilder-poster-content {
	width: 100%;
}
[data-content-type='banner'][data-appearance='collage-centered'] .pagebuilder-banner-wrapper .pagebuilder-overlay {
	margin-left: auto;
	margin-right: auto;
}
[data-content-type='banner'][data-appearance='collage-left'] .pagebuilder-banner-wrapper .pagebuilder-overlay {
	margin-right: auto;
}
[data-content-type='banner'][data-appearance='collage-right'] .pagebuilder-banner-wrapper .pagebuilder-overlay {
	margin-left: auto;
}
/* Slider */
[data-content-type='slider'] {
	visibility: hidden;
	position: relative;
}
[data-content-type='slider'].glider-initialized {
	visibility: visible;
}
[data-content-type='slider'] [data-role='glider-content'] {
	overflow-y: hidden;
}
[data-content-type='slider'] a.button {
        color: initial;
        padding: 10px;
        text-decoration: none;
    }
[data-content-type='slider'] .carousel-nav {
	position: absolute;
	bottom: 0px;
	margin-bottom: 0.5rem;
	--tw-bg-opacity: 1;
	background-color: rgba(249, 250, 251, var(--tw-bg-opacity));
	--tw-bg-opacity: 0.75;
        left: 50%;
        transform: translateX(-50%);
}
/* Slide */
[data-content-type='slide'] {
	box-sizing: border-box;
	overflow: hidden;
	line-height: 1.25rem;
    min-height: inherit;
}
[data-content-type='slide'] > [data-element='link'],
    [data-content-type='slide'] > [data-element='empty_link'] {
        color: inherit;
        min-height: inherit;
        text-decoration: inherit;
    }
[data-content-type='slide'] > [data-element='link']:hover, [data-content-type='slide'] > [data-element='empty_link']:hover {
            color: inherit;
            text-decoration: inherit;
        }
[data-content-type='slide'] + [data-content-type='slide'] {
	height: 0px;
	min-height: 0px;
}
[data-content-type='slide'] .pagebuilder-slide-wrapper {
	box-sizing: border-box;
	overflow-wrap: break-word;
        border-radius: inherit;
        min-height: inherit;
}
[data-content-type='slide'] .pagebuilder-slide-wrapper .jarallax-viewport-element {
	position: absolute;
	top: 0px;
	z-index: 50;
	height: 100%;
	width: 0.125rem;
            left: -15000vw;
}
[data-content-type='slide'] .pagebuilder-slide-wrapper.jarallax .video-overlay {
	z-index: 10;
                -webkit-transform: unset;
                z-index: 1;
}
[data-content-type='slide'] .pagebuilder-slide-wrapper.jarallax .pagebuilder-overlay {
	position: relative;
	z-index: 20;
}
[data-content-type='slide'] .pagebuilder-slide-wrapper.jarallax [id*='jarallax-container'] > div,
                [data-content-type='slide'] .pagebuilder-slide-wrapper.jarallax [id*='jarallax-container'] > img,
                [data-content-type='slide'] .pagebuilder-slide-wrapper.jarallax [id*='jarallax-container'] > video,
                [data-content-type='slide'] .pagebuilder-slide-wrapper.jarallax [id*='jarallax-container'] > iframe {
                    margin: auto !important;
                    transform: none !important;
                }
[data-content-type='slide'] .pagebuilder-slide-wrapper .pagebuilder-overlay {
	box-sizing: border-box;
	padding: 2rem;
	transition-property: background-color, border-color, color, fill, stroke;
	transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
	transition-duration: 150ms;
            border-radius: inherit;
}
[data-content-type='slide'] .pagebuilder-slide-wrapper .pagebuilder-overlay.pagebuilder-poster-overlay {
	display: flex;
	align-items: center;
	justify-content: center;
                min-height: inherit;
}
[data-content-type='slide'] .pagebuilder-slide-wrapper .pagebuilder-overlay:not(.pagebuilder-poster-overlay) {
	max-width: none;
}
@media (min-width: 768px) {
	[data-content-type='slide'] .pagebuilder-slide-wrapper .pagebuilder-overlay:not(.pagebuilder-poster-overlay) {
		max-width: 32rem;
	}
}
[data-content-type='slide'] .pagebuilder-slide-wrapper [data-element='content'] {
	overflow: auto;
            min-height: 50px;
}
[data-content-type='slide'] .pagebuilder-slide-wrapper .pagebuilder-slide-button {
	margin: 0px;
	margin-top: 1.25rem;
	max-width: 100%;
	overflow-wrap: break-word;
	transition-property: opacity;
	transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
	transition-duration: 150ms;
            text-align: inherit;
}
[data-content-type='slide'] .pagebuilder-slide-wrapper .pagebuilder-poster-content {
	width: 100%;
}
[data-content-type='slide'][data-appearance='collage-centered'] .pagebuilder-slide-wrapper .pagebuilder-overlay {
	margin-left: auto;
	margin-right: auto;
}
[data-content-type='slide'][data-appearance='collage-left'] .pagebuilder-slide-wrapper .pagebuilder-overlay {
	margin-right: auto;
}
[data-content-type='slide'][data-appearance='collage-right'] .pagebuilder-slide-wrapper .pagebuilder-overlay {
	margin-left: auto;
}
/* Map */
[data-content-type='map'] {
	box-sizing: border-box;
	height: 18rem;
}
/**
 * Add Content
 */
/* Block */
[data-content-type$='block'] .block p:last-child {
	margin-bottom: 1rem;
	margin-top: 0px;
}
/* Dynamic Block */
[data-content-type='dynamic_block'] [data-content-type='image'] img {
	display: inline;
}
[data-content-type='dynamic_block'] .block-banners .banner-item-content, [data-content-type='dynamic_block'] .block-banners-inline .banner-item-content {
	margin-bottom: auto;
}
/* Products */
[data-content-type='products'][data-appearance='carousel'] [data-role='glider-content'] {
        grid-template-columns: repeat(100, calc(50% - 1rem));
    }
@media (min-width: 768px) {
[data-content-type='products'][data-appearance='carousel'] [data-role='glider-content'] {
            grid-template-columns: repeat(100, calc(33% - 1rem))
    }
        }
@media (min-width: 1024px) {
[data-content-type='products'][data-appearance='carousel'] [data-role='glider-content'] {
            grid-template-columns: repeat(100, calc(25% - 1rem))
    }
        }
/**
 * Glider
 */
.glider-contain {
	position: relative;
	margin-top: 0px;
	margin-bottom: 0px;
	margin-left: auto;
	margin-right: auto;
	width: 100%;
}
.glider {
	overflow-y: hidden;
}
.glider.draggable {
	-webkit-user-select: none;
	   -moz-user-select: none;
	    -ms-user-select: none;
	        user-select: none;
        cursor: -webkit-grab;
        cursor: grab;
}
.glider.draggable .glider-slide img {
	pointer-events: none;
	-webkit-user-select: none;
	   -moz-user-select: none;
	    -ms-user-select: none;
	        user-select: none;
}
.glider.drag {
        cursor: -webkit-grabbing;
        cursor: grabbing;
    }
.glider::-webkit-scrollbar {
	height: 0px;
	opacity: 0;
}
.glider .glider-track {
	z-index: 10;
	margin: 0px;
	display: flex;
	width: 100%;
	padding: 0px;
}
.glider-slide {
	margin: 0.5rem;
	width: 100%;
	-webkit-user-select: none;
	   -moz-user-select: none;
	    -ms-user-select: none;
	        user-select: none;
	align-content: center;
	justify-content: center;
    min-width: 150px;
}
.glider-slide img {
	max-width: 100%;
}
.glider-hide {
	opacity: 0;
}
.glider-prev.disabled, .glider-next.disabled {
	cursor: default;
	--tw-text-opacity: 1;
	color: rgba(107, 114, 128, var(--tw-text-opacity));
	opacity: 0.25;
}
.glider-dot {
	margin: 0.25rem;
	display: block;
	height: 0.75rem;
	width: 0.75rem;
	cursor: pointer;
	border-radius: 9999px;
	--tw-bg-opacity: 1;
	background-color: rgba(0, 0, 0, var(--tw-bg-opacity));
	opacity: 0.25;
	--tw-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
	box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.glider-dot.active {
	opacity: 1;
}
@media (max-width: 36em) {
    .glider::-webkit-scrollbar {
		height: 0.25rem;
	}
    .glider::-webkit-scrollbar {
		width: 0.5rem;
	}
    .glider::-webkit-scrollbar {
		-webkit-appearance: none;
		        appearance: none;
	}
    .glider::-webkit-scrollbar {
		opacity: 1;
	}

    .glider::-webkit-scrollbar-thumb {
		opacity: 1;
	}

    .glider::-webkit-scrollbar-thumb {
        border-radius: 99px;
        background-color: rgba(156, 156, 156, .25);
        box-shadow: 0 0 1px rgba(255, 255, 255, .25)
    }
}
[data-show-arrows='false'] .glider-prev, [data-show-arrows='false'] .glider-next {
	display: none;
}
[data-show-dots='false'] .glider-dots {
	display: none;
}
/* purgecss end ignore */
.sr-only {
	position: absolute;
	width: 1px;
	height: 1px;
	padding: 0;
	margin: -1px;
	overflow: hidden;
	clip: rect(0, 0, 0, 0);
	white-space: nowrap;
	border-width: 0;
}
.pointer-events-none {
	pointer-events: none;
}
.visible {
	visibility: visible;
}
.invisible {
	visibility: hidden;
}
.fixed {
	position: fixed;
}
.absolute {
	position: absolute;
}
.relative {
	position: relative;
}
.inset-0 {
	top: 0px;
	right: 0px;
	bottom: 0px;
	left: 0px;
}
.inset-x-0 {
	left: 0px;
	right: 0px;
}
.inset-y-0 {
	top: 0px;
	bottom: 0px;
}
.bottom-0 {
	bottom: 0px;
}
.left-0 {
	left: 0px;
}
.right-0 {
	right: 0px;
}
.top-0 {
	top: 0px;
}
.top-full {
	top: 100%;
}
.z-10 {
	z-index: 10;
}
.z-50 {
	z-index: 50;
}
.z-30 {
	z-index: 30;
}
.z-20 {
	z-index: 20;
}
.z-0 {
	z-index: 0;
}
.order-first {
	order: -9999;
}
.order-1 {
	order: 1;
}
.order-2 {
	order: 2;
}
.order-3 {
	order: 3;
}
.col-span-2 {
	grid-column: span 2 / span 2;
}
.col-span-4 {
	grid-column: span 4 / span 4;
}
.col-span-3 {
	grid-column: span 3 / span 3;
}
.col-span-1 {
	grid-column: span 1 / span 1;
}
.float-left {
	float: left;
}
.clear-left {
	clear: left;
}
.-m-2 {
	margin: -0.5rem;
}
.m-0 {
	margin: 0px;
}
.m-2 {
	margin: 0.5rem;
}
.m-1 {
	margin: 0.25rem;
}
.m-auto {
	margin: auto;
}
.-m-4 {
	margin: -1rem;
}
.-m-3 {
	margin: -0.75rem;
}
.my-4 {
	margin-top: 1rem;
	margin-bottom: 1rem;
}
.mx-auto {
	margin-left: auto;
	margin-right: auto;
}
.my-2 {
	margin-top: 0.5rem;
	margin-bottom: 0.5rem;
}
.my-8 {
	margin-top: 2rem;
	margin-bottom: 2rem;
}
.my-1 {
	margin-top: 0.25rem;
	margin-bottom: 0.25rem;
}
.mx-1 {
	margin-left: 0.25rem;
	margin-right: 0.25rem;
}
.my-3 {
	margin-top: 0.75rem;
	margin-bottom: 0.75rem;
}
.-mx-2 {
	margin-left: -0.5rem;
	margin-right: -0.5rem;
}
.mx-6 {
	margin-left: 1.5rem;
	margin-right: 1.5rem;
}
.mx-4 {
	margin-left: 1rem;
	margin-right: 1rem;
}
.my-6 {
	margin-top: 1.5rem;
	margin-bottom: 1.5rem;
}
.-mx-1 {
	margin-left: -0.25rem;
	margin-right: -0.25rem;
}
.-mx-4 {
	margin-left: -1rem;
	margin-right: -1rem;
}
.mx-2 {
	margin-left: 0.5rem;
	margin-right: 0.5rem;
}
.-mx-6 {
	margin-left: -1.5rem;
	margin-right: -1.5rem;
}
.my-12 {
	margin-top: 3rem;
	margin-bottom: 3rem;
}
.mt-10 {
	margin-top: 2.5rem;
}
.ml-auto {
	margin-left: auto;
}
.mb-12 {
	margin-bottom: 3rem;
}
.mb-4 {
	margin-bottom: 1rem;
}
.mt-2 {
	margin-top: 0.5rem;
}
.mb-1 {
	margin-bottom: 0.25rem;
}
.mt-6 {
	margin-top: 1.5rem;
}
.mb-3 {
	margin-bottom: 0.75rem;
}
.-mr-1 {
	margin-right: -0.25rem;
}
.ml-2 {
	margin-left: 0.5rem;
}
.mt-1 {
	margin-top: 0.25rem;
}
.-mt-6 {
	margin-top: -1.5rem;
}
.mr-3 {
	margin-right: 0.75rem;
}
.mr-1 {
	margin-right: 0.25rem;
}
.ml-1 {
	margin-left: 0.25rem;
}
.mt-4 {
	margin-top: 1rem;
}
.mt-3 {
	margin-top: 0.75rem;
}
.ml-4 {
	margin-left: 1rem;
}
.mb-2 {
	margin-bottom: 0.5rem;
}
.-mt-4 {
	margin-top: -1rem;
}
.mr-4 {
	margin-right: 1rem;
}
.mt-8 {
	margin-top: 2rem;
}
.mb-6 {
	margin-bottom: 1.5rem;
}
.mr-2 {
	margin-right: 0.5rem;
}
.mr-10 {
	margin-right: 2.5rem;
}
.ml-10 {
	margin-left: 2.5rem;
}
.mb-8 {
	margin-bottom: 2rem;
}
.-mr-4 {
	margin-right: -1rem;
}
.mt-12 {
	margin-top: 3rem;
}
.ml-3 {
	margin-left: 0.75rem;
}
.-ml-px {
	margin-left: -1px;
}
.mb-10 {
	margin-bottom: 2.5rem;
}
.-mt-8 {
	margin-top: -2rem;
}
.mr-auto {
	margin-right: auto;
}
.mt-0 {
	margin-top: 0px;
}
.-mt-5 {
	margin-top: -1.25rem;
}
.-mr-2 {
	margin-right: -0.5rem;
}
.mt-auto {
	margin-top: auto;
}
.ml-6 {
	margin-left: 1.5rem;
}
.-ml-6 {
	margin-left: -1.5rem;
}
.mr-8 {
	margin-right: 2rem;
}
.box-content {
	box-sizing: content-box;
}
.block {
	display: block;
}
.inline-block {
	display: inline-block;
}
.inline {
	display: inline;
}
.flex {
	display: flex;
}
.inline-flex {
	display: inline-flex;
}
.table {
	display: table;
}
.table-caption {
	display: table-caption;
}
.table-cell {
	display: table-cell;
}
.table-row {
	display: table-row;
}
.grid {
	display: grid;
}
.contents {
	display: contents;
}
.hidden {
	display: none;
}
.h-48 {
	height: 12rem;
}
.h-5 {
	height: 1.25rem;
}
.h-8 {
	height: 2rem;
}
.h-6 {
	height: 1.5rem;
}
.h-10 {
	height: 2.5rem;
}
.h-full {
	height: 100%;
}
.h-4 {
	height: 1rem;
}
.h-3 {
	height: 0.75rem;
}
.h-auto {
	height: auto;
}
.h-9 {
	height: 2.25rem;
}
.h-16 {
	height: 4rem;
}
.h-screen {
	height: 100vh;
}
.max-h-screen {
	max-height: 100vh;
}
.max-h-screen-75 {
	max-height: 75vh;
}
.min-h-14 {
	min-height: 3.5rem;
}
.min-h-0 {
	min-height: 0px;
}
.w-full {
	width: 100%;
}
.w-1\/2 {
	width: 50%;
}
.w-5 {
	width: 1.25rem;
}
.w-56 {
	width: 14rem;
}
.w-64 {
	width: 16rem;
}
.w-8 {
	width: 2rem;
}
.w-6 {
	width: 1.5rem;
}
.w-10 {
	width: 2.5rem;
}
.w-4 {
	width: 1rem;
}
.w-screen {
	width: 100vw;
}
.w-40 {
	width: 10rem;
}
.w-0 {
	width: 0px;
}
.w-4\/12 {
	width: 33.333333%;
}
.w-8\/12 {
	width: 66.666667%;
}
.w-32 {
	width: 8rem;
}
.w-3 {
	width: 0.75rem;
}
.w-auto {
	width: auto;
}
.w-44 {
	width: 11rem;
}
.w-60 {
	width: 15rem;
}
.w-9 {
	width: 2.25rem;
}
.w-20 {
	width: 5rem;
}
.w-7\/12 {
	width: 58.333333%;
}
.w-5\/12 {
	width: 41.666667%;
}
.w-16 {
	width: 4rem;
}
.w-1\/4 {
	width: 25%;
}
.w-3\/4 {
	width: 75%;
}
.min-w-20 {
	min-width: 5rem;
}
.min-w-40 {
	min-width: 10rem;
}
.min-w-48 {
	min-width: 12rem;
}
.max-w-full {
	max-width: 100%;
}
.max-w-md {
	max-width: 28rem;
}
.max-w-screen-2xl {
	max-width: 1536px;
}
.max-w-prose {
	max-width: 65ch;
}
.flex-1 {
	flex: 1 1 0%;
}
.flex-none {
	flex: none;
}
.flex-shrink-0 {
	flex-shrink: 0;
}
.flex-grow-0 {
	flex-grow: 0;
}
.flex-grow {
	flex-grow: 1;
}
.table-auto {
	table-layout: auto;
}
.origin-top-left {
	transform-origin: top left;
}
.origin-top-right {
	transform-origin: top right;
}
.-translate-x-full {
	--tw-translate-x: -100%;
	transform: var(--tw-transform);
}
.-translate-y-full {
	--tw-translate-y: -100%;
	transform: var(--tw-transform);
}
.-translate-y-8 {
	--tw-translate-y: -2rem;
	transform: var(--tw-transform);
}
.-translate-x-1\/3 {
	--tw-translate-x: -33.333333%;
	transform: var(--tw-transform);
}
.-translate-x-12 {
	--tw-translate-x: -3rem;
	transform: var(--tw-transform);
}
.-translate-y-6 {
	--tw-translate-y: -1.5rem;
	transform: var(--tw-transform);
}
.translate-x-full {
	--tw-translate-x: 100%;
	transform: var(--tw-transform);
}
.translate-x-0 {
	--tw-translate-x: 0px;
	transform: var(--tw-transform);
}
.-translate-x-1\/2 {
	--tw-translate-x: -50%;
	transform: var(--tw-transform);
}
.-translate-x-1 {
	--tw-translate-x: -0.25rem;
	transform: var(--tw-transform);
}
.translate-y-1\/2 {
	--tw-translate-y: 50%;
	transform: var(--tw-transform);
}
.rotate-180 {
	--tw-rotate: 180deg;
	transform: var(--tw-transform);
}
.rotate-0 {
	--tw-rotate: 0deg;
	transform: var(--tw-transform);
}
.transform {
	transform: var(--tw-transform);
}
@-webkit-keyframes spin {
	to {
		transform: rotate(360deg);
	}
}
@keyframes spin {
	to {
		transform: rotate(360deg);
	}
}
.animate-spin {
	-webkit-animation: spin 1s linear infinite;
	        animation: spin 1s linear infinite;
}
.cursor-pointer {
	cursor: pointer;
}
.cursor-not-allowed {
	cursor: not-allowed;
}
.cursor-default {
	cursor: default;
}
.select-none {
	-webkit-user-select: none;
	   -moz-user-select: none;
	    -ms-user-select: none;
	        user-select: none;
}
.list-disc {
	list-style-type: disc;
}
.appearance-none {
	-webkit-appearance: none;
	   -moz-appearance: none;
	        appearance: none;
}
.grid-flow-row {
	grid-auto-flow: row;
}
.grid-cols-3 {
	grid-template-columns: repeat(3, minmax(0, 1fr));
}
.grid-cols-2 {
	grid-template-columns: repeat(2, minmax(0, 1fr));
}
.grid-cols-1 {
	grid-template-columns: repeat(1, minmax(0, 1fr));
}
.grid-cols-7 {
	grid-template-columns: repeat(7, minmax(0, 1fr));
}
.grid-cols-4 {
	grid-template-columns: repeat(4, minmax(0, 1fr));
}
.grid-cols-5 {
	grid-template-columns: repeat(5, minmax(0, 1fr));
}
.flex-row {
	flex-direction: row;
}
.flex-col {
	flex-direction: column;
}
.flex-wrap {
	flex-wrap: wrap;
}
.flex-nowrap {
	flex-wrap: nowrap;
}
.content-center {
	align-content: center;
}
.items-start {
	align-items: flex-start;
}
.items-end {
	align-items: flex-end;
}
.items-center {
	align-items: center;
}
.items-baseline {
	align-items: baseline;
}
.justify-start {
	justify-content: flex-start;
}
.justify-end {
	justify-content: flex-end;
}
.justify-center {
	justify-content: center;
}
.justify-between {
	justify-content: space-between;
}
.justify-evenly {
	justify-content: space-evenly;
}
.gap-2 {
	gap: 0.5rem;
}
.gap-4 {
	gap: 1rem;
}
.gap-1\.5 {
	gap: 0.375rem;
}
.gap-1 {
	gap: 0.25rem;
}
.gap-6 {
	gap: 1.5rem;
}
.gap-8 {
	gap: 2rem;
}
.gap-x-4 {
	-moz-column-gap: 1rem;
	     column-gap: 1rem;
}
.gap-y-2 {
	row-gap: 0.5rem;
}
.gap-y-0 {
	row-gap: 0px;
}
.gap-y-16 {
	row-gap: 4rem;
}
.gap-x-1 {
	-moz-column-gap: 0.25rem;
	     column-gap: 0.25rem;
}
.gap-x-2 {
	-moz-column-gap: 0.5rem;
	     column-gap: 0.5rem;
}
.space-x-4 > :not([hidden]) ~ :not([hidden]) {
	--tw-space-x-reverse: 0;
	margin-right: calc(1rem * var(--tw-space-x-reverse));
	margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-y-6 > :not([hidden]) ~ :not([hidden]) {
	--tw-space-y-reverse: 0;
	margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));
	margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));
}
.space-y-2 > :not([hidden]) ~ :not([hidden]) {
	--tw-space-y-reverse: 0;
	margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));
	margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));
}
.space-x-1 > :not([hidden]) ~ :not([hidden]) {
	--tw-space-x-reverse: 0;
	margin-right: calc(0.25rem * var(--tw-space-x-reverse));
	margin-left: calc(0.25rem * calc(1 - var(--tw-space-x-reverse)));
}
.self-end {
	align-self: flex-end;
}
.self-center {
	align-self: center;
}
.overflow-auto {
	overflow: auto;
}
.overflow-hidden {
	overflow: hidden;
}
.overflow-x-auto {
	overflow-x: auto;
}
.overflow-y-auto {
	overflow-y: auto;
}
.overflow-x-hidden {
	overflow-x: hidden;
}
.overflow-y-hidden {
	overflow-y: hidden;
}
.overflow-x-scroll {
	overflow-x: scroll;
}
.truncate {
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}
.whitespace-nowrap {
	white-space: nowrap;
}
.break-all {
	word-break: break-all;
}
.rounded-lg {
	border-radius: 0.5rem;
}
.rounded-sm {
	border-radius: 0.125rem;
}
.rounded-full {
	border-radius: 9999px;
}
.rounded {
	border-radius: 0.25rem;
}
.rounded-md {
	border-radius: 0.375rem;
}
.rounded-l-md {
	border-top-left-radius: 0.375rem;
	border-bottom-left-radius: 0.375rem;
}
.rounded-r-md {
	border-top-right-radius: 0.375rem;
	border-bottom-right-radius: 0.375rem;
}
.border {
	border-width: 1px;
}
.border-2 {
	border-width: 2px;
}
.border-0 {
	border-width: 0px;
}
.border-t-2 {
	border-top-width: 2px;
}
.border-b {
	border-bottom-width: 1px;
}
.border-t {
	border-top-width: 1px;
}
.border-b-2 {
	border-bottom-width: 2px;
}
.border-r-0 {
	border-right-width: 0px;
}
.border-l-0 {
	border-left-width: 0px;
}
.border-l-2 {
	border-left-width: 2px;
}
.border-l-4 {
	border-left-width: 4px;
}
.border-l {
	border-left-width: 1px;
}
.border-container-darker {
	--tw-border-opacity: 1;
	border-color: rgba(182, 182, 182, var(--tw-border-opacity));
}
.border-container {
	--tw-border-opacity: 1;
	border-color: rgba(231, 231, 231, var(--tw-border-opacity));
}
.border-red-500 {
	--tw-border-opacity: 1;
	border-color: rgba(239, 68, 68, var(--tw-border-opacity));
}
.border-gray-300 {
	--tw-border-opacity: 1;
	border-color: rgba(209, 213, 219, var(--tw-border-opacity));
}
.border-primary {
	--tw-border-opacity: 1;
	border-color: rgba(29, 78, 216, var(--tw-border-opacity));
}
.border-container-lighter {
	--tw-border-opacity: 1;
	border-color: rgba(245, 245, 245, var(--tw-border-opacity));
}
.border-gray-200 {
	--tw-border-opacity: 1;
	border-color: rgba(229, 231, 235, var(--tw-border-opacity));
}
.border-green-400 {
	--tw-border-opacity: 1;
	border-color: rgba(52, 211, 153, var(--tw-border-opacity));
}
.border-current {
	border-color: currentColor;
}
.bg-black {
	--tw-bg-opacity: 1;
	background-color: rgba(0, 0, 0, var(--tw-bg-opacity));
}
.bg-white {
	--tw-bg-opacity: 1;
	background-color: rgba(255, 255, 255, var(--tw-bg-opacity));
}
.bg-container-darker {
	--tw-bg-opacity: 1;
	background-color: rgba(245, 245, 245, var(--tw-bg-opacity));
}
.bg-container-lighter {
	--tw-bg-opacity: 1;
	background-color: rgba(255, 255, 255, var(--tw-bg-opacity));
}
.bg-container {
	--tw-bg-opacity: 1;
	background-color: rgba(250, 250, 250, var(--tw-bg-opacity));
}
.bg-primary {
	--tw-bg-opacity: 1;
	background-color: rgba(29, 78, 216, var(--tw-bg-opacity));
}
.bg-gray-900 {
	--tw-bg-opacity: 1;
	background-color: rgba(17, 24, 39, var(--tw-bg-opacity));
}
.bg-gray-200 {
	--tw-bg-opacity: 1;
	background-color: rgba(229, 231, 235, var(--tw-bg-opacity));
}
.bg-yellow-500 {
	--tw-bg-opacity: 1;
	background-color: rgba(245, 158, 11, var(--tw-bg-opacity));
}
.bg-gray-100 {
	--tw-bg-opacity: 1;
	background-color: rgba(243, 244, 246, var(--tw-bg-opacity));
}
.bg-green-500 {
	--tw-bg-opacity: 1;
	background-color: rgba(16, 185, 129, var(--tw-bg-opacity));
}
.bg-red-500 {
	--tw-bg-opacity: 1;
	background-color: rgba(239, 68, 68, var(--tw-bg-opacity));
}
.bg-opacity-50 {
	--tw-bg-opacity: 0.5;
}
.bg-opacity-60 {
	--tw-bg-opacity: 0.6;
}
.bg-opacity-25 {
	--tw-bg-opacity: 0.25;
}
.bg-opacity-100 {
	--tw-bg-opacity: 1;
}
.bg-opacity-95 {
	--tw-bg-opacity: 0.95;
}
.fill-current {
	fill: currentColor;
}
.stroke-current {
	stroke: currentColor;
}
.stroke-1 {
	stroke-width: 1;
}
.object-contain {
	-o-object-fit: contain;
	   object-fit: contain;
}
.object-cover {
	-o-object-fit: cover;
	   object-fit: cover;
}
.object-center {
	-o-object-position: center;
	   object-position: center;
}
.p-10 {
	padding: 2.5rem;
}
.p-2 {
	padding: 0.5rem;
}
.p-4 {
	padding: 1rem;
}
.p-0 {
	padding: 0px;
}
.p-1 {
	padding: 0.25rem;
}
.p-3 {
	padding: 0.75rem;
}
.p-8 {
	padding: 2rem;
}
.py-4 {
	padding-top: 1rem;
	padding-bottom: 1rem;
}
.px-6 {
	padding-left: 1.5rem;
	padding-right: 1.5rem;
}
.py-8 {
	padding-top: 2rem;
	padding-bottom: 2rem;
}
.py-0 {
	padding-top: 0px;
	padding-bottom: 0px;
}
.px-4 {
	padding-left: 1rem;
	padding-right: 1rem;
}
.py-2 {
	padding-top: 0.5rem;
	padding-bottom: 0.5rem;
}
.py-6 {
	padding-top: 1.5rem;
	padding-bottom: 1.5rem;
}
.py-3 {
	padding-top: 0.75rem;
	padding-bottom: 0.75rem;
}
.px-10 {
	padding-left: 2.5rem;
	padding-right: 2.5rem;
}
.px-2 {
	padding-left: 0.5rem;
	padding-right: 0.5rem;
}
.px-1 {
	padding-left: 0.25rem;
	padding-right: 0.25rem;
}
.px-3 {
	padding-left: 0.75rem;
	padding-right: 0.75rem;
}
.py-1 {
	padding-top: 0.25rem;
	padding-bottom: 0.25rem;
}
.px-5 {
	padding-left: 1.25rem;
	padding-right: 1.25rem;
}
.px-0 {
	padding-left: 0px;
	padding-right: 0px;
}
.py-16 {
	padding-top: 4rem;
	padding-bottom: 4rem;
}
.px-8 {
	padding-left: 2rem;
	padding-right: 2rem;
}
.pb-1 {
	padding-bottom: 0.25rem;
}
.pl-1 {
	padding-left: 0.25rem;
}
.pr-1 {
	padding-right: 0.25rem;
}
.pt-1 {
	padding-top: 0.25rem;
}
.pr-4 {
	padding-right: 1rem;
}
.pt-6 {
	padding-top: 1.5rem;
}
.pb-12 {
	padding-bottom: 3rem;
}
.pb-2 {
	padding-bottom: 0.5rem;
}
.pr-6 {
	padding-right: 1.5rem;
}
.pb-6 {
	padding-bottom: 1.5rem;
}
.pt-5 {
	padding-top: 1.25rem;
}
.pt-4 {
	padding-top: 1rem;
}
.pt-2 {
	padding-top: 0.5rem;
}
.pb-3 {
	padding-bottom: 0.75rem;
}
.pt-16 {
	padding-top: 4rem;
}
.pl-5 {
	padding-left: 1.25rem;
}
.pt-3 {
	padding-top: 0.75rem;
}
.pb-4 {
	padding-bottom: 1rem;
}
.pl-6 {
	padding-left: 1.5rem;
}
.pl-3 {
	padding-left: 0.75rem;
}
.pb-0 {
	padding-bottom: 0px;
}
.pb-16 {
	padding-bottom: 4rem;
}
.pl-2 {
	padding-left: 0.5rem;
}
.text-left {
	text-align: left;
}
.text-center {
	text-align: center;
}
.text-right {
	text-align: right;
}
.align-top {
	vertical-align: top;
}
.align-middle {
	vertical-align: middle;
}
.text-xl {
	font-size: 1.25rem;
	line-height: 1.75rem;
}
.text-base {
	font-size: 1rem;
	line-height: 1.5rem;
}
.text-sm {
	font-size: 0.875rem;
	line-height: 1.25rem;
}
.text-xs {
	font-size: 0.75rem;
	line-height: 1rem;
}
.text-lg {
	font-size: 1.125rem;
	line-height: 1.75rem;
}
.text-2xl {
	font-size: 1.5rem;
	line-height: 2rem;
}
.text-3xl {
	font-size: 1.875rem;
	line-height: 2.25rem;
}
.text-4xl {
	font-size: 2.25rem;
	line-height: 2.5rem;
}
.font-bold {
	font-weight: 700;
}
.font-medium {
	font-weight: 500;
}
.font-semibold {
	font-weight: 600;
}
.font-normal {
	font-weight: 400;
}
.uppercase {
	text-transform: uppercase;
}
.leading-relaxed {
	line-height: 1.625;
}
.leading-tight {
	line-height: 1.25;
}
.leading-6 {
	line-height: 1.5rem;
}
.leading-7 {
	line-height: 1.75rem;
}
.leading-5 {
	line-height: 1.25rem;
}
.leading-none {
	line-height: 1;
}
.leading-normal {
	line-height: 1.5;
}
.tracking-widest {
	letter-spacing: 0.1em;
}
.tracking-wider {
	letter-spacing: 0.05em;
}
.tracking-wide {
	letter-spacing: 0.025em;
}
.text-gray-900 {
	--tw-text-opacity: 1;
	color: rgba(17, 24, 39, var(--tw-text-opacity));
}
.text-gray-700 {
	--tw-text-opacity: 1;
	color: rgba(55, 65, 81, var(--tw-text-opacity));
}
.text-primary {
	--tw-text-opacity: 1;
	color: rgba(39, 39, 42, var(--tw-text-opacity));
}
.text-black {
	--tw-text-opacity: 1;
	color: rgba(0, 0, 0, var(--tw-text-opacity));
}
.text-white {
	--tw-text-opacity: 1;
	color: rgba(255, 255, 255, var(--tw-text-opacity));
}
.text-red {
	--tw-text-opacity: 1;
	color: rgba(239, 68, 68, var(--tw-text-opacity));
}
.text-gray-400 {
	--tw-text-opacity: 1;
	color: rgba(156, 163, 175, var(--tw-text-opacity));
}
.text-yellow-400 {
	--tw-text-opacity: 1;
	color: rgba(251, 191, 36, var(--tw-text-opacity));
}
.text-secondary {
	--tw-text-opacity: 1;
	color: rgba(82, 82, 91, var(--tw-text-opacity));
}
.text-gray-500 {
	--tw-text-opacity: 1;
	color: rgba(107, 114, 128, var(--tw-text-opacity));
}
.text-gray-600 {
	--tw-text-opacity: 1;
	color: rgba(75, 85, 99, var(--tw-text-opacity));
}
.text-primary-lighter {
	--tw-text-opacity: 1;
	color: rgba(63, 63, 70, var(--tw-text-opacity));
}
.text-gray-300 {
	--tw-text-opacity: 1;
	color: rgba(209, 213, 219, var(--tw-text-opacity));
}
.text-secondary-darker {
	--tw-text-opacity: 1;
	color: rgba(39, 39, 42, var(--tw-text-opacity));
}
.text-gray-800 {
	--tw-text-opacity: 1;
	color: rgba(31, 41, 55, var(--tw-text-opacity));
}
.text-orange-400 {
	--tw-text-opacity: 1;
	color: rgba(251, 146, 60, var(--tw-text-opacity));
}
.text-gray-200 {
	--tw-text-opacity: 1;
	color: rgba(229, 231, 235, var(--tw-text-opacity));
}
.text-blue-900 {
	--tw-text-opacity: 1;
	color: rgba(30, 58, 138, var(--tw-text-opacity));
}
.underline {
	text-decoration: underline;
}
.line-through {
	text-decoration: line-through;
}
.no-underline {
	text-decoration: none;
}
.opacity-0 {
	opacity: 0;
}
.opacity-100 {
	opacity: 1;
}
.opacity-25 {
	opacity: 0.25;
}
.opacity-75 {
	opacity: 0.75;
}
.opacity-50 {
	opacity: 0.5;
}
.shadow-xl {
	--tw-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
	box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-lg {
	--tw-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
	box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow {
	--tw-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
	box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-sm {
	--tw-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
	box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-2xl {
	--tw-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
	box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.outline-none {
	outline: 2px solid transparent;
	outline-offset: 2px;
}
.ring {
	--tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
	--tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color);
	box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.ring-primary {
	--tw-ring-opacity: 1;
	--tw-ring-color: rgba(30, 64, 175, var(--tw-ring-opacity));
}
.ring-blue-500 {
	--tw-ring-opacity: 1;
	--tw-ring-color: rgba(59, 130, 246, var(--tw-ring-opacity));
}
.ring-opacity-50 {
	--tw-ring-opacity: 0.5;
}
.ring-opacity-75 {
	--tw-ring-opacity: 0.75;
}
.ring-offset-2 {
	--tw-ring-offset-width: 2px;
}
.filter {
	filter: var(--tw-filter);
}
.transition {
	transition-property: background-color, border-color, color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;
	transition-property: background-color, border-color, color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
	transition-property: background-color, border-color, color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;
	transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
	transition-duration: 150ms;
}
.transition-colors {
	transition-property: background-color, border-color, color, fill, stroke;
	transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
	transition-duration: 150ms;
}
.transition-transform {
	transition-property: transform;
	transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
	transition-duration: 150ms;
}
.transition-all {
	transition-property: all;
	transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
	transition-duration: 150ms;
}
.duration-300 {
	transition-duration: 300ms;
}
.duration-200 {
	transition-duration: 200ms;
}
.duration-500 {
	transition-duration: 500ms;
}
.duration-150 {
	transition-duration: 150ms;
}
.ease-out {
	transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
}
.ease-in {
	transition-timing-function: cubic-bezier(0.4, 0, 1, 1);
}
.ease-in-out {
	transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
.page-footer{}
.page-header {
}
.page-main {

}
.table-row-items > div.table-row-item {
	--tw-bg-opacity: 1;
	background-color: rgba(245, 245, 245, var(--tw-bg-opacity));
}
.table-row-items > div.table-row-item:nth-child(2n + 1) {
	--tw-bg-opacity: 1;
	background-color: rgba(255, 255, 255, var(--tw-bg-opacity));
}
.category-view .sidebar .block.filter {}
.account-nav ul li a, .account-nav ul li strong {
	display: flex;
	justify-content: space-between;
	padding-top: 0.25rem;
	padding-bottom: 0.25rem;
	--tw-text-opacity: 1;
	color: rgba(82, 82, 91, var(--tw-text-opacity));
}
.account-nav ul li a:hover {
	--tw-text-opacity: 1;
	color: rgba(0, 0, 0, var(--tw-text-opacity));
}
.account-nav ul li strong {
	font-weight: 400;
	--tw-text-opacity: 1;
	color: rgba(0, 0, 0, var(--tw-text-opacity));
	text-decoration: underline;
}
.actions-toolbar {
	margin-top: 1.5rem;
	display: flex;
	flex-direction: row-reverse;
	align-items: center;
	justify-content: space-between;
	border-top-width: 1px;
	--tw-border-opacity: 1;
	border-color: rgba(182, 182, 182, var(--tw-border-opacity));
	padding-top: 1rem;
}
.actions-toolbar a.back {
	--tw-text-opacity: 1;
	color: rgba(39, 39, 42, var(--tw-text-opacity));
	text-decoration: underline;
}
body > div > div.grecaptcha-badge {
	display: none;
	height: 0px !important;
	width: 0px !important;
}
.order-items > div:nth-child(even) {
	--tw-bg-opacity: 1;
	background-color: rgba(245, 245, 245, var(--tw-bg-opacity));
}
.order-links {
	display: block;
	align-items: center;
	--tw-bg-opacity: 1;
	background-color: rgba(250, 250, 250, var(--tw-bg-opacity));
}
.order-links li {
	display: inline-block;
	padding-top: 0.5rem;
	padding-bottom: 0.5rem;
	padding-left: 1rem;
	padding-right: 1rem;
	cursor: pointer;
	white-space: nowrap;
}
.order-links li.current {
	flex-grow: 1;
	--tw-text-opacity: 1;
	color: rgba(39, 39, 42, var(--tw-text-opacity));
}
.order-links li a {
	text-decoration: underline;
}
.order-date {
	font-size: 0.875rem;
	line-height: 1.25rem;
}
form .field, fieldset .field {
	margin-top: 0.25rem;
}
/* Reserve space for single line form validation messages */
form .field.field-reserved, fieldset .field.field-reserved {
	margin-bottom: 1.75rem;
}
form .field.field-reserved ul:last-of-type, fieldset .field.field-reserved ul:last-of-type {
	margin-bottom: -1.5rem;
	padding-bottom: 0.25rem;
}
form .field.field-reserved ul, fieldset .field.field-reserved ul {
	font-size: 0.875rem;
	line-height: 1.25rem;
}
form label, fieldset label {
	margin-bottom: 0.5rem;
	display: block;
	--tw-text-opacity: 1;
	color: rgba(39, 39, 42, var(--tw-text-opacity));
}
form .field.choice, fieldset .field.choice {
	display: flex;
	align-items: center;
}
form .field.choice input, fieldset .field.choice input {
	margin-right: 1rem;
}
form .field.choice label, fieldset .field.choice label {
	margin-bottom: 0px;
}
form .field.field-error .messages, fieldset .field.field-error .messages {
	--tw-text-opacity: 1;
	color: rgba(239, 68, 68, var(--tw-text-opacity));
}
form legend, fieldset legend {
	margin-bottom: 0.75rem;
	font-size: 1.25rem;
	line-height: 1.75rem;
	--tw-text-opacity: 1;
	color: rgba(39, 39, 42, var(--tw-text-opacity));
}
form legend + br, fieldset legend + br {
	display: none;
}
fieldset ~ fieldset {
	margin-top: 2rem;
}
.first\:mt-0:first-child {
	margin-top: 0px;
}
.last\:mr-0:last-child {
	margin-right: 0px;
}
.last\:mb-6:last-child {
	margin-bottom: 1.5rem;
}
.last\:mb-0:last-child {
	margin-bottom: 0px;
}
.last\:border-0:last-child {
	border-width: 0px;
}
.last\:border-b-0:last-child {
	border-bottom-width: 0px;
}
.last\:border-b:last-child {
	border-bottom-width: 1px;
}
.last\:pb-0:last-child {
	padding-bottom: 0px;
}
.even\:bg-container-darker:nth-child(even) {
	--tw-bg-opacity: 1;
	background-color: rgba(245, 245, 245, var(--tw-bg-opacity));
}
.even\:bg-container:nth-child(even) {
	--tw-bg-opacity: 1;
	background-color: rgba(250, 250, 250, var(--tw-bg-opacity));
}
.invalid\:ring-2:invalid {
	--tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
	--tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
	box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.invalid\:ring-red-500:invalid {
	--tw-ring-opacity: 1;
	--tw-ring-color: rgba(239, 68, 68, var(--tw-ring-opacity));
}
.focus-within\:ring-1:focus-within {
	--tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
	--tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
	box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.focus-within\:ring-2:focus-within {
	--tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
	--tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
	box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.hover\:border-primary:hover {
	--tw-border-opacity: 1;
	border-color: rgba(29, 78, 216, var(--tw-border-opacity));
}
.hover\:bg-gray-100:hover {
	--tw-bg-opacity: 1;
	background-color: rgba(243, 244, 246, var(--tw-bg-opacity));
}
.hover\:bg-container-darker:hover {
	--tw-bg-opacity: 1;
	background-color: rgba(245, 245, 245, var(--tw-bg-opacity));
}
.hover\:text-primary:hover {
	--tw-text-opacity: 1;
	color: rgba(39, 39, 42, var(--tw-text-opacity));
}
.hover\:text-black:hover {
	--tw-text-opacity: 1;
	color: rgba(0, 0, 0, var(--tw-text-opacity));
}
.hover\:text-secondary-darker:hover {
	--tw-text-opacity: 1;
	color: rgba(39, 39, 42, var(--tw-text-opacity));
}
.hover\:text-primary-darker:hover {
	--tw-text-opacity: 1;
	color: rgba(24, 24, 27, var(--tw-text-opacity));
}
.hover\:text-gray-400:hover {
	--tw-text-opacity: 1;
	color: rgba(156, 163, 175, var(--tw-text-opacity));
}
.hover\:text-gray-500:hover {
	--tw-text-opacity: 1;
	color: rgba(107, 114, 128, var(--tw-text-opacity));
}
.hover\:text-yellow-500:hover {
	--tw-text-opacity: 1;
	color: rgba(245, 158, 11, var(--tw-text-opacity));
}
.hover\:text-red-600:hover {
	--tw-text-opacity: 1;
	color: rgba(220, 38, 38, var(--tw-text-opacity));
}
.hover\:text-blue-600:hover {
	--tw-text-opacity: 1;
	color: rgba(37, 99, 235, var(--tw-text-opacity));
}
.hover\:underline:hover {
	text-decoration: underline;
}
.hover\:no-underline:hover {
	text-decoration: none;
}
.hover\:opacity-100:hover {
	opacity: 1;
}
.hover\:shadow-sm:hover {
	--tw-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
	box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.focus\:z-10:focus {
	z-index: 10;
}
.focus\:border-0:focus {
	border-width: 0px;
}
.focus\:border-red-500:focus {
	--tw-border-opacity: 1;
	border-color: rgba(239, 68, 68, var(--tw-border-opacity));
}
.focus\:border-blue-300:focus {
	--tw-border-opacity: 1;
	border-color: rgba(147, 197, 253, var(--tw-border-opacity));
}
.focus\:border-primary-lighter:focus {
	--tw-border-opacity: 1;
	border-color: rgba(37, 99, 235, var(--tw-border-opacity));
}
.focus\:border-primary:focus {
	--tw-border-opacity: 1;
	border-color: rgba(29, 78, 216, var(--tw-border-opacity));
}
.focus\:border-transparent:focus {
	border-color: transparent;
}
.focus\:text-gray-500:focus {
	--tw-text-opacity: 1;
	color: rgba(107, 114, 128, var(--tw-text-opacity));
}
.focus\:outline-none:focus {
	outline: 2px solid transparent;
	outline-offset: 2px;
}
.focus\:ring-0:focus {
	--tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
	--tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
	box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.focus\:ring-red-500:focus {
	--tw-ring-opacity: 1;
	--tw-ring-color: rgba(239, 68, 68, var(--tw-ring-opacity));
}
.active\:bg-gray-100:active {
	--tw-bg-opacity: 1;
	background-color: rgba(243, 244, 246, var(--tw-bg-opacity));
}
.active\:text-gray-500:active {
	--tw-text-opacity: 1;
	color: rgba(107, 114, 128, var(--tw-text-opacity));
}
.active\:text-gray-700:active {
	--tw-text-opacity: 1;
	color: rgba(55, 65, 81, var(--tw-text-opacity));
}
.active\:ring-0:active {
	--tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
	--tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
	box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.disabled\:pointer-events-none:disabled {
	pointer-events: none;
}
.disabled\:bg-gray-100:disabled {
	--tw-bg-opacity: 1;
	background-color: rgba(243, 244, 246, var(--tw-bg-opacity));
}
.disabled\:opacity-75:disabled {
	opacity: 0.75;
}
@media (min-width: 640px) {
	.sm\:order-3 {
		order: 3;
	}
	.sm\:order-2 {
		order: 2;
	}
	.sm\:order-1 {
		order: 1;
	}
	.sm\:col-span-6 {
		grid-column: span 6 / span 6;
	}
	.sm\:col-span-2 {
		grid-column: span 2 / span 2;
	}
	.sm\:-mx-8 {
		margin-left: -2rem;
		margin-right: -2rem;
	}
	.sm\:ml-3 {
		margin-left: 0.75rem;
	}
	.sm\:mt-0 {
		margin-top: 0px;
	}
	.sm\:mr-8 {
		margin-right: 2rem;
	}
	.sm\:mb-0 {
		margin-bottom: 0px;
	}
	.sm\:ml-2 {
		margin-left: 0.5rem;
	}
	.sm\:ml-6 {
		margin-left: 1.5rem;
	}
	.sm\:block {
		display: block;
	}
	.sm\:flex {
		display: flex;
	}
	.sm\:w-1\/2 {
		width: 50%;
	}
	.sm\:w-48 {
		width: 12rem;
	}
	.sm\:w-5\/6 {
		width: 83.333333%;
	}
	.sm\:w-1\/3 {
		width: 33.333333%;
	}
	.sm\:w-auto {
		width: auto;
	}
	.sm\:w-20 {
		width: 5rem;
	}
	.sm\:grid-cols-2 {
		grid-template-columns: repeat(2, minmax(0, 1fr));
	}
	.sm\:grid-cols-8 {
		grid-template-columns: repeat(8, minmax(0, 1fr));
	}
	.sm\:flex-row {
		flex-direction: row;
	}
	.sm\:items-start {
		align-items: flex-start;
	}
	.sm\:gap-8 {
		gap: 2rem;
	}
	.sm\:overflow-hidden {
		overflow: hidden;
	}
	.sm\:px-6 {
		padding-left: 1.5rem;
		padding-right: 1.5rem;
	}
	.sm\:py-24 {
		padding-top: 6rem;
		padding-bottom: 6rem;
	}
	.sm\:py-1 {
		padding-top: 0.25rem;
		padding-bottom: 0.25rem;
	}
	.sm\:pb-0 {
		padding-bottom: 0px;
	}
	.sm\:text-right {
		text-align: right;
	}
	.sm\:text-2xl {
		font-size: 1.5rem;
		line-height: 2rem;
	}
	.sm\:text-4xl {
		font-size: 2.25rem;
		line-height: 2.5rem;
	}
	.sm\:text-5xl {
		font-size: 3rem;
		line-height: 1;
	}
	.sm\:text-3xl {
		font-size: 1.875rem;
		line-height: 2.25rem;
	}
	.sm\:duration-700 {
		transition-duration: 700ms;
	}
}
@media (min-width: 768px) {
	.md\:order-3 {
		order: 3;
	}
	.md\:col-span-3 {
		grid-column: span 3 / span 3;
	}
	.md\:col-span-1 {
		grid-column: span 1 / span 1;
	}
	.md\:my-0 {
		margin-top: 0px;
		margin-bottom: 0px;
	}
	.md\:-mx-4 {
		margin-left: -1rem;
		margin-right: -1rem;
	}
	.md\:mx-4 {
		margin-left: 1rem;
		margin-right: 1rem;
	}
	.md\:mx-0 {
		margin-left: 0px;
		margin-right: 0px;
	}
	.md\:mx-auto {
		margin-left: auto;
		margin-right: auto;
	}
	.md\:mt-5 {
		margin-top: 1.25rem;
	}
	.md\:mt-0 {
		margin-top: 0px;
	}
	.md\:mb-0 {
		margin-bottom: 0px;
	}
	.md\:mr-5 {
		margin-right: 1.25rem;
	}
	.md\:mr-0 {
		margin-right: 0px;
	}
	.md\:ml-0 {
		margin-left: 0px;
	}
	.md\:block {
		display: block;
	}
	.md\:inline-block {
		display: inline-block;
	}
	.md\:flex {
		display: flex;
	}
	.md\:grid {
		display: grid;
	}
	.md\:hidden {
		display: none;
	}
	.md\:h-6 {
		height: 1.5rem;
	}
	.md\:h-auto {
		height: auto;
	}
	.md\:w-2\/3 {
		width: 66.666667%;
	}
	.md\:w-1\/3 {
		width: 33.333333%;
	}
	.md\:w-1\/2 {
		width: 50%;
	}
	.md\:w-full {
		width: 100%;
	}
	.md\:w-6 {
		width: 1.5rem;
	}
	.md\:w-auto {
		width: auto;
	}
	.md\:w-2\/6 {
		width: 33.333333%;
	}
	.md\:w-5\/12 {
		width: 41.666667%;
	}
	.md\:w-7\/12 {
		width: 58.333333%;
	}
	.md\:-translate-x-1\/3 {
		--tw-translate-x: -33.333333%;
		transform: var(--tw-transform);
	}
	.md\:grid-cols-3 {
		grid-template-columns: repeat(3, minmax(0, 1fr));
	}
	.md\:grid-cols-2 {
		grid-template-columns: repeat(2, minmax(0, 1fr));
	}
	.md\:grid-cols-4 {
		grid-template-columns: repeat(4, minmax(0, 1fr));
	}
	.md\:flex-row {
		flex-direction: row;
	}
	.md\:items-start {
		align-items: flex-start;
	}
	.md\:items-center {
		align-items: center;
	}
	.md\:justify-start {
		justify-content: flex-start;
	}
	.md\:justify-end {
		justify-content: flex-end;
	}
	.md\:justify-between {
		justify-content: space-between;
	}
	.md\:border-0 {
		border-width: 0px;
	}
	.md\:bg-transparent {
		background-color: transparent;
	}
	.md\:px-8 {
		padding-left: 2rem;
		padding-right: 2rem;
	}
	.md\:py-0 {
		padding-top: 0px;
		padding-bottom: 0px;
	}
	.md\:px-0 {
		padding-left: 0px;
		padding-right: 0px;
	}
	.md\:px-6 {
		padding-left: 1.5rem;
		padding-right: 1.5rem;
	}
	.md\:px-1 {
		padding-left: 0.25rem;
		padding-right: 0.25rem;
	}
	.md\:py-6 {
		padding-top: 1.5rem;
		padding-bottom: 1.5rem;
	}
	.md\:pl-16 {
		padding-left: 4rem;
	}
	.md\:pl-5 {
		padding-left: 1.25rem;
	}
	.md\:text-left {
		text-align: left;
	}
	.md\:text-sm {
		font-size: 0.875rem;
		line-height: 1.25rem;
	}
	.md\:text-2xl {
		font-size: 1.5rem;
		line-height: 2rem;
	}
	.md\:text-lg {
		font-size: 1.125rem;
		line-height: 1.75rem;
	}
	.md\:text-3xl {
		font-size: 1.875rem;
		line-height: 2.25rem;
	}
	.md\:font-bold {
		font-weight: 700;
	}
}
@media (min-width: 1024px) {
	.lg\:relative {
		position: relative;
	}
	.lg\:sticky {
		position: -webkit-sticky;
		position: sticky;
	}
	.lg\:order-2 {
		order: 2;
	}
	.lg\:order-1 {
		order: 1;
	}
	.lg\:col-span-6 {
		grid-column: span 6 / span 6;
	}
	.lg\:col-span-2 {
		grid-column: span 2 / span 2;
	}
	.lg\:float-right {
		float: right;
	}
	.lg\:mt-3 {
		margin-top: 0.75rem;
	}
	.lg\:ml-5 {
		margin-left: 1.25rem;
	}
	.lg\:mt-8 {
		margin-top: 2rem;
	}
	.lg\:ml-2 {
		margin-left: 0.5rem;
	}
	.lg\:mr-4 {
		margin-right: 1rem;
	}
	.lg\:ml-0 {
		margin-left: 0px;
	}
	.lg\:block {
		display: block;
	}
	.lg\:inline-block {
		display: inline-block;
	}
	.lg\:inline {
		display: inline;
	}
	.lg\:flex {
		display: flex;
	}
	.lg\:table-cell {
		display: table-cell;
	}
	.lg\:table-header-group {
		display: table-header-group;
	}
	.lg\:table-row {
		display: table-row;
	}
	.lg\:grid {
		display: grid;
	}
	.lg\:hidden {
		display: none;
	}
	.lg\:min-h-0 {
		min-height: 0px;
	}
	.lg\:w-1\/2 {
		width: 50%;
	}
	.lg\:w-1\/4 {
		width: 25%;
	}
	.lg\:w-2\/3 {
		width: 66.666667%;
	}
	.lg\:w-1\/3 {
		width: 33.333333%;
	}
	.lg\:w-3\/4 {
		width: 75%;
	}
	.lg\:w-auto {
		width: auto;
	}
	.lg\:grid-cols-4 {
		grid-template-columns: repeat(4, minmax(0, 1fr));
	}
	.lg\:grid-cols-6 {
		grid-template-columns: repeat(6, minmax(0, 1fr));
	}
	.lg\:grid-cols-8 {
		grid-template-columns: repeat(8, minmax(0, 1fr));
	}
	.lg\:grid-cols-2 {
		grid-template-columns: repeat(2, minmax(0, 1fr));
	}
	.lg\:flex-row {
		flex-direction: row;
	}
	.lg\:justify-start {
		justify-content: flex-start;
	}
	.lg\:px-5 {
		padding-left: 1.25rem;
		padding-right: 1.25rem;
	}
	.lg\:py-2 {
		padding-top: 0.5rem;
		padding-bottom: 0.5rem;
	}
	.lg\:px-8 {
		padding-left: 2rem;
		padding-right: 2rem;
	}
	.lg\:py-32 {
		padding-top: 8rem;
		padding-bottom: 8rem;
	}
	.lg\:px-16 {
		padding-left: 4rem;
		padding-right: 4rem;
	}
	.lg\:pr-8 {
		padding-right: 2rem;
	}
	.lg\:pl-24 {
		padding-left: 6rem;
	}
	.lg\:pl-10 {
		padding-left: 2.5rem;
	}
	.lg\:pt-2 {
		padding-top: 0.5rem;
	}
	.lg\:pt-0 {
		padding-top: 0px;
	}
	.lg\:text-left {
		text-align: left;
	}
	.lg\:text-right {
		text-align: right;
	}
	.lg\:text-sm {
		font-size: 0.875rem;
		line-height: 1.25rem;
	}
	.lg\:text-xl {
		font-size: 1.25rem;
		line-height: 1.75rem;
	}
}
@media (min-width: 1280px) {
	.xl\:col-span-2 {
		grid-column: span 2 / span 2;
	}
	.xl\:mt-0 {
		margin-top: 0px;
	}
	.xl\:-mt-12 {
		margin-top: -3rem;
	}
	.xl\:w-1\/2 {
		width: 50%;
	}
	.xl\:w-1\/4 {
		width: 25%;
	}
	.xl\:w-1\/3 {
		width: 33.333333%;
	}
	.xl\:flex-grow {
		flex-grow: 1;
	}
	.xl\:grid-cols-3 {
		grid-template-columns: repeat(3, minmax(0, 1fr));
	}
	.xl\:border-none {
		border-style: none;
	}
	.xl\:px-2 {
		padding-left: 0.5rem;
		padding-right: 0.5rem;
	}
}
