const colors = require('tailwindcss/colors');
const hyvaModules = require('@hyva-themes/hyva-modules');

const themeColors = {
  transparent: 'transparent',
  current: 'currentColor',
  black: '#1D1D1D',
  white: colors.white,

  primary: {
    DEFAULT: '#00281E',
    '100': '#B4C6B9',
    '300': '#19B99C',
    '500': '#00281E',
    '600': '#033B2C',
    '700': '#DBE2DD',
    '800': '#5E5E5E',
    '900': '#d2dcd6'
  },
  secondary: {
    DEFAULT: '#E6DFD4',
    '300': '#F5F2EE',
    '500': '#E6DFD4',
    '600': '#D5C7B3',
    '700': '#C2A88F',
    '900': '#9c978e',
  },
  tertiary: {
    DEFAULT: '#F36C2C',
    '300': '#FFDBC3',
    '500': '#F36C2C',
  },
  pink: {
    DEFAULT: '#FFA5AC',
  },
  yellow: {
    DEFAULT: '#F8D073',
  },
  blue: {
    DEFAULT: '#81D7DE',
  },
  purple: {
    DEFAULT: '#A49BC5',
  },
  error: {
    DEFAULT: '#EFBFC1',
    '700': '#EA545A'
  }
};

module.exports = hyvaModules.mergeTailwindConfig({
  content: [
    // this theme's phtml and layout XML files
    '../../**/*.phtml',
    '../../*/layout/*.xml',
    '../../*/page_layout/override/base/*.xml',
    // parent theme in Vendor (if this is a child-theme)
    '../../../../../../../vendor/hyva-themes/magento2-default-theme/**/*.phtml',
    '../../../../../../../vendor/hyva-themes/magento2-default-theme/*/layout/*.xml',
    '../../../../../../../vendor/hyva-themes/magento2-default-theme/*/page_layout/override/base/*.xml',
    // app/code phtml files (if need tailwind classes from app/code modules)
    '../../../../../../../app/code/**/*.phtml',
  ],
  corePlugins: {
    container: false,
  },
  future: {
    hoverOnlyWhenSupported: true,
  },
  plugins: [
    require('@tailwindcss/forms'),
    require('@tailwindcss/typography'),
  ],
  safelist: [
    'btn',
    { pattern: /btn-./ },
  ],
  theme: {
    extend: {
      animation: {
        'slide-in-right': 'slide-in-right 0.3s cubic-bezier(0.250, 0.460, 0.450, 0.940) both'
      },

      // for existing components
      backgroundColor: {
        container: {
          lighter: colors.white,
          'DEFAULT': themeColors.secondary[300],
          darker: themeColors.secondary,
        }
      },
      // for existing components
      borderColor: {
        container: {
          lighter: colors.gray[300],
          'DEFAULT': colors.gray[100],
          darker: colors.gray[500],
        }
      },
      boxShadow: {
        header: '0 -0.25rem 2.75rem 0 rgba(0, 0, 0, 0.19)',
      },
      colors: themeColors,
      fontFamily: {
        sans: ['Poppins', 'Arial', 'sans-serif'],
      },
      fontSize: { 
        'inherit': 'inherit',
        xxs: '0.625rem'
      },
      fontWeight: {
        'inherit': 'inherit'
      },
      maxWidth: {
        container: 'var(--max-w-container)'
      },
      keyframes: {
        'slide-in-right': {
          '0%': {
            transform: 'translateX(1000px)',
            opacity: 0
          },
          '100%': {
            transform: 'translateX(0)',
            opacity: 1
          }
        }
      },
      padding: {
        container: 'var(--p-container)'
      },
      spacing: {
        header: 'var(--h-header)',
      },
      // for existing components
      textColor: {
        orange: colors.orange,
        primary: {
          lighter: colors.gray['500'],
          darker: colors.gray['900'],
        },
        secondary: {
          lighter: colors.gray['500'],
          darker: colors.gray['900'],
        },
      },
      width: {
        'login-form': '34rem'
      },
      zIndex: {
        'dialog': '99',
        'product-filter': '98',
        header: '97'
      }
    },
  },
});
