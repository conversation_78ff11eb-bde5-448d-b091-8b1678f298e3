<?php

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsSolid;
use Magento\Customer\Block\Account\Resetpassword;
use Magento\Framework\Escaper;

/** @var Resetpassword $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */

$heroicons = $viewModels->require(HeroiconsSolid::class);
$minimumPasswordLength = $block->getMinimumPasswordLength();
$passwordMinCharacterSets = $block->getRequiredCharacterClassesNumber();
?>

<div class="container">
    <div class="max-w-xl bg-white rounded-xl p-8 m-auto">
        <h1 class="heading-4 mb-10"><?= $escaper->escapeHtml(__('Set a New Password')) ?></h1>
        <form action="<?= $escaper->escapeUrl(
            $block->getUrl(
                '*/*/resetpasswordpost',
                ['_query' => ['id' => $block->getRpCustomerId(), 'token' => $block->getResetPasswordLinkToken()]]
            )
        ) ?>"
              x-data="Object.assign(hyva.formValidation($el), {showPassword: false, showPasswordConfirm: false})"
              @submit="onSubmit"
              method="post"
              id="form-validate"
              class="form password reset">
            <fieldset class="fieldset">
                <?= $block->getBlockHtml('formkey'); ?>
                <div class="field field-reserved required">
                    <label class="label" for="password">
                        <span><?= $escaper->escapeHtml(__('New Password')) ?></span>
                    </label>
                    <div class="control flex items-center">
                        <input :type="showPassword ? 'text' : 'password'" type="password"
                               class="form-input grow !rounded-[18.75rem]" name="password" id="password" required
                               minlength="<?= $escaper->escapeHtmlAttr($minimumPasswordLength) ?>"
                               @input="onChange"
                               data-validate='{"password-strength": {"minCharacterSets": <?= (int) $passwordMinCharacterSets ?>}}'
                               autocomplete="<?= $block->isAutocompleteDisabled() ? 'off' : 'new-password' ?>"
                        />
                        <div x-on:click="showPassword = !showPassword"
                             class="cursor-pointer px-4"
                             :aria-label="showPassword ? '<?= $escaper->escapeJs(__('Hide Password')) ?>' : '<?= $escaper->escapeJs(__('Show Password')) ?>'"
                        >
                            <template x-if="!showPassword">
                                <?= $heroicons->eyeHtml('w-5 h-5'); ?>
                            </template>
                            <template x-if="showPassword">
                                <?= $heroicons->eyeOffHtml('w-5 h-5'); ?>
                            </template>
                        </div>
                    </div>
                </div>
                <div class="field field-reserved">
                    <label class="label" for="password-confirmation">
                        <span><?= $escaper->escapeHtml(__('Confirm New Password')) ?></span>
                    </label>
                    <div class="control flex items-center">
                        <input :type="showPasswordConfirm ? 'text' : 'password'" type="password"
                               class="form-input grow !rounded-[18.75rem]" name="password_confirmation"
                               id="password-confirmation"
                               data-validate='{"equalTo": "password"}'
                               @input="onChange"
                               autocomplete="<?= $block->isAutocompleteDisabled() ? 'off' : 'new-password' ?>"
                        />
                        <div x-on:click="showPasswordConfirm = !showPasswordConfirm"
                             class="cursor-pointer px-4"
                             :aria-label="showPasswordConfirm ? '<?= $escaper->escapeJs(__('Hide Password')) ?>' : '<?= $escaper->escapeJs(__('Show Password')) ?>'"
                        >
                            <template x-if="!showPasswordConfirm">
                                <?= $heroicons->eyeHtml('w-5 h-5'); ?>
                            </template>
                            <template x-if="showPasswordConfirm">
                                <?= $heroicons->eyeOffHtml('w-5 h-5'); ?>
                            </template>
                        </div>
                    </div>
                </div>
            </fieldset>
            <div class="actions-toolbar">
                <div class="primary">
                    <button type="submit" class="btn btn-primary w-full action submit primary"><span><?= $escaper->escapeHtml(__('Set a New Password')) ?></span></button>
                </div>
            </div>
        </form>
    </div>
</div>