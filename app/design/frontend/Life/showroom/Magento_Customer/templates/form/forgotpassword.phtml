<?php

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\Customer\ForgotPasswordButton;
use Hyva\Theme\ViewModel\ReCaptcha;
use Hyva\Theme\ViewModel\StoreConfig;
use Magento\Customer\Block\Account\Forgotpassword;
use Magento\Framework\Escaper;

/** @var Forgotpassword $block */
/** @var Escaper $escaper */
/** @var ReCaptcha $recaptcha */
/** @var ForgotPasswordButton $forgotPasswordButtonViewModel */
/** @var ViewModelRegistry $viewModels */

$forgotPasswordButtonViewModel = $viewModels->require(ForgotPasswordButton::class);
$storeConfig = $viewModels->require(StoreConfig::class);
$isAutocompleteEnabled = $storeConfig->getStoreConfig('customer/password/autocomplete_on_storefront');

$formId = 'user_forgotpassword';

// Do not replace this with $viewModels->require(ReCaptcha::class); that might break the dependency
// on the Magento_ReCaptchaCustomer module
$recaptcha = $block->getData('viewModelRecaptcha');
?>
<div class="container">
    <div class="max-w-xl bg-white rounded-xl p-8 m-auto">
        <header class="mb-10">
            <h1 class="heading-4"><?= $escaper->escapeHtml(__('Password forgotten')) ?></h1>
            <p class="mt-4">
                <?= $escaper->escapeHtml(__('Please enter your email address below to receive a password reset link.')) ?>
            </p>
        </header>
        <form action="<?= $escaper->escapeUrl($block->getUrl('*/*/forgotpasswordpost')) ?>"
              method="post"
              id="<?= $escaper->escapeHtmlAttr($formId) ?>"
              x-data="initPasswordForm()"
              @submit.prevent="submitForm();">
            <?= $block->getBlockHtml('formkey') ?>
            <input type="hidden" name="formId" value="<?= $escaper->escapeHtmlAttr($formId) ?>"/>
            <?= $block->getChildHtml('form_fields_before') ?>
            <?= $recaptcha ? $recaptcha->getInputHtml(ReCaptcha::RECAPTCHA_FORM_ID_CUSTOMER_FORGOT_PASSWORD) : '' ?>
            <fieldset class="fieldset grid gap-6 mb-6">
                <div class="field email required">
                    <label for="email_address" class="label">
                        <span><?= $escaper->escapeHtml(__('Email')) ?></span>
                    </label>
                    <div class="control">
                        <input
                            type="email"
                            name="email"
                            alt="email"
                            id="email_address"
                            class="form-input w-full !rounded-[18.75rem]"
                            autocomplete="<?= $isAutocompleteEnabled ? 'username' : 'off' ?>"
                            required
                            value="<?= $escaper->escapeHtmlAttr($block->getEmailValue()) ?>"
                        >
                    </div>
                </div>
                <?= $block->getChildHtml('form_additional_info') ?>
                <?= $recaptcha ? $recaptcha->getLegalNoticeHtml(ReCaptcha::RECAPTCHA_FORM_ID_CUSTOMER_FORGOT_PASSWORD) : '' ?>
            </fieldset>
            <div class="actions-toolbar grid gap-2 text-center">
                <div class="primary">
                    <button type="submit" class="btn btn-primary w-full action submit disabled:opacity-75"
                        <?php if ($forgotPasswordButtonViewModel->disabled()): ?> disabled data-recaptcha-btn<?php endif; ?>
                    >
                        <span><?= $escaper->escapeHtml(__('Reset My Password')) ?></span>
                    </button>
                </div>
                <div class="secondary">
                    <a
                        class="underline text-primary-800 text-sm lowercase underline-offset-2 action back"
                        href="<?= $escaper->escapeUrl($block->getLoginUrl()) ?>"
                    >
                        <span><?= $escaper->escapeHtml(__('Go back')) ?></span>
                    </a>
                </div>
            </div>
        </form>
        <script>
            function initPasswordForm() {
                return {
                    errors: 0,
                    hasCaptchaToken: 0,
                    submitForm() {
                        // Do not rename $form, the variable is expected to be declared in the recaptcha output
                        const $form = document.querySelector('#<?= $escaper->escapeJs($formId) ?>');
                        <?= $recaptcha ? $recaptcha->getValidationJsHtml(ReCaptcha::RECAPTCHA_FORM_ID_CUSTOMER_FORGOT_PASSWORD) : '' ?>

                        if (this.errors === 0) {
                            $form.submit();
                        }
                    }
                }
            }
        </script>
    </div>
</div>
