<?php

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Life\ShowroomBase\ViewModel\ActorResolver;
use Magento\Framework\Escaper;
use Life\ShowroomQuoteHyva\ViewModel\QuoteCustomer;
use Magento\Framework\View\Element\Template;

/** @var Template $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */
/** @var ActorResolver $actorResolverViewModel */
/** @var QuoteCustomer $quoteCustomerViewModel */

$actorResolverViewModel = $viewModels->require(ActorResolver::class);
$quoteCustomerViewModel = $viewModels->require(QuoteCustomer::class);

?>
<!-- display subtotal excluding tax -->
<template x-if="segment.code === 'grand_total' && checkoutConfig.includeTaxInGrandTotal">
    <div>
        <div class="flex py-2 pt-6 my-2 mx-auto text-xl md:grid md:grid-cols-2 md:w-full border-t border-container">
            <div class="w-7/12 font-semibold text-left md:w-auto"
                 x-html="`${segment.title} ${includingTaxMessage}` "
            ></div>
            <div class="w-5/12 text-right font-semibold text-primary md:w-auto"
                 x-text="hyva.formatPrice(segment.value)"
            ></div>
        </div>

        <div class="flex py-2 my-2 mx-auto mb-12 text-md text-primary font-semibold md:grid md:grid-cols-2 md:w-full">
            <?php if ($actorResolverViewModel->getEmployeeResolver()->isEmployee()): ?>
                <button class="w-7/12 font-semibold text-left md:w-auto underline"
                     x-html="`${segment.title} ${excludingTaxMessage}` "
                     @click.prevent="() => document.getElementById('order-discount-dialog').showModal()"
                ></button>
            <?php else: ?>
                <div class="w-7/12 font-semibold text-left md:w-auto"
                     x-html="`${segment.title} ${excludingTaxMessage}` "
                ></div>
            <?php endif; ?>
            <div class="w-5/12 text-right md:w-auto"
                 x-text="hyva.formatPrice(totalsData.grand_total)"
            ></div>
        </div>
    </div>
</template>

<!-- display subtotal including tax -->
<template x-if="segment.code === 'grand_total' && !checkoutConfig.includeTaxInGrandTotal">
    <div class="flex justify-between gap-2 border-t border-secondary-900/50 py-2 leading-8">
        <?php if ($actorResolverViewModel->getEmployeeResolver()->isEmployee()): ?>
            <button class="w-7/12 font-semibold text-left md:w-auto underline"
                 x-html="`${segment.title}`"
                 @click.prevent="() => document.getElementById('order-discount-dialog').showModal()"
            ></button>
        <?php else: ?>
            <div class="w-7/12 font-semibold text-left md:w-auto"
                 x-html="`${segment.title}`"
            ></div>
        <?php endif; ?>
        <div class="w-5/12 text-right text-primary font-semibold md:w-auto"
             x-text="hyva.formatPrice(segment.value)"
        ></div>
    </div>
</template>


<?php if ($actorResolverViewModel->getEmployeeResolver()->isEmployee()): ?>
    <?php $quote = $quoteCustomerViewModel->getQuote(); ?>
    <dialog
        id="order-discount-dialog"
        class="m-auto w-[26rem] max-w-full"
        x-data="grandTotalDiscount()"
        x-init="initialize()"
    >
        <fieldset class="space-y-6">
            <legend class="leading-loose text-base mb-0">
                <span class="block font-semibold"><?= $escaper->escapeHtml(__('Edit price')) ?></span>
                <span class="block opacity-60"><?= $escaper->escapeHtml(__('Grand total price')) ?></span>
            </legend>
            <label class="grid gap-1 mb-0">
                <span class="label"><?= $escaper->escapeHtml(__('Price')) ?></span>
                <input
                    type="number"
                    step="0.01"
                    class="no-spin-buttons"
                    x-model="grandTotal"
                    @input="updatePercent"
                />
            </label>
            <label class="grid gap-1 mb-0">
                <span class="label"><?= $escaper->escapeHtml(__('Percentage')) ?></span>
                <div class="flex">
                    <input
                        type="number"
                        step="0.01"
                        class="no-spin-buttons w-full !pr-8 !rounded-r-none"
                        x-model="percent"
                        @input="updatePrice"
                    />
                    <span
                        aria-hidden="true"
                        class="inline-flex items-center justify-center h-full aspect-square  bg-secondary rounded-r-md text-sm"
                    >%</span>
                </div>
            </label>
        </fieldset>
        <div class="flex mt-6 gap-4">
            <button
                class="btn btn-secondary"
                @click.prevent="() => document.getElementById('order-discount-dialog').close()"
            >
                <?= $escaper->escapeHtml(__('Cancel')) ?>
            </button>

            <button
                class="btn btn-primary"
                @click.prevent="applyDiscount(event)"
            >
                <?= $escaper->escapeHtml(__('Apply discount')) ?>
            </button>
        </div>
    </dialog>
    <script>
        function grandTotalDiscount() {
            return {
                grandTotal: <?= $quote->getGrandTotal() ?>,
                percent: 100,
                originalGrandTotal: <?= $quote->getExtensionAttributes()?->getOriginalGrandTotal() ?: $quote->getGrandTotal() ?>,
                initialize: function() {
                    this.grandTotal = this.grandTotal.toFixed(2);
                    this.originalGrandTotal = this.originalGrandTotal.toFixed(2);
                    this.updatePercent();
                },
                updatePercent() {
                    this.percent = ((1 - this.grandTotal / this.originalGrandTotal) * 100).toFixed(2);
                },
                updatePrice() {
                    this.grandTotal = ((1 - this.percent / 100) * this.originalGrandTotal).toFixed(2);
                },
                async applyDiscount(event) {
                    const button = event.target;
                    button.disabled = true;
                    const token = JSON.parse(localStorage.getItem('mage-cache-storage'))?.customer?.signin_token;
                    const putUrl = `<?= $block->getBaseUrl() ?>rest/V1/showroom_carts/mine`;
                    const quoteData = {
                        "quote": {
                            "extension_attributes": {
                                "custom_grand_total": this.grandTotal
                            }
                        }
                    };
                    fetch(putUrl, {
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': `Bearer ${token}`
                        },
                        credentials: 'include',
                        method: 'PUT',
                        body: JSON.stringify(quoteData)
                    })
                    .then(response => response.json())
                    .then(() => {
                        if (this.$refs.cartForm) {
                            document.getElementById('order-discount-dialog').close();
                            this.$refs.cartForm.requestSubmit();
                        }
                    })
                    .catch(error => {
                        this.responseMessage.content = '<?= $escaper->escapeHtml(__('Request failed')) ?>';
                        this.responseMessage.status = 'error';
                        console.log(error);
                    }).finally(() => {
                        button.disabled = false;
                    });
                }
            }
        }
    </script>
<?php endif; ?>
