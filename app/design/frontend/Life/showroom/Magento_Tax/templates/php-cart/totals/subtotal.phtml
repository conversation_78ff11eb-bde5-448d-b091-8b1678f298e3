<?php
/**
 * The AlpineJS scope of this file is the method `initCartTotals()` in Magento_Checkout::php-cart/totals.phtml
 */
?>
<!-- display subtotal including and excluding tax -->
<template x-if="segment.code === 'subtotal' && 'both' === checkoutConfig.reviewTotalsDisplayMode">
    <div>
        <div class="flex justify-between gap-2 border-b border-secondary-900/50 py-2 leading-8">
            <div
                class="w-7/12 text-left md:w-auto font-semibold"
                x-html="`${segment.title} (${excludingTaxMessage})` "
            ></div>
            <div class="w-5/12 text-right text-primary md:w-auto font-semibold"
                 x-text="hyva.formatPrice(totalsData.subtotal)"
            ></div>
        </div>

        <div class="flex justify-between gap-2 border-b border-secondary-900/50 py-2 leading-8">
            <div class="w-7/12 text-left md:w-auto font-semibold"
                 x-html="`${segment.title} (${includingTaxMessage})` "
            ></div>
            <div class="w-5/12 text-right md:w-auto font-semibold text-primary"
                 x-text="hyva.formatPrice(totalsData.subtotal_incl_tax)"
            ></div>
        </div>
    </div>
</template>

<!-- display subtotal excluding tax -->
<template x-if="segment.code === 'subtotal' && 'excluding' === checkoutConfig.reviewTotalsDisplayMode">
    <div class="flex justify-between gap-2 border-b border-secondary-900/50 py-2 leading-8">
        <div
            class="w-7/12 text-left md:w-auto font-semibold"
            x-html="`${segment.title}` "
        ></div>
        <div class="w-5/12 text-right md:w-auto font-semibold text-primary"
             x-text="hyva.formatPrice(totalsData.subtotal)"
        ></div>
    </div>
</template>

<!-- display subtotal including tax -->
<template x-if="segment.code === 'subtotal' && 'including' === checkoutConfig.reviewTotalsDisplayMode">
    <div class="flex justify-between gap-2 border-b border-secondary-900/50 py-2 leading-8">
        <div
            class="w-7/12 text-left md:w-auto font-semibold"
            x-html="`${segment.title}`"
        ></div>
        <div class="w-5/12 text-right text-primary md:w-auto font-semibold"
             x-text="hyva.formatPrice(totalsData.subtotal_incl_tax)"
        ></div>
    </div>
</template>
