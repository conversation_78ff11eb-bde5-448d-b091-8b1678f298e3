<?php

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\ProductListItem;
use Hyva\Theme\ViewModel\ProductStockItem;
use Hyva\Theme\ViewModel\SvgIcons;
use Life\ProductsConnector\ViewModel\ProductPrice;
use Life\ShowroomBase\ViewModel\ActorResolver;
use Life\ShowroomCheckoutHyva\ViewModel\CartItemPrice;
use Life\ShowroomCheckoutHyva\ViewModel\PriceFormatter;
use Life\ShowroomDeliveryDate\ViewModel\DeliveryDate;
use Life\ShowroomProduct\ViewModel\ProductSubtitle;
use Life\ShowroomProductSwitcher\Model\AlternativeProvider;
use Life\ShowroomProductSwitcher\ViewModel\ProductAlternativeProvider;
use Magento\Checkout\Block\Cart\Item\Renderer;
use Magento\Framework\Escaper;
use Magento\Framework\Pricing\Helper\Data as PricingHelper;

/** @var Renderer $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */
/** @var ProductSubtitle $productSubtitleViewModel */
/** @var SvgIcons $svgIcons */
/** @var PriceFormatter $priceFormatterViewModel */

$svgIcons = $viewModels->require(SvgIcons::class);
$priceFormatterViewModel = $viewModels->require(PriceFormatter::class);
$cartItemPriceViewModel = $viewModels->require(CartItemPrice::class);
$productSubtitleViewModel = $viewModels->require(ProductSubtitle::class);
$actorResolverViewModel = $viewModels->require(ActorResolver::class);

$item = $block->getItem();
$childItem = $item;
$childItems = $item->getChildren();
if (count($childItems)) {
    $childItem = current($childItems);
}

if ($item->getProductId() && $item->getData('product')) {
    $item->setData('product', null);
}
$product = $item->getProduct();
$isVisibleProduct = $product->isVisibleInSiteVisibility();

/** @var ProductStockItem $stockItemViewModel */
$stockItemViewModel = $viewModels->require(ProductStockItem::class);
$step = (int)($stockItemViewModel->getQtyIncrements($product) ?: 1);

/** @var ProductListItem $productListItemViewModel */
$productListItemViewModel = $viewModels->require(ProductListItem::class);

/** @var ProductPrice $productPriceViewModel */
$productPriceViewModel = $viewModels->require(ProductPrice::class);

/** @var DeliveryDate $deliveryDateViewModel */
$deliveryDateViewModel = $viewModels->require(DeliveryDate::class);

/** @var ProductAlternativeProvider $productAlternativeViewModel */
$productAlternativeViewModel = $viewModels->require(ProductAlternativeProvider::class);

$discountAmount = $cartItemPriceViewModel->getDiscountAmount($item);

/** @var PricingHelper $pricingHelper */
$pricingHelper = $this->helper(PricingHelper::class);
?>

<div class="flex flex-col relative bg-white rounded-xl border border-secondary-900/50 gap-1.5 border-l-[5px] border-l-primary md:flex-row md:items-center">
    <div class="flex flex-col grow md:flex-row">
        <a
            href="<?= $escaper->escapeUrl($block->getProductUrl()) ?>"
            tabindex="-1"
            class="product-item-photo flex py-4 items-center justify-center w-full shrink-0 md:w-[10rem] md:py-0"
        >
            <?= $block->getImage($block->getProductForThumbnail(), 'cart_page_product_thumbnail') /** @phpstan-ignore-line */
                ->setTemplate('Magento_Catalog::product/image.phtml')
                ->toHtml() ?>
        </a>
        <div class="grow border-t border-secondary-900/50 p-4 md:border-t-0 md:border-l">

            <?php // product name ?>
            <span class="break-all">
                <?php if ($block->hasProductUrl()): ?>
                    <a href="<?= $escaper->escapeUrl($block->getProductUrl()) ?>">
                        <?= $escaper->escapeHtml($block->getProductName()) ?>
                    </a>
                <?php else: ?>
                    <?= $escaper->escapeHtml($block->getProductName()) ?>
                <?php endif; ?>
            </span>

            <?php // attributes ?>
            <ul class="flex flex-wrap gap-2 mt-1.5 text-sm text-primary/50">
                <?= $productSubtitleViewModel->getSubtitle($product) ?>
            </ul>

            <?php // color alternatives ?>
            <?php $alternatives = $productAlternativeViewModel->findAlternatives($product) ?>
            <?php if ($alternatives): ?>
                <?php $defaultStyles = 'width: 25px; height: 25px; border: 1px solid #ccc; display: inline-block;'; ?>
                <div class="mt-1.5 text-sm">
                    <?php foreach ($alternatives as $alternative): ?>
                        <a title="<?= $alternative[AlternativeProvider::ALTERNATIVE_KEY_PRODUCT]->getName() ?>"
                           href="<?= $alternative[ProductAlternativeProvider::KEY_SWITCH_ALTERNATIVE_ACTION] ?>">
                            <div class="color-square"
                                 style="<?= $defaultStyles . ' ' . $alternative[ProductAlternativeProvider::KEY_COLOR_STYLE] ?>"
                            ></div>
                        </a>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>

            <?php // delivery time ?>
            <p class="mt-1.5 text-sm">
                <?= $escaper->escapeHtml(__('Expected delivery date')) ?>:
                <span data-sku-delivery-date="<?= $product->getSku() ?>" class="text-red-500 font-bold">
                    <?=
                    $product->getData('shipping_date') ?
                        $deliveryDateViewModel->getLocaleDate($product->getData('shipping_date')) :
                        $escaper->escapeHtml(__('No delivery date available'))
                    ?>
                </span>
            </p>

            <?php if ($options = $block->getOptionList()): ?>
                <dl class="clearfix w-full mt-2 text-sm break-all item-options text-black/60">
                    <?php foreach ($options as $option): ?>
                        <?php $formatedOptionValue = $block->getFormatedOptionValue($option) ?>
                        <dt class="float-left mb-1 mr-2 text-xs clear-left">
                            <?= $escaper->escapeHtml($option['label']) ?>:
                        </dt>
                        <dd class="float-left text-xs font-bold">
                            <?php if (isset($formatedOptionValue['full_view'])): ?>
                                    <?= $escaper->escapeHtml($formatedOptionValue['full_view']) ?>
                                <?php else: ?>
                                    <?= $escaper->escapeHtml($formatedOptionValue['value'], ['span', 'a']) ?>
                            <?php endif; ?>
                        </dd>
                    <?php endforeach; ?>
                </dl>
            <?php endif;?>

            <?php $addInfoBlock = $block->getProductAdditionalInformationBlock(); ?>
            <?php if ($addInfoBlock): ?>
                <?= $addInfoBlock->setItem($item)->toHtml() ?>
            <?php endif;?>

            <?php if ($messages = $block->getMessages()): ?>
                <div class="messages">
                    <?php foreach ($messages as $message): ?>
                        <div class="cart item message <?= $escaper->escapeHtmlAttr($message['type']) ?>">
                            <div><?= $escaper->escapeHtml($message['text']) ?></div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif;?>


            <div
                class="flex flex-wrap items-end gap-2 mt-2"
                x-data="{
                    rawPrice: <?= $item->getRowTotalInclTax() ?>,
                    price: null,
                    formattedPrice: null
                }"
                x-init="
                    formattedPrice = formatPrice(rawPrice, false);
                    price = rawPrice;
                "
            >

                <?php // pricing ?>
                <div class="flex flex-col gap-0.5 items-start md:flex-row md:items-center md:gap-2">
                    <?php if ($discountAmount > 0): ?>
                        <span class="inline-flex items-center px-1 py-px text-xs bg-tertiary-300 rounded-xl text-tertiary min-w-fit">
                            -<?= /* @noEscape */ $priceFormatterViewModel->currency($discountAmount * $item->getQty(), true, false) ?>
                        </span>
                    <?php endif; ?>

                    <?php if ($discountAmount > 0): ?>
                        <span
                            class="text-sm line-through old-price text-primary-800"
                            x-html="formatPrice(<?= $item->getQty() * $productPriceViewModel->getNormalPrice($childItem->getProduct()) ?>, false)"
                        ></span>
                    <?php endif; ?>

                    <?php if ($actorResolverViewModel->getEmployeeResolver()->isEmployee()): ?>
                        <button
                            @click.prevent="toggleDiscountDialog('<?= $item->getId() ?>', true)"
                            class="text-sm font-bold price underline"
                            x-html="formattedPrice"
                        ></button>
                        <dialog
                            id="product-discount-dialog-<?= $item->getId() ?>"
                            class="m-auto w-[26rem] max-w-full"
                            x-data="cartItemDiscount<?= $item->getId() ?>()"
                            x-init="initialize()"
                        >
                            <fieldset class="space-y-6">
                                <legend class="leading-loose text-base mb-0">
                                    <span class="block font-semibold"><?= $escaper->escapeHtml(__('Edit price')) ?></span>
                                    <span class="block opacity-60"><?= $product->getName() ?></span>
                                </legend>
                                <label class="grid gap-1 mb-0">
                                    <span class="label"><?= $escaper->escapeHtml(__('Price')) ?></span>
                                    <input
                                        type="number"
                                        step="0.01"
                                        class="no-spin-buttons"
                                        x-model="finalPrice"
                                        @input="updatePercent"
                                    />
                                </label>
                                <label class="grid gap-1 mb-0">
                                    <span class="label"><?= $escaper->escapeHtml(__('Percentage')) ?></span>
                                    <div class="flex">
                                        <input
                                            type="number"
                                            step="0.01"
                                            class="no-spin-buttons w-full !pr-8 !rounded-r-none"
                                            x-model="percent"
                                            @input="updatePrice"
                                        />
                                        <span
                                            aria-hidden="true"
                                            class="inline-flex items-center justify-center h-full aspect-square  bg-secondary rounded-r-md text-sm"
                                        >%</span>
                                    </div>
                                </label>
                            </fieldset>
                            <div class="flex mt-6 gap-4">
                                <button
                                    class="btn btn-secondary"
                                    @click.prevent="toggleDiscountDialog('<?= $item->getId() ?>', false)"
                                >
                                    <?= $escaper->escapeHtml(__('Cancel')) ?>
                                </button>

                                <button
                                    class="btn btn-primary"
                                    @click.prevent="applyDiscount(event, '<?= $item->getId() ?>')"
                                >
                                    <?= $escaper->escapeHtml(__('Apply discount')) ?>
                                </button>
                            </div>
                        </dialog>
                        <script>
                            function cartItemDiscount<?= $item->getId() ?>() {
                                return {
                                    originalPrice: <?= $productPriceViewModel->getNormalPrice($childItem->getProduct()) ?>,
                                    percent: 100,
                                    finalPrice: <?= $item->getPriceInclTax() ?>,
                                    initialize: function() {
                                        this.finalPrice = this.finalPrice.toFixed(2);
                                        this.originalPrice = this.originalPrice.toFixed(2);
                                        this.updatePercent();
                                    },
                                    updatePercent() {
                                        this.percent = ((1 - this.finalPrice / this.originalPrice) * 100).toFixed(2);
                                    },
                                    updatePrice() {
                                        this.finalPrice = ((1 - this.percent / 100) * this.originalPrice).toFixed(2);
                                    },
                                    async applyDiscount(event, id) {
                                        const button = event.target;
                                        button.disabled = true;
                                        const token = JSON.parse(localStorage.getItem('mage-cache-storage'))?.customer?.signin_token;
                                        const putUrl = `<?= $block->getBaseUrl() ?>rest/V1/showroom_carts/mine/items/<?= $item->getId() ?>`;
                                        const cartItemData = {
                                            "cartItem": {
                                                "item_id": <?= $item->getId() ?>,
                                                "quote_id": <?= $item->getQuote()->getId() ?>,
                                                "qty": <?= $item->getQty() ?>,
                                                "extension_attributes": {
                                                    "custom_price": this.finalPrice
                                                }
                                            }
                                        };

                                        fetch(putUrl, {
                                            headers: {
                                                'Content-Type': 'application/json',
                                                'Authorization': `Bearer ${token}`
                                            },
                                            credentials: 'include',
                                            method: 'PUT',
                                            body: JSON.stringify(cartItemData)
                                        })
                                        .then(response => response.json())
                                        .then(data => {
                                            if (data.item_id) {
                                                if (this.$refs.cartForm) {
                                                    this.$refs.cartForm.requestSubmit();
                                                    this.toggleDiscountDialog(id, false);
                                                }
                                            } else {
                                                this.responseMessage.content = '<?= $escaper->escapeHtml(__('An error occurred')) ?>';
                                                this.responseMessage.status = 'error';
                                                console.log(data);
                                            }
                                        })
                                        .catch(error => {
                                            this.responseMessage.content = '<?= $escaper->escapeHtml(__('Request failed')) ?>';
                                            this.responseMessage.status = 'error';
                                            console.log(error);
                                        }).finally(() => {
                                            button.disabled = false;
                                        });
                                    }
                                }
                            }
                        </script>
                    <?php endif; ?>
                </div>

                <?php // qty input ?>
                <div
                    class="flex ml-auto col qty"
                    x-data="{qty: <?= $block->getQty() ?>}"
                >
                    <div class="!mt-0 field qty">
                        <div class="control qty">
                            <label for="cart-<?= $escaper->escapeHtmlAttr($item->getId()) ?>-qty" class="flex mb-0">
                                <span class="sr-only label"><?= $escaper->escapeHtml(__('Qty')) ?></span>
                                <button
                                    type="button"
                                    x-on:click="qty = Math.max(0, qty - <?= $step ?>);
                                        $refs.qtyInput.value = qty;
                                        $refs.cartForm.dispatchEvent(new Event('change-qty', { qty: qty }))"
                                    class="relative flex items-center justify-center w-8 h-8 border border-secondary-900/50 -right-px rounded-l-md"
                                >
                                    <span class="sr-only">
                                        <?= $escaper->escapeHtml(__('Decrease by 1')) /** @phpstan-ignore-line */ ?>
                                    </span>
                                    <?= $svgIcons->minusHtml("h-4 w-4") ?>
                                </button>
                                <input
                                    id="cart-<?= $escaper->escapeHtmlAttr($item->getId()) ?>-qty"
                                    name="cart[<?= $escaper->escapeHtmlAttr($item->getId()) ?>][qty]"
                                    value="<?= $escaper->escapeHtmlAttr($block->getQty()) ?>"
                                    type="number"
                                    size="4"
                                    step="any"
                                    class="no-spin-buttons w-8 h-8 !min-h-full !p-0 !rounded-none qty text-center"
                                    required="required"
                                    min="0"
                                    data-role="cart-item-qty"
                                    x-ref="qtyInput"
                                    x-model.number="qty"
                                    x-on:change="$refs.cartForm.dispatchEvent(new Event('change-qty', { qty: qty }))"
                                />
                                <button
                                    type="button"
                                    x-on:click="qty = Math.max(<?= $step ?>, qty + <?= $step ?>);
                                        $refs.qtyInput.value = qty;
                                        $refs.cartForm.dispatchEvent(new Event('change-qty', { qty: qty }));"
                                    class="relative flex items-center justify-center w-8 h-8 border border-secondary-900/50 -left-px rounded-r-md"
                                >
                                    <span class="sr-only">
                                        <?= $escaper->escapeHtml(__('Increase by 1')) /** @phpstan-ignore-line */ ?>
                                    </span>
                                    <?= $svgIcons->plusHtml("h-4 w-4") ?>
                                </button>
                            </label>
                        </div>
                    </div>
                </div>

                <div class="item-actions flex items-end justify-end">
                    <?= /* @noEscape */ $block->getActions($item) ?>
                </div>
            </div>

        </div>
    </div>
</div>

