<?php

declare(strict_types=1);

use Hyva\Theme\ViewModel\SvgIcons;

/** @var \Magento\Checkout\Block\Cart\Item\Renderer\Actions\Remove $block */
/** @var \Hyva\Theme\Model\ViewModelRegistry $viewModels */
/** @var \Magento\Framework\Escaper $escaper */

/** @var SvgIcons $svgIcons */
$svgIcons = $viewModels->require(SvgIcons::class);
$item = $block->getItem();

if ($item->getProductType() === 'configurable') {
    $productOptions = $item->getProduct()->getTypeInstance(true)->getOrderOptions($item->getProduct());
    $productName = $productOptions["simple_name"];
} else {
    $productName = $item->getName();
}
?>
<button
    class="a11y-pointer inline-flex items-center justify-center w-8 aspect-square bg-secondary rounded-full relative after:rounded-full"
    x-data="{}"
    @click.prevent='hyva.postForm(<?= /* @noEscape */ $block->getDeletePostJson() ?>)'
    type="button"
>
    <?= $svgIcons->xHtml('w-5 h-5', 24, 24, ['aria-hidden' => 'true']) ?>
    <span class="sr-only"><?= $escaper->escapeHtmlAttr(__('Remove %1', $productName)) ?></span>
</button>
