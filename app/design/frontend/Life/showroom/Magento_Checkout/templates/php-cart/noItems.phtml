<?php

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\SvgIcons;
use Magento\Framework\Escaper;

/** @var $block \Magento\Checkout\Block\Cart */
/** @var SvgIcons $svgIcons */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */
$svgIcons = $viewModels->require(SvgIcons::class);

?>
<div class="container">
    <header class="cart-empty bg-primary text-white rounded-xl px-6 py-4">
        <div class="flex flex-wrap items-center gap-x-7 gap-y-4 mb-6 pb-2 border-b border-white/20 text-white text-xs">
            <a href="<?= $escaper->escapeUrl($block->getBaseUrl()) ?>" class="inline-flex gap-2 items-center">
                <?= $svgIcons->arrowLeftHtml("", 20, 20, ['aria-hidden' => 'true']) ?>
                <span><?= $escaper->escapeHtml(__('Back')) ?></span>
            </a>
            <span class="text-primary-100"><?= $block->escapeHtml(__('Products')) ?></span>
        </div>
        <div class="max-w-lg mx-auto py-14">
            <h1 class="heading-5 mb-2">
                <?= $block->escapeHtml(__('Cart')) ?>
            </h1>
            <?= $block->getChildHtml('checkout_cart_empty_widget') ?>
            <p class="text-primary-100 font-semibold"><?= $block->escapeHtml(__('You have no items in your shopping cart.')) ?></p>
            <p class="text-primary-100 font-semibold"><?= $block->escapeHtml(
                __(
                    'Click <a href="%1" class="underline">here</a> to continue shopping.',
                    $escaper->escapeUrl($block->getContinueShoppingUrl())
                ),
                ['a']
            ) ?>
            </p>
        </div>
    </header>
    <?= $block->getChildHtml('shopping.cart.table.after') ?>
</div>
<script type="text/x-magento-init">
{
    "*": {
        "Magento_Checkout/js/empty-cart": {}
    }
}
</script>
