<?php

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\SvgIcons;
use Life\ShowroomCustomer\ViewModel\CustomerDataProvider;
use Life\ShowroomQuoteHyva\ViewModel\QuoteCustomer;
use Life\ThemeConfigurations\ViewModel\WebsiteCountry;
use Magento\Checkout\Block\Cart;
use Magento\Framework\Escaper;

/** @var CustomerDataProvider $customerData */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */
/** @var SvgIcons $svgIcons */
/** @var Cart $block */

$svgIcons = $viewModels->require(SvgIcons::class);
$quoteCustomer = $viewModels->require(QuoteCustomer::class);
$websiteCountry = $viewModels->require(WebsiteCountry::class);

$quoteRealCustomer = $quoteCustomer->getQuoteRealCustomer();

$quoteId = $block->getQuote()->getId();

?>
<div x-data="initCustomerSelect()" x-init="init()">
    <details open>
        <summary>
            <span><?= $escaper->escapeHtml(__('Customer')) ?></span>
        </summary>
        <div>
            <?php if ($quoteRealCustomer): ?>
                <div class="grid gap-4 bg-white rounded-xl border border-secondary-900/50 p-4 text-sm">
                    <?php
                        $firstAddress = $quoteRealCustomer->getAddresses()[0] ?? null;
                        $secondAddress = $quoteRealCustomer->getAddresses()[1] ?? null;
                    ?>
                    <div class="grid gap-2">
                        <span class="font-bold leading-normal"><?= $escaper->escapeHtml(__('E-mail')) ?></span>
                        <span><?= $quoteRealCustomer->getEmail() ?></span>
                    </div>

                    <div class="flex flex-wrap gap-x-32 gap-y-6">

                        <?php // billing address ?>
                        <?php if ($firstAddress): ?>
                        <div class="grid gap-2">
                            <span class="font-bold leading-normal">
                                <?= $escaper->escapeHtml(__('Billing address')) ?>
                            </span>
                            <address class="grid">
                                <span><?= $firstAddress->getFirstname() . ' ' . $firstAddress->getLastname() ?></span>
                                <span>
                                    <?= $firstAddress->getStreet()[0] ?? '' ?>
                                    <?= isset($firstAddress->getStreet()[1]) ? ' ' . $firstAddress->getStreet()[1] : '' ?>
                                    <?= isset($firstAddress->getStreet()[2]) ? ' ' . $firstAddress->getStreet()[2] : '' ?>
                                </span>
                                <span><?= $firstAddress->getPostcode() ?></span>
                                <span><?= $firstAddress->getCity() ?></span>
                                <span><?= $websiteCountry->getCountryName($firstAddress->getCountryId()) ?></span>
                                <span><?= $firstAddress->getTelephone() ?></span>
                            </address>
                            <?php if (!$secondAddress): ?>
                            <span class="inline-flex gap-4 mt-2 opacity-60">
                                <?= $svgIcons->checkHtml("", 16, 16, ['aria-hidden' => 'true']) ?>
                                <?= $escaper->escapeHtml(__('Billing address is the same as shipping address')) ?>
                            </span>
                            <?php endif; ?>
                        </div>
                        <?php else: ?>
                        <div><?= $escaper->escapeHtml(__('No address provided')) ?></div>
                        <?php endif; ?>

                        <?php // shipping address ?>
                        <?php if ($secondAddress): ?>
                        <div class="grid gap-2">
                            <span class="font-bold leading-normal"><?= $escaper->escapeHtml(__('Shipping address')) ?></span>
                            <address class="grid">
                                <span><?= $secondAddress->getFirstname() . ' ' . $secondAddress->getLastname() ?></span>
                                <span>
                                    <?= $secondAddress->getStreet()[0] ?? '' ?>
                                    <?= isset($secondAddress->getStreet()[1]) ? ' ' . $secondAddress->getStreet()[1] : '' ?>
                                    <?= isset($secondAddress->getStreet()[2]) ? ' ' . $secondAddress->getStreet()[2] : '' ?>
                                </span>
                                <span><?= $secondAddress->getPostcode() ?></span>
                                <span><?= $secondAddress->getCity() ?></span>
                                <span><?= $websiteCountry->getCountryName($secondAddress->getCountryId()) ?></span>
                                <span><?= $secondAddress->getTelephone() ?></span>
                            </address>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
                <div class="flex flex-wrap gap-4 mt-4">
                    <a
                        href="<?= $block->getUrl(CustomerDataProvider::CUSTOMER_EDIT_ROUTE, ['customer' => $quoteRealCustomer->getId()]) ?>"
                        class="btn btn-secondary-outline"
                    >
                        <?= $svgIcons->pencilHtml("", 20, 20, ['aria-hidden' => 'true']) ?>
                        <?= $escaper->escapeHtml(__('Edit details')) ?>
                    </a>
                    <button
                        @click.prevent="toggleDialog('assign', true)"
                        class="btn btn-secondary-outline"
                    >
                        <?= $svgIcons->arrowsCounterClockwiseHtml("", 20, 20, ['aria-hidden' => 'true']) ?>
                        <?= $escaper->escapeHtml(__('Change customer')) ?>
                    </button>
                </div>
            <?php else: ?>
                <div class="customer-selection">
                    <p class="mb-6">
                        <?= $escaper->escapeHtml(__('No customer selected yet')) ?>
                    </p>
                    <button
                        id="assign-customer"
                        @click.prevent="toggleDialog('assign', true)"
                        type="button"
                        class="flex w-full h-24 p-4 gap-4 items-center justify-center border border-error-700 rounded-xl bg-white"
                    >
                        <?= $escaper->escapeHtml(__('Add customer')) ?>
                        <?= $svgIcons->plusHtml("", 24, 24, ['aria-hidden' => 'true']) ?>
                    </button>
                </div>
            <?php endif; ?>
        </div>
    </details>

    <div
        role="dialog"
        aria-labelledby="add-customer-dialog-header"
        class="fixed inset-0 z-dialog isolate opacity-0 pointer-events-none"
        :class="assignDialog && '!opacity-100 !pointer-events-auto'"
        :open="assignDialog"
    >
        <div
            class="absolute inset-x-0 bottom-0 h-[80vh] bg-secondary-300 rounded-t-xl overflow-auto transition-transform ease-out duration-300"
            :class="assignDialog ? 'translate-y-0' : 'translate-y-full'"
        >
            <div class="container px-6 py-10">
                <header class="flex gap-4 mb-6">
                    <h5 id="add-customer-dialog-header"><?= $escaper->escapeHtml(__('Add customer to order')) ?></h5>
                    <button
                        @click.prevent="toggleDialog('assign', false)"
                        class="a11y-pointer ml-auto"
                    >
                        <?= $svgIcons->closeHtml("", 24, 24, ['aria-hidden' => 'true']) ?>
                        <span class="sr-only"><?= $escaper->escapeHtml(__('Close')) ?></span>
                    </button>
                </header>

                <!-- TODO: add the app/code/Life/ShowroomCustomerHyva/view/frontend/templates/customer/search.phtml template here -->
                <script>
                    document.addEventListener('alpine:init', () => {
                        Alpine.store('customerSearch', {
                            searched: false,
                            results: []
                        });
                    });

                    function initCustomerSearchForm() {
                        return {
                            searchValue: '',
                            searching: false,
                            async search() {
                                if (this.searchValue.trim().length < 3) return;
                                this.searching = true;
                                const encodedSearchValue = encodeURIComponent(this.searchValue.trim());
                                const url = `<?= $block->getBaseUrl(); ?>rest/V1/showroom_customer/selection-list/${encodedSearchValue}`;
                                const token = JSON.parse(localStorage.getItem('mage-cache-storage'))?.customer?.signin_token;
                                try {
                                    const response = await fetch(url, {
                                        method: 'GET',
                                        headers: { 'Authorization': `Bearer ${token}` },
                                    });
                                    if (!response.ok) throw new Error('Network response was not ok');
                                    const data = await response.json();
                                    Alpine.store('customerSearch').results = this.processResults(data);
                                } catch (error) {
                                    console.error('Error fetching data:', error);
                                    Alpine.store('customerSearch').results = [];
                                }
                                this.searching = false;
                                Alpine.store('customerSearch').searched = true;
                            },
                            processResults(data) {
                                return data.map(contact => {
                                    const highlight = text => {
                                        if (!text) return;
                                        const regex = new RegExp(`(${this.searchValue})`, 'gi');
                                        return text.replace(regex, '<span class="font-bold">$1</span>');
                                    };
                                    return {
                                        ...contact,
                                        id: contact.entity_id,
                                        firstname: highlight(contact.firstname),
                                        lastname: highlight(contact.lastname),
                                        email: highlight(contact.email),
                                        telephone: contact.telephone,
                                        postcode: highlight(contact.postcode),
                                        city: contact.city
                                    };
                                });
                            }
                        }
                    }
                </script>

                <search x-data="initCustomerSearchForm()">
                    <form
                        @submit.prevent="search"
                        @input="searchValue = $event.target.value"
                        class="relative"
                    >
                        <label>
                            <span class="sr-only">
                                <?= $escaper->escapeHtml(__('Postcode, phone number, email')) ?>
                            </span>
                            <input
                                id="customer-search"
                                type="search"
                                inputmode="search"
                                required
                                minlength="3"
                                placeholder="<?= $escaper->escapeHtml(__('Search by postcode, phone number, email')) ?>"
                                class="w-full !min-h-[48px] !rounded-[18.75rem] !pr-16"
                                :disabled="searching"
                            >
                        </label>
                        <button
                            role="button"
                            class="inline-flex pr-6 pl-4 items-center justify-center absolute right-0 top-1/2 -translate-y-1/2 h-12 text-black"
                            :disabled="searching"
                        >
                            <?= $svgIcons->searchHtml("", 24, 24, ['aria-hidden' => 'true']) ?>
                            <span class="sr-only">
                                <?= $escaper->escapeHtml(__('Search')) ?>
                            </span>
                        </button>
                    </form>
                </search>

                <?php // search results ?>
                <div
                    aria-live="polite"
                    class="mt-4"
                    x-show="$store.customerSearch.searched"
                >
                    <?php // no results ?>
                    <p x-show="$store.customerSearch.results.length === 0">
                        <?= $escaper->escapeHtml(__('No results found')) ?>.
                    </p>

                    <a
                        id="add-customer-button"
                        href="<?= $block->getBaseUrl() ?>showroom_customer/selection/create/"
                        class="flex w-full h-24 mt-6 p-4 gap-4 items-center justify-center border border-secondary-900/50 rounded-xl bg-white"
                        x-show="$store.customerSearch.searched"
                    >
                        <span><?= $escaper->escapeHtml(__('Create new customer')) ?></span>
                        <?= $svgIcons->plusHtml("", 24, 24, ['aria-hidden' => 'true']) ?>
                    </a>

                    <?php // results ?>
                    <output
                        x-show="$store.customerSearch.results.length > 0"
                        class="grid gap-2 mt-2"
                        for="customer-search"
                        name="customer-search-results"
                    >
                        <ul class="grid gap-2">
                            <template x-for="(result, index) in $store.customerSearch.results" :key="`${result.id}-${index}`">
                                <li class="relative bg-white rounded-xl p-4 border border-secondary-900/50">
                                    <button
                                        @click.prevent="assignCustomer(result.id)"
                                        class="text-left after:absolute after:inset-0"
                                        :disabled="assigning"
                                    >
                                        <h6
                                            class="text-base font-normal"
                                            x-html="
                                                `
                                                    ${result.firstname ? `${result.firstname} ` : ''}
                                                    ${result.lastname || ''}
                                                `
                                            "
                                        ></h6>
                                    </button>
                                    <ul class="inline-grid gap-2 text-sm md:grid-cols-3 md:gap-12">
                                        <li
                                            x-html="`
                                                ${result.postcode ? `${result.postcode}, ` : ''}
                                                ${result.city || ''}
                                            `"
                                        ></li>
                                        <li x-html="`${result.email || ''}`"></li>
                                        <li x-html="`${result.telephone || ''}`"></li>
                                    </ul>
                                </li>
                            </template>
                        </ul>
                    </output>
                </div>
            </div>
        </div>
        <div
            @click="toggleDialog('assign', false)"
            class="absolute inset-0 -z-10 bg-[#000]/20 backdrop-blur-sm transition-opacity ease-out duration-300"
            :class="{'opacity-0': !assignDialog, 'opacity-100': assignDialog}"
        ></div>
    </div>
</div>

<script>
    'use strict';
    function initCustomerSelect() {
        return {
            assignedCustomer: {
                id: <?= $quoteRealCustomer ? $quoteRealCustomer->getId() : 0 ?>,
            },
            assignDialog: false,
            assigning: false,
            init() {
                document.addEventListener('keydown', (event) => {
                    if (this.assignDialog && event.key === 'Escape') {
                        event.preventDefault();
                        event.stopPropagation();
                        this.toggleDialog('assign', false);
                    }
                });
            },
            toggleDialog(dialog, bool) {
                if (dialog === 'assign') {
                    this.assignDialog = bool;
                    this.$nextTick(() => {
                        if (this.assignDialog) {
                            const searchInput = document.getElementById('customer-search');
                            if (searchInput) {
                                searchInput.focus();
                            }
                        } else {
                            const assignCustomerButton = document.getElementById('assign-customer');
                            if (assignCustomerButton) {
                                assignCustomerButton.focus();
                            }
                        }
                    })
                } else {
                    return;
                }
            },
            assignCustomer(id) {
                if (!id) {
                    console.error('No customer id');
                    return;
                }
                const quoteId = '<?= $quoteId ?>';

                // TODO: url seem to return a 404 error
                const postUrl = `<?= $block->getBaseUrl(); ?>rest/V1/showroom_customer/customer/${id}/assign-to-quote/${quoteId}`;
                const token = JSON.parse(localStorage.getItem('mage-cache-storage'))?.customer?.signin_token;

                if (!token) {
                    console.error('No token');
                    return;
                }

                this.assigning = true;

                fetch(postUrl, {
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    method: 'GET',
                })
                .then(response => response.json())
                .then(data => {
                    if (data === true) {
                        window.location.reload();
                    }
                })
                .catch(error => {
                    console.error(error);
                }).finally(() => {
                    this.assigning = false;
                });
            }
        }
    }
</script>
