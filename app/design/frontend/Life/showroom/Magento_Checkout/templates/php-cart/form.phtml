<?php

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\Cart\ItemOutput;
use Hyva\Theme\ViewModel\Currency;
use Hyva\Theme\ViewModel\Modal;
use Life\HyvaCheckoutExtension\ViewModel\ShippingInfo;
use Magento\Checkout\Block\Cart\Grid;
use Magento\Checkout\ViewModel\Cart as CartViewModel;
use Magento\Framework\Escaper;

/** @var Grid $block */
/** @var ViewModelRegistry $viewModels */
/** @var Escaper $escaper */

/** @var CartViewModel $cartViewModel */
$cartViewModel = $viewModels->require(CartViewModel::class);

/** @var Modal $modalViewModel */
$modalViewModel = $viewModels->require(Modal::class);

/** @var ItemOutput $cartItemOutputViewModel */
$cartItemOutputViewModel = $viewModels->require(ItemOutput::class);

/** @var Currency $currencyViewModel */
$currencyViewModel = $viewModels->require(Currency::class);

/** @var ShippingInfo $shippingInfo */
$shippingInfo = $viewModels->require(ShippingInfo::class);
$freeLabel = $shippingInfo->getShippingFreeCostLabel();

$quoteId = $block->getQuote()->getId();

?>
<?php $mergedCells = ($cartItemOutputViewModel->isItemPriceDisplayBoth() ? 2 : 1); ?>
<?= $block->getChildHtml('form_before') ?>
<details
    open
    x-data="cartData()"
    x-init="getDeliveryTimes()"
>
    <summary>
        <span><?= $escaper->escapeHtml(__('Items in order')) ?></span>
    </summary>
    <div class="grid gap-4">
        <form
            action="<?= $escaper->escapeUrl($block->getUrl('checkout/cart/updatePost')) ?>"
            x-ref="cartForm"
            @submit.prevent="isLoading = true; hyva.postCart($event.target)"
            @change-qty.this="isLoading = true; hyva.postCart($event.target)"
            method="post"
            id="form-validate"
            class="float-left w-full form form-cart"
        >
            <template x-if="isLoading">
                <?= $block->fetchView((string) $block->getTemplateFile('Hyva_Theme::ui/loading.phtml')) ?>
            </template>
            <?= $block->getBlockHtml('formkey') ?>

            <div class="cart table-wrapper<?= $mergedCells == 2 ? ' detailed' : '' ?>">
                <?php if ($block->getPagerHtml()): ?>
                    <div class="cart-products-toolbar cart-products-toolbar-top toolbar">
                        <?= $block->getPagerHtml() ?>
                    </div>
                <?php endif ?>
                <div id="shopping-cart-table" class="cart items data grid gap-2">
                    <div class="table-caption sr-only">
                        <?= $escaper->escapeHtml(__('Shopping Cart Items')) ?>
                    </div>
                    <?php foreach ($block->getItems() as $item): ?>
                        <?= $block->getItemHtml($item) ?>
                    <?php endforeach ?>
                </div>
                <?php if ($block->getPagerHtml()): ?>
                    <div class="cart-products-toolbar cart-products-toolbar-bottom toolbar">
                        <?= $block->getPagerHtml() ?>
                    </div>
                <?php endif ?>
            </div>
        </form>

        <?php // total delivery time ?>
        <div class="text-center p-1 bg-primary-100 rounded-lg leading-8" aria-live="polite">
            <span><?= $escaper->escapeHtml(__('Expected delivery date')) ?>:&nbsp;</span>
            <span x-show="!finalDeliveryDate"><?= $escaper->escapeHtml(__('No delivery date available')) ?></span>
            <time
                x-cloak
                x-show="finalDeliveryDate"
                x-text="finalDeliveryDate"
            >
            </time>
        </div>
        <?= $block->getChildHtml('totals') ?>
        <?= $block->getChildHtml('checkout.cart.order.actions') ?>
        <?= $block->getChildHtml('shopping.cart.table.after') ?>
    </div>
</details>
<script>
    function cartData() {
        return {
            isLoading: false,
            finalDeliveryDate: false,
            async getDeliveryTimes() {
                const token = JSON.parse(localStorage.getItem('mage-cache-storage'))?.customer?.signin_token;
                const headers = new Headers();

                headers.append("Content-Type", "application/json");
                headers.append("Authorization", `Bearer ${token}`);

                <?php
                $productsForDelivery = [];
                foreach ($block->getItems() as $item) {
                    $productsForDelivery[] = [
                        'sku' => $item->getSku(),
                        'quantity' => (int)$item->getQty()
                    ];
                }
                $queryData = [
                    'quote_id' => $quoteId,
                    'products' => $productsForDelivery
                ];
                $productsForDelivery = json_encode($queryData);
                ?>

                <?php // TODO: body needs to be checked when real data is available ?>
                const body = JSON.stringify(
                    {
                        "queryData": '<?= $productsForDelivery ?>'
                    }
                );

                const requestOptions = {
                    method: "POST",
                    headers,
                    body
                };

                fetch("<?= $block->getBaseUrl(); ?>rest/V1/showroom/delivery_date/prepare", requestOptions)
                .then((response) => response.json())
                .then(([result]) => {
                    const currentDate = new Date();

                    <?php // set final delivery date ?>
                    this.finalDeliveryDate = result.max_delivery_date || false;

                    <?php // set delivery date on each product ?>                  
                    for (const [sku, date] of Object.entries(result.products)) {
                        const dateNode = document.querySelector(`[data-sku-delivery-date='${sku}']`);
                        if (!dateNode || !date) return;
                        dateNode.innerText = date;
                        if (new Date(date) > currentDate) {
                            dateNode.classList.remove('text-red-500');
                            dateNode.classList.add('tertiary');
                        } else if (new Date(date) === currentDate) {
                            dateNode.classList.remove('text-red-500');
                            dateNode.classList.add('primary');
                        }
                    }
                })
                .catch((error) => console.error(error));
            },
            toggleDiscountDialog(id, bool) {
                if (!id) {
                    console.error('No product id provided');
                }
                const dialog = document.getElementById(`product-discount-dialog-${id}`);
                if (bool) {
                    dialog.showModal();
                } else {
                    dialog.close();
                }
            }
        };
    }
</script>
<script defer="defer">
    function formatPrice(priceValue, showCurrency) {
        if (parseFloat(priceValue) === 0) {
            return '<?= $escaper->escapeHtml($freeLabel); ?>';
        }

        let formattedPrice = hyva.formatPrice(priceValue).replace(/\s/g, ''),
            currency = "<?= $escaper->escapeJs($currencyViewModel->getCurrentCurrencySymbol()) ?>";

        if (!showCurrency) {
            formattedPrice = formattedPrice.replace(currency, '');
        }

        return formattedPrice;
    }
</script>
