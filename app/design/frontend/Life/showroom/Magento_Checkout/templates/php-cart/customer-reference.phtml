<?php

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\SvgIcons;
use Magento\Framework\Escaper;
use Magento\Checkout\Block\Cart;

/** @var Escaper $escaper */
/** @var Cart $block */
/** @var SvgIcons $svgIcons */
/** @var ViewModelRegistry $viewModels */

$svgIcons = $viewModels->require(SvgIcons::class);

$quote = $block->getQuote();

?>
<details <?= $quote->getData('customer_reference') ? 'open' : '' ?>>
    <summary>
        <span><?= $escaper->escapeHtml(__('Customer Reference')) ?></span>
    </summary>
    <div x-data="initCustomerReference">
        <label class="block">
            <span class="sr-only">
                <?= $escaper->escapeHtml(__('Customer Reference')) ?>
            </span>
            <textarea
                id="customer_reference"
                name="customer_reference"
                class="block w-full"
                @focus="closeMessage"
                rows="3"
                placeholder="<?= $escaper->escapeHtml(__('Place the customer reference here')) ?>"
            ><?= $quote->getData('customer_reference') ?></textarea>
        </label>
        <div class="flex flex-wrap items-center gap-x-4 gap-y-2 mt-4">
            <button
                @click.prevent="save"
                type="button"
                class="btn btn-secondary gap-2 border border-secondary-900/50 bg-transparent"
                :disabled="saving"
            >
                <?= $escaper->escapeHtml(__('Save comments')) ?>
                <?= $svgIcons->floppyDiskHtml("", 24, 24, ['aria-hidden' => 'true']) ?>
            </button>
            <p
                aria-live="polite"
                x-show="response.status"
                class="inline-flex items-center gap-x-2 text-sm font-semibold"
                :class="response.status === 'success' ? 'text-primary' : 'text-error-700'"
            >
                <span x-show="response.status === 'success'">
                    <?= $svgIcons->checkHtml("", 32, 32, ['aria-hidden' => 'true']) ?>
                </span>
                <span x-show="response.status !== 'success'">
                    <?= $svgIcons->closeHtml("", 32, 32, ['aria-hidden' => 'true']) ?>
                </span>
                <span x-html="response.message"></span>
            </p>
        </div>
    </div>
</details>
<script>
    function initCustomerReference() {
        return {
            saving: false,
            response: {
                status: false,
                message: ''
            },
            save() {
                this.saving = true;
                const postUrl = `${BASE_URL}rest/V1/carts/mine`;
                const token = JSON.parse(localStorage.getItem('mage-cache-storage'))?.customer?.signin_token;
                if (!token) {
                    this.response.message = '<?= $escaper->escapeHtml(__('Something went wrong')) ?>';
                    this.response.status = 'error';
                    console.error('Customer token not found');
                    this.saving = false;
                    return;
                }
                const cartData = {
                    quote: {
                        customer: {
                            id: <?= $quote->getCustomerId() ?>
                        },
                        extension_attributes: {
                            customer_reference: document.getElementById('customer_reference').value
                        }

                    }
                };

                fetch(postUrl, {
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    credentials: 'include',
                    method: 'PUT',
                    body: JSON.stringify(cartData)
                })
                    .then(response => response.json())
                    .then(data => {
                        this.response.message = '<?= $escaper->escapeHtml(__('Comments saved')) ?>';
                        this.response.status = 'success';
                    })
                    .catch(error => {
                        this.response.message = '<?= $escaper->escapeHtml(__('Something went wrong')) ?>';
                        this.response.status = 'error';
                        console.error(error);
                    }).finally(() => {
                        this.saving = false;
                    });
            },
            closeMessage(){
                this.response.status = false;
                this.response.message = '';
            }
        }
    }
</script>
