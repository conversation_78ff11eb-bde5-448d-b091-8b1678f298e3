<?php

declare(strict_types=1);

use Magento\Checkout\Block\Cart;
use Magento\Framework\Escaper;

/** @var Cart $block */
/** @var Escaper $escaper */

?>
<?php if (!$block->hasError()): ?>
    <?php $methods = $block->getMethods('methods') ?: $block->getMethods('top_methods') ?>
    <ul class="checkout methods items checkout-methods-items container p-4 grid grid-cols-1 gap-4 bg-secondary-300 rounded-xl md:grid-cols-2 md:p-6">
        <?php foreach ($methods as $method): ?>
            <?php $methodHtml = $block->getMethodHtml($method); ?>
            <?php if (trim($methodHtml) !== ''): ?>
                <li><?= /* @noEscape */ $methodHtml ?></li>
            <?php endif; ?>
        <?php endforeach; ?>
    </ul>
<?php endif; ?>
