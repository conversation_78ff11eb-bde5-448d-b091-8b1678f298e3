<?php

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Life\ShowroomDealer\ViewModel\DealerInfo;
use Life\ShowroomQuote\ViewModel\QuoteDataProvider;
use Magento\Framework\Escaper;
use Magento\Checkout\Block\Cart;

/** @var Escaper $escaper */
/** @var Cart $block */
/** @var ViewModelRegistry $viewModels */
/** @var DealerInfo $dealerViewModel */

$dealerViewModel = $viewModels->require(DealerInfo::class);
?>

<?php if (!$dealerViewModel->isDealer()): ?>
<!-- TODO: make functionality work. says it's parked, but the cart content persists -->
<a
    href="<?= $block->getUrl(QuoteDataProvider::QUOTE_PARK_ROUTE) ?>"
    class="btn btn-secondary w-full"
>
    <?= $escaper->escapeHtml(__('Park quote')); ?>
</a>
<?php endif; ?>
