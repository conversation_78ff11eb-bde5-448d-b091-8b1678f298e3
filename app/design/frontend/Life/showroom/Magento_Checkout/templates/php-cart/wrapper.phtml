<?php

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\SvgIcons;
use Life\ShowroomBase\ViewModel\ActorResolver;
use Life\ShowroomQuoteHyva\ViewModel\QuoteCustomer;
use Magento\Checkout\Block\Cart;
use Magento\Framework\Escaper;

/** @var Cart $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */
/** @var SvgIcons $svgIcons */
/** @var ActorResolver $actorResolver */

$svgIcons = $viewModels->require(SvgIcons::class);
/** @var ActorResolver $actorResolver */
$actorResolver = $viewModels->require(ActorResolver::class);
/** @var QuoteCustomer $quoteViewModel */
$quoteViewModel = $viewModels->require(QuoteCustomer::class);
?>

<div class="container grid gap-2">
    <header class="cart-empty bg-primary text-white rounded-xl px-6 py-4">
        <div class="flex flex-wrap items-center gap-x-7 gap-y-4 mb-6 pb-2 border-b border-white/20 text-white text-xs">
            <a href="<?= $escaper->escapeUrl($block->getBaseUrl()) ?>" class="inline-flex gap-2 items-center">
                <?= $svgIcons->arrowLeftHtml("", 20, 20, ['aria-hidden' => 'true']) ?>
                <span><?= $escaper->escapeHtml(__('Back')) ?></span>
            </a>
            <span class="text-primary-100"><?= $block->escapeHtml(__('Products')) ?></span>
        </div>
        <div class="max-w-lg mx-auto py-14">
            <h1 class="heading-5 mb-2">
                <?= $block->escapeHtml(__('Cart')) ?>
            </h1>
            <p class="font-semibold text-primary-100">
                <?= __('Quote #%1', $quoteViewModel->getQuote()->getId()??'') ?>
            </p>
        </div>
    </header>
    <?= $block->getChildHtml('form_before') ?>
    <?= $block->getChildHtml('cart-items') ?>
    <?= $actorResolver->getEmployeeResolver()->isEmployee() ? $block->getChildHtml('checkout.cart.customer') : '' ?>
    <?= $block->getChildHtml('checkout.cart.order.comment') ?>
    <?=
    $actorResolver->getEmployeeResolver()->isEmployee() ?
        $block->getChildHtml('checkout.cart.internal.comment') :
        $block->getChildHtml('checkout.cart.customer.reference');
    ?>
    <?= $block->getChildHtml('checkout_cart_widget') ?>
    <?= $block->getChildHtml('cart.summary') ?>
</div>
