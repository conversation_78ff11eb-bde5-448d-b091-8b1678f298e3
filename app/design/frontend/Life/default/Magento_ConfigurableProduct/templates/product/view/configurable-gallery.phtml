<?php

declare(strict_types=1);

use Life\ProductsConnector\ViewModel\ConfigurableProductGallery;

/** @var \Magento\Framework\Escaper $escaper */
/** @var \Magento\Catalog\Block\Product\View\Gallery $block */
/** @var \Hyva\Theme\Model\ViewModelRegistry $viewModels */

/** @var ConfigurableProductGallery $configurableProductGalleryViewModel */
$configurableProductGalleryViewModel = $viewModels->require(ConfigurableProductGallery::class);

/** @var \Magento\Catalog\Model\Product $product */
$product = $block->getProduct();
?>
<script defer="defer">
    function initConfigurableGallery() {
        return {
            "initialImages": <?= /* @noEscape */ $block->getGalleryImagesJson() ?>,
            "images": <?= /* @noEscape */ $configurableProductGalleryViewModel->getGallery($product, $block) ?>,
            isMobile: window.outerWidth < 1024,

            updateImages(event) {
                let productId = event?.detail?.productIndex;

                if (productId) {
                    let images = this.initialImages;

                    if (this.images[productId]) {
                        images = this.images[productId];
                    }
                    this.triggerUpdateGalleryEvent(images)
                }
            },

            triggerUpdateGalleryEvent(images) {
                window.dispatchEvent(
                    new CustomEvent(
                        "update-gallery",
                        {detail: images}
                    )
                );
            },

            checkIsMobileResolution() {
                this.isMobile = window.matchMedia('(max-width: 1024px)').matches;
            }
        }
    }
</script>
<section
    x-data="initConfigurableGallery()"
    @configurable-selection-changed.window="updateImages($event)"
    @resize.window.debounce="checkIsMobileResolution()"
></section>
