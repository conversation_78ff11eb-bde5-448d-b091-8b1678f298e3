<?php

declare(strict_types=1);

/** @var Hyva\Theme\Model\ViewModelRegistry $viewModels */
/** @var \MageWorx\StoreLocator\Block\Locations $block */
/** @var Hyva\Theme\Model\ViewModelRegistry $viewModels */
/** @var \Magento\Framework\Escaper $escaper */
?>
<div id="mw-filter-left-map"
     class="flex">
        <div id="mw-filter-block">
            <?= /* @noEscape */  $block->getLocationsWrapperHtml('filter'); ?>
        </div>
        <?= /* @noEscape */  $block->getLocationsMapHtml(); ?>
</div>
<script>
    mwLocatorData.mwLocations = JSON.parse('<?= $escaper->escapeJs(json_encode($block->getLocationsIds()))?>');
    mwLocatorData.availableLocations = JSON.parse('<?= $escaper->escapeJs(json_encode($block->getAvailableLocationsIds()))?>');
    mwLocatorData.isWorkingHoursEnabled = <?= (int)$block->isWorkingHoursEnabled() === 1 ? 'true' : 'false' ?>;
</script>

<?= /* @noEscape */ $block->getGoogleMapScript(); ?>

<script>
    document.addEventListener("mw-on-map-mount", () => {
        const $map = document.getElementById("mw-sl-map");
        if ($map) {
            $map.classList.add("w-full", "md:h-full");

            document.getElementById("mw-map-block-init")?.classList.add("max-md:grow");
            document.getElementById("mw-map-block-init")?.classList.add("w-full");
            document.getElementById("mw-map-block-init")?.classList.add("lg:w-[calc(100%-calc(27rem+0.5rem))]");
            document.getElementById("mw-map-block-init")?.classList.add("lg:shrink-0");
            document.getElementById("mw-map-block")?.classList.add("h-full");
        }

        document.getElementById("mw-filter-block")?.classList.add("grow-0");
        document.getElementById("mw-filter-left-map").classList.add("md:flex-row", "md:gap-2", "flex-col")
    })
</script>
