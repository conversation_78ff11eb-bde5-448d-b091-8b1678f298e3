<?php

declare(strict_types=1);

/** @var Hyva\Theme\Model\ViewModelRegistry $viewModels */
/** @var \MageWorx\StoreLocator\Block\Search $block */
/** @var \Hyva\Theme\ViewModel\HeroiconsOutline $heroicons */
/** @var Hyva\Theme\Model\ViewModelRegistry $viewModels */
/** @var Magento\Framework\Escaper $escaper */

$heroicons = $viewModels->require(\Hyva\Theme\ViewModel\HeroiconsOutline::class);
?>
<script>
    mwLocatorData.defaultRadius = '<?= $escaper->escapeHtml($block->getDefaultRadiusValue()) ?>';
    mwLocatorData.defaultPlace = '<?= $escaper->escapeHtml($block->getDefaultPlace()) ?>';
</script>

<div
    id="mw-sl__search"
    class="p-4 border w-full bg-white rounded-xl lg:max-w-none"
    :class="{'hidden': isLoading}"
    x-ref="mw-search-block"
>
    <div class="flex justify-between" x-show="!search.isShowSearchForm">
        <div>
            <div class="flex gap-x-4">
                <?= $heroicons->locationMarkerHtml('w-6 h-6') ?>
                <div>
                    <span x-ref="mw-location-current-location-info" id="mw_location_current_location_info"
                              class="leading-4">
                        <?= $escaper->escapeHtml($block->getCurrentPlaceName()) ?>
                    </span>
                    <div class="mw-sl__search-select-location">
                        <div x-show="search.isShowRadius">
                            (<span x-text="search.radiusValue">
                                    <?= $escaper->escapeHtml($block->getDefaultRadiusValue()) ?>
                                </span>
                            <?= $escaper->escapeHtml($block->getRadiusUnit() . ' ' . __('radius')) ?>)
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="flex flex-col items-end ml-4 gap-2">
            <button
                class="m-0 text-sm leading-none underline font-semibold hover:text-primary focus-visible:text-primary"
                type="button"
                @click="search.onEditFormClick()"
            >
                <?= $escaper->escapeHtml(__('Edit')) ?>
            </button>
            <button
                class="m-0 text-sm leading-none underline font-semibold hover:text-primary focus-visible:text-primary"
                type="button"
                @click="search.onResetFormClick()"
            >
                <?= $escaper->escapeHtml(__('Reset')) ?>
            </button>
        </div>
    </div>
    <div class="mw-sl__search-form-content" x-show="search.isShowSearchForm">
        <div class="flex gap-4">
            <div class="flex basis-72 relative items-center grow">
                <label class="w-full">
                    <small class="text-sm leading-none"><?= $escaper->escapeHtml(__('Find a location')) ?></small>
                    <input
                        name="search_text"
                        class="form-input block w-full !pr-10 border-0 !text-base !leading-none"
                        id="mw-sl_search_text"
                        type="text"
                        value="<?= $escaper->escapeHtml($block->getCurrentPlaceName()) ?>"
                    />
                </label>
                <button
                    class="inline-flex min-h-a11y aspect-square items-center justify-center absolute right-0 bottom-0"
                    @click="search.searchLocation()"
                >
                    <span class="sr-only"><?= $escaper->escapeHtml(__('Search')) ?></span>
                    <?= $heroicons->searchHtml('w-5 h-5') ?>
                </button>
            </div>
            <label class="mw-sl__search__radius relative">
                <small class="mw-sl__search__text text-sm leading-none"><?= $escaper->escapeHtml(__('Radius')) ?></small>
                <?= $heroicons->chevronDownHtml('w-5 h-5 absolute right-[0.7rem] bottom-[0.85rem]') ?>
                <select
                    id="radius-select"
                    x-ref="mw-radius-select"
                    name="radius"
                    class="form-select w-full !pr-8 !text-base !leading-none"
                >
                    <?php if (!empty($block->getPredefinedRadiusValues())) : ?>
                        <?php foreach ($block->getPredefinedRadiusValues() as $value) : ?>
                            <option value="<?= $escaper->escapeHtml($value) ?>"
                                <?= $value == $block->getCurrentRadiusValue() ? 'selected' : '' ?>
                            >
                                <?= $escaper->escapeHtml($value) . ' ' . $escaper->escapeHtml(
                                    $block->getRadiusUnit()) ?>
                            </option>
                        <?php endforeach; ?>
                    <?php endif; ?>
                    <option value="0" <?= '0' == $block->getCurrentRadiusValue() ? 'selected' : '' ?>>
                        <?= $block->escapeHtml(__('All Stores')) ?>
                    </option>
                </select>
            </label>
        </div>
        <?php if ($block->isSetToUseCurrentLocation()) : ?>
            <div class="text-center my-2"><?= $escaper->escapeHtml(__('OR')) ?></div>
        <?php endif; ?>
        <div>
            <?php if ($block->isSetToUseCurrentLocation()) : ?>
                <div @click="search.getLocation()">
                    <button id="use_location" class="btn btn-secondary w-full">
                        <span class="flex w-full gap-2 items-center justify-center">
                            <?= $heroicons->locationMarkerHtml('w-5 h-5') ?>
                            <?= $escaper->escapeHtml(__('Use my current location')) ?>
                        </span>
                    </button>
                </div>
            <?php endif; ?>
        </div>
        <div class="mw-sl__search__footer">
            <button class="btn btn-primary w-full text-color-white my-4 justify-center" @click="search.searchLocation('by_radius')">
                <span><?= $escaper->escapeHtml(__('Search')) ?></span>
            </button>
        </div>
        <template x-if="search.errors.length > 0">
            <div x-ref="mw-search-store-error" class="text-red-500 py-4">
                <template x-for="(error, index) in search.errors" :key="index">
                    <span x-text="error"></span>
                </template>
            </div>
        </template>

        <input id="mw-sl__lat" name="autocomplete[lat]" type="hidden" value="<?=$block->escapeHtml($block->getCurrentLat()) ?>"/>
        <input id="mw-sl__lng" name="autocomplete[lng]" type="hidden" value="<?=$block->escapeHtml($block->getCurrentLng()) ?>"/>
        <input class="field" name="autocomplete[small_city]" id="mw_sublocality_level_1" type="hidden" value="<?=$block->escapeHtml($block->getCurrentSmallCity()) ?>"/>
        <input class="field" name="autocomplete[city]" id="mw_locality" type="hidden" value="<?=$block->escapeHtml($block->getCurrentCity()) ?>"/>
        <input class="field" name="autocomplete[region]" id="mw_administrative_area_level_1" type="hidden" value="<?=$block->escapeHtml($block->getCurrentRegion()) ?>"/>
        <input class="field" name="autocomplete[postcode]" id="mw_postal_code" type="hidden" value="<?=$block->escapeHtml($block->getCurrentPostCode()) ?>"/>
        <input class="field" name="autocomplete[country_id]" id="mw_country" type="hidden" value="<?=$block->escapeHtml($block->getCurrentCountryId()) ?>"/>
    </div>
</div>
