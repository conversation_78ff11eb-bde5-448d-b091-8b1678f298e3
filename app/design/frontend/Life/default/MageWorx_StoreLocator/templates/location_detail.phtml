<?php
declare(strict_types=1);

use MageWorx\Locations\Api\Data\LocationInterface;
use Hyva\Theme\ViewModel\SvgIcons;

/** @var \MageWorx\StoreLocator\Block\LocationDetail $block */
/** @var LocationInterface $location */
/** @var \Magento\Framework\Escaper $escaper */
/** @var \Hyva\Theme\Model\ViewModelRegistry $viewModels */

/** @var SvgIcons $svgIcons */
$svgIcons = $viewModels->require(SvgIcons::class);

$location = $block->getLocation(); ?>

<div class="mt-3" id="location-details_<?= $escaper->escapeHtml($location->getId()) ?>" x-data="{isShowWorkTable:false}" class="overflow-y-visible">
    <ul class="flex flex-col gap-y-2">
        <?= /* @noEscape */ $block->getWorkingHoursHtml($location) ?>

        <?php if ($location->getPhoneNumber()) : ?>
            <li class="flex items-center gap-x-4">
                <?= $svgIcons->phoneHtml('flex-shrink-0', 32, 32, [ 'aria-hidden' => 'true']); ?>
                <a class="text-link after:hidden before:hover:delay-0" href="tel:<?= $escaper->escapeHtmlAttr($escaper->escapeUrl($location->getPhoneNumber())); ?>">
                    <?= $escaper->escapeHtml($location->getPhoneNumber()); ?>
                </a>
            </li>
        <?php endif; ?>
        <?php if ($location->getEmail()) : ?>
            <li class="flex items-center gap-x-4">
                <?= $svgIcons->envelopeHtml('flex-shrink-0', 32, 32, [ 'aria-hidden' => 'true']); ?>
                <a class="text-link after:hidden before:hover:delay-0" href="mailto: <?= $escaper->escapeHtmlAttr($escaper->escapeUrl($location->getEmail())); ?>"
                   target="_blank">
                    <?= $escaper->escapeHtml($location->getEmail()); ?>
                </a>
            </li>
        <?php endif; ?>
        <li class="mt-4">
            <ul class="flex gap-x-4">
                <?php if ($location->getInstagram()) : ?>
                    <li>
                        <a href="https://instagram.com/<?= $escaper->escapeHtmlAttr($escaper->escapeUrl($location->getInstagram())); ?>"
                           target="_blank">
                            <i>
                                <img src="<?= $escaper->escapeHtmlAttr($escaper->escapeUrl(
                                    $block->getViewFileUrl('MageWorx_StoreLocator::images/svg/instagram.svg')
                                )); ?>" alt="<?= $escaper->escapeHtmlAttr(__('Instagram icon')) ?>"/>
                            </i>
                        </a>
                    </li>
                <?php endif; ?>
                <?php if ($location->getFacebook()) : ?>
                    <li>
                        <a href="https://facebook.com/<?= $escaper->escapeHtmlAttr($escaper->escapeUrl($location->getFacebook())); ?>"
                           target="_blank">
                            <i>
                                <img src="<?= $escaper->escapeHtmlAttr($escaper->escapeUrl(
                                    $block->getViewFileUrl(
                                        'MageWorx_StoreLocator::images/svg/facebook.svg'
                                    )
                                )); ?>" alt="<?= $escaper->escapeHtml(__('Facebook icon')) ?>"/>
                            </i>
                        </a>
                    </li>
                <?php endif; ?>
                <?php if ($location->getWhatsapp()) : ?>
                    <li>
                        <a href="https://wa.me/<?= $escaper->escapeHtmlAttr($escaper->escapeUrl($location->getWhatsapp())); ?>" target="_blank">
                            <i>
                                <img src="<?= $escaper->escapeHtmlAttr($escaper->escapeUrl(
                                    $block->getViewFileUrl('MageWorx_StoreLocator::images/svg/whatsapp.svg')
                                )); ?>" alt="<?= $escaper->escapeHtml(__('Whatsapp icon')) ?>"/>
                            </i>
                        </a>
                    </li>
                <?php endif; ?>
                <?php if ($location->getSkype()) : ?>
                    <li>
                        <a href="skype:<?= $escaper->escapeHtmlAttr($escaper->escapeUrl($location->getSkype())); ?>?call">
                            <i>
                                <img src="<?= $escaper->escapeHtmlAttr($escaper->escapeUrl(
                                    $block->getViewFileUrl('MageWorx_StoreLocator::images/svg/skype.svg')
                                )); ?>" alt="<?= $escaper->escapeHtml(__('Skype icon')) ?>"/>
                            </i>
                        </a>
                    </li>
                <?php endif; ?>
            </ul>
        </li>

        <?php if ($location->getWebsiteUrl()) : ?>
            <li class="flex gap-x-4">
                <a class="btn btn-secondary" href="<?= $escaper->escapeHtmlAttr($escaper->escapeUrl($location->getWebsiteUrl())); ?>">
                   <span><?= $escaper->escapeHtml(__('More info')) ?></span>
                </a>
            </li>
        <?php endif; ?>
    </ul>
    <div>
        <ul class="flex-col items-center gap-x-6">
            <?php foreach ($block->getExtraAttributes() as $attribute) : ?>
                <?php if ($location->getData($attribute['attribute_code'])) : ?>
                    <li class="flex items-center gap-x-2">
                        <i>
                            <?php $icon = $block->getAttributeIcon($attribute['attribute_code']);
                            if ($icon): ?>
                                <img class="w-4 h-4"
                                     src="<?= $escaper->escapeUrl($icon); ?>"
                                     alt="<?= $escaper->escapeHtml(__($attribute['frontend_label'])) ?>"/>
                            <?php endif;?>
                        </i>
                        <span><?= $escaper->escapeHtml(__($attribute['frontend_label'])) ?><span>
                    </li>
                <?php endif; ?>
            <?php endforeach; ?>
        </ul>
    </div>
    <div class="mt-2">
        <?= /* @noEscape */ $location->getDescription() ?>
    </div>
</div>
