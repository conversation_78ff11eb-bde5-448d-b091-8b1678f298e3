<?php

declare(strict_types=1);

use MageWorx\Locations\Api\Data\LocationInterface;
use Hyva\Theme\ViewModel\SvgIcons;

/** @var \MageWorx\StoreLocator\Block\LocationInfo $block */
/** @var \Hyva\Theme\Model\ViewModelRegistry $viewModels */
/** @var \Magento\Framework\Escaper $escaper */
/** @var LocationInterface $location */
$location = $block->getLocation();

/** @var SvgIcons $svgIcons */
$svgIcons = $viewModels->require(SvgIcons::class);

?>
<?php if (($workingHours = $location->getFormattedWorkingHours())
    || $location->getWorkingHoursType() == LocationInterface::WORKING_24_HOURS_A_DAY) : ?>
    <li class="flex gap-x-4">
        <?= $svgIcons->clockHtml('flex-shrink-0', 32, 32, [ 'aria-hidden' => 'true']); ?>
        <div class="mt-1 text-base">
            <div @click="isShowWorkTable=!isShowWorkTable" class="flex cursor-pointer">
                <div class="<?= $location->isOpenNow() ? 'text-green-600' : 'text-red-600' ?> pr-2 font-normal align-top">
                    <?= $escaper->escapeHtml(__('Now')) ?>
                    <?= /* @noEscape */ $location->getWorkingHoursInfo() ?>
                </div>
                <div>
                    <template x-if="isShowWorkTable">
                        <?= $svgIcons->chevronLeftHtml('w-6 h-6 tranform rotate-90 ml-1') ?>
                    </template>
                    <template x-if="!isShowWorkTable">
                        <?= $svgIcons->chevronLeftHtml('w-6 h-6 tranform -rotate-90 ml-1c') ?>
                    </template>
                </div>
            </div>
            <?php foreach ($workingHours as $day => $time) : ?>
                <div x-show="isShowWorkTable" class="flex items-center justify-between">
                    <div class="pr-2 font-normal">
                        <?= $escaper->escapeHtml(__($day)) ?>
                    </div>
                    <?php if ($time['off']) : ?>
                        <div class="pl-2"">
                            <?= $escaper->escapeHtml(__('Closed')) ?>
                        </div>
                    <?php else : ?>
                        <div class="pl-2"">
                            <span>
                                <?= $escaper->escapeHtml($time['from'] . ' - ' . $time['to']) ?>
                            </span>
                            <?php if ($time['has_lunch_time']) : ?>
                                <span>
                                    <?= $escaper->escapeHtml('(' . __('Lunch time') . ' ' . $time['lunch_from']
                                        . ' - ' . $time['lunch_to'] . ')') ?>
                                </span>
                            <?php endif ?>
                        </div>
                    <?php endif ?>
                </div>
            <?php endforeach; ?>
        </div>
    </li>
<?php endif; ?>
