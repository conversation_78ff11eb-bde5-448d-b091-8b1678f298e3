<?php

declare(strict_types=1);

use MageWorx\Locations\Api\Data\LocationInterface;
use Hyva\Theme\ViewModel\SvgIcons;

/** @var Hyva\Theme\Model\ViewModelRegistry $viewModels */
/** @var \MageWorx\StoreLocator\Block\LocationInfo $block */
/** @var Hyva\Theme\Model\ViewModelRegistry $viewModels */
/** @var \Magento\Framework\Escaper $escaper */
/** @var LocationInterface $location */

/** @var SvgIcons $svgIcons */
$svgIcons = $viewModels->require(SvgIcons::class);

$location = $block->getLocation();

$checkedLoc = $escaper->escapeHtml($block->getCodeByScale($location, $block->getParentScale()));
?>

<script>
    const mw_attributes_<?= $escaper->escapeHtml($location->getId()) ?> = [];
</script>

<?php foreach ($block->getExtraAttributes() as $attribute) : ?>
    <?php if ($location->getData($attribute['attribute_code'])) : ?>
        <script>
            mw_attributes_<?= $escaper->escapeHtml($location->getId()) ?>.push('<?= $escaper->escapeHtml($attribute['attribute_code']); ?>');
        </script>
    <?php endif; ?>
<?php endforeach; ?>

<li
    x-show="(!filter.checkedLocation || filter.checkedLocation === 'all' || filter.checkedLocation === '<?= $checkedLoc; ?>') && (filter.checkedAttrSubmitted.length === 0 || filter.checkAttr(mw_attributes_<?= $escaper->escapeHtml($location->getId()) ?>))"
    class="
        mt-2 p-4 bg-white rounded-xl relative
        location-info_<?= $escaper->escapeHtml($block->getCodeByScale($location, $block->getScale())) ?>
        location-info_<?= $escaper->escapeHtml($block->getCodeByScale($location, $block->getParentScale())) ?>
    "
    id="mw-location_info_<?= $escaper->escapeHtml($location->getId()) ?>"
>

    <div class="flex flex-col flex-wrap items-start gap-2 text-sm lg:items-center lg:flex-row">
        <button
            class="absolute inset-0"
            :class="{
                'hidden': '<?= $escaper->escapeHtml($location->getId()) ?>' === list.firstElemCode
            }"
            @click="list.onLocationDetailClick('<?= $escaper->escapeHtml($location->getId()) ?>');"
        >
            <div class="absolute inset-0 z-10 flex items-center justify-center rounded-xl bg-white/70" x-show="(list.locationDetailsLoading.includes('<?= $escaper->escapeHtml($location->getId()) ?>'))">
                <div style="border-top-color:transparent"
                     class="w-8 h-8 p-2 border-4 border-solid rounded-full border-primary animate-spin"></div>
            </div>
            <span
                class="absolute transition transform top-4 right-4"
                :class="{
                    'rotate-180': list.locationDetailsShow.includes('<?= $escaper->escapeHtml($location->getId()) ?>')
                }"
            ><?= $svgIcons->arrowDownHtml("w-6 h-6") ?></span>
            <span class="sr-only"><?= $escaper->escapeHtml(__('Select location')) ?> <?= $escaper->escapeHtml($block->getLocationName($location)); ?></span>
        </button>
        <address class="flex text-base gap-x-4">
            <?= $svgIcons->markerHtml('flex-shrink-0', 32, 32, [ 'aria-hidden' => 'true']); ?>
            <span>
                <div class="text-lg font-semibold leading-8">
                    <a href="<?= $escaper->escapeUrl($location->getWebsiteUrl()); ?>">
                        <span><?= $escaper->escapeHtml($location->getName()) ?></span>
                    </a>
                </div>
                <?= $escaper->escapeHtml($location->getAddress()); ?><br />
                <?= $escaper->escapeHtml($location->getPostcode()); ?> <?= $escaper->escapeHtml($location->getCity()); ?><br />
                <a
                    href="<?= $escaper->escapeUrl($block->getRouteUrl($location)); ?>"
                    target="_blank"
                    class="inline-block mt-1 font-semibold leading-none text-link"
                >
                    <?= $escaper->escapeHtml(__('Plan your route')) ?>
                </a>
            </span>
        </address>
    </div>
    <div
        x-ref="mw-location_details_<?= $escaper->escapeHtml($location->getId()) ?>"
        id="mw-location_details_<?= $escaper->escapeHtml($location->getId()) ?>"
    ></div>
</li>
