<?php

declare(strict_types=1);

/** @var Hyva\Theme\Model\ViewModelRegistry $viewModels */
/** @var \MageWorx\StoreLocator\Block\Filter $block */
/** @var Hyva\Theme\Model\ViewModelRegistry $viewModels */
/** @var \Magento\Framework\Escaper $escaper */
$locations    = $block->getLocations();
$defaultPlace = $block->getCurrentPlace();

/** @var \Hyva\Theme\ViewModel\HeroiconsOutline $heroicons */
$heroicons = $viewModels->require(\Hyva\Theme\ViewModel\HeroiconsSolid::class);

/** @var $escaper \Magento\Framework\Escaper */
?>

<script defer="defer">
    var locations = [];
    var placesCoordinates = [];

    var locationsDefault = [];
    var placesCoordinatesDefault = [];

    <?php
        $loc_data = [];
        $places_data = [];
    ?>

    //Add markers on map
    <?php /** @var \MageWorx\Locations\Api\Data\LocationInterface $location */


    foreach ($locations as $location) :?>
        locations['<?= $escaper->escapeJs($location->getId())?>'] = <?= $escaper->escapeHtml($block->getLocationCoordinate($location))?>;
        locationsDefault['<?= $escaper->escapeJs($location->getId())?>'] = <?= $escaper->escapeHtml($block->getLocationCoordinate($location))?>;
        <?php
            $id = $escaper->escapeJs($location->getId());
            $loc = $escaper->escapeHtml($block->getLocationCoordinate($location));
            $loc_data[] = array($id, $loc);
        ?>
    <?php endforeach; ?>

    <?php /** @var \MageWorx\Locations\Api\Data\LocationInterface $location */
    $places = $block->getParentFilterList();

    foreach ($places as $place) :?>
        placesCoordinates['<?= $escaper->escapeJs($place->getId()) ?>'] = <?= $escaper->escapeHtml($block->getLocationCoordinate($place))?>;
        placesCoordinatesDefault['<?= $escaper->escapeJs($place->getId()) ?>'] = <?= $escaper->escapeHtml($block->getLocationCoordinate($place))?>;
        <?php
            $id = $escaper->escapeJs($place->getId());
            $place = $escaper->escapeHtml($block->getLocationCoordinate($place));
            $places_data[] = array($id, $place);
        ?>
    <?php endforeach; ?>
</script>


<div
    data-stores-locations='<?= json_encode($loc_data); ?>'
    data-places='<?= json_encode($places_data); ?>'
    id="data-locations"
    class="flex flex-col"
>
    <div id="mw-sl__stores__list_block" x-show="filter.showMode === 'stores'">
        <ul
            class="flex flex-col"
            @mw-marker-click.window="list.onLocationDetailClick($event.detail.code, true)"
        >
            <?php /** @var $location \MageWorx\Locations\Api\Data\LocationInterface $location */
            if (count($locations)) :
                $markerId = 0;
                foreach ($locations as $location) : ?>
                    <?php
                        $location->setMarkerId($markerId);
                        $markerId++;
                    ?>
                    <?= /* @noEscape */ $block->getLocationInfoHtml($location); ?>
                <?php endforeach;
            else : ?>
                <div class="p-4">
                    <?= $escaper->escapeHtml(__('There are no stores.'));?>
                </div>
            <?php endif?>
        </ul>
        <div id="mw-stores-not-found" class="hidden mx-4">
            <?= $escaper->escapeHtml(__('No stores found.')); ?>
        </div>
    </div>

    <div
        x-show="filter.showMode === 'locations'"
        class="bg-white rounded-br-xl rounded-bl-xl"
    >
        <ul class="flex flex-col mx-4">
            <li>
                <select x-model="filter.currentFilterListItem" class="w-full text-base form-select">
                    <option value="" id="location-info_all_stores">
                        <?= $escaper->escapeHtml(__('All Stores')); ?>
                    </option>
                    <?php foreach ($block->getParentFilterList() as $place) : ?>
                        <option :value="'<?= $escaper->escapeHtml($place->getName()); ?>' + '_' + '<?= $escaper->escapeHtml($place->getId()); ?>'"
                                id="location-info_<?= $escaper->escapeHtml($place->getId()); ?>">
                            <?= $escaper->escapeHtml($place->getName()); ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </li>
            <li>
                <div>
                    <ul class="mt-4">
                        <?php foreach ($block->getExtraAttributes() as $attribute) : ?>
                            <li class="flex items-center gap-x-2">
                                <input type="checkbox" x-model="filter.checkedAttr"
                                       value="<?= $escaper->escapeHtml($attribute['attribute_code']); ?>"
                                       id="<?= $escaper->escapeHtml($attribute['attribute_code']); ?>">
                                <label class="flex items-center gap-x-2"
                                       for="<?= $escaper->escapeHtml($attribute['attribute_code']); ?>">
                                    <i class="mw-sl__icon mw-sl__icon--website">
                                        <?php $icon = $block->getAttributeIcon($attribute['attribute_code']);
                                        if ($icon): ?>
                                            <img class="w-4 h-4"
                                                 src="<?= $escaper->escapeUrl($icon); ?>"
                                                 alt="<?= $escaper->escapeHtml(__($attribute['frontend_label'])) ?>"/>
                                        <?php endif;?>
                                    </i>
                                    <span><?= $escaper->escapeHtml(__($attribute['frontend_label'])) ?></span>
                                </label>
                            </li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            </li>

            <li class="flex justify-between mt-4">
                <button class="text-underline" @click="filter.resetFilterList()"><?= $escaper->escapeHtml(__('Clear all')); ?></button>
                <button class="justify-center my-4 btn btn-primary text-color-white"
                        @click="filter.onFilterListItemClick()"><?= $escaper->escapeHtml(__('Apply')); ?></button>
            </li>
        </ul>
    </div>
    <div id="location_details"></div>
</div>
