<?php

use MageWorx\Locations\Api\Data\LocationInterface;
use Hyva\Theme\ViewModel\HeroiconsOutline;

/** @var Hyva\Theme\Model\ViewModelRegistry $viewModels */
/** @var \MageWorx\StoreLocator\Block\LocationInfo $block */
/** @var Hyva\Theme\Model\ViewModelRegistry $viewModels */
/** @var \Magento\Framework\Escaper $escaper */
/** @var LocationInterface $location */
/** @var $location LocationInterface $location */
/** @var HeroiconsOutline $heroicons */
$heroicons = $viewModels->require(HeroiconsOutline::class);

$location = $block->getLocation();
?>

<?php if (($workingHours = $location->getWorkingHours())
    || $location->getWorkingHoursType() == LocationInterface::WORKING_24_HOURS_A_DAY) : ?>
    <div>
        <span class="store__info_<?= $escaper->escapeHtml($location->getId()) ?>"></span>
        <span
            class="
                flex gap-2 items-center
                store__info_wh_<?= $escaper->escapeHtml($location->getId()) ?>
            "
        >
            <?php if ($location->isOpenNow()) : ?>
                <?= $heroicons->clockHtml('w-5 h-5') ?>
            <?php endif; ?>
            <?= $escaper->escapeHtml($location->getWorkingHoursInfo()) ?>
        </span>
    </div>
    <?= $block->assign('location', $location)->fetchView(
        (string)$block->getTemplateFile('Life_MageWorxStoreLocatorExtension::location-working-hours-structured-data.phtml')
    ) ?>
<?php endif; ?>
