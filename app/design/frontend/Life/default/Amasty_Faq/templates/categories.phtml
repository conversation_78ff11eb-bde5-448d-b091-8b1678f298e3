<?php

declare(strict_types=1);

/** @var \Magento\Framework\Escaper $escaper */
/** @var \Amasty\Faq\Block\Widgets\Categories $block */
/** @var \Amasty\Faq\Api\Data\CategoryInterface $category */
/** @var \Amasty\Faq\Api\Data\QuestionInterface $question */
?>
<?php if ($categories = $block->getCategories()): ?>
    <div class="<?= $escaper->escapeHtml($block->getLayoutType()) ?> widget">
        <?php foreach ($categories as $category): ?>
            <div class="mb-4">
                <div class="flex items-center mb-2">
                    <?php if ($block->canShowCategoryIcon($category)): ?>
                        <div class="mr-4">
                            <img
                                src="<?= $escaper->escapeUrl($block->getCategoryIconUrl($category)); ?>"
                                width="50"
                                height="50"
                                alt="<?= $escaper->escapeHtml($category->getIcon()); ?>"
                            >
                        </div>
                    <?php endif; ?>
                    <span class="title-font text-xl font-semibold">
                        <?= $escaper->escapeHtml($category->getTitle()); ?>
                    </span>
                </div>
                <?php if (!$block->isShowWithoutQuestions() && $questions = $block->getCategoryQuestions($category)): ?>
                <div class="mb-2">
                    <?php foreach ($questions as $question): ?>
                        <details class="mb-2 pb-2 border-b" id="<?= $question->getUrlKey() ?>">
                            <summary class="cursor-pointer text-lg">
                                <?= $escaper->escapeHtml($question->getTitle()); ?>
                            </summary>
                            <div class="my-2">
                                <div class="mb-2">
                                    <?= /** @noEscape */ $block->getShortAnswer($question) ?>
                                </div>
                                <?php if (!$question->isShowFullAnswer()): ?>
                                    <a
                                        href="<?= $escaper->escapeUrl($block->getQuestionUrl($question)) ?>"
                                        class="text-sm underline"
                                    >
                                        <?= $escaper->escapeHtml(__('Read More')); ?>
                                    </a>
                                <?php endif; ?>
                            </div>
                        </details>
                    <?php endforeach; ?>
                </div>
                <?php endif; ?>
                <div class="text-right">
                    <a
                        href="<?= $escaper->escapeUrl($block->getCategoryUrl($category)) ?>"
                        class="text-sm underline"
                    >
                        <?= $escaper->escapeHtml(__('View all')); ?>
                    </a>
                </div>
            </div>
        <?php endforeach; ?>
    </div>
<?php endif; ?>
<?php if ($block->isAddStructuredData()): ?>
    <?= /** @noEscape */
    $block->getCategoriesStructuredDataHtml(); ?>
<?php endif; ?>
