<?php

declare(strict_types=1);

use Amasty\Faq\Api\Data\QuestionInterface;
use Hyva\Theme\ViewModel\SvgIcons;
use Life\ThemeConfigurations\ViewModel\FilterContent;

/** @var \Magento\Framework\Escaper $escaper */
/** @var \Amasty\Faq\Block\Lists\QuestionsList $block */
/** @var \Amasty\Faq\Block\Rating\Rating $rating */
/** @var \Hyva\Theme\Model\ViewModelRegistry $viewModels */

$rating = $block->getChildBlock('amasty_faq_rating');

/** @var SvgIcons $svgIcons */
$svgIcons = $viewModels->require(SvgIcons::class);

/** @var FilterContent $svgIcons */
$filterContent = $viewModels->require(FilterContent::class);
?>
<div class="max-w-4xl mx-auto">
    <?php if ($block->hasBlockTitle()): ?>
        <div class="mb-8" id="<?= $escaper->escapeHtml(str_replace(' ', '-', strtolower((string)$block->getBlockTitle()))) ?>">
            <span class="title-font text-2xl md:text-5xl font-light">
                <?= $escaper->escapeHtml((string)$block->getBlockTitle()) ?>
            </span>
        </div>
    <?php endif; ?>
    <?php if ($questions = $block->getQuestions()): ?>
        <div x-data="{ selected: 0 }" class="mb-2">
            <?php foreach ($questions as $index => $question): ?>
                <div
                    class="bg-white rounded-lg mb-4 px-3 py-3.5"
                    id="<?= $question->getUrlKey() ?: 'question-' . $question->getQuestionId() ?>"
                    itemprop="questionEntity"
                >
                    <div
                        class="cursor-pointer text-base font-semibold flex justify-between items-center"
                        itemprop="question"
                        @click="selected !== <?= $index ?> ? selected = <?= $index ?> : selected = null"
                    >
                        <div class="line-clamp-2"><?= /** @noEscape */ $block->highlight($escaper->escapeHtml($question->getTitle())); ?></div>

                        <template x-if="selected != <?= $index ?>">
                            <?= $svgIcons->plusHtml('p-1.5', 32, 32) ?>
                        </template>

                        <template x-if="selected === <?= $index ?>">
                            <?= $svgIcons->minusHtml('p-1.5', 32, 32) ?>
                        </template>
                    </div>
                    <div x-show="selected === <?= $index ?>" class="faq-content my-2 text-base">
                        <div itemprop="acceptedAnswer" class="mb-2">
                            <?= /** @noEscape */ $block->highlight(
                                $filterContent->filter($question->getData(QuestionInterface::ANSWER))
                            ); ?>
                        </div>
                        <?php if ($rating): ?>
                            <?= /** @noEscape */ $rating->ratingItemHtml((int)$question->getQuestionId()); ?>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
        <?php if ($pager = $block->getPagerHtml()): ?>
            <div><?= /** @noEscape */ $pager; ?></div>
        <?php endif; ?>
    <?php else: ?>
        <div>
            <?= $escaper->escapeHtml($block->getNoItemsLabel()); ?>
        </div>
    <?php endif; ?>
</div>
<?php if ($rating): ?>
    <?= /** @noEscape */ $block->getChildHtml('amasty_faq_rating'); ?>
<?php endif; ?>
<?php if ($block->isShowQuestionForm()): ?>
    <?= /** @noEscape */ $block->getChildHtml('amasty_faq_ask_question_form'); ?>
<?php endif; ?>
<?= /** @noEscape */ $block->getChildHtml('amasty_faq_structureddata'); ?>
