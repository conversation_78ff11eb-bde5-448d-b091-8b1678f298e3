<?php

declare(strict_types=1);

/** @var \Magento\Framework\Escaper $escaper */
/** @var \Amasty\Faq\Block\Lists\CategoryList $block */

$currentCategoryId = $block->getCurrentCategoryId();
?>
<?php if ($categories = $block->getCategories()): ?>
    <div class="card my-4">
        <div class="flex justify-between items-center border-container border-b pb-4">
            <span class="title text-md md:text-lg font-semibold">
                <?= $escaper->escapeHtml(__('Categories')); ?>
            </span>
        </div>
        <div class="pt-3">
            <?php foreach ($categories as $category): ?>
                <a
                    href="<?= $escaper->escapeUrl($block->getCategoryUrl($category)) ?>"
                    class="block py-1 hover:text-black<?= ($category->getCategoryId() === $currentCategoryId) ? ' font-semibold' : ''; ?>"
                >
                    <?= $escaper->escapeHtml($category->getTitle()); ?>
                </a>
            <?php endforeach; ?>
        </div>
    </div>
<?php endif;
