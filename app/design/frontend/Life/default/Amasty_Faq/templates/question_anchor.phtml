<script defer="defer">
    setTimeout(function() {
        let anchor = getAnchor();

        if (
            anchor
            && document.getElementById(anchor)
        ) {
            openQuestion(anchor);
        }
    }, 2000);

    function openQuestion(anchor) {
        let questionElement = document.getElementById(anchor);
        questionElement.scrollIntoView({block: "start", behavior: "smooth"});

        // Auto-open question
        if (questionElement.querySelector('[itemprop="question"]')) {
            questionElement.querySelector('[itemprop="question"]').click();
        }
    }

    function getAnchor() {
        let currentUrl = document.URL,
            urlParts   = currentUrl.split('#');

        return (urlParts.length > 1) ? urlParts[1] : null;
    }
</script>
