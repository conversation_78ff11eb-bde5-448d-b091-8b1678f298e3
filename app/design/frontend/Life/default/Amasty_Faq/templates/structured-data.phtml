<section x-data="faqStructuredData()"></section>
<script defer="defer">
    function faqStructuredData() {
        return {
            init() {
                let questions = [];
                Array.from(document.querySelectorAll(`[itemprop="questionEntity"]`))
                    .map(element => {
                        let question = element.querySelector(`[itemprop="question"]`).innerText.trim(),
                            acceptedAnswer = element.querySelector(`[itemprop="acceptedAnswer"]`).cloneNode(true);
                        acceptedAnswer.removeChild(acceptedAnswer.querySelector('style'));

                        if (question && acceptedAnswer.innerText.trim()) {
                            questions.push({
                                "@type": "Question",
                                "name": question,
                                "acceptedAnswer": {
                                    "@type": "Answer",
                                    "text": acceptedAnswer.innerText.trim()
                                }
                            });
                        }
                    });

                if (questions.length) {
                    const faqStructuredDataElement = document.createElement('script');
                    faqStructuredDataElement.type = 'application/ld+json';

                    faqStructuredDataElement.text = JSON.stringify({
                        "@context": "http://schema.org/",
                        "@type": "FAQPage",
                        "mainEntity": questions
                    });

                    document.querySelector('body').appendChild(faqStructuredDataElement);
                }
            }
        }
    }
</script>
