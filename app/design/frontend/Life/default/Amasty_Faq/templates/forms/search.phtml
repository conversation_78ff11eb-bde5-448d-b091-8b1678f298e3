<?php

declare(strict_types=1);

/** @var \Magento\Framework\Escaper $escaper */
/** @var \Amasty\Faq\Block\Forms\Search $block */
?>

<script defer="defer">
    function initFaqSearch() {
        return {
            url: '<?= $escaper->escapeUrl($block->getSuggestUrl()); ?>',
            minSearchLength: 3,
            maxSearchLength: 100,
            results: [],
            noResults: false,
            showAutocomplete: false,
            currentRequest: null,

            getSearchResults: function () {
                let value = document.querySelector('#search-faq').value.trim()
                this.noResults = false
                this.results = []

                if (value.length < parseInt(this.minSearchLength) || value.length > parseInt(this.maxSearchLength)) {
                    this.results = [];
                    return false;
                }

                let url = this.url + '?' + new URLSearchParams({
                    q: value
                }).toString();

                if (this.currentRequest !== null) {
                    this.currentRequest.abort()
                }
                this.currentRequest = new AbortController()

                fetch(url, {
                    method: 'GET',
                    signal: this.currentRequest.signal
                }).then((response) => {
                    if (response.ok) return response.json()
                }).then((data) => {
                    this.showAutocomplete = true

                    if (data.length > 0) {
                        this.results = data
                    } else {
                        this.noResults = true
                    }
                }).catch((error) => {
                    console.error(error)
                })
            }
        }
    }
</script>

<div>
    <form
        id="amfaq_search"
        action="<?= $escaper->escapeUrl($block->getUrlAction()); ?>"
        method="get"
        x-data="initFaqSearch()"
        @click.outside="showAutocomplete = false"
        class="max-w-4xl mx-auto"
    >
        <div class="flex">
            <div class="w-full">
                <input
                    class="form-input w-full"
                    type="search"
                    placeholder="<?= $escaper->escapeHtml(__("What are you looking for?")); ?>"
                    title="<?= $escaper->escapeHtml(__("Search")); ?>"
                    id="search-faq"
                    name="query"
                    x-on:input.debounce="getSearchResults()"
                    value="<?= $escaper->escapeHtml($block->getQuery()); ?>"
                    maxlength="100"
                    minlength="3"
                    autocapitalize="off"
                    autocomplete="off"
                    autocorrect="off"
                />
                <div class="relative w-full" x-show="showAutocomplete">
                    <div class="absolute bg-white top-2 shadow-lg z-10 w-full">
                        <div class="p-4" x-show="noResults" style="display:none;">
                            <?= __("No results found.") ?>
                        </div>
                        <div x-show="results">
                            <template x-for="result in results">
                                <a
                                    :href="'#question-' + result.id"
                                    @click="openQuestion('question-' + result.id)"
                                    class="px-4 py-2 block hover:bg-gray-100 border-b border-gray-100"
                                >
                                    <span x-text="result.title" class="block"></span>
                                    <span x-show="result.category" class="block text-xs">
                                <?= __('Category:') ?>
                                <span x-text="result.category"></span>
                            </span>
                                </a>
                            </template>
                        </div>
                    </div>
                </div>
            </div>
            <button
                class="btn btn-primary ml-2"
                x-on:click="getSearchResults()"
                title="<?= $escaper->escapeHtml(__("Search")); ?>"
            >
                <span class="sr-only">
                    <?= $escaper->escapeHtml(__("Search")); ?>
                </span>
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 14.202 14.202" class="inline-block h-4 fill-white">
                    <path id="search-regular_1_" data-name="search-regular (1)" d="M14.1,13.006,10.737,9.639a.329.329,0,0,0-.236-.1h-.366a5.768,5.768,0,1,0-.594.594V10.5a.341.341,0,0,0,.1.236L13.006,14.1a.333.333,0,0,0,.472,0l.627-.627A.333.333,0,0,0,14.1,13.006Zm-8.335-2.8a4.438,4.438,0,1,1,4.438-4.438A4.437,4.437,0,0,1,5.769,10.207Z" fill="#fff"/>
                </svg>
            </button>
        </div>

    </form>
</div>

<?= /** @escapeNotVerified */ $block->getChildHtml() ?>
