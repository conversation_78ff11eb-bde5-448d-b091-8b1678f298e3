<?xml version="1.0"?>
<page
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd"
>
    <head>
        <meta name="robots" content="NOINDEX,NOFOLLOW"/>
    </head>
    <body>
        <referenceBlock name="customer-account-navigation-account-link" template="Magento_Customer::account/navigation-item.phtml">
            <arguments>
                <argument name="sortOrder" xsi:type="number">250</argument>
                <argument name="icon" xsi:type="string">dashboard</argument>
                <argument name="path" xsi:type="string">customer/account/index</argument>
            </arguments>
        </referenceBlock>

        <referenceBlock name="customer-account-navigation-orders-link" template="Magento_Customer::account/navigation-item.phtml">
            <arguments>
                <argument name="sortOrder" xsi:type="number">240</argument>
                <argument name="icon" xsi:type="string">my-orders</argument>
            </arguments>
        </referenceBlock>

        <referenceBlock name="customer-account-navigation-address-link" template="Magento_Customer::account/navigation-item.phtml">
            <arguments>
                <argument name="sortOrder" xsi:type="number">230</argument>
                <argument name="icon" xsi:type="string">address-book</argument>
            </arguments>
        </referenceBlock>

        <referenceBlock name="customer-account-navigation-account-edit-link" template="Magento_Customer::account/navigation-item.phtml">
            <arguments>
                <argument name="sortOrder" xsi:type="number">220</argument>
                <argument name="icon" xsi:type="string">person</argument>
            </arguments>
        </referenceBlock>

         <referenceBlock name="customer-account-navigation-newsletter-subscriptions-link" template="Magento_Customer::account/navigation-item.phtml">
            <arguments>
                <argument name="sortOrder" xsi:type="number">210</argument>
                <argument name="icon" xsi:type="string">preferences</argument>
            </arguments>
        </referenceBlock>

        <referenceBlock name="page.main.title" remove="true" />
        <referenceBlock name="customer-account-navigation-downloadable-products-link" remove="true" />
        <referenceBlock name="customer-account-navigation-wish-list-link" remove="true" />
        <referenceBlock name="customer-account-navigation-my-credit-cards-link" remove="true" />
    </body>
</page>
