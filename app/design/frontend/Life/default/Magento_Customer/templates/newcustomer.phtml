<?php

declare(strict_types=1);

use Hyva\Theme\ViewModel\SvgIcons;
use Magento\Customer\Block\Form\Login\Info;
use Magento\Framework\Escaper;

/** @var Info $block */
/** @var Escaper $escaper */
/** @var Hyva\Theme\Model\ViewModelRegistry $viewModels */


/** @var SvgIcons $svgIcons */
$svgIcons = $viewModels->require(SvgIcons::class);
?>
<?php if ($block->getRegistration()->isAllowed()): ?>
    <div class="w-full my-8 block-new-customer md:my-0">
        <div class="flex relative justify-center text-sm before:content-[''] before:w-full before:h-px before:bg-secondary-900/50 before:absolute before:left-0 before:top-1/2 before:transform before:-translate-y-1/2">
            <span class="relative px-2 bg-white z-1"><?= $escaper->escapeHtml(__('New Customers')) ?></span>
        </div>
        <div class="w-full mb-10 actions-toolbar">
            <a href="<?= $escaper->escapeUrl($block->getCreateAccountUrl()) ?>"
               class="justify-center w-full btn btn-secondary"><span><?= $escaper->escapeHtml(__('Create an Account')) ?></span></a>
        </div>
        <div class="block-content" aria-labelledby="block-new-customer-heading">
            <ul class="account-usps">
                <li>
                    <?= $svgIcons->checkHtml("w-4 h-4 flex-shrink-0") ?>
                    <?= $escaper->escapeHtml(__('Account created within 1 minute')) ?>
                </li>
                <li>
                    <?= $svgIcons->checkHtml("w-4 h-4 flex-shrink-0") ?>    
                    <?= $escaper->escapeHtml(__('All your orders clearly organized together')) ?>
                </li>
            </ul>
        </div>
    </div>
<?php endif; ?>
