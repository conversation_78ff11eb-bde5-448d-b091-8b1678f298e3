<?php

declare(strict_types=1);
/** @var \Magento\Framework\View\Element\Template $block */
/** @var \Magento\Framework\Escaper $escaper */

?>
<div class="container pb-12">
    <p>
        <?= $escaper->escapeHtml(__('You have signed out and will be redirected to our homepage in 5 seconds.')) ?>
    </p>
    <p>
        <?= $escaper->escapeHtml(__('Thank you for your visit, we\'re looking forward to see you again soon.')) ?>
    </p>
</div>
<script defer="defer">
    setTimeout(function() {
        location.href = "<?= $escaper->escapeJs($escaper->escapeUrl($block->getUrl())) ?>"
    }, 5000);
</script>
