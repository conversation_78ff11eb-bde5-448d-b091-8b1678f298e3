<?php

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\Customer\LoginButton;
use Hyva\Theme\ViewModel\HeroiconsOutline;
use Hyva\Theme\ViewModel\HeroiconsSolid;
use Hyva\Theme\ViewModel\ReCaptcha;
use Magento\Customer\Block\Form\Login;
use Magento\Framework\Escaper;

/** @var Escaper $escaper */
/** @var Login $block */
/** @var LoginButton $loginButtonViewModel */
/** @var ViewModelRegistry $viewModels */
/** @var ReCaptcha $recaptcha */
/** @var HeroiconsOutline $heroiconsoutline */
/** @var HeroiconsSolid $heroiconssolid */

$heroiconsoutline = $viewModels->require(HeroiconsOutline::class);
$heroiconssolid = $viewModels->require(HeroiconsSolid::class);
$loginButtonViewModel = $viewModels->require(LoginButton::class);

// Disabling autocomplete on form fields is not recommended,
// Enable it in Stores->Configuration->Customers configuration->Password Options

// Do not replace this with $viewModels->require(ReCaptcha::class); that might break the dependency
// on the Magento_ReCaptchaCustomer module
$recaptcha = $block->getData('viewModelRecaptcha');
?>
<div class="w-[32rem] h-[64rem] -scale-x-100 rotate-[32deg] bg-style8 bg-contain bg-no-repeat absolute -top-36 -left-36 -z-1 opacity-5 blur-[6px]"></div>
<div class="w-full mr-4">
    <div aria-labelledby="block-customer-login-heading">
        <form class="form form-login"
              action="<?= $escaper->escapeUrl($block->getPostActionUrl()) ?>"
              method="post"
              x-data="initCustomerLoginForm()"
              @submit.prevent="submitForm()"
              id="customer-login-form">
            <?= $block->getBlockHtml('formkey') ?>
            <fieldset class="fieldset login">
                <legend class="mb-4 md:mb-10">
                    <h2 class="heading-4">
                        <?= $escaper->escapeHtml(__('Login')) ?>
                    </h2>
                </legend>
                <div class="field email required">
                    <label class="label" for="email"><span><?= $escaper->escapeHtml(__('Email')) ?></span></label>
                    <div class="control">
                        <input name="login[username]"
                               class="form-input"
                               required
                               value="<?= $escaper->escapeHtmlAttr($block->getUsername()) ?>"
                               autocomplete="<?= $block->isAutocompleteDisabled() ? 'off' : 'email' ?>"
                               id="email"
                               type="email"
                               title="<?= $escaper->escapeHtmlAttr(__('Email')) ?>"
                        />
                    </div>
                </div>
                <div class="field password required">
                    <label for="pass" class="label">
                        <span>
                            <?= $escaper->escapeHtml(__('Password')) ?>
                        </span>
                    </label>
                    <div class="flex items-center control">
                        <div class="sr-only" aria-live="polite">
                            <template x-if="!showPassword">
                                <span>
                                    <?= $escaper->escapeHtml(__('Password hidden')) ?>
                                </span>
                            </template>
                            <template x-if="showPassword">
                                <span>
                                    <?= $escaper->escapeHtml(__('Password shown')) ?>
                                </span>
                            </template>
                        </div>
                        <input name="login[password]"
                            class="form-input"
                            required
                            :type="showPassword ? 'text' : 'password'"
                            autocomplete="<?= $block->isAutocompleteDisabled() ? 'off' : 'current-password' ?>"
                            id="pass"
                            title="<?= $escaper->escapeHtmlAttr(__('Password')) ?>"
                        />
                        <button
                            type="button"
                            x-on:click="showPassword = !showPassword"
                            :aria-pressed="showPassword ? 'true' : 'false'"
                            class="px-4 py-3"
                            :aria-label="showPassword ? '<?= $escaper->escapeJs(__('Hide Password')) ?>' : '<?= $escaper->escapeJs(__('Show Password')) ?>'"
                        >
                            <template x-if="!showPassword">
                                <?= $heroiconssolid->eyeHtml('w-5 h-5'); ?>
                            </template>
                            <template x-if="showPassword">
                                <?= $heroiconssolid->eyeOffHtml('w-5 h-5'); ?>
                            </template>
                        </button>
                    </div>
                </div>

                <?= $recaptcha ? $recaptcha->getInputHtml(ReCaptcha::RECAPTCHA_FORM_ID_CUSTOMER_LOGIN) : '' ?>
                <?= $block->getChildHtml('form_additional_info') ?>
                <div class="flex flex-col items-start mb-4 text-sm md:mb-10 gap-y-4">
                    <button type="submit" class="justify-center w-full btn btn-primary disabled:opacity-75" name="send"
                        <?php if ($loginButtonViewModel->disabled()): ?> disabled="disabled" data-recaptcha-btn<?php endif; ?>
                    >
                        <span><?= $escaper->escapeHtml(__('Sign In')) ?></span></button>
                    <a class="text-link" href="<?= $escaper->escapeUrl(
                        $block->getForgotPasswordUrl()
                    ) ?>"><span><?= $escaper->escapeHtml(__('Forgot Your Password?')) ?></span>
                    </a>
                </div>
                <div>
                    <template x-if="displayErrorMessage">
                        <p class="flex items-center text-red">
                            <span class="inline-block w-8 h-8 mr-3">
                                <?= $heroiconsoutline->exclamationCircleHtml(); ?>
                            </span>
                            <template x-for="errorMessage in errorMessages">
                                <span x-html="errorMessage"></span>
                            </template>
                        </p>
                    </template>
                </div>
            </fieldset>
        </form>
    </div>
    <div class="w-full">
        <?= $recaptcha ? $recaptcha->getLegalNoticeHtml(ReCaptcha::RECAPTCHA_FORM_ID_CUSTOMER_LOGIN) : '' ?>
    </div>
    <script defer="defer">
        function initCustomerLoginForm() {
            return {
                errors: 0,
                hasCaptchaToken: 0,
                showPassword: false,
                displayErrorMessage: false,
                errorMessages: [],
                setErrorMessages(messages) {
                    this.errorMessages = [messages]
                    this.displayErrorMessage = this.errorMessages.length
                },
                submitForm() {
                    // do not rename $form, the variable is the expected name in the recaptcha output
                    const $form = document.querySelector('#customer-login-form');
                    <?= $recaptcha ? $recaptcha->getValidationJsHtml(ReCaptcha::RECAPTCHA_FORM_ID_CUSTOMER_LOGIN) : '' ?>

                    if (this.errors === 0) {
                        $form.submit();
                    }
                }
            }
        }
    </script>
</div>
