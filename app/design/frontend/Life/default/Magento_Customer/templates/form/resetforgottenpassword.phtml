<?php

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsSolid;
use Magento\Customer\Block\Account\Resetpassword;
use Magento\Framework\Escaper;

/** @var Resetpassword $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */

$heroicons = $viewModels->require(HeroiconsSolid::class);
$minimumPasswordLength = $block->getMinimumPasswordLength();
$passwordMinCharacterSets = $block->getRequiredCharacterClassesNumber();
?>
<div class="w-[32rem] h-[64rem] -scale-x-100 rotate-[32deg] bg-style8 bg-contain bg-no-repeat absolute -top-36 -left-36 -z-1 opacity-5 blur-[6px]"></div>
<div class="block max-w-xl mx-auto card lg:p-8">
    <h2 class="mb-4 heading-4">
        <?= $escaper->escapeHtml(__('Set a New Password')) ?>
    </h2>
    <form action="<?= $escaper->escapeUrl(
        $block->getUrl(
            '*/*/resetpasswordpost',
            ['_query' => ['id' => $block->getRpCustomerId(), 'token' => $block->getResetPasswordLinkToken()]]
        )
    ) ?>"
            x-data="Object.assign(hyva.formValidation($el), {showPassword: false, showPasswordConfirm: false})"
            @submit="onSubmit"
            method="post"
        <?php if ($block->isAutocompleteDisabled()): ?> autocomplete="off"<?php endif; ?>
            id="form-validate"
            class="form password reset">
        <fieldset class="fieldset">
            <?= $block->getBlockHtml('formkey'); ?>
            <div class="field field-reserved">
                <label class="label" for="password"><span><?= $escaper->escapeHtml(__('New Password')) ?></span></label>
                <div class="flex items-center control">
                    <input :type="showPassword ? 'text' : 'password'" type="password"
                            class="form-input" name="password" id="password" required
                            minlength="<?= $escaper->escapeHtmlAttr($minimumPasswordLength) ?>"
                            @input="onChange"
                            data-validate='{"password-strength": {"minCharacterSets": <?= (int) $passwordMinCharacterSets ?>}}'
                            autocomplete="off"/>
                    <div x-on:click="showPassword = !showPassword"
                            class="px-4 cursor-pointer"
                            :aria-label="showPassword ? '<?= $escaper->escapeJs(__('Hide Password')) ?>' : '<?= $escaper->escapeJs(__('Show Password')) ?>'"
                    >
                        <template x-if="!showPassword">
                            <?= $heroicons->eyeHtml('w-5 h-5'); ?>
                        </template>
                        <template x-if="showPassword">
                            <?= $heroicons->eyeOffHtml('w-5 h-5'); ?>
                        </template>
                    </div>
                </div>
            </div>
            <div class="field field-reserved">
                <label class="label" for="password-confirmation">
                    <span><?= $escaper->escapeHtml(__('Confirm New Password')) ?></span>
                </label>
                <div class="flex items-center control">
                    <input :type="showPasswordConfirm ? 'text' : 'password'" type="password"
                            class="form-input" name="password_confirmation"
                            id="password-confirmation"
                            data-validate='{"equalTo": "password"}'
                            @input="onChange"
                            autocomplete="off"/>
                    <div x-on:click="showPasswordConfirm = !showPasswordConfirm"
                            class="px-4 cursor-pointer"
                            :aria-label="showPasswordConfirm ? '<?= $escaper->escapeJs(__('Hide Password')) ?>' : '<?= $escaper->escapeJs(__('Show Password')) ?>'"
                    >
                        <template x-if="!showPasswordConfirm">
                            <?= $heroicons->eyeHtml('w-5 h-5'); ?>
                        </template>
                        <template x-if="showPasswordConfirm">
                            <?= $heroicons->eyeOffHtml('w-5 h-5'); ?>
                        </template>
                    </div>
                </div>
            </div>
        </fieldset>
        <button type="submit" class="justify-center w-full mt-4 action submit btn btn-primary"><span><?= $escaper->escapeHtml(__('Set a New Password')) ?></span></button>
    </form>
</div>
