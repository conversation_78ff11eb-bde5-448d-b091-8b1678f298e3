<?php

declare(strict_types=1);

use Hyva\Theme\ViewModel\SvgIcons;

/** @var \Magento\Customer\Block\Newsletter $block */
/** @var \Magento\Framework\Escaper $escaper */
/** @var \Hyva\Theme\Model\ViewModelRegistry $viewModels */

/** @var SvgIcons $svgIcons */
$svgIcons = $viewModels->require(SvgIcons::class);
?>
<?= $block->getChildHtml('form_before') ?>
    <form class="p-4 bg-white form form-newsletter-manage rounded-xl"
          action="<?= $escaper->escapeUrl($block->getAction()) ?>"
          method="post"
          id="form-validate">
        <fieldset class="fieldset">
            <?= $block->getBlockHtml('formkey') ?>
            <div class="mb-6 text-2xl">
                <span>
                    <?= $escaper->escapeHtml(__('Subscription option')) ?>
                </span>
            </div>
            <div class="field choice">
                <input type="checkbox" name="is_subscribed" id="subscription" value="1"
                       title="<?= $escaper->escapeHtmlAttr(__('General Subscription')) ?>"
                        <?php if ($block->getIsSubscribed()): ?> checked="checked"<?php endif; ?>
                       class="checkbox" />
                <label for="subscription" class="label">
                    <span>
                        <?= $escaper->escapeHtml(__('General Subscription')) ?>
                    </span>
                </label>
            </div>
            <?php /* Extensions placeholder */ ?>
            <?= $block->getChildHtml('customer.form.newsletter.extra') ?>
        </fieldset>
        <div class="actions-toolbar">
            <a class="flex items-center text-link after:hidden before:hover:delay-0 gap-x-2" href="<?= $escaper->escapeUrl($block->getBackUrl()) ?>">
                <?= $svgIcons->chevronLeftHtml("w-4 h-4") ?>
                <?= $escaper->escapeHtml(__('Back')) ?>
            </a>

            <button type="submit" class="block w-full text-center btn btn-primary" title="<?= $escaper->escapeHtmlAttr(__('Save')) ?>">
                <span><?= $escaper->escapeHtml(__('Save')) ?></span>
            </button>
        </div>
    </form>
<?php /* Extensions placeholder */ ?>
<?= $block->getChildHtml('customer.form.newsletter.extra2') ?>
