<?php

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\Customer\ForgotPasswordButton;
use Hyva\Theme\ViewModel\ReCaptcha;
use Hyva\Theme\ViewModel\SvgIcons;
use Magento\Customer\Block\Account\Forgotpassword;
use Magento\Framework\Escaper;

/** @var Forgotpassword $block */
/** @var Escaper $escaper */
/** @var ReCaptcha $recaptcha */
/** @var ForgotPasswordButton $forgotPasswordButtonViewModel */
/** @var ViewModelRegistry $viewModels */

$forgotPasswordButtonViewModel = $viewModels->require(ForgotPasswordButton::class);

$formId = 'user_forgotpassword';

// Do not replace this with $viewModels->require(ReCaptcha::class); that might break the dependency
// on the Magento_ReCaptchaCustomer module
$recaptcha = $block->getData('viewModelRecaptcha');

/** @var SvgIcons $svgIcons */
$svgIcons = $viewModels->require(SvgIcons::class);
?>
<div class="w-[32rem] h-[64rem] -scale-x-100 rotate-[32deg] bg-style8 bg-contain bg-no-repeat absolute -top-36 -left-36 -z-1 opacity-5 blur-[6px]"></div>
<div class="block max-w-xl mx-auto card lg:p-8">
    <h2 class="mb-4 heading-4">
        <?= $escaper->escapeHtml(__('Forgot Your Password?')) ?>
    </h2>
    <form action="<?= $escaper->escapeUrl($block->getUrl('*/*/forgotpasswordpost')) ?>"
            method="post"
            id="<?= $escaper->escapeHtmlAttr($formId) ?>"
            x-data="initPasswordForm()"
            @submit.prevent="submitForm();">
        <?= $block->getBlockHtml('formkey') ?>
        <input type="hidden" name="formId" value="<?= $escaper->escapeHtmlAttr($formId) ?>"/>
        <?= $block->getChildHtml('form_fields_before') ?>
        <?= $recaptcha ? $recaptcha->getInputHtml(ReCaptcha::RECAPTCHA_FORM_ID_CUSTOMER_FORGOT_PASSWORD) : '' ?>
        <fieldset class="fieldset">
            <div class="mb-4 text-sm md:mb-10"><?= $escaper->escapeHtml(
                __('Please enter your email address below to receive a password reset link.')
            ) ?></div>
            <div class="field email required">
                <label for="email_address" class="label"><span><?= $escaper->escapeHtml(
                    __('Email')
                ) ?></span></label>
                <div class="control">
                    <input type="email" name="email" alt="email" id="email_address" class="form-input" required
                            value="<?= $escaper->escapeHtmlAttr($block->getEmailValue()) ?>">
                </div>
            </div>
            <?= $block->getChildHtml('form_additional_info') ?>
            <?= $recaptcha ? $recaptcha->getLegalNoticeHtml(ReCaptcha::RECAPTCHA_FORM_ID_CUSTOMER_FORGOT_PASSWORD) : '' ?>
        </fieldset>
        <div class="flex flex-col items-center w-full gap-y-4">
            <button
                type="submit"
                class="block w-full text-center btn btn-primary"
                title="<?= $escaper->escapeHtmlAttr(__('Send')) ?>"
                <?php if ($forgotPasswordButtonViewModel->disabled()): ?> disabled="disabled" data-recaptcha-btn<?php endif; ?>
            >
                <span><?= $escaper->escapeHtml(__('Send')) ?></span>
            </button>
            <a class="flex items-center text-link after:hidden before:hover:delay-0 gap-x-2" href="<?= $escaper->escapeUrl($block->getLoginUrl()) ?>">
                <?= $svgIcons->chevronLeftHtml("w-4 h-4") ?>
                <?= $escaper->escapeHtml(__('Back')) ?>
            </a>
        </div>
    </form>
    <script defer="defer">
        function initPasswordForm() {
            return {
                errors: 0,
                hasCaptchaToken: 0,
                submitForm() {
                    // Do not rename $form, the variable is expected to be declared in the recaptcha output
                    const $form = document.querySelector('#<?= $escaper->escapeJs($formId) ?>');
                    <?= $recaptcha ? $recaptcha->getValidationJsHtml(ReCaptcha::RECAPTCHA_FORM_ID_CUSTOMER_FORGOT_PASSWORD) : '' ?>

                    if (this.errors === 0) {
                        $form.submit();
                    }
                }
            }
        }
    </script>
</div>
