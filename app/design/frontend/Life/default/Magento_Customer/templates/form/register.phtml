<?php

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\Customer\CreateAccountButton;
use Hyva\Theme\ViewModel\HeroiconsSolid;
use Hyva\Theme\ViewModel\SvgIcons;
use Hyva\Theme\ViewModel\ReCaptcha;
use Magento\Customer\Block\Form\Register;
use Magento\Customer\Block\Widget\Name as NameWidget;
use Magento\Framework\Escaper;

// phpcs:disable Magento2.Templates.ThisInTemplate.FoundThis
// phpcs:disable Magento2.Templates.ThisInTemplate.FoundHelper
// phpcs:disable Generic.Files.LineLength.TooLong

/** @var Register $block */
/** @var Escaper $escaper */
/** @var ReCaptcha $recaptcha */
/** @var CreateAccountButton $createAccountButtonViewModel */
/** @var ViewModelRegistry $viewModels */
/** @var HeroiconsSolid $heroicons */

$formId = 'accountcreate';

// Do not replace this with $viewModels->require(ReCaptcha::class); that might break the dependency
// on the Magento_ReCaptchaCustomer module
$recaptcha = $block->getData('viewModelRecaptcha');
$heroicons = $viewModels->require(HeroiconsSolid::class);
$createAccountButtonViewModel = $viewModels->require(CreateAccountButton::class);
$region = $block->getAttributeData()->getFrontendLabel('region');
$selectRegion = 'Please select a region, state or province.';
$showOptionalRegions = $block->getConfig('general/region/display_all');
$regionLabel = $block->getAttributeData()->getFrontendLabel('region');
$minimumPasswordLength = $block->getMinimumPasswordLength();
$passwordMinCharacterSets = $block->getRequiredCharacterClassesNumber();

/** @var SvgIcons $svgIcons */
$svgIcons = $viewModels->require(SvgIcons::class);
?>
<div class="w-[32rem] h-[64rem] -scale-x-100 rotate-[32deg] bg-style8 bg-contain bg-no-repeat absolute -top-36 -left-36 -z-1 opacity-5 blur-[6px]"></div>
<div class="block max-w-xl mx-auto card lg:p-8">
    <h2 class="mb-4 heading-4">
        <?= $escaper->escapeHtml(__('Create account')) ?>
    </h2>
    <?php /* Extensions placeholder */ ?>
    <?= $block->getChildHtml('customer.form.register.extra') ?>
    <form class="form create account form-create-account"
          action="<?= $escaper->escapeUrl($block->getPostActionUrl()) ?>"
          x-data="Object.assign(hyva.formValidation($el), initForm())"
          <?php if ($block->getShowAddressFields()): ?>
          @private-content-loaded.window="onPrivateContentLoaded(event.detail.data)"
          <?php endif; ?>
          id="<?= $escaper->escapeHtmlAttr($formId) ?>"
          @submit.prevent="submitForm()"
          method="post" id="form-validate" enctype="multipart/form-data" autocomplete="off"
    >
        <?= /* @noEscape */ $block->getBlockHtml('formkey'); ?>
        <?= $block->getChildHtml('form_fields_before') ?>
        <?= $recaptcha ? $recaptcha->getInputHtml(ReCaptcha::RECAPTCHA_FORM_ID_CUSTOMER_CREATE) : '' ?>
        <fieldset>
            <input type="hidden" name="success_url" value="<?= $escaper->escapeUrl($block->getSuccessUrl()) ?>">
            <input type="hidden" name="error_url" value="<?= $escaper->escapeUrl($block->getErrorUrl()) ?>">
            <div class="field field-reserved required">
                <label for="email_address" class="label">
                    <span>
                        <?= $escaper->escapeHtml(__('Email')) ?>
                    </span>
                </label>
                <div class="control">
                    <input
                        type="email"
                        name="email"
                        autocomplete="email"
                        id="email_address"
                        required
                        value="<?= $escaper->escapeHtmlAttr($block->getFormData()->getEmail()) ?>"
                        title="<?= $escaper->escapeHtmlAttr(__('Email')) ?>"
                        class="form-input"
                        @input.debounce="onChange"
                    />
                </div>
            </div>

            <div class="grid-cols-2 md:grid gap-x-4">
                <?= $block->getLayout()->createBlock(NameWidget::class)->setObject($block->getFormData())->setForceUseCustomerAttributes(true)->toHtml() ?>
            </div>

            <div class="field field-reserved">
                <label for="password" class="label">
                    <span>
                        <?= $escaper->escapeHtml(__('Password')) ?>
                    </span>
                </label>
                <div class="flex flex-wrap items-center control">
                    <?php $minimumPasswordLength = $block->getMinimumPasswordLength() ?>
                    <div class="sr-only" aria-live="polite">
                        <template x-if="!showPassword">
                            <span>
                                <?= $escaper->escapeHtml(__('Password hidden')) ?>
                            </span>
                        </template>
                        <template x-if="showPassword">
                            <span>
                                <?= $escaper->escapeHtml(__('Password shown')) ?>
                            </span>
                        </template>
                    </div>
                    <div class="flex w-full">
                        <input
                            :type="showPassword ? 'text' : 'password'"
                            type="password"
                            id="password"
                            name="password"
                            title="<?= $escaper->escapeHtmlAttr(__('Password')) ?>"
                            minlength="<?= $escaper->escapeHtmlAttr($minimumPasswordLength) ?>"
                            class="!w-auto form-input flex-grow"
                            required
                            data-validate='{"password-strength": {"minCharacterSets": <?= (int) $passwordMinCharacterSets ?>}}'
                            @input.debounce="onChange"
                            autocomplete="off"
                        >
                        <button
                            type="button"
                            :aria-pressed="showPassword ? true : false"
                            x-on:click="showPassword = !showPassword"
                            class="px-4 py-3"
                            :aria-label="showPassword ? '<?= $escaper->escapeJs(__('Hide Password')) ?>' : '<?= $escaper->escapeJs(__('Show Password')) ?>'"
                        >
                            <template x-if="!showPassword">
                                <?= $heroicons->eyeHtml('w-5 h-5'); ?>
                            </template>
                            <template x-if="showPassword">
                                <?= $heroicons->eyeOffHtml('w-5 h-5'); ?>
                            </template>
                        </button>
                    </div>
                    <div
                        id="password-strength-meter-container"
                        data-role="password-strength-meter"
                        aria-live="polite"
                    >
                        <div id="password-strength-meter" class="password-strength-meter">
                            <?= $escaper->escapeHtml(__('Password Strength')) ?>:
                            <span id="password-strength-meter-label" data-role="password-strength-meter-label">
                                <?= $escaper->escapeHtml(__('No Password')) ?>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="field field-reserved">
                <label for="password-confirmation" class="label">
                    <span>
                        <?= $escaper->escapeHtml(__('Confirm Password')) ?>
                    </span>
                </label>
                <div class="flex items-center control">
                    <div class="sr-only" aria-live="polite">
                        <template x-if="!showPasswordConfirm">
                            <span>
                                <?= $escaper->escapeHtml(__('Confirm password hidden')) ?>
                            </span>
                        </template>
                        <template x-if="showPasswordConfirm">
                            <span>
                                <?= $escaper->escapeHtml(__('confirm password shown')) ?>
                            </span>
                        </template>
                    </div>
                    <input
                        :type="showPasswordConfirm ? 'text' : 'password'"
                        type="password"
                        name="password_confirmation"
                        title="<?= $escaper->escapeHtmlAttr(__('Confirm Password')) ?>"
                        id="password-confirmation"
                        data-validate='{"equalTo": "password"}'
                        @input.debounce="onChange"
                        required
                        class="form-input"
                        autocomplete="off"
                    >
                    <button
                        type="button"
                        x-on:click="showPasswordConfirm = !showPasswordConfirm"
                        :aria-pressed="showPasswordConfirm ? true : false"
                        class="px-4 py-3"
                        :aria-label="
                            showPasswordConfirm ? '<?= $escaper->escapeJs(__('Hide Password')) ?>' : '<?= $escaper->escapeJs(__('Show Password')) ?>'
                        "
                    >
                        <template x-if="!showPasswordConfirm">
                            <?= $heroicons->eyeHtml('w-5 h-5'); ?>
                        </template>
                        <template x-if="showPasswordConfirm">
                            <?= $heroicons->eyeOffHtml('w-5 h-5'); ?>
                        </template>
                    </button>
                </div>
            </div>

            <?php if ($block->isNewsletterEnabled()): ?>
                <div class="field choice newsletter">
                    <input type="checkbox" name="is_subscribed"
                            title="<?= $escaper->escapeHtmlAttr(__('Sign Up for Newsletter')) ?>" value="1"
                            id="is_subscribed"
                            <?php if ($block->getFormData()->getIsSubscribed()): ?>
                                checked="checked"
                            <?php endif; ?>
                            class="checkbox">
                    <label for="is_subscribed" class="label">
                        <span>
                            <?= $escaper->escapeHtml(__('Keep me informed of the latest deals and outdoor living trends')) ?>
                        </span>
                    </label>
                </div>
                <?php /* Extensions placeholder */ ?>
                <?= $block->getChildHtml('customer.form.register.newsletter') ?>
            <?php endif ?>

            <?= $recaptcha ? $recaptcha->getLegalNoticeHtml(ReCaptcha::RECAPTCHA_FORM_ID_CUSTOMER_CREATE) : '' ?>
        </fieldset>
        <div class="flex flex-col items-center w-full gap-y-4">
            <button
                type="submit"
                class="block w-full text-center btn btn-primary"
                title="<?= $escaper->escapeHtmlAttr(__('Create an Account')) ?>"
                <?php if ($createAccountButtonViewModel->disabled()): ?> disabled="disabled" data-recaptcha-btn<?php endif; ?>
            >
                <span><?= $escaper->escapeHtml(__('Create an Account')) ?></span>
            </button>
            <a class="flex items-center text-link after:hidden before:hover:delay-0 gap-x-2" href="<?= $escaper->escapeUrl($block->getBackUrl()) ?>">
                <?= $svgIcons->chevronLeftHtml("w-4 h-4") ?>
                <?= $escaper->escapeHtml(__('Back')) ?>
            </a>
        </div>
    </form>

    <script defer="defer">
        function initForm() {
            return {
                errors: 0,
                hasCaptchaToken: 0,
                showPassword: false,
                showPasswordConfirm: false,
                submitForm() {
                    this.validate()
                        .then(() => {
                            // Do not rename $form, the variable is expected to be declared in the recaptcha output
                            const $form = document.querySelector('#<?= $escaper->escapeJs($formId) ?>');
                            <?= $recaptcha ? $recaptcha->getValidationJsHtml(ReCaptcha::RECAPTCHA_FORM_ID_CUSTOMER_CREATE) : '' ?>

                            if (this.errors === 0) {
                                $form.submit();
                            }
                        })
                        .catch((invalid) => {
                            if (invalid.length > 0) {
                                invalid[0].focus();
                            }
                        });
                },
                <?php if ($block->getShowAddressFields()): ?>
                directoryData: {},
                availableRegions: {},
                selectedRegion: <?= $escaper->escapeJs($block->getRegion() ?: 0) ?>,
                isZipRequired: true,
                isRegionRequired: true,
                showOptionalRegions: <?= $showOptionalRegions ? 'true' : 'false' ?>,
                onPrivateContentLoaded(data) {
                    this.directoryData = data['directory-data'] || {};

                    <?php if ($block->getCountryId()): ?>
                    this.setCountry(this.$refs['country_id'], '<?= $escaper->escapeJs($block->getRegion()) ?>');
                    <?php endif; ?>

                },
                setRegionInputValue(regionName) {
                    this.$nextTick(() => {
                        const regionInputElement = this.$refs['region'];
                        if (regionInputElement) {
                            regionInputElement.value = regionName;
                        }
                    })
                },
                setCountry(countrySelectElement, initialRegion) {
                    const selectedOption = countrySelectElement.options[countrySelectElement.selectedIndex];
                    const countryCode = countrySelectElement.value;
                    const countryData = this.directoryData[countryCode] || false;

                    if (!countryData) {
                        this.setRegionInputValue('');
                        return;
                    }

                    this.isZipRequired = selectedOption.dataset.isZipRequired === '1';
                    this.isRegionRequired = selectedOption.dataset.isRegionRequired === '1';

                    this.availableRegions = countryData.regions || {};

                    const initialRegionId = Object.keys(this.availableRegions).filter(regionId => this.availableRegions[regionId].name === initialRegion)[0];
                    this.selectedRegion = initialRegionId || '0';
                    this.setRegionInputValue(initialRegionId && this.availableRegions[initialRegionId].name || '');

                },
                changeCountry(countrySelectElement, initialRegion) {
                    this.setCountry(countrySelectElement, initialRegion);

                    this.validateCountryDependentFields();
                },
                validateCountryDependentFields() {
                    this.$nextTick(() => {
                        <?php /* Reinitialize validation rules for fields that depend on the country */ ?>
                        this.fields['postcode'] && this.removeMessages(this.fields['postcode'])
                        this.fields['region'] && this.removeMessages(this.fields['region'])
                        delete this.fields['postcode'];
                        delete this.fields['region'];
                        <?php /* Initialize country_id, too, because the postcode validation depends on it */ ?>
                        this.setupField(this.$refs['country_id']);
                        this.setupField(this.$refs['postcode']);
                        this.setupField(this.$refs['region']);

                        this.fields['postcode'] && this.validateField(this.fields['postcode']);
                        this.fields['region'] && this.validateField(this.fields['region']);
                    })
                },
                hasAvailableRegions() {
                    return Object.keys(this.availableRegions).length > 0;
                }
                <?php endif; ?>
            }
        }

        window.addEventListener('DOMContentLoaded', () => {

            hyva.formValidation.addRule('telephone', (value, options) => {
                const phoneNumber = value.trim().replace(' ', '');
                if (phoneNumber && phoneNumber.length < (options.minlength || 3)) {
                    return '<?= $escaper->escapeJs(__('The telephone number is too short.')) ?>';
                }

                return true;
            });

            const postCodeSpecs = <?= /* @noEscape */ $block->getPostCodeConfig()->getSerializedPostCodes() ?>;

            hyva.formValidation.addRule('postcode', (postCode, options, field, context) => {
                context.removeMessages(field, 'postcode-warning')
                const countryId = (context.fields['country_id'] && context.fields['country_id'].element.value),
                    validatedPostCodeExamples = [],
                    countryPostCodeSpecs = countryId && postCodeSpecs ? postCodeSpecs[countryId] : false;

                if (! postCode || ! countryPostCodeSpecs) return true;

                for (const postCodeSpec of Object.values(countryPostCodeSpecs)) {
                    if (new RegExp(postCodeSpec.pattern).test(postCode)) return true;
                    validatedPostCodeExamples.push(postCodeSpec.example);
                }
                if (validatedPostCodeExamples) {
                    context.addMessages(field, 'postcode-warning', [
                        '<?= $escaper->escapeJs(__('Provided Zip/Postal Code seems to be invalid.')) ?>'
                    ]);
                    // A message should always be provided to prevent showing a generic error message, and form submission
                    return '<?= $escaper->escapeJs(__(' Example: ')) ?>' + validatedPostCodeExamples.join('; ') + '. ';
                }

                return true;
            });
        })
    </script>
</div>
