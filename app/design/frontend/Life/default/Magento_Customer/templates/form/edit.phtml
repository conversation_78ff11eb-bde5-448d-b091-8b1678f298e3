<?php

declare(strict_types=1);

use Hyva\Theme\ViewModel\HeroiconsSolid;
use Hyva\Theme\ViewModel\SvgIcons;
use Hyva\Theme\ViewModel\ReCaptcha;
use Magento\Customer\Block\Widget\Name;

/** @var \Magento\Customer\Block\Form\Edit $block */
/** @var \Magento\Framework\Escaper $escaper */
/** @var \Hyva\Theme\Model\ViewModelRegistry $viewModels */

// Do not replace this with $viewModels->require(ReCaptcha::class); that might break the dependency
// on the Magento_ReCaptchaCustomer module
/** @var ReCaptcha|null $recaptcha */
$recaptcha = $block->getData('viewModelRecaptcha');

/** @var HeroiconsSolid $heroicons */
$heroicons = $viewModels->require(HeroiconsSolid::class);
$minimumPasswordLength = $block->getMinimumPasswordLength();
$passwordMinCharacterSets = $block->getRequiredCharacterClassesNumber();

/** @var SvgIcons $svgIcons */
$svgIcons = $viewModels->require(SvgIcons::class);
?>
<form
    class="p-4 bg-white form form-edit-account rounded-xl"
    action="<?= $escaper->escapeUrl($block->getUrl('customer/account/editPost')) ?>"
    method="post" id="form-validate"
    enctype="multipart/form-data"
    x-data="Object.assign(hyva.formValidation($el), initForm())"
    @submit.prevent="submitForm"
    autocomplete="off"
>
    <fieldset class="fieldset info">
        <?= $block->getBlockHtml('formkey') ?>
        <legend class="legend"><span><?= $escaper->escapeHtml(__('Account Information')) ?></span></legend>
        <div class="sm:flex gap-x-6">
            <?= $block->getLayout()->createBlock(Name::class)->setObject($block->getCustomer())->toHtml() ?>
        </div>
        <div class="grid-cols-2 md:grid gap-x-4 gap-y-0">
            <?php $dob = $block->getLayout()->createBlock(\Magento\Customer\Block\Widget\Dob::class) ?>
            <?php $taxvat = $block->getLayout()->createBlock(\Magento\Customer\Block\Widget\Taxvat::class) ?>
            <?php $gender = $block->getLayout()->createBlock(\Magento\Customer\Block\Widget\Gender::class) ?>
            <?php if ($dob->isEnabled()): ?>
                <?= $dob->setDate($block->getCustomer()->getDob())->toHtml() ?>
            <?php endif ?>
            <?php if ($taxvat->isEnabled()): ?>
                <?= $taxvat->setTaxvat($block->getCustomer()->getTaxvat())->toHtml() ?>
            <?php endif ?>
            <?php if ($gender->isEnabled()): ?>
                <?= $gender->setGender($block->getCustomer()->getGender())->toHtml() ?>
            <?php endif ?>

            <div
                class="field field-reserved email required"
                id="email-fields"
            >
                <label class="label" for="email"><span><?= $escaper->escapeHtml(__('Email')) ?></span></label>
                <div class="control">
                    <input
                        type="email"
                        name="email"
                        id="email"
                        autocomplete="email"
                        required
                        value="<?= $escaper->escapeHtmlAttr($block->getCustomer()->getEmail()) ?>"
                        title="<?= $escaper->escapeHtmlAttr(__('Email')) ?>"
                        class="form-input"
                    />
                </div>
            </div>
        </div>

        <div class="mt-4 field choice">
            <input
                type="checkbox"
                name="change_password"
                id="change-password"
                value="1"
                title="<?= $escaper->escapeHtmlAttr(__('Change Password')) ?>"
                @change="handleCheckboxChange('password-fields')"
                <?php if ($block->getChangePassword()): ?> checked="checked"<?php endif; ?>
                class="hidden checkbox"
                aria-describedby="change_password_description"
            />
            <label class="flex items-center cursor-pointer label gap-x-2" for="change-password">
                <span class="text-link after:hidden before:hover:delay-0">
                    <?= $escaper->escapeHtml(__('Change Password')) ?>
                </span>
                <template x-if="!showPasswordFields">
                    <?= $svgIcons->chevronRightHtml("w-4 h-4 transform rotate-90") ?>
                </template>
                <template x-if="showPasswordFields">
                    <?= $svgIcons->chevronRightHtml("w-4 h-4 transform -rotate-90") ?>
                </template>
            </label>
            <span
                class="sr-only"
                id="change_password_description"
            >
                <?= $escaper->escapeHtml(__('form appears when field is selected')) ?>
            </span>
        </div>

        <template x-if="showPasswordFields">
            <div class="mt-6">
                <div
                    class="field field-reserved password current required"
                    id="password-fields"
                >
                    <label class="label" for="current-password">
                        <span><?= $escaper->escapeHtml(__('Current Password')) ?></span>
                    </label>
                    <div class="flex items-center control">
                        <?php // Fix for Firefox saved password autofill: add a "hidden" dummy password field ?>
                        <input name="old_password" type="password" style="display: none" />
                        <input
                            :type="showPasswordCurrent ? 'text' : 'password'"
                            type="password"
                            class="form-input"
                            name="current_password"
                            id="current-password"
                            data-input="current-password"
                            required
                            <?php // Fix for Chrome saved password autofill ?>
                            autocomplete="one-time-code"
                            aria-describedby="password-strength-meter"
                        />
                        <button
                            type="button"
                            @click="showPasswordCurrent = !showPasswordCurrent"
                            class="self-stretch px-4"
                            :aria-label="showPasswordCurrent ? '<?= $escaper->escapeJs(__('Hide Password')) ?>' :
                                '<?= $escaper->escapeJs(__('Show Password')) ?>'"
                        >
                            <template x-if="!showPasswordCurrent">
                                <?= $heroicons->eyeHtml('w-5 h-5'); ?>
                            </template>

                            <template x-if="showPasswordCurrent">
                                <?= $heroicons->eyeOffHtml('w-5 h-5'); ?>
                            </template>
                        </button>
                    </div>
                </div>

                <div class="sm:flex gap-x-6">
                    <div class="w-full field field-reserved">
                        <label class="label" for="password">
                            <span><?= $escaper->escapeHtml(__('New Password')) ?></span>
                        </label>
                        <div class="flex items-center control">
                            <input
                                :type="showPasswordNew ? 'text' : 'password'"
                                type="password"
                                class="form-input"
                                name="password"
                                id="password"
                                required
                                data-validate='{"password-strength": {"minCharacterSets": <?= (int) $passwordMinCharacterSets ?>}}'
                                @input="onChange"
                                minlength="<?= $escaper->escapeHtmlAttr($minimumPasswordLength) ?>"
                                autocomplete="off"
                                aria-describedby="password-strength-meter"
                            />
                            <button
                                type="button"
                                @click="showPasswordNew = !showPasswordNew"
                                class="self-stretch px-4"
                                :aria-label="showPasswordNew ? '<?= $escaper->escapeJs(__('Hide Password')) ?>' :
                                    '<?= $escaper->escapeJs(__('Show Password')) ?>'"
                            >
                                <template x-if="!showPasswordNew">
                                    <?= $heroicons->eyeHtml('w-5 h-5'); ?>
                                </template>
                                <template x-if="showPasswordNew">
                                    <?= $heroicons->eyeOffHtml('w-5 h-5'); ?>
                                </template>
                            </button>
                        </div>
                        <div id="password-strength-meter-container" data-role="password-strength-meter" aria-live="polite">
                            <div id="password-strength-meter" class="mt-2 password-strength-meter">
                                <?= $escaper->escapeHtml(__('Password Strength')) ?>:
                                <span id="password-strength-meter-label" data-role="password-strength-meter-label">
                                    <?= $escaper->escapeHtml(__('No Password')) ?>
                                </span>
                            </div>
                        </div>
                    </div>

                    <div class="w-full field field-reserved">
                        <label class="label" for="password-confirmation">
                            <span><?= $escaper->escapeHtml(__('Confirm New Password')) ?></span>
                        </label>
                        <div class="flex items-center control">
                            <input :type="showPasswordConfirm ? 'text' : 'password'" type="password" class="form-input"
                                name="password_confirmation" id="password-confirmation"
                                data-validate='{"equalTo": "password"}'
                                @input="onChange"
                                autocomplete="off"
                                aria-describedby="password-strength-meter"
                            />
                            <button
                                type="button"
                                @click="showPasswordConfirm = !showPasswordConfirm"
                                class="self-stretch px-4"
                                :aria-label="showPasswordConfirm ? '<?= $escaper->escapeJs(__('Hide Password')) ?>' :
                                    '<?= $escaper->escapeJs(__('Show Password')) ?>'"
                            >
                                <template x-if="!showPasswordConfirm">
                                    <?= $heroicons->eyeHtml('w-5 h-5'); ?>
                                </template>
                                <template x-if="showPasswordConfirm">
                                    <?= $heroicons->eyeOffHtml('w-5 h-5'); ?>
                                </template>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </template>
    </fieldset>
    <?= $recaptcha ? $recaptcha->getInputHtml(ReCaptcha::RECAPTCHA_FORM_ID_CUSTOMER_EDIT) : '' ?>
    <div class="actions-toolbar">
        <a
            class="flex items-center text-link after:hidden before:hover:delay-0 gap-x-2"
            href="<?= $escaper->escapeUrl($block->getBackUrl()) ?>"
        >
            <?= $svgIcons->chevronLeftHtml("w-4 h-4") ?>
            <?= $escaper->escapeHtml(__('Back')) ?>
        </a>

        <button
            type="submit"
            class="block w-full text-center btn btn-primary"
            title="<?= $escaper->escapeHtmlAttr(__('Save')) ?>"
        >
            <span><?= $escaper->escapeHtml(__('Save')) ?></span>
        </button>
    </div>
</form>
<div class="w-full">
    <?= $recaptcha ? $recaptcha->getLegalNoticeHtml(ReCaptcha::RECAPTCHA_FORM_ID_CUSTOMER_EDIT) : '' ?>
</div>
<script defer="defer">
    function initForm() {
        return {
            errors: 0,
            hasCaptchaToken: 0,
            displayErrorMessage: false,
            errorMessages: [],
            showPasswordNew: false,
            showPasswordConfirm: false,
            showPasswordCurrent: false,
            showPasswordFields: <?= $block->getChangePassword() ?
                $escaper->escapeJs('true') :
                $escaper->escapeJs('false') ?>,
            setErrorMessages(messages) {
                this.errorMessages = [messages]
                this.displayErrorMessage = this.errorMessages.length
            },
            submitForm() {
                this.validate()
                    .then(() => {
                        // Do not rename $form, the variable is expected to be declared in the recaptcha output
                        const $form = document.querySelector('#form-validate');
                        <?= $recaptcha ? $recaptcha->getValidationJsHtml(ReCaptcha::RECAPTCHA_FORM_ID_CUSTOMER_EDIT) : '' ?>

                        if (this.errors === 0) {
                            $form.submit();
                        }
                    })
                    .catch((invalid) => {
                        if (invalid.length > 0) {
                            invalid[0].focus();
                        }
                    })
            },
            handleCheckboxChange(checkboxId) {
                if(checkboxId ==='password-fields') {
                    this.showPasswordFields = !this.showPasswordFields
                }

                this.$nextTick(() => {
                    const firstFocusableElement = document.querySelector(`
                        #${checkboxId} input,
                        #${checkboxId} textarea,
                        #${checkboxId} select
                    `)

                    firstFocusableElement && firstFocusableElement.focus()
                })
            }
        }
    }
</script>
