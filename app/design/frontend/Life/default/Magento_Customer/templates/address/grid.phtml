<?php

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsOutline;
use Hyva\Theme\ViewModel\SvgIcons;
use Magento\Customer\Block\Address\Grid;
use Magento\Framework\Escaper;

/** @var Grid $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */

/** @var HeroiconsOutline $heroicons */
$heroicons = $viewModels->require(HeroiconsOutline::class);

/** @var SvgIcons $svgIcons */
$svgIcons = $viewModels->require(SvgIcons::class);
?>

<div class="block p-4 bg-white block-addresses-list rounded-xl">
    <div class="mb-6 text-xl leading-9 block-title">
<?= $escaper->escapeHtml(__('Address Book')) ?></div>
    <div class="block-content" x-data="initAddresses()">
        <?php if ($additionalAddresses = $block->getAdditionalAddresses()): ?>
            <div class="grid grid-cols-3 lg:grid-cols-5">
                <div class="px-1 text-sm font-bold border-b border-secondary-900/50">
                    <?= $escaper->escapeHtml(__('Name')) ?>
                </div>
                <div class="hidden px-1 text-sm font-bold border-b lg:block border-secondary-900/50">
                    <?= $escaper->escapeHtml(__('Street Address')) ?>
                </div>
                <div class="px-1 text-sm font-bold border-b border-secondary-900/50">
                    <?= $escaper->escapeHtml(__('City')) ?>
                </div>
                <div class="hidden px-1 text-sm font-bold border-b lg:block border-secondary-900/50">
                    <?= $escaper->escapeHtml(__('Country')) ?>
                </div>
                <div class="px-1 text-sm font-bold border-b border-secondary-900/50"></div>
                <?php $i = 0; ?>
                <?php foreach ($additionalAddresses as $address): ?>
                    <?php $i++ ?>
                    <div class="flex items-center h-12 px-1 text-xs border-b border-secondary-900/50">
                        <?= $escaper->escapeHtml($address->getLastname()) ?>,
                        <?= $escaper->escapeHtml($address->getFirstname()) ?>
                    </div>
                    <div class="items-center hidden h-12 px-1 text-xs break-all border-b lg:flex border-secondary-900/50">
                        <?= $escaper->escapeHtml($block->getStreetAddress($address)) ?>
                    </div>
                    <div class="flex items-center h-12 px-1 text-xs border-b border-secondary-900/50">
                        <?= $escaper->escapeHtml($address->getCity()) ?>
                    </div>
                    <div class="items-center hidden h-12 px-1 text-xs border-b lg:flex border-secondary-900/50">
                        <?= $escaper->escapeHtml($block->getCountryByCode($address->getCountryId())) ?>
                    </div>
                    <div class="flex items-center justify-end h-12 px-1 text-xs border-b border-secondary-900/50 gap-x-2">
                        <a class="inline-block text-sm underline action edit text-secondary-darker"
                            title="<?= $escaper->escapeHtmlAttr(__('Edit')) ?>"
                            href="<?= $escaper->escapeUrl($block->getUrl(
                                'customer/address/edit',
                                ['id' => $address->getId()]
                            )) ?>">
                            <?= $svgIcons->editHtml('w-4 h-4') ?>
                            <span></span>
                        </a>
                        <a class="inline-block ml-2 text-sm underline action delete text-secondary-darker"
                           title="<?= $escaper->escapeHtmlAttr(__('Delete')) ?>"
                           @click.prevent="deleteAddressById(<?= $escaper->escapeJs($address->getId()) ?>)" href="#">
                           <?= $svgIcons->trashHtml('w-4 h-4') ?>
                        </a>
                    </div>
                <?php endforeach; ?>
            </div>
            <?php if ($block->getChildHtml('pager')): ?>
                <div class="mt-6 customer-addresses-toolbar toolbar bottom">
                    <?= $block->getChildHtml('pager') ?>
                </div>
            <?php endif; ?>

        <?php else: ?>
            <p class="empty">
                <?= $escaper->escapeHtml(__('You have no other address entries in your address book.')) ?>
            </p>
        <?php endif ?>

        <script defer="defer">
            function initAddresses() {
                return {
                    deleteAddressById(id) {
                        if (window.confirm(
                            '<?= $escaper->escapeJs(__('Are you sure you want to delete this address?')) ?>'
                        )) {
                            hyva.postForm(
                                {
                                    "action": '<?= $escaper->escapeJs($block->getDeleteUrl()) ?>',
                                    "data": {
                                        "id": id
                                    }
                                }
                            );
                        }
                    }
                }
            }
        </script>
    </div>
</div>

<div class="actions-toolbar">
    <a class="flex items-center text-link after:hidden before:hover:delay-0 gap-x-2" href="<?= $escaper->escapeUrl($block->getUrl('customer/account')) ?>">
        <?= $svgIcons->chevronLeftHtml("w-4 h-4") ?>
        <?= $escaper->escapeHtml(__('Back')) ?>
    </a>
    <a href="<?= $escaper->escapeUrl($block->getUrl('customer/address/new')) ?>" class="block w-full text-center btn btn-primary">
        <span><?= $escaper->escapeHtml(__('Add New Address')) ?></span>
    </a>
</div>
