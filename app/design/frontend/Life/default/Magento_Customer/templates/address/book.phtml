<?php

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\SvgIcons;
use Magento\Customer\Block\Address\Book;
use Magento\Framework\Escaper;

/** @var Book $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */

/** @var SvgIcons $svgIcons */
$svgIcons = $viewModels->require(SvgIcons::class);
?>
<div class="block p-4 mb-6 bg-white block-addresses-default rounded-xl">
    <div class="mb-6 text-xl leading-9 block-title">
            <?= $escaper->escapeHtml(__('Default Addresses')) ?>
    </div>
    <div class="grid gap-4 md:grid-cols-2 block-content">
        <?php if ($customerDefaultBillingAddress = $block->getDefaultBilling()): ?>
            <div class="box box-address-billing">
                <div class="mb-2 text-base leading-[1.875rem] font-bold title-font">
                    <span><?= $escaper->escapeHtml(__('Default Billing Address')) ?></span>
                </div>
                <div class="box-content">
                    <address class="text-sm">
                        <?= $block->getAddressHtml($block->getAddressById($customerDefaultBillingAddress)) ?>
                    </address>
                </div>
                <div class="box-actions">
                    <a href="<?= $escaper->escapeUrl($block->getAddressEditUrl($customerDefaultBillingAddress)) ?>"
                        class="inline-flex items-center mt-6 text-sm gap-x-2"
                       >
                        <span class="text-link">
                            <?= $escaper->escapeHtml(__('Edit Address')) ?>
                        </span>
                        <?= $svgIcons->arrowRightHtml('w-5 h-5', 20, 20, ['aria-hidden' => 'true']); ?>
                    </a>
                </div>
            </div>
        <?php else: ?>
            <div class="box box-billing-address card">
                <div class="mb-2 text-base leading-[1.875rem] font-bold title-font">
                    <span>
                        <?= $escaper->escapeHtml(__('Default Billing Address')) ?>
                    </span>
                </div>
                <div class="box-content">
                    <p><?= $escaper->escapeHtml(__('You have no default billing address in your address book.')) ?></p>
                </div>
            </div>
        <?php endif ?>

        <?php if ($customerDefaultBillingAddress = $block->getDefaultShipping()): ?>
            <div class="box box-address-shipping">
                <div class="mb-2 text-base leading-[1.875rem] font-bold title-font">
                    <span><?= $escaper->escapeHtml(__('Default Shipping Address')) ?></span>
                </div>
                <div class="box-content">
                    <address class="text-sm">
                        <?= $block->getAddressHtml($block->getAddressById($customerDefaultBillingAddress)) ?>
                    </address>
                </div>
                <div class="box-actions">
                    <a href="<?= $escaper->escapeUrl($block->getAddressEditUrl($customerDefaultBillingAddress)) ?>"
                    class="inline-flex items-center mt-6 text-sm gap-x-2"
                    >
                        <span class="text-link"><?= $escaper->escapeHtml(__('Edit Address')) ?></span>
                        <?= $svgIcons->arrowRightHtml('w-5 h-5', 20, 20, ['aria-hidden' => 'true']); ?>
                    </a>
                </div>
            </div>
        <?php else: ?>
            <div class="box box-shipping-address card">
                <div class="mb-2 text-base leading-[1.875rem] font-bold title-font">
                    <span>
                        <?= $escaper->escapeHtml(__('Default Shipping Address')) ?>
                    </span>
                </div>
                <div class="box-content">
                    <p>
                        <?= $escaper->escapeHtml(__('You have no default shipping address in your address book.')) ?>
                    </p>
                </div>
            </div>
        <?php endif ?>
    </div>
</div>
