<?php

declare(strict_types=1);

use Hyva\Theme\ViewModel\SvgIcons;
use Hyva\Theme\ViewModel\CustomerRegistration;
use Magento\Customer\Model\Account\Redirect;

/** @var \Magento\Customer\Block\Account\Customer $block */
/** @var \Magento\Framework\Escaper $escaper */
/** @var \Hyva\Theme\Model\ViewModelRegistry $viewModels */

/** @var SvgIcons $svgIcons */
$svgIcons = $viewModels->require(SvgIcons::class);

/** @var CustomerRegistration $customerRegistration */
$customerRegistration = $viewModels->require(CustomerRegistration::class);

?>

<div
    class="relative hidden md:inline-block customer-menu"
    x-data="{ open: false }"
    @keyup.escape="open = false">
    <button
        type="button"
        href="<?= $escaper->escapeHtmlAttr($escaper->escapeUrl($block->getUrl('customer/account'))) ?>"
        id="customer-menu"
        class="flex flex-col items-center p-1 transition-all duration-300 ease-in-out rounded focus:ring-secondary-700/50 focus:border-transparent focus:outline-none focus:ring-2"
        :class="{ 'hover:bg-secondary focus:bg-secondary': scrollPosTop, 'hover:bg-secondary-300 focus:bg-secondary-300': !scrollPosTop }"
        @click="open = !open"
        @click.outside="open = false"
        :aria-expanded="open ? 'true' : 'false'"
        aria-label="<?= $escaper->escapeHtmlAttr(__('My Account')) ?>"
        aria-haspopup="true">
        <?php if ($block->customerLoggedIn()) : ?>
            <?= $svgIcons->accountLoggedInHtml('w-[2.1875rem] h-[2.1875rem] md:w-8 md:h-8', 32, 32, ['aria-hidden' => 'true']) ?>
        <?php else : ?>
            <?= $svgIcons->accountHtml('w-[2.1875rem] h-[2.1875rem] md:w-8 md:h-8', 32, 32, ['aria-hidden' => 'true']) ?>
        <?php endif; ?>
        <span class="hidden mt-0.5 text-xs md:inline customer-label"><?= $escaper->escapeHtmlAttr(__('Account')) ?></span>
    </button>
    <nav
        class="absolute right-0 z-50 w-40 p-2 mt-2 -mr-4 overflow-auto origin-top-right bg-white shadow-lg sm:w-48 lg:mt-3 rounded-xl customer-dropdown"
        x-cloak
        x-show="open"
        aria-labelledby="customer-menu"
        @click.outside="open = false">
        <?php if ($block->customerLoggedIn()) : ?>
            <a class="text-[.6875rem] md:text-[.8125rem] block px-4 py-2 rounded-md lg:px-5 lg:py-2 hover:bg-secondary-300"
                href="<?= $escaper->escapeUrl($block->getUrl('customer/account')) ?>">
                <?= $escaper->escapeHtml(__('My Account')); ?>
            </a>
            <a class="text-[.6875rem] md:text-[.8125rem] block px-4 py-2 rounded-md lg:px-5 lg:py-2 hover:bg-secondary-300"
                href="<?= $escaper->escapeUrl($block->getUrl('sales/order/history')) ?>">
                <?= $escaper->escapeHtml(__('My Orders')); ?>
            </a>
            <a class="text-[.6875rem] md:text-[.8125rem] block px-4 py-2 rounded-md lg:px-5 lg:py-2 hover:bg-secondary-300"
                href="<?= $escaper->escapeUrl($block->getUrl('customer/address')) ?>">
                <?= $escaper->escapeHtml(__('Address Book')); ?></a>
            <a class="text-[.6875rem] md:text-[.8125rem] block px-4 py-2 rounded-md lg:px-5 lg:py-2 hover:bg-secondary-300"
                href="<?= $escaper->escapeUrl($block->getUrl('customer/account/edit')) ?>">
                <?= $escaper->escapeHtml(__('Account Information')); ?></a>
            <a class="text-[.6875rem] md:text-[.8125rem] block px-4 py-2 rounded-md lg:px-5 lg:py-2 hover:bg-secondary-300"
                href="<?= $escaper->escapeUrl($block->getUrl('newsletter/manage')) ?>">
                <?= $escaper->escapeHtml(__('Newsletter Subscriptions')); ?></a>
            <a class="text-[.6875rem] md:text-[.8125rem] block px-4 py-2 rounded-md lg:px-5 lg:py-2 hover:bg-secondary-300"
                href="<?= $escaper->escapeUrl($block->getUrl('customer/account/logout')) ?>">
                <?= $escaper->escapeHtml(__('Sign Out')); ?>
            </a>
        <?php else : ?>
            <a class="text-[.6875rem] md:text-[.8125rem] block px-4 py-2 rounded-md lg:px-5 lg:py-2 hover:bg-secondary-300"
                onclick="hyva.setCookie && hyva.setCookie(
                   '<?= /* @noEscape */ Redirect::LOGIN_REDIRECT_URL ?>',
                   window.location.href,
                   1
                   )"
                href="<?= $escaper->escapeUrl($block->getUrl('customer/account/index')) ?>">
                <?= $escaper->escapeHtml(__('Sign In')); ?>
            </a>
            <?php if ($customerRegistration->isAllowed()) : ?>
                <a class="text-[.6875rem] md:text-[.8125rem] block px-4 py-2 rounded-md lg:px-5 lg:py-2 hover:bg-secondary-300"
                    href="<?= $escaper->escapeUrl($block->getUrl('customer/account/create')) ?>">
                    <?= $escaper->escapeHtml(__('Create an Account')); ?>
                </a>
            <?php endif; ?>
        <?php endif; ?>
    </nav>
</div>
