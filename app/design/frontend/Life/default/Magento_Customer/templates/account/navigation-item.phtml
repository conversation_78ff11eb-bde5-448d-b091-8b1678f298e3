<?php

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\SvgIcons;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;

/** @var Template $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */

/** @var SvgIcons $svgIcons */
$svgIcons = $viewModels->require(SvgIcons::class);

$currentLink = $block->getUrl('*/*');
$targetUrl = $block->getUrl($block->getPath());
$isActive = ($currentLink === $targetUrl);

// For customer/account pages we need to check action too
if ($currentLink === $block->getUrl('customer/account')) {
    $isActive = ($block->getUrl('*/*/*') === $targetUrl);
}

// For order pages we need to check the root path only
if (
    ($currentLink === $block->getUrl('sales/order'))
    && ($block->getPath() === 'sales/order/history')
) {
    $isActive = true;
}
?>

<li class="nav item">
    <a 
        href="<?= $escaper->escapeUrl($block->getUrl($block->getPath())); ?>" 
        class="flex items-center gap-x-2<?php if ($isActive): ?> font-bold<?php endif; ?>"
    >
        <?php if ($block->getIcon()): ?>
            <?= $svgIcons->renderHtml($block->getIcon(),'w-4 h-4') ?>
        <?php endif; ?>
        <span class="text-link after:hidden before:hover:delay-0"><?= $block->getLabel() ?></span>
    </a>
</li>
