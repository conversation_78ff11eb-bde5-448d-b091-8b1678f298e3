<?php

use Hyva\Theme\ViewModel\SvgIcons;

/** @var \Magento\Customer\Block\Account\AuthorizationLink $block */
/** @var \Magento\Framework\Escaper $escaper */
/** @var ViewModelRegistry $viewModels */

/** @var SvgIcons $svgIcons */
$svgIcons = $viewModels->require(SvgIcons::class);

if ($block->isLoggedIn()): ?>
    <?php $dataPostParam = $block->getPostParams(); ?>
    <li class="nav item authorization-link">
        <a <?= /* @noEscape */ $block->getLinkAttributes() ?>
            @click.prevent='hyva.postForm(<?= /* @noEscape */ $dataPostParam ?>)'
            class="group"
        >
            <span class="text-link after:hidden before:hover:delay-0"><?= $escaper->escapeHtml($block->getLabel()) ?></span>
            <?= $svgIcons->arrowRightHtml("w-5 h-5") ?>
        </a>
    </li>
<?php else: ?>
    <li class="nav item authorization-link">
        <a <?= /* @noEscape */ $block->getLinkAttributes() ?> class="group">
            <span class="text-link after:hidden before:hover:delay-0"><?= $escaper->escapeHtml($block->getLabel()) ?></span>
            <?= $svgIcons->arrowRightHtml("w-5 h-5") ?>
        </a>
    </li>
<?php endif; ?>
