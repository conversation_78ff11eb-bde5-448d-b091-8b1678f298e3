<?php

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\SvgIcons;
use Magento\Customer\Block\Account\Dashboard\Address;
use Magento\Framework\Escaper;

/** @var Address $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */

/** @var SvgIcons $svgIcons */
$svgIcons = $viewModels->require(SvgIcons::class);
?>
<div class="block p-4 bg-white block-dashboard-addresses rounded-xl">
    <h2 class="flex items-center justify-between mb-6 block-title">
        <span class="block text-xl leading-9"><?= $escaper->escapeHtml(__('Address Book')) ?></span>
    </h2>
    <div class="block-content">
        <div class="flex flex-wrap justify-between gap-4">
            <div class="w-full lg:w-[calc(50%-0.5rem)]">
                <div class="flex flex-col h-full sm:flex-row">
                    <div class="grow">
                        <h3 class="mb-2 text-base leading-[1.875rem] font-bold title-font">
                            <span><?= $escaper->escapeHtml(__('Default Billing Address')) ?></span>
                        </h3>
                        <address class="text-sm">
                            <?= $block->getPrimaryBillingAddressHtml() ?>
                        </address>
                        <a
                            class="inline-flex items-center mt-6 text-sm gap-x-2"
                            href="<?= $escaper->escapeUrl($block->getPrimaryBillingAddressEditUrl()) ?>"
                            data-ui-id="default-billing-edit-link"
                            aria-label="<?= $escaper->escapeHtmlAttr(__('Edit Address - billing default')) ?>"
                        >
                            <span class="text-link"><?= $escaper->escapeHtml(__('Edit Address')) ?></span>
                            <?= $svgIcons->arrowRightHtml('w-5 h-5', 20, 20, ['aria-hidden' => 'true']); ?>
                        </a>
                    </div>
                </div>
            </div>
            <div class="w-full lg:w-[calc(50%-0.5rem)]">
                <div class="flex flex-col h-full sm:flex-row">
                    <div class="grow">
                        <h3 class="mb-2 text-base leading-[1.875rem] font-bold title-font">
                            <span><?= $escaper->escapeHtml(__('Default Shipping Address')) ?></span>
                        </h3>
                        <address class="text-sm">
                            <?= $block->getPrimaryShippingAddressHtml() ?>
                        </address>
                        <a
                            class="inline-flex items-center mt-6 text-sm gap-x-2"
                            href="<?= $escaper->escapeUrl($block->getPrimaryShippingAddressEditUrl()) ?>"
                            data-ui-id="default-billing-edit-link"
                            aria-label="<?= $escaper->escapeHtmlAttr(__('Edit Address - shipping default')) ?>"
                        >
                            <span class="text-link"><?= $escaper->escapeHtml(__('Edit Address')) ?></span>
                            <?= $svgIcons->arrowRightHtml('w-5 h-5', 20, 20, ['aria-hidden' => 'true']); ?>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
