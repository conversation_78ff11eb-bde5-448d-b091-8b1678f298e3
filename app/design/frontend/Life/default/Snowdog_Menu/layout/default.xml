<?xml version="1.0" ?>
<page
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd"
>
    <body>
        <referenceBlock name="header-content">
            <referenceBlock name="topmenu_generic" template="Snowdog_Menu::topmenu-mobile.phtml">
                <block name="main_menu" class="Snowdog\Menu\Block\Menu">
                    <arguments>
                        <argument name="menu" xsi:type="string">main</argument>
                    </arguments>
                </block>
                <block name="main_secondary_menu" class="Snowdog\Menu\Block\Menu">
                    <arguments>
                        <argument name="menu" xsi:type="string">main_secondary</argument>
                    </arguments>
                </block>
                <block
                    class="Magento\Customer\Block\Account\Customer"
                    name="header.customer.mobile"
                    as="customer_mobile"
                    template="Magento_Customer::header/customer-menu.phtml"
                />
                <block class="Magento\Customer\Block\Account\Customer" name="authentication-popup"
                    as="authentication-popup" template="Magento_Customer::account/authentication-popup.phtml"/>
            </referenceBlock>
        </referenceBlock>

        <referenceBlock name="page.wrapper">
            <block name="topmenu_desktop" template="Snowdog_Menu::topmenu-desktop.phtml">
                <block name="main_menu_desktop" class="Snowdog\Menu\Block\Menu">
                    <arguments>
                        <argument name="menu" xsi:type="string">main</argument>
                    </arguments>
                </block>
                <block name="main_secondary_menu_desktop" class="Snowdog\Menu\Block\Menu">
                    <arguments>
                        <argument name="menu" xsi:type="string">main_secondary</argument>
                    </arguments>
                </block>
            </block>
        </referenceBlock>

        <referenceBlock name="footer-content">
            <block name="footer_menu_wrapper" template="Snowdog_Menu::footer-menu/wrapper.phtml">
                <block name="footer_menu" class="Snowdog\Menu\Block\Menu" template="Snowdog_Menu::footer-menu.phtml">
                    <arguments>
                        <argument name="menu" xsi:type="string">footer</argument>
                        <argument name="subMenuTemplate" xsi:type="string">Snowdog_Menu::footer-menu/sub_menu.phtml</argument>
                    </arguments>
                </block>
                <block name="footer_social" class="Snowdog\Menu\Block\Menu" template="Snowdog_Menu::footer-menu.phtml">
                    <arguments>
                        <argument name="menu" xsi:type="string">footer-social</argument>
                        <argument name="subMenuTemplate" xsi:type="string">Snowdog_Menu::footer-menu/sub_menu.phtml</argument>
                    </arguments>
                </block>
                <block name="footer_newsletter" class="Snowdog\Menu\Block\Menu" template="Snowdog_Menu::footer-menu.phtml">
                    <arguments>
                        <argument name="menu" xsi:type="string">footer-newsletter</argument>
                        <argument name="subMenuTemplate" xsi:type="string">Snowdog_Menu::footer-menu/sub_menu.phtml</argument>
                    </arguments>
                </block>
                <block name="footer_trustmarks" class="Snowdog\Menu\Block\Menu" template="Snowdog_Menu::footer-menu.phtml">
                    <arguments>
                        <argument name="menu" xsi:type="string">footer-trustmarks</argument>
                        <argument name="subMenuTemplate" xsi:type="string">Snowdog_Menu::footer-menu/sub_menu.phtml</argument>
                    </arguments>
                </block>
            </block>
        </referenceBlock>

        <referenceBlock name="footer-copyright">
            <block name="footer_privacy" class="Snowdog\Menu\Block\Menu">
                <arguments>
                    <argument name="menu" xsi:type="string">footer-privacy</argument>
                </arguments>
            </block>
            <block name="footer_payment" class="Snowdog\Menu\Block\Menu">
                <arguments>
                    <argument name="menu" xsi:type="string">footer-payment</argument>
                </arguments>
            </block>
        </referenceBlock>

        <referenceContainer name="header.container" htmlClass="page-header md:sticky top-0 z-30 w-full"/>
        <move element="topmenu_desktop" destination="page.wrapper" after="header.container"/>
    </body>
</page>
