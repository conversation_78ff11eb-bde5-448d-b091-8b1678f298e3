<?php /** @var \Snowdog\Menu\Block\NodeType\CustomUrl $block */ ?>
<?php
$attributes = [];
$class = $block->getMenuClass();
$imageUrl = $block->getImageUrl();
$imageAltText = $block->getImageAltText();
$content = $block->getContent();

if ($block->getIsViewAllLink()) {
    $title = __('View All');
    $classLink = $class . '__inner-link';
} else {
    $classLink = $block->getIsRoot() ? $class . '__link' : $class . '__inner-link';
    $title = $block->getTitle();
    if ($block->getId()) {
        $attributes[] = 'data-menu="menu-' . $block->getId() . '"';
    }
}
$attributes[] = 'class="' . $classLink . '"';

if ($block->getTarget()) {
    $attributes[] = 'target="_blank" rel="noopener"';
}
$allAttributes = implode(' ', $attributes);
?>
<?php if ($content): ?>
    <a
        href="<?= $block->escapeUrl($content); ?>"
        <?= $allAttributes; ?>
    >
        <?php if ($imageUrl): ?>
            <img
                src="<?= $block->escapeUrl($imageUrl); ?>"
                alt="<?= $block->escapeHtmlAttr($imageAltText); ?>"
                width="40"
                height="40"
                loading="lazy"
            />
        <?php endif; ?>
        <span class="title"><?= $block->escapeHtml($title); ?></span>
    </a>
<?php else: ?>
    <span
        <?= $allAttributes; ?>
    >
        <?php if ($imageUrl): ?>
            <img
                src="<?= $block->escapeUrl($imageUrl); ?>"
                alt="<?= $block->escapeHtmlAttr($imageAltText); ?>"
                width="40"
                height="40"
                loading="lazy"
            />
        <?php endif; ?>
        <span class="title"><?= $block->escapeHtml($title); ?></span>
    </span>
<?php endif; ?>
