<?php

use Hyva\Theme\ViewModel\SvgIcons;

/** @var \Snowdog\Menu\Block\Menu $block */
/** @var Hyva\Theme\Model\ViewModelRegistry $viewModels */
/** @var \Magento\Framework\Escaper $escaper */

/** @var SvgIcons $svgIcons */
$svgIcons = $viewModels->require(SvgIcons::class);

?>

<script defer="defer">
    function initSubMenu ($el) {
        return {
            columnCount: 1,
            openMobileSubSubmenu: false,
            getColumnCount() {
                let columnCount = $el.querySelectorAll('.__inner-item.font-bold').length;

                if (columnCount == 0) {
                    return;
                }

                if (columnCount == 1) {
                    columnCount = 2;
                }

                this.columnCount = columnCount;
            },
            showSubmenu() {
                setTimeout(function(){
                    $el.style.display = '';
                    window.dispatchEvent(new CustomEvent("show-submenu"));
                }, 300);
            }
        }
    }

    function initSubSubMenu ($el) {
        return {
            scrollContainer: document.querySelector('.mobile-nav > div'),
            openMobileSubSubmenu: false,
            openSubSubMenu() {
                this.scrollContainer.scrollTop = 0;
                $el.classList.add('subsubmenu-active');
                $el.parentNode.classList.add('subsubmenu-open');
            },
            closeSubSubMenu() {
                $el.classList.remove('subsubmenu-active');
                $el.parentNode.classList.remove('subsubmenu-open');
            }
        }
    }
</script>

<?php if ($block->getMenu()): ?>
    <?php
    $menuClass = $block->getMenu()->getCssClass();
    $parentNode = $block->getParentNode();
    $level = $block->getLevel();

    $wrapperAttributes = [
        'class' => [
            $menuClass . '__inner-list',
            $menuClass . '__inner-list--level' . $level
        ],
        'data-menu' => 'menu-' . $parentNode->getNodeId()
    ];
    ?>

    <ul
        <?= $block->buildAttrFromArray($wrapperAttributes) ?>
        x-data="initSubMenu($el)"
        x-init="getColumnCount(); showSubmenu()"
        :class="{ 'subsubmenu-open': openMobileSubSubmenu }"
        <?php if ($block->getLevel() === 2): ?>
            :style="{columns: `${columnCount}`, minHeight: `${dropdownHeight}px`}"
        <?php else: ?>
            :style="{columns: `${columnCount}`, width:`calc(25*${columnCount}%)`}"
        <?php endif; ?>
        style="display: none"
    >
        <li class="md:hidden">
            <button
                class="flex items-center w-full mt-3 mb-6 gap-x-2"
                @click="openMobileSubmenu = false, closeSubMenu(), openMobileSubSubmenu = false, closeSubSubMenu()"'
            >
                <?= $svgIcons->chevronLeftHtml("w-4 h-4") ?>
                <span class="max-md:order-4"><?= $escaper->escapeHtml(__('All categories')) ?></span>
            </button>
        </li>
        <?php if ($block->isViewAllLinkAllowed($parentNode->getType())): ?>
            <?php
            $viewAllAttributes = [
                'class' => [
                    $menuClass . '__inner-item',
                    $menuClass . '__inner-item--all',
                    $menuClass . '__inner-item--level' . $level
                ]
            ];
            ?>

            <li <?= $block->buildAttrFromArray($viewAllAttributes) ?>>
                <?= $block->renderViewAllLink($parentNode) ?>
            </li>
        <?php endif ?>

        <?php foreach ($block->getSubmenuNodes() as $node): ?>
            <?php
            $childrenLevel = $node->getLevel() + 1;
            $children = $block->getNodes($childrenLevel, $node);
            $node->setIsParent((bool) $children);

            $nodeAttributes = [
                'class' => [
                    $menuClass . '__inner-item',
                    $menuClass . '__inner-item--level' . $level
                ]
            ];

            if ($children) {
                $nodeAttributes['class'][] = $menuClass . '__inner-item--parent';
            }

            if ($node->getClasses()) {
                $nodeAttributes['class'][] = $node->getClasses();
            }
            ?>

            <li
                x-data="initSubSubMenu($el)"
                <?= $block->buildAttrFromArray($nodeAttributes) ?>
            >
                <span
                    class="flex items-center w-full gap-x-2 max-md:justify-between __inner-item-wrapper<?php if ($block->getLevel() === 2): ?> md:hidden<?php endif; ?>"
                    <?php if ($children): ?>
                        @click="openMobileSubmenu = true, openSubMenu(), openMobileSubSubmenu = true, openSubSubMenu()"
                    <?php endif; ?>
                >
                    <span class="flex items-center flex-grow gap-x-2 <?= ((int)$block->getLevel() === 1) ? 'max-md:pointer-events-none' : ''; ?>">
                        <?= $block->renderMenuNode($node) ?>
                    </span>
                    <?php if ($children): ?>
                        <?= $svgIcons->chevronRightHtml("w-4 h-4 flex-shrink-0") ?>
                    <?php endif; ?>
                </span>
                <?= $block->renderSubmenu($children, $node, $childrenLevel) ?>
            </li>
        <?php endforeach ?>
    </ul>
    <?php if ($block->getLevel() === 1): ?>
        <div
            class="__bg-dropdown"
            :style="{height: `${dropdownHeight}px`}"
        ></div>
        <div class="__bg-dropdown-overlay"></div>
    <?php endif; ?>
<?php endif; ?>
