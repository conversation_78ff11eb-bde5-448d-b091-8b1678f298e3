<?php

use Hyva\Theme\ViewModel\SvgIcons;
use Life\DisableCheckoutExtensions\Helper\Config as DisableCheckoutConfigHelper;

/** @var \Magento\Framework\View\Element\Template $block */
/** @var Hyva\Theme\Model\ViewModelRegistry $viewModels */
/** @var Magento\Framework\Escaper $escaper */

/** @var SvgIcons $svgIcons */
$svgIcons = $viewModels->require(SvgIcons::class);

/** @var Hyva\Theme\ViewModel\StoreConfig $storeConfig */
$storeConfig = $viewModels->require(Hyva\Theme\ViewModel\StoreConfig::class);
$showMiniCart = $storeConfig->getStoreConfig(\Magento\Checkout\Block\Cart\Sidebar::XML_PATH_CHECKOUT_SIDEBAR_DISPLAY);

/** @var DisableCheckoutConfigHelper $disableCheckoutConfigHelper */
$disableCheckoutConfigHelper = $this->helper(DisableCheckoutConfigHelper::class);
$disableCheckout = $disableCheckoutConfigHelper->getConfig()->getDisableCheckout();
?>

<div class="order-1 w-1/4 md:hidden">
    <button
        id="mobile-menu"
        class="cursor-pointer"
        @click="toggleMenu()"
        :aria-expanded="menuOpen ? 'true' : 'false'"
        aria-label="<?= $escaper->escapeHtmlAttr(__('Menu')) ?>"
        aria-haspopup="true"
    >
        <?= $svgIcons->menuHtml() ?>
    </button>
    <div
        class="
            absolute bg-secondary-300 top-[8.75rem] bottom-0 w-4/5 mobile-nav flex flex-col text-sm overflow-hidden
            motion-safe:transition-all motion-safe:duration-300 motion-safe:ease-in-out z-20
        "
        :class="{'left-0': menuOpen, '-left-full': !menuOpen, 'scrolled-down': !scrollPosTop }"
        aria-labelledby="mobile-menu"
        x-cloak
    >
        <div class="mobile-nav-inner flex-grow bg-white rounded-br-[3rem] flex flex-col justify-between overflow-x-hidden overflow-y-auto">
            <div class="main"><?= $block->getChildHtml('main_menu') ?></div>
            <div class="main_secondary"><?= $block->getChildHtml('main_secondary_menu') ?></div>
        </div>

        <!--Icons-->
        <div class="z-10 flex items-center justify-center py-6 gap-x-6 bg-secondary-300">
            <!--Customer Icon & Dropdown-->
            <?= $block->getChildHtml('customer_mobile') ?>

            <?php if (!$disableCheckout): ?>
                <!--Cart Icon-->
                <?php if ($showMiniCart): ?>
                    <button
                <?php else: ?>
                    <a
                <?php endif ?>
                    id="menu-cart-icon-mobile"
                    class="relative inline-flex flex-col items-center no-underline hover:text-black"
                    x-ref="cartButton"
                    :aria-disabled="isCartEmpty()"
                    title="<?= $escaper->escapeHtmlAttr(__('Cart')) ?>"
                    <?php if ($showMiniCart): ?>
                        @click.prevent.stop="() => {
                            $dispatch('toggle-cart', { isOpen: true })
                        }"
                        @toggle-cart.window="toggleCart($event)"
                        :aria-expanded="isCartOpen"
                        aria-haspopup="dialog"
                    <?php else: ?>
                        href="<?= $escaper->escapeUrl($block->getUrl('checkout/cart/index')) ?>"
                    <?php endif ?>
                >

                    <?= $svgIcons->cartEmptyHtml("w-8 h-8") ?>
                    <span class="mt-0.5 text-xs"><?= $escaper->escapeHtmlAttr(__('Cart')) ?></span>

                    <span
                        x-text="cart.summary_count"
                        x-show="!isCartEmpty()"
                        x-cloak
                        class="absolute top-0 h-5 px-2 py-1 -mt-5 -mr-4 text-xs font-semibold leading-none text-center text-white uppercase translate-x-1/2 translate-y-1/2 rounded-full left-1/2 bg-primary"
                        aria-hidden="true"
                    ></span>
                <?php if ($showMiniCart): ?>
                    </button>
                <?php else: ?>
                    </a>
                <?php endif ?>
            <?php endif ?>
        </div>
    </div>
    <div
        class="fixed top-0 bottom-0 right-0 block w-full  bg-black/40 -z-10 motion-safe:transition-all motion-safe:duration-300 motion-safe:ease-in-out"
        :class="{'opacity-100 pointer-events-auto': menuOpen, 'opacity-0 pointer-events-none': !menuOpen }"
        @click="closeMenu()"
    ></div>
</div>
