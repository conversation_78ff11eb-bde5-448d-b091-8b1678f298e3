<?php

/** @var \Magento\Framework\View\Element\Template $block */

?>

<script defer="defer">
    function initTopMenu () {
        return {
            scrollingDown: false,
            scrollPosTop: true,
            startingScrollPos: window.pageYOffset,
            getScrollDir() {
                // only trigger when scrolling down or up more then 50 pixels
                const scrollBound = 50;
                if (window.pageYOffset < this.startingScrollPos - scrollBound | window.pageYOffset > this.startingScrollPos + scrollBound) {

                    // reset starting point
                    this.startingScrollPos = window.pageYOffset;

                    // handle scroll direction
                    let goingDown = false;
                    let scrollPos = window.pageYOffset;

                    if (scrollPos > this.prevScrollPos && window.pageYOffset > 0) {
                        goingDown = true;
                    }

                    this.scrollingDown = goingDown;
                    this.prevScrollPos = scrollPos;
                    this.scrollPosTop = (window.pageYOffset == 0) ? true : false;
                }
            },
        }
    }
</script>

<div
    x-data="initTopMenu()"
    class="
        desktop-nav
        sticky z-[25] hidden
        md:block
        motion-safe:transition-all motion-safe:duration-300 motion-safe:ease-in-out
    "
    :class="{ 'top-0': scrollingDown, 'top-[5.25rem]': !scrollingDown }"
    @scroll.window.debounce.10="getScrollDir()"
    x-cloak
>
    <div
        class="
            container flex justify-between
            md:border-b md:border-secondary-900/50
            motion-safe:transition-all motion-safe:duration-300 motion-safe:ease-in-out
        "
        :class="{ 'bg-secondary-300': scrollPosTop, 'bg-white': !scrollPosTop }"
    >
        <?= $block->getChildHtml('main_menu_desktop') ?>
        <?= $block->getChildHtml('main_secondary_menu_desktop') ?>
    </div>
</div>

<div id="scrolled" class="absolute left-0 w-px h-px top-20"></div>
