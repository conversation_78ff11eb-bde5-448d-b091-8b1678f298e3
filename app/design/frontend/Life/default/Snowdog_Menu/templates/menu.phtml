<?php

use Hyva\Theme\ViewModel\SvgIcons;

/** @var \Snowdog\Menu\Block\Menu $block */
/** @var Hyva\Theme\Model\ViewModelRegistry $viewModels */

/** @var SvgIcons $svgIcons */
$svgIcons = $viewModels->require(SvgIcons::class);

?>

<?php if ($block->getMenu()): ?>
    <?php $menuClass = $block->getMenu()->getCssClass() ?>

    <script defer="defer">
        function initDropdown($el) {
            return {
                scrollContainer: document.querySelector('.mobile-nav > div'),
                dropdownHeight: 100,
                openMobileSubmenu: false,
                getDropdownHeight() {
                    setTimeout(() => {
                        const lists = $el.querySelectorAll('.__inner-list');
                        this.dropdownHeight =  Math.max(...Array.from(lists).map(el => el.getBoundingClientRect().height));
                    }, 100);
                },
                openSubMenu() {
                    this.scrollContainer.scrollTop = 0;
                    $el.parentNode.classList.add('submenu-open');
                },
                closeSubMenu() {
                    $el.parentNode.classList.remove('submenu-open');
                }
            };
        }
    </script>

    <nav class="<?= $menuClass ?>">
        <ul
            class="<?= $menuClass ?>__list"
            :class="{ 'show-dropdown': !scrollingDown }"
        >
            <?php foreach ($block->getNodes() as $node): ?>
                <?php
                    $childrenLevel = $node->getLevel() + 1;
                    $children = $block->getNodes($childrenLevel, $node);
                    $node->setIsParent((bool) $children);
                    $parentClass = $children ? ' ' . $menuClass . '__item--parent' : '';
                    $additionalClasses = $node->getClasses() ? ' ' . $node->getClasses() : '';
                    $itemClasses = $parentClass . $additionalClasses;
                ?>
                <li
                    class="<?= $menuClass ?>__item <?= $itemClasses ?>"
                    <?php if ($children): ?>
                        x-data="initDropdown($el)"
                        x-init="getDropdownHeight()"
                        @show-submenu.window="getDropdownHeight()"
                        x-on:resize.window.debounce="getDropdownHeight()"
                        :class="{ 'submenu-active': openMobileSubmenu }"
                    <?php endif; ?>
                >
                    <span class="flex items-center gap-x-1 max-md:justify-between" @click="openMobileSubmenu = true, openSubMenu()">
                        <?= $block->renderMenuNode($node) ?>
                        <?php if ($children): ?>
                            <?= $svgIcons->chevronRightHtml("w-4 h-4 md:transform md:rotate-90") ?>
                        <?php endif; ?>
                    </span>
                    <?= $block->renderSubmenu($children, $node, $childrenLevel) ?>
                </li>
            <?php endforeach ?>
        </ul>
    </nav>
<?php endif; ?>
