<?php /** @var \Snowdog\Menu\Block\Menu $block */ ?>

<script defer="defer">
    function initFooterMenu () {
        return {
            menuOpen: false,
        }
    }
</script>

<?php if ($block->getMenu()): ?>
    <?php
    $menuClass = $block->getMenu()->getCssClass();
    $parentNode = $block->getParentNode();
    $level = $block->getLevel();

    $wrapperAttributes = [
        'class' => [
            $menuClass . '__inner-list',
            $menuClass . '__inner-list--level' . $level
        ],
        'data-menu' => 'menu-' . $parentNode->getNodeId()
    ];
    ?>
    <ul <?= $block->buildAttrFromArray($wrapperAttributes) ?> x-show="menuOpen" aria-labelledby="<?= $parentNode->getTitle() ?>">
        <?php foreach ($block->getSubmenuNodes() as $node): ?>
            <?php
            $childrenLevel = $node->getLevel() + 1;
            $children = $block->getNodes($childrenLevel, $node);
            $node->setIsParent((bool) $children);

            $nodeAttributes = [
                'class' => [
                    $menuClass . '__inner-item',
                    $menuClass . '__inner-item--level' . $level
                ]
            ];

            if ($children) {
                $nodeAttributes['class'][] = $menuClass . '__inner-item--parent';
            }

            if ($node->getClasses()) {
                $nodeAttributes['class'][] = $node->getClasses();
            }
            ?>

            <li <?= $block->buildAttrFromArray($nodeAttributes) ?>>
                <?= $block->renderMenuNode($node) ?>
                <?= $block->renderSubmenu($children, $node, $childrenLevel) ?>
            </li>
        <?php endforeach ?>
    </ul>
<?php endif; ?>
