<?php

use Hyva\Theme\ViewModel\SvgIcons;

/** @var \Snowdog\Menu\Block\Menu $block */
/** @var ViewModelRegistry $viewModels */
/** @var Escaper $escaper */

/** @var SvgIcons $svgIcons */
$svgIcons = $viewModels->require(SvgIcons::class);

?>
<?php if ($block->getMenu()): ?>
    <?php $menuClass = $block->getMenu()->getCssClass() ?>

    <script defer="defer">
        function initFooterMenu () {
            return {
                menuOpen: false,
            }
        }
    </script>

    <nav class="<?= $menuClass ?>">
        <ul class="__list">
            <?php foreach ($block->getNodes() as $node): ?>
                <?php
                    $childrenLevel = $node->getLevel() + 1;
                    $children = $block->getNodes($childrenLevel, $node);
                    $node->setIsParent((bool) $children);
                    $parentClass = $children ? ' ' . $menuClass . '__item--parent' : '';
                    $additionalClasses = $node->getClasses() ? ' ' . $node->getClasses() : '';
                    $itemClasses = $parentClass . $additionalClasses;
                ?>
                <li class="__item <?= $itemClasses ?>"<?php if ($children): ?> x-data="initFooterMenu()"<?php endif; ?>>
                    <?= $block->renderMenuNode($node) ?>

                    <?php if ($children): ?>
                        <button
                            id="<?= $node->getTitle() ?>"
                            @click="menuOpen = !menuOpen"
                            :class="{'menu-open': menuOpen}"
                            :aria-expanded="menuOpen ? 'true' : 'false'"
                            aria-label="<?= $escaper->escapeHtmlAttr(__('Menu')) ?>"
                            aria-haspopup="true"
                        >
                            <?= $node->getTitle() ?>
                            <?= $svgIcons->arrowDownHtml("lg:hidden w-5 h-5 text-white pointer-events-none absolute right-0 top-0 transition duration-300 ease-in-out") ?>
                        </button>
                    <?php endif; ?>
                    <?= $block->renderSubmenu($children, $node, $childrenLevel) ?>
                </li>
            <?php endforeach ?>
        </ul>
    </nav>
<?php endif; ?>
