<?php
/* @var Magento\Framework\View\Element\Template $block */
/* @var Squeezely\Plugin\ViewModel\PixelManager $viewModel */
$viewModel = $block->getViewModel();

if (!$viewModel->isEnabled()) {
    return '';
}
?>
<!-- Squeezely Pixel  -->
<script defer="defer">
    (function(s,q,z,l,y){s._sqzl=s._sqzl||[];l=q.createElement('script'),
        y=q.getElementsByTagName('script')[0];l.async=1;l.type='text/javascript';
        l.defer=true;l.src=z;l.setAttribute('rel', 'preconnect');y.parentNode.insertBefore(l,y)})
    (window,document, '<?= /* @noEscape */ $viewModel->getJsLink();?>');
    window._sqzl = _sqzl || [];
    window._sqzl.push({
        "event": "PageView",
        "language": "<?= /* @noEscape */ $viewModel->getStoreLocale(); ?>"
    });

    if (!sessionStorage.getItem("new-session")) {
        window._sqzl.push({
            "event": "UpdateCart",
            "set_cart": true,
            "products": []
        });

        sessionStorage.setItem("new-session", "true");
    }
</script>
<!-- End Squeezely Pixel -->
