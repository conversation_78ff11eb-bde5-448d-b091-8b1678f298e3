<?php

declare(strict_types=1);

use Life\ThemeConfigurations\ViewModel\SwiperScript;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;

/** @var Template $block */
/** @var Escaper $escaper */
?>
<script id="carousel-script">
    'use strict';

    (() => {
        const reducedMotion = window.matchMedia(`(prefers-reduced-motion: reduce)`) === true || window.matchMedia(`(prefers-reduced-motion: reduce)`).matches === true;

        const initCarousels = (elements) => {
            if (typeof Swiper === 'undefined' || !Swiper) {
                return;
            }

            // Product carousel
            const initProductCarousel = (slider) => {
                const swiperElement = slider.querySelector('.swiper');

                // Add Swiper pagination & arrows
                if (slider.dataset.showArrows === 'true' || slider.dataset.showDots === 'true') {
                        slider.insertAdjacentHTML(
                        'beforeend',
                        '<?= $escaper->escapeJs($block->getBlockHtml('pagebuilder.carousel.nav')) ?>'
                    );

                    if (slider.dataset.showArrows === 'true') {
                        const arrows = slider.querySelectorAll('.swiper-nav button');

                        [...arrows].map((arrow) => {
                            arrow.classList.remove('!hidden');
                        })
                    }

                    if (slider.dataset.showDots === 'true') {
                        slider.querySelector('.swiper-pagination').classList.remove('!hidden');
                    }
                }

                // Swiper options
                const swiper = new Swiper(swiperElement, {
                    slidesPerView: 1.2,
                    spaceBetween: 16,
                    loop: true == (slider.dataset.infiniteLoop === 'true'),
                    autoplay: (!reducedMotion && slider.dataset.autoplay === 'true') ? {
                        delay: slider.dataset.autoplaySpeed,
                    } : false,
                    navigation: (slider.dataset.showArrows === 'true') ? {
                        nextEl: slider.querySelector('.swiper-button-next'),
                        prevEl: slider.querySelector('.swiper-button-prev'),
                    } : false,
                    pagination: (slider.dataset.showDots === 'true') ? {
                        el: '.swiper-pagination',
                        type: 'bullets',
                        clickable: true,
                    } : false,
                    breakpoints: {
                        768: {
                            slidesPerView: 2.75,
                            spaceBetween: 24,
                        },
                        1024: {
                            slidesPerView: 3,
                        },
                        1780: {
                            slidesPerView: 4,
                        },
                    }
                });
            };


            // Key visual slider
            const initSlider = (slider) => {
                const swiperElement = slider.parentElement;
                const slides = slider.querySelectorAll('[data-content-type="slide"]');

                // Add Swiper classes
                swiperElement.classList.add('swiper', 'slider');
                slider.classList.add('swiper-wrapper');

                [...slides].map((slide) => {
                    slide.classList.add('swiper-slide');
                })

                // Add Swiper pagination & arrows
                if (slider.dataset.showArrows === 'true' || slider.dataset.showDots === 'true') {
                    swiperElement.insertAdjacentHTML(
                        'beforeend',
                        '<?= $escaper->escapeJs($block->getBlockHtml('pagebuilder.carousel.nav')) ?>'
                    );

                    if (slider.dataset.showArrows === 'true') {
                        const arrows = swiperElement.querySelectorAll('.swiper-nav button');

                        [...arrows].map((arrow) => {
                            arrow.classList.remove('!hidden');
                        })
                    }

                    if (slider.dataset.showDots === 'true') {
                        swiperElement.querySelector('.swiper-pagination').classList.remove('!hidden');
                    }
                }

                // Swiper options
                const swiper = new Swiper(swiperElement, {
                    slidesPerView: 1,
                    loop: true == (slider.dataset.infiniteLoop === 'true'),
                    autoplay: (!reducedMotion && slider.dataset.autoplay === 'true') ? {
                        delay: slider.dataset.autoplaySpeed,
                    } : false,
                    navigation: (slider.dataset.showArrows === 'true') ? {
                        nextEl: swiperElement.querySelector('.swiper-button-next'),
                        prevEl: swiperElement.querySelector('.swiper-button-prev'),
                    } : false,
                    pagination: (slider.dataset.showDots === 'true') ? {
                        el: '.swiper-pagination',
                        type: 'bullets',
                        clickable: true,
                    } : false,
                });
            };


            // Category doorways - carousel
            const initCarousel = (slider) => {
                const swiperElement = slider.parentElement;
                const slides = slider.querySelectorAll('[data-content-type="slide"]');

                // Add Swiper classes
                swiperElement.classList.add('swiper', '!container');
                swiperElement.parentElement.classList.add('carousel');
                slider.classList.add('swiper-wrapper');

                [...slides].map((slide) => {
                    slide.classList.add('swiper-slide');
                })

                // Add Swiper pagination & arrows
                if (slider.dataset.showArrows === 'true' || slider.dataset.showDots === 'true') {
                    swiperElement.insertAdjacentHTML(
                        'beforeend',
                        '<?= $escaper->escapeJs($block->getBlockHtml('pagebuilder.carousel.nav')) ?>'
                    );

                    if (slider.dataset.showArrows === 'true') {
                        const arrows = swiperElement.querySelectorAll('.swiper-nav button');

                        [...arrows].map((arrow) => {
                            arrow.classList.remove('!hidden');
                        })
                    }

                    if (slider.dataset.showDots === 'true') {
                        swiperElement.querySelector('.swiper-pagination').classList.remove('!hidden');
                    }
                }

                // Swiper options
                const swiper = new Swiper(swiperElement, {
                    slidesPerView: 3.2,
                    spaceBetween: 16,
                    loop: true == (slider.dataset.infiniteLoop === 'true'),
                    autoplay: (!reducedMotion && slider.dataset.autoplay === 'true') ? {
                        delay: slider.dataset.autoplaySpeed,
                    } : false,
                    navigation: (slider.dataset.showArrows === 'true') ? {
                        nextEl: swiperElement.querySelector('.swiper-button-next'),
                        prevEl: swiperElement.querySelector('.swiper-button-prev'),
                    } : false,
                    pagination: (slider.dataset.showDots === 'true') ? {
                        el: '.swiper-pagination',
                        type: 'bullets',
                        clickable: true,
                    } : false,
                    breakpoints: {
                        640: {
                            spaceBetween: 24,
                        },
                        768: {
                            spaceBetween: 48,
                            slidesPerView: 3,
                        },
                        1024: {
                            spaceBetween: 48,
                            slidesPerView: slider.dataset.slidesPerPage,
                        },
                    }
                });
            };


            // Image slider
            const initImageSlider = (slider) => {
                const swiperElement = slider.parentElement;
                const slides = slider.querySelectorAll('[data-content-type="slide"]');

                [...slides].map((slide) => {
                    slide.classList.add('swiper-slide');
                })

                // Add Swiper pagination & arrows
                if (slider.dataset.showArrows === 'true' || slider.dataset.showDots === 'true') {
                    swiperElement.insertAdjacentHTML(
                        'beforeend',
                        '<?= $escaper->escapeJs($block->getBlockHtml('pagebuilder.carousel.nav')) ?>'
                    );

                    if (slider.dataset.showArrows === 'true') {
                        const arrows = swiperElement.querySelectorAll('.swiper-nav button');

                        [...arrows].map((arrow) => {
                            arrow.classList.remove('!hidden');
                        })
                    }

                    if (slider.dataset.showDots === 'true') {
                        swiperElement.querySelector('.swiper-pagination').classList.remove('!hidden');
                    }
                }

                // Swiper options
                const swiper = new Swiper(swiperElement, {
                    slidesPerView: 1,
                    spaceBetween: 24,
                    loop: true == (slider.dataset.infiniteLoop === 'true'),
                    autoplay: (!reducedMotion && slider.dataset.autoplay === 'true') ? {
                        delay: slider.dataset.autoplaySpeed,
                    } : false,
                    navigation: (slider.dataset.showArrows === 'true') ? {
                        nextEl: swiperElement.querySelector('.swiper-button-next'),
                        prevEl: swiperElement.querySelector('.swiper-button-prev'),
                    } : false,
                    pagination: (slider.dataset.showDots === 'true') ? {
                        el: '.swiper-pagination',
                        type: 'bullets',
                        clickable: true,
                    } : false,
                    breakpoints: {
                        768: {
                            slidesPerView: 'auto',
                        },
                        1024: {
                            slidesPerView: 'auto',
                            slidesOffsetAfter: 48,
                        }
                    }
                });
            };


            // Init scripts
            elements.forEach(element => {
                if (element.dataset.contentType === 'products') {
                    initProductCarousel(element);
                }

                if (element.dataset.appearance === 'slider' || element.dataset.appearance === 'default') {
                    initSlider(element);
                }

                if (element.dataset.appearance === 'carousel' && element.dataset.contentType === "slider") {
                    initCarousel(element);
                }

                if (element.dataset.appearance === 'image_slider') {
                    initImageSlider(element);
                }
            });
        };

        const loadCarousels = () => {
            const carouselElements = document.querySelectorAll(
                `[data-content-type="products"],[data-appearance="carousel"],[data-appearance="image_slider"],[data-content-type="slider"]`
            );

            // Load Carousels
            if (carouselElements.length > 0) {
                const swiperScript = document.createElement('script');
                const hasSwiperScript = document.querySelector(`script[data-swiper-script="init"]`);
                if (!hasSwiperScript) {
                    // Load Swiper script
                    swiperScript.type = 'text/javascript';
                    swiperScript.src = '<?= SwiperScript::SWIPER_JS ?>';
                    swiperScript.setAttribute('data-swiper-script', 'init');
                    swiperScript.setAttribute('rel', 'preconnect');
                    document.head.appendChild(swiperScript);
                }

                const swiperStyle = document.createElement('link');
                const hasSwiperStyle = document.querySelector(`script[data-swiper-style="init"]`);
                if (!hasSwiperStyle) {
                    // Load Swiper stylesheet
                    swiperStyle.rel = 'stylesheet';

                    swiperStyle.href = '<?= SwiperScript::SWIPER_CSS ?>';
                    swiperStyle.setAttribute('data-swiper-style', 'init');
                    document.head.appendChild(swiperStyle);
                }
                if (hasSwiperScript) {
                    initCarousels(carouselElements);
                } else {
                    swiperScript.addEventListener('load', () => {
                        initCarousels(carouselElements);
                    });
                }
            }
        }

        ['DOMContentLoaded', 'init-page-builder-elements', 'category-description-rendered'].forEach(event => {
            window.addEventListener(event, () => {
                loadCarousels();
            });
        });
    })();
</script>
