<?php

declare(strict_types=1);

use Hyva\Theme\ViewModel\ProductListItem;
use Hyva\Theme\ViewModel\ProductPage;
use Magento\Catalog\Block\Product\ReviewRendererInterface;

/** @var \Magento\Catalog\Block\Product\ListProduct $block */
/** @var \Hyva\Theme\Model\ViewModelRegistry $viewModels */
/** @var \Magento\Framework\Escaper $escaper */

/** @var ProductPage $productViewModel */
$productViewModel = $viewModels->require(ProductPage::class);
/** @var ProductListItem $productListItemViewModel */
$productListItemViewModel = $viewModels->require(ProductListItem::class);

if (!$block->getProductCollection() || !$block->getProductCollection()->getSize()) {
    return '';
}
?>
<?php
$type = 'widget-product-carousel';
$mode = 'grid';
$image = 'new_products_content_widget_grid';
$items = $block->getProductCollection()->addMediaGalleryData()->getItems();
$templateType = ReviewRendererInterface::SHORT_VIEW;
$description = false;
?>
<div class="container mb-6 lg:min-h-[3rem] product-carousel-header">
    <?php if (!empty($title = $block->getTitle())) : ?>
        <h2 data-content-type="heading" class="!mb-0 heading-4"><?= $escaper->escapeHtml($title); ?></h2>
    <?php endif; ?>
</div>
<div class="lg:container products-container">
    <div class="swiper mb-8 <?= /* @noEscape */ $type ?> max-lg:!container">
        <div class="relative grid mx-auto swiper-wrapper sm:grid-cols-2 xl:grid-cols-3 3xl:grid-cols-4">
            <?php foreach ($items as $item): ?>
                <div class="swiper-slide !h-auto">
                    <?= $productListItemViewModel->getItemHtml($item, $block, $mode, $templateType, $image, $description) ?>
                </div>
            <?php endforeach ?>
        </div>
    </div>
</div>
