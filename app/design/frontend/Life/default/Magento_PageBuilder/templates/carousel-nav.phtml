<?php

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\SvgIcons;
use Magento\Framework\View\Element\Template;
use Magento\Framework\Escaper;

/** @var Template $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */

/** @var SvgIcons $svgIcons */
$svgIcons = $viewModels->require(SvgIcons::class);
?>

<div class="swiper-nav">
    <button
        aria-label="<?= $escaper->escapeHtml(__('Previous')) ?>"
        class="!hidden swiper-button-prev">
        <?= $svgIcons->arrowCircleLeftHtml("!w-12 !h-12 flex-shrink-0") ?>
    </button>
    <div class="!hidden swiper-pagination"></div>
    <button
        aria-label="<?= $escaper->escapeHtml(__('Next')) ?>"
        class="!hidden swiper-button-next">
        <?= $svgIcons->arrowCircleRightHtml("!w-12 !h-12 flex-shrink-0") ?>
    </button>
</div>
