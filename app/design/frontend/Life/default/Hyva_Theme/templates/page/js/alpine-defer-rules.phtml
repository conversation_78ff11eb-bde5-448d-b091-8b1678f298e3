<?php

declare(strict_types=1);

/** @var \Magento\Framework\View\Element\Template $block */
/** @var \Hyva\Theme\Model\ViewModelRegistry $viewModels */

use Hyva\Theme\ViewModel\StoreConfig;

$storeConfig = $viewModels->require(StoreConfig::class);
$deferred = $block->getData('deferred_components') ?? [];

if ($deferredComponentsConfig = $storeConfig->getStoreConfig('hyva_theme_general/alpine_defer/deferred_components')) {
    foreach (json_decode($deferredComponentsConfig, true) ?: [] as $tuple) {
        $deferred[$tuple['selector']] = $tuple['defer_until'];
    }
}

if (!$deferred) {
    return '';
}
?>
<script>
    for (const [selector, deferUntil] of Object.entries(<?= /** @noEscape */ json_encode($deferred) ?>)) {
        document.querySelectorAll(selector).forEach(el => {
            if (!el.classList.contains('skip-defer')) {
                el.setAttribute('x-defer', `${deferUntil}`)
            }
        });
    }
</script>
