<?xml version="1.0"?>
<page
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd"
>
    <body>
        <referenceContainer name="before.body.end">
            <block
                class="Magento\Catalog\Block\Product\View\Gallery"
                name="list.product.gallery.ui"
                template="Magento_Catalog::product/list/gallery-ui.phtml"
            />
            <block
                class="Magento\Framework\View\Element\Template"
                name="list.product.swatches.renderer.ui"
                template="Hyva_SmileElasticsuite::swatch/product/listing/renderer-ui.phtml"
            />
        </referenceContainer>

        <referenceBlock name="page-js-dependencies">
            <arguments>
                <argument name="blockOutputPatternMap" xsi:type="array">
                    <!-- Make sure carousel JS is loaded on all pages -->
                    <item name="page_builder.carousel" xsi:type="array">
                        <item name="regex" xsi:type="string">/html/</item>
                        <item name="template" xsi:type="string">Magento_PageBuilder::widgets/carousel.phtml</item>
                    </item>
                    <item name="page_builder.parallax" xsi:type="array">
                        <item name="regex" xsi:type="string">/html/</item>
                        <item name="template" xsi:type="string">Magento_PageBuilder::widgets/parallax.phtml</item>
                    </item>
                </argument>
            </arguments>
        </referenceBlock>

        <referenceBlock name="script-alpine-js">
            <block name="alpine-plugin-focus" template="Hyva_Theme::page/js/plugins/focus.phtml" />
        </referenceBlock>

        <referenceBlock name="add-store-locator-link" remove="true" />
    </body>
</page>
