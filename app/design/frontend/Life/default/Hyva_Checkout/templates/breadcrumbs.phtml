<?php

declare(strict_types=1);

use Hyva\Checkout\ViewModel\Breadcrumbs;

/** @var \Magento\Framework\View\Element\Template $block */
/** @var \Hyva\Theme\Model\ViewModelRegistry $viewModels */

/** @var \Hyva\Checkout\ViewModel\Breadcrumbs $viewModel */
$viewModel = $viewModels->require(Breadcrumbs::class);

$config = $viewModel->getCheckoutConfig();
?>
<nav
    id="breadcrumbs"
    class="relative overflow-hidden after:bottom-6 after:absolute pb-10 nav-breadcrumbs after:content-[''] after:h-px after:w-full after:bg-secondary-900/50 after:block">
    <?php if ($config->hasSteps() && $config->isMultiStepper()): ?>
        <?= $block->getChildHtml('hyva.checkout.breadcrumbs.waypoints') ?>
    <?php endif ?>
</nav>

<div class="relative flex flex-col w-full ml-auto space-y-4 md:space-x-4 md:space-y-0 md:flex-row checkout-login">
    <?= $block->getChildHtml('hyva.checkout.breadcrumbs.additional') ?>
</div>
