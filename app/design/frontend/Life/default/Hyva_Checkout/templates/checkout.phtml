<?php

/** @var $block MagePal\GoogleAnalytics4\Block\Data\Checkout **/

?>

<script defer="defer" type="text/javascript">
    var data = {
        'currency': "<?= /* @noEscape */ $block->getStoreCurrencyCode() ?>",
        'items':  <?= /* @noEscape */ $block->getCartJson() ?>,
        'value': <?= /* @noEscape */ $block->getQuote()->getGrandTotal() ?>
    };

    window.dataLayer.push({
        'event': 'begin_checkout',
        'ecommerce': data
    });

    function handleEcommerceDatalayerEvent(eventName) {
        if(eventName === 'add_shipping_info') {
            const selectedDeliveryMethod = document.querySelectorAll('#shipping-method-list input[type="radio"]:checked');

            delete data.payment_type;
            data.shipping_tier = selectedDeliveryMethod[0].value ?? null;

            window.dataLayer.push({
                'event': eventName,
                'ecommerce': data
            });
        }

        if(eventName === 'add_payment_info') {
            const selectedPaymentMethod = document.querySelectorAll('#payment-method-list input[type="radio"]:checked');

            delete data.shipping_tier;
            data.payment_type = selectedPaymentMethod[0].value ?? null;

            window.dataLayer.push({
                'event': eventName,
                'ecommerce': data
            });
        }
    }
</script>
