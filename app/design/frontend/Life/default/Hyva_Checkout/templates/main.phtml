<?php

declare(strict_types=1);

use Hyva\Checkout\ViewModel\Main as ViewModel;

/** @var \Magento\Framework\View\Element\Template $block */
/** @var \Hyva\Checkout\Magewire\Main $magewire */
/** @var \Magento\Framework\Escaper $escaper */
/** @var \Hyva\Theme\Model\ViewModelRegistry $viewModels */

/** @var ViewModel $viewModel */
$viewModel = $viewModels->require(ViewModel::class);
?>
<div
    id="hyva-checkout-main"
    class="<?= $escaper->escapeHtmlAttr($viewModel->getStepClassesAsString()) ?> relative"
    role="main"
>
    <?= $block->getChildHtml('hyva.checkout.breadcrumbs') ?>

    <?php /* Change the Frontend API main.container when you change the id attribute value. */ ?>
    <div id="hyva-checkout-container">
        <?= $block->getChildHtml('hyva.checkout.container') ?>
    </div>

    <?php /* Render if it is still is a child of "hyva.checkout.main", usually it will be moved into a column. */ ?>
    <?= $block->getChildHtml('hyva.checkout.navigation') ?>

    <?= $block->fetchView(
        (string)$block->getTemplateFile('Hyva_CheckoutPostcodeNL::form/field/postcodenl/address-auto-complete-ui.phtml')
    ) ?>
</div>
