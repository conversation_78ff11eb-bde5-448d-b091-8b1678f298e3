<?php

declare(strict_types=1);

use Hyva\Checkout\ViewModel\Breadcrumbs;
use Hyva\Theme\ViewModel\HeroiconsOutline;

/** @var \Magento\Framework\View\Element\Template $block */
/** @var \Hyva\Theme\Model\ViewModelRegistry $viewModels */
/** @var \Magento\Framework\Escaper $escaper */

/** @var Breadcrumbs $viewModel */
$viewModel = $viewModels->require(Breadcrumbs::class);

/** @var \Hyva\Theme\ViewModel\HeroiconsOutline $heroiconsViewModel */
$heroiconsViewModel = $viewModels->require(HeroiconsOutline::class);

$config = $viewModel->getCheckoutConfig();
$current = $config->getCurrentStep();
$settingShowWaypoints = $viewModel->getSystemConfig()->showBreadcrumbsWaypoints();
$settingShowBreadcrumbsCart = $viewModel->getSystemConfig()->showBreadcrumbsCart();
?>
<?php $stepCount = count($config->getSteps()) ?>
<ul class="flex items-center justify-between breadcrumbs">
    <?php if ($settingShowBreadcrumbsCart): ?>
        <li class="inline-flex items-center justify-center w-1/3 space-x-4 text-sm">
            <a href="<?= $escaper->escapeUrl($block->getUrl('checkout/cart', ['_secure' => true])) ?>"
               class="item completed"
            >
                <span itemprop="name">
                    <?= $heroiconsViewModel->shoppingCartHtml('w-5 h-5 text-gray-400') ?>
                </span>
            </a>
        </li>
    <?php endif ?>

    <?php foreach ($config->getSteps() as $step): ?>
        <li class="inline-flex items-center justify-center w-1/3 space-x-4 text-sm">
            <?php if (
                ($settingShowWaypoints || $step['position'] <= $current['position'])
                && $step['position'] !== $config->getFirstStep('position')
            ): ?>
            <?php endif ?>
            <?php if ($config->canStepTo($step['name'])): ?>
                <button type="button"
                    <?php if ($step['position'] === $current['position']): ?>
                        class="item active"
                    <?php else: ?>
                        class="item completed"
                    <?php endif ?>
                        onclick="hyvaCheckout.navigation.stepTo('<?= $escaper->escapeHtmlAttr($step['route']) ?>', false)"
                >
                    <?= $escaper->escapeHtml(__($step['label'])) ?>
                </button>
            <?php elseif ($settingShowWaypoints): ?>
                <span class="item locked">
                    <?= $escaper->escapeHtml(__($step['label'])) ?>
                </span>
            <?php endif ?>
        </li>
    <?php endforeach ?>
    <li class="inline-flex items-center justify-center w-1/3 space-x-4 text-sm">
        <span class="item locked">
            <?= $escaper->escapeHtml(__('Payment')); ?>
        </span>
    </li>
</ul>
