<?php

declare(strict_types=1);

use Hyva\Checkout\ViewModel\Breadcrumbs\SigninRegister;

/** @var \Magento\Framework\View\Element\Template $block */
/** @var \Hyva\Theme\Model\ViewModelRegistry $viewModels */
/** @var \Magento\Framework\Escaper $escaper */

/** @var SigninRegister $viewModel */
$viewModel = $viewModels->require(SigninRegister::class);
?>
<?php if ($viewModel->getCustomerSession()->isLoggedIn() === false): ?>
<div class="sm:absolute sm:right-0 text-base leading-8 sm:top-[1.125rem]">
    <span><?= $escaper->escapeHtml(__('Do you already have an account?')); ?></span>
    <a
        class="text-link"
        href="<?= $escaper->escapeUrl(
            $block->getUrl('customer/account/login', ['referer' => base64_encode($block->getUrl('checkout'))])
        ) ?>"
    >
        <?= $escaper->escapeHtml(__('Log in')) ?>
    </a>
</div>
<?php endif ?>
