<?php

declare(strict_types=1);

/** @var \Magento\Framework\View\Element\Template $block */

$navHtmlLeft = $block->getChildHtml('hyva.checkout.navigation.left');
$navHtmlRight = $block->getChildHtml('hyva.checkout.navigation.right');
?>
<nav x-data="initCheckoutNavigation()">
    <?php if (! empty($navHtmlLeft)): ?>
        <div class="flex flex-col-reverse w-full rounded-lg md:flex-row gap-y-2 md:gap-x-2 md:items-center md:rounded-r-none">
            <?= /* @noEscape */ $navHtmlLeft ?>
        </div>
    <?php endif ?>

    <div class="flex flex-wrap items-center justify-between gap-3">
        <?= /* @noEscape */ $navHtmlRight ?>
    </div>
</nav>
