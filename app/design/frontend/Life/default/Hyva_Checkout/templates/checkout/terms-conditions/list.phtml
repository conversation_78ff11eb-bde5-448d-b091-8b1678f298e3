<?php

declare(strict_types=1);

use Hyva\Theme\ViewModel\HeroiconsOutline;

/** @var \Magento\Framework\View\Element\Template $block */
/** @var \Hyva\Checkout\Magewire\Checkout\TermsConditions $magewire */
/** @var \Hyva\Theme\Model\ViewModelRegistry $viewModels */
/** @var \Magento\Framework\Escaper $escaper */

/** @var HeroiconsOutline $iconsViewModel */
$iconsViewModel = $viewModels->require(HeroiconsOutline::class);

$availableTerms = $block->getData('terms');
?>
<?php if (is_array($availableTerms) && count($availableTerms) !== 0): ?>
<div class="space-y-4">
    <?php foreach ($availableTerms as $agreement): ?>
        <div
            x-data="{ open: false, hasError: false }"
            x-on:quote:actions:error.window="hasError = ('terms' in $event.detail) && $event.detail.terms.includes(<?=
                $escaper->escapeJs($agreement->getAgreementId()) ?>)"
        >
            <div class="flex gap-x-4">
                <div class="flex items-center">
                    <input
                        type="checkbox"
                        class="flex-none disabled:opacity-25 relative after:absolute after:w-[44px] after:h-[44px] after:left-1/2 after:top-1/2 after:-translate-x-1/2 after:-translate-y-1/2"
                        id="agreement_<?= $escaper->escapeHtmlAttr($agreement->getAgreementId()) ?>"
                        value="<?= $escaper->escapeHtmlAttr($agreement->getMode()) ?>"
                        wire:model="termAcceptance.<?= $escaper->escapeHtmlAttr($agreement->getAgreementId()) ?>"
                        required
                        x-on:input="hasError = hasError && ! $event.target.checked"
                    />
                </div>

                <div class="flex justify-between text-xs gap-x-4 terms-and-conditions sm:text-sm">
                    <label for="agreement_<?= $escaper->escapeHtmlAttr($agreement->getAgreementId()) ?>">
                        <?= /** @noEscape */ __(
                                'I agree to the <a target="_blank" href="%1">terms and conditions</a>',
                                $block->getBaseUrl() . trim($agreement->getContent(), '/')
                            );
                        ?>
                    </label>
                </div>
            </div>
            <div x-show="hasError" class="relative my-4 text-sm component-messenger message error" role="alert">
                <?= $escaper->escapeHtml(__('Please accept all terms & conditions')); ?>
            </div>
        </div>
    <?php endforeach ?>
</div>
<?php endif ?>
