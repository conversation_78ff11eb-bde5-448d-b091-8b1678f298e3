<?php

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\SvgIcons;
use Hyva\Checkout\Magewire\Checkout\CouponCode as Magewire;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;

/** @var Template $block */
/** @var Magewire $magewire */
/** @var ViewModelRegistry $viewModels */
/** @var Escaper $escaper */

/** @var SvgIcons $svgIcons */
$svgIcons = $viewModels->require(SvgIcons::class);

$classCouponAvailability = $magewire->couponCode ? 'available' : 'not-available';
?>
<div
    x-data="{ expand: true,
        couponCode: $wire.entangle('couponCode'),
        couponHits: $wire.entangle('couponHits')
    }"
    id="coupon-code"
>
    <header>
        <button
            type="button"
            class="flex items-center w-full gap-x-3 focus:outline-none"
            x-on:click="expand = ! expand"
            x-bind:title="expand ? '<?= $escaper->escapeHtml(__('Hide items')) ?>' : '<?= $escaper->escapeHtml(__('Show items')) ?>'"
        >
            <div class="relative flex text-sm font-bold">
                <?= $escaper->escapeHtml(__('Discount Code')) ?>
            </div>

            <span :class="{ 'transform rotate-180': expand }">
                <?= $svgIcons->arrowDownHtml('w-4 h-4', 16, 16) ?>
            </span>
        </button>
    </header>

    <div
        class="coupon-code border border-secondary-900/50 rounded-xl p-6 my-3 <?= $escaper->escapeHtmlAttr($classCouponAvailability) ?>"
        x-show="expand"
        x-cloak
    >
        <div class="flex flex-wrap gap-1 sm:flex-nowrap">
            <input
                type="text"
                class="!rounded-full form-input focus:ring-0 sm:w-auto flex-grow sm:max-w-[20rem]"
                placeholder="<?= $escaper->escapeHtmlAttr(__('Enter your code')) ?>"
                wire:model.defer="couponCode"
                wire:loading.attr="disabled"
                x-bind:class="{ 'has-coupon': couponCode, 'disabled': couponCode, 'invalid': couponHits !== 0 }"
                wire:keydown.enter="applyCouponCode"
            />

            <button
                type="button"
                class="justify-center btn btn-secondary !rounded-full h-10"
                wire:click="applyCouponCode"
                wire:loading.attr="disabled"
                wire:loading.class="disabled"
                x-bind:class="{ 'has-coupon': couponCode, 'disabled': ! couponCode, 'invalid': couponHits !== 0 }"
                x-cloak
            >
                <span wire:loading.block class="hidden">
                    <?= $escaper->escapeHtml(__('Processing...')) ?>
                </span>
                <span wire:loading.remove>
                    <?= $escaper->escapeHtml(__('Apply')) ?>
                </span>
            </button>
        </div>
        <?php if ($magewire->couponCode): ?>
            <div class="text-sm font-bold rounded-md bg-secondary-300 px-2 py-1.5 mt-1 inline-flex gap-x-6 items-center">
                <?= $escaper->escapeHtml($magewire->couponCode) ?>
                <span
                    class="cursor-pointer"
                    wire:click="revokeCouponCode"
                    wire:loading.attr="disabled"
                    wire:loading.class="disabled"
                    x-cloak
                    wire:loading.remove
                >
                    <?= $svgIcons->closeHtml('w-4 h-4', 16, 16) ?>
                </span>
            </div>
        <?php endif ?>
    </div>
</div>
