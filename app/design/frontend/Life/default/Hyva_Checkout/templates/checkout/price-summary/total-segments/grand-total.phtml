<?php

declare(strict_types=1);

use Hyva\Checkout\ViewModel\Checkout\Formatter as FormatterViewModel;

/** @var \Magento\Framework\View\Element\Template $block */
/** @var \Hyva\Theme\Model\ViewModelRegistry $viewModels */
/** @var \Magento\Framework\Escaper $escaper */

/** @var FormatterViewModel $formatterViewModel */
$formatterViewModel = $viewModels->require(FormatterViewModel::class);

$segment = $block->getSegment();
?>
<div class="flex items-center justify-between gap-4 pt-4 mt-4 border-t border-secondary-900/50">
    <span class="text-base leading-[1.875rem] label">
        <?= $escaper->escapeHtml(__(($segment['title'] ?? 'Order Total'))) ?>
    </span>
    <span class="font-bold text-base leading-[1.875rem] text-right value">
        <?= /* @noEscape */ $formatterViewModel->currency($segment['value'] ?? 0) ?>
    </span>
</div>
