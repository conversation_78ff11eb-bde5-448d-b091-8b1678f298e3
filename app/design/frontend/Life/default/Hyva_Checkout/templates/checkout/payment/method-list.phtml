<?php

declare(strict_types=1);

use Hyva\Checkout\ViewModel\Checkout\Payment\MethodList as ViewModel;

/** @var \Magento\Framework\View\Element\Template $block */
/** @var \Hyva\Theme\Model\ViewModelRegistry $viewModels */
/** @var \Magento\Quote\Api\Data\PaymentMethodInterface $method */
/** @var \Hyva\Checkout\Magewire\Checkout\Payment\MethodList $magewire */
/** @var \Magento\Framework\Escaper $escaper */
/** @var \Hyva\Checkout\Model\MethodMetaDataInterface $methodMetaData */

/** @var ViewModel $viewModel */
$viewModel = $viewModels->require(ViewModel::class);
$methods = $viewModel->getList();
?>
<div id="payment-methods" class="relative">
    <?= /** @noEscape */ $block->getChildHtml('checkout.payment.methods.title') ?>
    <?php if ($methods): ?>
        <script>
            window.addEventListener('checkout:step:loaded', () => {
                if('<?= $escaper->escapeJs($magewire->method) ?>' && document.getElementById('payment-method-list')) {
                    window.dispatchEvent(
                        new CustomEvent(
                            'checkout:payment:method-activate',
                            { detail: { method: '<?= $escaper->escapeJs($magewire->method) ?>'} }
                        )
                    )
                }
            }, { once: true })
        </script>

        <ol id="payment-method-list" class="mt-3">
            <?php foreach ($methods as $method): ?>
                <?php $methodCodeAttr = $escaper->escapeHtmlAttr($method->getCode()) ?>
                <?php $methodMetaData = $viewModel->getMethodMetaData($block, $method) ?>

                <li
                    id="payment-method-option-<?= /* @noEscape */ $methodCodeAttr ?>"
                    class="
                        border border-secondary-900/50 rounded-xl mb-3
                        <?= $magewire->method === $method->getCode() ? 'active' : 'inactive' ?>
                    "
                    wire:key="<?= /* @noEscape */ $methodCodeAttr ?>"
                >
                    <span class="relative block">
                        <input
                            type="radio"
                            class="absolute transform -translate-y-1/2 top-1/2 left-4 sm:left-6 disabled:opacity-25"
                            id="payment-method-<?= /* @noEscape */ $methodCodeAttr ?>"
                            name="payment-method-option"
                            value="<?= /* @noEscape */ $methodCodeAttr ?>"
                            wire:model="method"
                        />

                        <label
                            class="relative flex items-center w-full py-4 pl-10 pr-6 cursor-pointer sm:pl-14 gap-x-2"
                            for="payment-method-<?= /* @noEscape */ $methodCodeAttr ?>"
                        >
                            <?php if ($methodMetaData->getData('icon') && $methodMetaData->canRenderIcon()): ?>
                                <div class="px-1 py-[0.1875rem] border rounded-md border-secondary-900/50 payment-icon">
                                    <?= /* @noEscape */ $methodMetaData->renderIcon() ?>
                                </div>
                            <?php endif ?>
                            <div class="flex flex-col text-sm text-black">
                                <?= $escaper->escapeHtml($method->getTitle()) ?>

                                <?php if ($methodMetaData->hasSubTitle()): ?>
                                    <span class="text-sm font-medium text-gray-500">
                                        <?= $escaper->escapeHtml($methodMetaData->getSubTitle()) ?>
                                    </span>
                                <?php endif ?>
                            </div>
                        </label>
                    </span>

                    <?php if ($viewModel->canShowMethod($block, $method, $magewire->method)): ?>
                        <?php $html = $viewModel->getMethodBlock($block, $method)->toHtml() ?>

                        <?php if (! empty($html)): ?>
                            <div
                                id="<?= 'payment-method-view-' . /* @noEscape */ $methodCodeAttr ?>"
                                class="w-full px-6 pb-4"
                            >
                                <?= /* @noEscape */ $html ?>
                            </div>
                        <?php endif ?>
                    <?php endif ?>

                    <?php if ($method->getCode() === $magewire->method): ?>
                        <div class="px-6 pb-4">
                            <?= /** @noEscape */ $block->getChildHtml('checkout.payment.place-order') ?>
                        </div>
                    <?php endif; ?>
                </li>
            <?php endforeach ?>
        </ol>
    <?php else: ?>
        <div class="message warning">
            <?= $escaper->escapeHtml(__('No Payment method available.')) ?>
        </div>
    <?php endif ?>
    <div
        class="absolute -inset-1 backdrop-blur-xs after:bg-white after:opacity-80"
        id="payment-methods-loading"
        style="display: none"
    >
        <div class="flex h-full items-center justify-center">
            <svg
                class="animate-spin h-6 w-6"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                aria-hidden="true"
            >
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="text-primary" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <span aria-role="alert" class="sr-only">
                <?= $escaper->escapeHtml(__('saving')) /** @phpstan-ignore-line */ ?>
            </span>
        </div>
    </div>
</div>
