<?php

declare(strict_types=1);

use Hyva\Theme\ViewModel\HeroiconsOutline;

/** @var \Magento\Framework\View\Element\Template $block */
/** @var \Hyva\Theme\Model\ViewModelRegistry $viewModels */
/** @var \Hyva\Checkout\Magewire\Checkout\AddressView\ShippingDetails\AddressList $magewire */
/** @var \Magento\Customer\Api\Data\AddressSearchResultsInterface $shippingAddressList */
/** @var \Magento\Customer\Api\Data\AddressInterface $address */
/** @var \Hyva\Theme\ViewModel\HeroiconsOutline $iconsViewModel */
/** @var \Magento\Framework\Escaper $escaper */
/** @var \Hyva\Checkout\ViewModel\Checkout\AddressView\AbstractAddressList $addressList */

$iconsViewModel = $viewModels->require(HeroiconsOutline::class);
$addressList = $block->getData('address_list');
$addressListItems = $addressList->getAddressListItems();
?>
<?= /** @noEscape */ $block->getChildHtml('checkout.billing-shipping.address-list.grid.title') ?>
<?php if ($addressListItems): ?>
    <div class="flex grid grid-cols-1 gap-4 mt-3 address-grid lg:grid-cols-2" data-view="grid">
        <?php foreach ($addressListItems as $address): ?>
            <div
                class="relative text-sm border border-secondary-900/50 rounded-xl address-item group"
                wire:key="address-<?= /* @noEscape */ (int)$address->getId() ?>"
                x-data="{ id: <?= /* @noEscape */ (int)$address->getId() ?> }"
                x-bind:class="{ active: id === <?= /* @noEscape */ (int) $magewire->activeAddressEntity ?> }"
            >
                <input
                    type="radio"
                    wire:model="activeAddressEntity"
                    value="<?=  /* @noEscape */ $address->getId() ?? 0 ?>"
                    name="<?= $escaper->escapeHtmlAttr('address_' . $addressList->getTypeNamespace()) ?>"
                    id="<?= $escaper->escapeHtmlAttr($addressList->renderEntityName($address)) ?>"
                    class="absolute w-4 h-4 left-6 top-5 peer"
                />

                <label
                    for="<?= $escaper->escapeHtmlAttr($addressList->renderEntityName($address)) ?>"
                    class="flex flex-col h-full py-4 pl-12 pr-4 space-y-4 leading-normal cursor-pointer"
                >
                    <span class="h-full">
                        <?= /* @noEscape */ $addressList->renderAddress($address) ?>
                    </span>

                    <?php if (! is_numeric($address->getId())): ?>
                        <div class="flex toolbar gap-x-4">
                            <button
                                wire:click="$emitTo('<?= /* @noEscape */ $addressList->getModalAddressFormBlockName() ?>', 'edit')"
                                class="flex-shrink-0 bg-gray-200 border-0 rounded-full w-9 h-9 group-hover:bg-gray-300 group-hover:text-gray-600"
                                title="<?= $escaper->escapeHtml(__('Edit Address')) ?>"
                            >
                                <span class="inline-flex items-center justify-center w-full h-full rounded-full hover:text-green-700"
                                >
                                    <?= $iconsViewModel->pencilAltHtml('w-5 h-5') ?>
                                </span>
                            </button>
                        </div>
                    <?php endif ?>
                </label>
            </div>
        <?php endforeach ?>
    </div>
<?php endif ?>
