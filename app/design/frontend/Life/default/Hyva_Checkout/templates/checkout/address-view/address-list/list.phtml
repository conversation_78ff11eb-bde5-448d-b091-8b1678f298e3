<?php

declare(strict_types=1);

use Hyva\Theme\ViewModel\HeroiconsOutline;

/** @var \Magento\Framework\View\Element\Template $block */
/** @var \Hyva\Theme\Model\ViewModelRegistry $viewModels */
/** @var \Hyva\Checkout\Magewire\Checkout\AddressView\ShippingDetails\AddressList $magewire */
/** @var \Magento\Customer\Api\Data\AddressSearchResultsInterface $shippingAddressList */
/** @var \Magento\Customer\Api\Data\AddressInterface $address */
/** @var \Hyva\Theme\ViewModel\HeroiconsOutline $iconsViewModel */
/** @var \Magento\Framework\Escaper $escaper */
/** @var \Hyva\Checkout\ViewModel\Checkout\AddressView\AbstractAddressList $addressList */

$iconsViewModel = $viewModels->require(HeroiconsOutline::class);
$addressList = $block->getData('address_list');
$addressListItems = $addressList->getAddressListItems();
?>
<?php if ($addressListItems): ?>
    <ol class="address-grid address-list flex flex-col gap-y-4" data-view="list">
        <?php foreach ($addressListItems as $address): ?>
            <li
                class="address-item"
                wire:key="address-<?= /* @noEscape */ (int)$address->getId() ?>"
                x-data="{ id: <?= /* @noEscape */ (int)$address->getId() ?> }"
                x-bind:class="{
                    active: id === <?= /* @noEscape */ (int) $magewire->activeAddressEntity ?>,
                    inactive: id !== <?= /* @noEscape */ (int) $magewire->activeAddressEntity ?>
                }"
            >
                <div class="flex gap-x-4">
                    <div class="flex items-center">
                        <input
                            type="radio"
                            class="flex-none disabled:opacity-25"
                            wire:model="activeAddressEntity"
                            value="<?=  /* @noEscape */ $address->getId() ?? 0 ?>"
                            name="<?= $escaper->escapeHtmlAttr('address_' . $addressList->getTypeNamespace()) ?>"
                            id="<?= $escaper->escapeHtmlAttr($addressList->renderEntityName($address)) ?>"
                        />
                    </div>

                    <label class="flex-grow cursor-pointer" for="<?= $escaper->escapeHtmlAttr($addressList->renderEntityName($address)) ?>">
                        <span class="text-gray-700 font-bold">
                            <?= /* @noEscape */ $addressList->renderAddress($address, 'oneline') ?>
                        </span>
                    </label>

                    <?php if (! is_numeric($address->getId())): ?>
                        <div class="toolbar block flex gap-x-4">
                            <button
                                wire:click="$emitTo('<?= /* @noEscape */ $addressList->getModalAddressFormBlockName() ?>', 'edit')"
                                class="rounded-full
                                       w-9 h-9
                                       bg-gray-200
                                       border-0
                                       flex-shrink-0
                                       group-hover:bg-gray-300 group-hover:text-gray-600"
                                title="<?= $escaper->escapeHtml(__('Edit Address')) ?>"
                            >
                                <span class="w-full h-full
                                         rounded-full
                                         inline-flex
                                         items-center justify-center
                                         hover:text-green-700"
                                >
                                    <?= $iconsViewModel->pencilAltHtml('w-5 h-5') ?>
                                </span>
                            </button>
                        </div>
                    <?php endif ?>
                </div>
            </li>
        <?php endforeach ?>
    </ol>
<?php endif ?>
