<?php

declare(strict_types=1);

use Magento\CheckoutAgreements\Api\Data\AgreementInterface;

/** @var \Magento\Framework\View\Element\Template $block */
/** @var \Hyva\Checkout\Magewire\Checkout\TermsConditions $magewire */
/** @var \Magento\Framework\Escaper $escaper */

$availableTerms = $block->getData('terms');
?>
<?php if (is_array($availableTerms) && count($availableTerms) !== 0): ?>
    <?php /** @var AgreementInterface $agreement */ ?>
    <?php foreach ($availableTerms as $agreement): ?>
        <p class="bg-gray-100 p-6 rounded-lg space-y-6 mb-6">
            <?= $escaper->escapeHtml(__($agreement->getContent())) ?>
        </p>

        <?php if ($agreement->getData('includes_page') && $url = $agreement->getData('page_url')): ?>
            <a href="<?= $escaper->escapeUrl($url) ?>"
               class="text-sm hover:underline"
               target="_blank"
            >
                <?= $escaper->escapeHtml(__('Read Terms &amp; Conditions')) ?>
            </a>
        <?php endif ?>
    <?php endforeach ?>
<?php endif ?>
