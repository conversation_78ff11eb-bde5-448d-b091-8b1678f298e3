<?php

declare(strict_types=1);

use Hyva\Checkout\ViewModel\Checkout\AddressView;

/** @var \Magento\Framework\View\Element\Template $block */
/** @var \Hyva\Theme\Model\ViewModelRegistry $viewModels */
/** @var \Hyva\Checkout\ViewModel\Checkout\AddressView $addressView */
/** @var \Magento\Framework\Escaper $escaper */

$addressView = $viewModels->require(AddressView\AddressViewBilling::class);
?>
<div class="px-6 py-4 mt-3 text-sm border border-secondary-900/50 rounded-xl">
    <?= /* @noEscape */ $addressView->renderView() ?>

    <?php if (! $addressView->isVirtual()): ?>
        <div
            class="flex items-start mt-4"
            wire:target="billingAsShipping"
            wire:loading.class="opacity-25"
        >
            <label class="inline-flex items-center gap-x-4" for="billing-as-shipping">
                <input
                    id="billing-as-shipping"
                    name="billing-as-shipping"
                    type="checkbox"
                    wire:model="billingAsShipping"
                    wire:loading.attr="disabled"
                />

                <span class="text-black hover:cursor-pointer">
                    <?= $escaper->escapeHtml(__('My billing and shipping address are the same')) ?>
                </span>
            </label>
        </div>
    <?php endif ?>
</div>
