<?php

declare(strict_types=1);

use Hyva\Checkout\ViewModel\Checkout\Formatter as FormatterViewModel;

/** @var \Magento\Framework\View\Element\Template $block */
/** @var \Hyva\Theme\Model\ViewModelRegistry $viewModels */
/** @var \Magento\Framework\Escaper $escaper */

/** @var FormatterViewModel $formatterViewModel */
$formatterViewModel = $viewModels->require(FormatterViewModel::class);

$segment = $block->getSegment();
?>
<div class="flex justify-between gap-4 mb-2">
    <span class="text-sm label">
        <?= $escaper->escapeHtml(__(($segment['title'] ?? 'Cart Subtotal'))) ?>
    </span>
    <span class="text-sm font-bold value">
        <?= /* @noEscape */ $formatterViewModel->currency($segment['value'] ?? 0) ?>
    </span>
</div>
