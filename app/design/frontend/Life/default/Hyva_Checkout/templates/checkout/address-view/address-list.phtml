<?php

declare(strict_types=1);

use Hyva\Theme\ViewModel\Modal;

/** @var \Magento\Framework\View\Element\Template $block */
/** @var \Hyva\Theme\Model\ViewModelRegistry $viewModels */
/** @var \Hyva\Checkout\Magewire\Checkout\AddressView\ShippingDetails\AddressList $magewire */
/** @var \Magento\Customer\Api\Data\AddressSearchResultsInterface $shippingAddressList */
/** @var \Magento\Customer\Api\Data\AddressInterface $shippingAddress */
/** @var \Hyva\Theme\ViewModel\HeroiconsOutline $heroiconsViewModel */
/** @var \Magento\Framework\Escaper $escaper */
/** @var \Hyva\Checkout\ViewModel\Checkout\AddressView\AddressListInterface $addressList */

$addressList = $block->getData('address_list');
$addressListItems = $addressList->getAddressListItems();
$addressFormBlock = $block->getChildBlock('address-form');
?>
<div>
    <?= /* @noEscape */ $block->getChildBlock($addressList->getRendererTypeAlias($addressListItems))
        ->setData('address_list', $addressList)
        ->setData('active_entity', $magewire->activeAddressEntity)
        ->toHtml()
    ?>

    <?php if ($addressFormBlock): ?>
        <?php $modal = $viewModels->require(Modal::class)
            ->createModal()
            ->withDialogRefName($addressFormBlock->getNameInLayout())
            ->withContent($addressFormBlock->toHtml())
            ->addDialogClass('w-full max-w-4xl');
        ?>

        <div
            x-data="hyva.modal()"
            x-on:address-form-modal-show.window="if ($event.detail.type === '<?= $escaper->escapeJs($addressList->getTypeNamespace()) ?>') {
                $dispatch('<?= /* @noEscape */ $addressList->getShowModalEvent() ?>')
            }"
            x-on:address-form-modal-hide.window="if ($event.detail.type === '<?= $escaper->escapeJs($addressList->getTypeNamespace()) ?>') {
                $dispatch('<?= /* @noEscape */ $addressList->getHideModalEvent() ?>')
            }"
            x-on:<?= /* @noEscape */ $addressList->getShowModalEvent() ?>.window="<?= /* @noEscape */ $modal->getShowJs() ?>"
            x-on:<?= /* @noEscape */ $addressList->getHideModalEvent() ?>.window="hide()"
        >
            <?= /* @noEscape */ $modal ?>
        </div>

        <?php if ($addressList->canCreateAddresses()): ?>
            <button
                type="button"
                wire:click="$emitTo('<?= /* @noEscape */ $addressList->getModalAddressFormBlockName() ?>', 'create')"
                class="mt-3 mb-6 text-link"
            >
                <?= $escaper->escapeHtml(__('Add new address')) ?>
            </button>
        <?php endif ?>
    <?php endif ?>
</div>
