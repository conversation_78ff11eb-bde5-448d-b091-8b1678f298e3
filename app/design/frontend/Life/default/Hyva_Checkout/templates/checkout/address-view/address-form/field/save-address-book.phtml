<?php

declare(strict_types=1);

use Hyva\Checkout\Model\Form\EntityFieldInterface;
use Hyva\Theme\Model\ViewModelRegistry;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;
use Magewirephp\Magewire\Component\Form as MagewireFormComponent;

/** @var Template $block */
/** @var EntityFieldInterface $element */
/** @var ViewModelRegistry $viewModels */
/** @var Escaper $escaper */
/** @var MagewireFormComponent $magewire */

/** @Tailwind md:w-1/4 md:w-2/4 md:w-3/4 md:w-4/4 mb-2 */

$element = $block->getData('element');
?>
<div class="<?= /* @noEscape */ $element->isRequired() ? 'required' : 'not-required' ?>">
    <?= /* @noEscape */ $element->getRenderer()->renderBefore($element) ?>

    <?php if ($element->hasRelatives()): ?>
        <div class="group space-y-2 <?= $escaper->escapeHtmlAttr('group-' . $element->getId()) ?>">
    <?php endif ?>

    <label for="<?= $escaper->escapeHtmlAttr($element->getForm()->getNamespace() . '-' . $element->getId()) ?>" class="inline-flex items-center mb-0 cursor-pointer gap-x-2">
        <input class="<?= $escaper->escapeHtmlAttr($element->getClass(['block'])) ?>"
               <?php if ($element->hasAttributes()): ?>
                    <?= /* @noEscape */ $element->renderAttributes($escaper) ?>
               <?php endif ?>
        />

        <span>
            <?= $escaper->escapeHtml(__('Save in address book')) ?>
        </span>
    </label>

    <?php if ($element->hasRelatives()): ?>
        <?php foreach ($element->getRelatives() as $relative): ?>
            <?= /* @noEscape */ $relative->render() ?>
        <?php endforeach ?>
    <?php endif ?>

    <?php if ($element->hasRelatives()): ?>
        </div>
    <?php endif ?>

    <?= /* @noEscape */ $element->getRenderer()->renderAfter($element) ?>
</div>
