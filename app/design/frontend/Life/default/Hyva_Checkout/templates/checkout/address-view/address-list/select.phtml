<?php

declare(strict_types=1);

/** @var \Magento\Framework\View\Element\Template $block */
/** @var \Hyva\Theme\Model\ViewModelRegistry $viewModels */
/** @var \Magento\Customer\Api\Data\AddressSearchResultsInterface $shippingAddressList */
/** @var \Magento\Customer\Api\Data\AddressInterface $shippingAddress */
/** @var \Hyva\Theme\ViewModel\HeroiconsOutline $heroiconsViewModel */
/** @var \Magento\Framework\Escaper $escaper */

$activeEntity = $block->getData('active_entity');
$addressList = $block->getData('address_list');

$addressListItems = $addressList->getAddressListItems();
?>
<?php if ($addressListItems): ?>
    <div class="address-grid address-select flex" data-view="select">
        <label class="sr-only" for="address-list"></label>

        <select
            id="address-list"
            name="address-list"
            class="block w-full h-full form-input"
            wire:model="activeAddressEntity"
        >
            <?php foreach ($addressListItems as $address): ?>
                <option value="<?= $escaper->escapeHtmlAttr($address->getId()) ?>">
                    <?= /* @noEscape */ $addressList->renderAddress($address, 'oneline') ?>
                </option>
            <?php endforeach ?>
        </select>

        <?php if (! is_numeric($activeEntity)): ?>
            <button
                wire:click="$emitTo('<?= /* @noEscape */ $addressList->getModalAddressFormBlockName() ?>', 'edit')"
                class="btn btn-secondary py-0 flex justify-center items-center"
                title="<?= $escaper->escapeHtmlAttr(__('Edit Address')) ?>"
            >
                <?= $heroiconsViewModel->pencilAltHtml('w-5 h-5') ?>
            </button>
        <?php endif ?>
    </div>
<?php else: ?>
    <div class="bg-yellow-100 p-4 border-dashed border-2 border-yellow-400 text-center">
        <?= $escaper->escapeHtml(__('No addresses available.')) ?>
    </div>
<?php endif ?>
