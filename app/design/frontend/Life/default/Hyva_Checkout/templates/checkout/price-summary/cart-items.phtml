<?php

declare(strict_types=1);

use Hyva\Checkout\ViewModel\Checkout\PriceSummary\CartItems as CartItemsViewModel;
use Hyva\Checkout\ViewModel\Checkout\Formatter as FormatterViewModel;
use Hyva\Theme\ViewModel\HeroiconsOutline;

/** @var \Magento\Framework\Escaper $escaper */
/** @var \Magento\Framework\View\Element\Template $block */
/** @var \Hyva\Theme\Model\ViewModelRegistry $viewModels */
/** @var \Hyva\Checkout\Magewire\Checkout\PriceSummary\CartItems $magewire */

/** @var HeroiconsOutline $icons */
$icons = $viewModels->require(HeroiconsOutline::class);

/** @var FormatterViewModel $formatterViewModel */
$formatterViewModel = $viewModels->require(FormatterViewModel::class);

/** @var CartItemsViewModel $cartItemsViewModel */
$cartItemsViewModel = $viewModels->require(CartItemsViewModel::class);

$quoteItems = $magewire->getQuoteItemData();
?>
<div>
    <?php if ($quoteItems): ?>
        <div class="pb-4 mb-4 border-b cart-items border-secondary-900/50">
            <?php foreach ($quoteItems as $item): ?>
                <div class="flex items-center mb-6 gap-x-3 sm:gap-x-6 sm:mb-4 gap-y-2">
                    <div class="relative flex-none">
                        <img
                            src="<?= $escaper->escapeUrl($item['thumbnail']) ?>"
                            width="140"
                            height="80"
                            alt="<?= $escaper->escapeHtmlAttr($item['name']) ?>"
                            loading="lazy"
                            class="w-20 rounded-xl sm:w-auto max-w-[8.75rem]"
                        />
                    </div>

                    <div class="flex-grow space-y-2">
                        <div class="flex justify-between gap-4">
                            <div class="product-title">
                                <p class="text-sm font-bold">
                                    <?= $escaper->escapeHtml($item['name']) ?>
                                </p>
                            </div>

                            <div class="text-sm font-bold product-price">
                                <?= /* @noEscape */ $formatterViewModel->currency($item['row_total_incl_tax']) ?>
                            </div>
                        </div>

                        <?php $optionsRenderer = $cartItemsViewModel->getProductOptionsRenderer($block, $item['product_type']) ?>
                        <?php if ($optionsRenderer): ?>
                            <?= /* @noEscape */ $optionsRenderer->setData('quote_item', $item)->toHtml() ?>
                        <?php endif ?>
                    </div>
                </div>
            <?php endforeach ?>
        </div>
    <?php endif ?>
</div>
