<?php

declare(strict_types=1);

use Hyva\Checkout\ViewModel\Checkout\Formatter as FormatterViewModel;
use Hyva\Checkout\ViewModel\Checkout\Shipping\MethodList as ViewModel;
use Hyva\Theme\ViewModel\SvgIcons;
use Life\HyvaCheckoutExtension\ViewModel\ShippingInfo;

/** @var \Magento\Framework\View\Element\Template $block */
/** @var \Hyva\Theme\Model\ViewModelRegistry $viewModels */
/** @var \Magento\Framework\Escaper $escaper */
/** @var \Magento\Quote\Api\Data\ShippingMethodInterface $method */
/** @var \Hyva\Checkout\Magewire\Checkout\Shipping\MethodList $magewire */

/** @var ViewModel $viewModel */
$viewModel = $viewModels->require(ViewModel::class);

/** @var FormatterViewModel $formatterViewModel */
$formatterViewModel = $viewModels->require(FormatterViewModel::class);

/** @var ShippingInfo $shippingInfo */
$shippingInfo = $viewModels->require(ShippingInfo::class);

/** @var SvgIcons $svgIcons */
$svgIcons = $viewModels->require(SvgIcons::class);

$methods = $viewModel->getList();
?>
<div id="shipping-methods" class="mt-3">
    <?php if ($methods): ?>
        <script>
            window.addEventListener('checkout:step:loaded', () => {
                if('<?= $escaper->escapeJs($magewire->method) ?>' && document.getElementById('shipping-method-list')) {
                    window.dispatchEvent(
                        new CustomEvent('checkout:shipping:method-activate', {
                            detail: {
                                method: '<?= $escaper->escapeJs($magewire->method) ?>'
                            }
                        })
                    )
                }
            }, { once: true })
        </script>

        <ol id="shipping-method-list">
            <?php foreach ($methods as $method): ?>
                <?php if ($method->getAvailable()): ?>
                    <?php $methodCode = $escaper->escapeHtml($method->getMethodCode()) ?>
                    <?php $methodCodeAttr = $escaper->escapeHtmlAttr($method->getMethodCode()) ?>
                    <?php $methodCarrierCode = $escaper->escapeHtmlAttr($method->getCarrierCode()) ?>
                    <?php $methodPrice = $formatterViewModel->currency($viewModel->getMethodPrice($method)) ?>
                    <?php $methodTitle = $escaper->escapeHtml($method->getMethodTitle() ?: __('Unknown')) ?>

                    <li
                        id="shipping-method-option-<?= /* @noEscape */ $methodCarrierCode . '-' . $methodCode ?>"
                        class="border border-secondary-900/50 rounded-xl mb-3 <?= $magewire->method === $methodCarrierCode . '_' . $methodCodeAttr ? 'active' : 'inactive' ?>"
                        wire:key="<?= /* @noEscape */ $methodCodeAttr ?>"
                        x-data="{ disabled: <?= ! $method->getErrorMessage() ? 'false' : 'true' ?> }"
                    >
                        <span class="relative block">
                            <input
                                type="radio"
                                class="absolute transform -translate-y-1/2 top-1/2 left-4 sm:left-6 disabled:opacity-25"
                                id="shipping-method-<?= /* @noEscape */ $methodCarrierCode . '-' . $methodCode ?>"
                                name="shipping-method-option"
                                value="<?= $escaper->escapeHtmlAttr($methodCarrierCode . '_' . $methodCodeAttr) ?>"
                                wire:model="method"
                                x-bind:disabled="disabled"
                            />

                            <label
                                class="relative flex items-center justify-between w-full py-4 pl-10 pr-6 cursor-pointer sm:pl-14 gap-x-2"
                                for="shipping-method-<?= /* @noEscape */ $methodCarrierCode . '-' . $methodCode ?>"
                            >

                                <div class="flex items-center gap-x-2">
                                    <?= $svgIcons->deliveryHtml("w-8 h-8 flex-shrink-0") ?>
                                    <div>
                                        <div class="flex items-center text-sm gap-x-2">
                                            <?= $escaper->escapeHtml($method->getCarrierTitle()); ?>

                                            <?php if ($infoContent = $shippingInfo->getInfoContent($methodCarrierCode . '_' . $methodCodeAttr)): ?>
                                                <div x-data="{show: false}">
                                                    <span class="cursor-pointer" @click="show = true">
                                                        <?= $svgIcons->infoHtml("w-4 h-4") ?>
                                                    </span>
                                                    <div
                                                        class="w-full fixed top-0 bottom-0 right-0 z-50 flex justify-end transition duration-500 ease-in-out bg-black/30"
                                                        x-show="show"
                                                        x-transition:enter-start="opacity-0"
                                                        x-transition:enter-end="opacity-100"
                                                        x-transition:leave-start="opacity-100"
                                                        x-transition:leave-end="opacity-0"
                                                    >
                                                        <div
                                                            class="info-drawer bg-white w-[90%] md:w-3/4 lg:w-1/2 2xl:w-5/12 h-full relative py-6 px-3 md:px-0 md:py-12 rounded-tl-xl rounded-bl-xl"
                                                        >
                                                            <div
                                                                class="absolute cursor-pointer top-6 right-6 md:top-12 md:right-12 text-primary"
                                                                @click="show = false"
                                                            >
                                                                <?= $svgIcons->closeHtml("w-6 h-6") ?>
                                                            </div>
                                                            <div
                                                                @click.outside="show = false"
                                                                class="h-full overflow-y-auto"
                                                            >
                                                                <?= /* @noEscape */ $infoContent; ?>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                        <div class="text-xs text-secondary-900"><?= /** @noEscape */ $methodTitle ?></div>
                                    </div>
                                </div>

                                <div class="text-sm font-bold text-secondary-900">
                                    <?php if (
                                        ($viewModel->getMethodPrice($method) === 0.0)
                                        && in_array($method->getCarrierCode(), ['full_service', 'package_delivery'])
                                    ): ?>
                                        <?= $escaper->escapeHtml(__('Free post-calculation')); ?>
                                    <?php elseif ($viewModel->getMethodPrice($method) === 0.0): ?>
                                        <?= $escaper->escapeHtml(__('Free')); ?>
                                    <?php else: ?>
                                        <?= /** @noEscape */ $methodPrice ?>
                                    <?php endif; ?>
                                </div>
                            </label>

                            <?php if ($method->getErrorMessage()): ?>
                                <div role="alert" class="w-full messages">
                                    <p class="mb-0 message error">
                                        <?= $escaper->escapeHtml(__($method->getErrorMessage())) ?>
                                    </p>
                                </div>
                            <?php endif ?>
                        </span>

                        <?php if ($viewModel->isCurrentShippingMethod($method, $magewire->method)):
                            $additionalCarrierBlock = $block->getChildBlock($method->getCarrierCode());
                            $html = '';
                            if ($viewModel->hasAdditionalView($block, $method)) {
                                $html = $viewModel->getAdditionalViewBlock($block, $method)->toHtml();
                            } elseif ($additionalCarrierBlock) {
                                $html = $additionalCarrierBlock->toHtml();
                            }
                            ?>
                            <?php if (! empty($html)): ?>
                                <div
                                    id="<?= /* @noEscape */ 'shipping-method-view-' . $methodCarrierCode . '-' . $methodCode ?>"
                                    class="w-full mt-2"
                                >
                                    <?= /* @noEscape */ $html ?>
                                </div>
                            <?php endif ?>
                        <?php endif ?>
                    </li>
                <?php endif ?>
            <?php endforeach ?>
        </ol>
    <?php endif ?>
</div>
