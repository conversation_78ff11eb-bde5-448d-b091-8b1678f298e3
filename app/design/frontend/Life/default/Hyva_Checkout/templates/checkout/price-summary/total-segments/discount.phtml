<?php

declare(strict_types=1);

use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;
use Hyva\Checkout\ViewModel\Checkout\Formatter as FormatterViewModel;
use Hyva\Theme\Model\ViewModelRegistry;

/** @var Template $block */
/** @var FormatterViewModel $formatterViewModel */
/** @var ViewModelRegistry $viewModels */
/** @var Escaper $escaper */

$formatterViewModel = $viewModels->require(FormatterViewModel::class);
$segment = $block->getSegment();
?>
<div class="flex justify-between gap-4 mb-2">
    <span class="text-sm label">
        <?= $escaper->escapeHtml(__((ucfirst($segment['code']) ?? 'Discount'))) ?>
    </span>
    <span class="text-sm font-bold value">
        <?= /* @noEscape */ $formatterViewModel->currency($segment['value'] ?? 0) ?>
    </span>
</div>
