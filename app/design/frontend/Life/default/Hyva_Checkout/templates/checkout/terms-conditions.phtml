<?php

declare(strict_types=1);

use Hyva\Checkout\Magewire\Checkout\TermsConditions;
use Magento\Framework\View\Element\Template;

/** @var Template $block */
/** @var TermsConditions $magewire */

$availableTerms = $magewire->getAgreements();
$viewType = $magewire->getSystemConfig()->getTermsAndConditionsType();
?>
<div class="mb-4">
    <?= $block->getChildBlock($viewType)->setData([
        'terms' => $availableTerms
    ])->toHtml() ?>
</div>
