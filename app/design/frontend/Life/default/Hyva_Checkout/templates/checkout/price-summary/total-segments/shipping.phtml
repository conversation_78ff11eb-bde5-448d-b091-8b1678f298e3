<?php

declare(strict_types=1);

use Hyva\Checkout\ViewModel\Checkout\Formatter as FormatterViewModel;
use Life\HyvaCheckoutExtension\ViewModel\ShippingInfo;

/** @var \Magento\Framework\View\Element\Template $block */
/** @var \Hyva\Theme\Model\ViewModelRegistry $viewModels */
/** @var \Magento\Framework\Escaper $escaper */

/** @var FormatterViewModel $formatterViewModel */
$formatterViewModel = $viewModels->require(FormatterViewModel::class);

/** @var ShippingInfo $shippingInfo */
$shippingInfo = $viewModels->require(ShippingInfo::class);
$freeLabel = $shippingInfo->getShippingFreeCostLabel();

$segment = $block->getSegment();
?>
<div class="flex justify-between gap-4 mb-2">
    <span class="text-sm label">
        <?= $escaper->escapeHtml($shippingInfo->getCurrentShippingMethodDescription() ?: __('Shipping')); ?>
    </span>
    <span class="text-sm font-bold value">
        <?= /* @noEscape */ ($segment['value'] == 0) ?
            $freeLabel :
            $formatterViewModel->currency($segment['value'] ?? 0)
        ?>
    </span>
</div>
