<?php

declare(strict_types=1);

use Hyva\Theme\ViewModel\HeroiconsOutline;

/** @var \Magento\Framework\View\Element\Template $block */
/** @var \Hyva\Theme\Model\ViewModelRegistry $viewModels */
/** @var \Hyva\Checkout\Magewire\Checkout\AddressView\MagewireAddressFormInterface $magewire */
/** @var \Magento\Directory\Model\Country $country */
/** @var \Magento\Directory\Model\Region $region */
/** @var \Magento\Framework\Escaper $escaper */
/** @var \Hyva\Checkout\Model\Form\EntityFormInterface $form */

$iconsViewModel = $viewModels->require(HeroiconsOutline::class);
$form = $magewire->getPublicForm();
$namespace = $form->getNamespace();
?>
<div>
    <header class="pb-1 mb-6 border-b border-gray-400 section-title">
        <h2 class="text-xl font-medium text-gray-800">
            <?= $escaper->escapeHtml(__('New Address')) ?>
        </h2>
    </header>

    <form <?= /* @noEscape */ $form->renderAttributes($escaper) ?>
          class="space-y-6 address-form"
          x-data="hyva.formValidation($el)"
          novalidate
    >
        <div class="grid grid-cols-12 gap-x-4">
            <?= /** @noEscape */ $block->getChildHtml('address-form.before') ?>

            <?php if ($form->hasFields()): ?>
                <?php foreach ($form->getFields() as $field): ?>
                    <?php if ($field->canRender()): ?>

                        <?php /** @purge: md:col-span-3 md:col-span-4 md:col-span-6 md:col-span-9 md:col-span-12 */ ?>
                        <?php if ($field->hasRelatives()): ?>
                            <?php
                                $wrapperClasses = $field->renderWrapperClass(['col-span-12']);
                                $wrapperClasses = str_replace(
                                    ['md:col-span-3', 'md:col-span-6', 'md:col-span-9', 'md:col-span-12'],
                                    'md:col-span-4',
                                    $wrapperClasses
                                );
                            ?>
                            <?php foreach ($field->getRelatives() as $relative): ?>
                                <div
                                    class="<?= $escaper->escapeHtmlAttr($wrapperClasses) ?>"
                                    <?= /* @noEscape */ $field->renderAttributes($escaper, 'wrapper') ?>
                                >
                                    <?= /* @noEscape */ $relative->render() ?>
                                </div>
                            <?php endforeach ?>
                        <?php endif ?>

                        <?php
                            $wrapperClasses = $field->renderWrapperClass(['col-span-12']);
                            if ($field->getName() === 'street') {
                                $wrapperClasses = str_replace(
                                    ['md:col-span-3', 'md:col-span-9', 'md:col-span-12'],
                                    'md:col-span-6',
                                    $wrapperClasses
                                );
                            } elseif ($field->getName() === 'postcode') {
                                $wrapperClasses = str_replace(
                                    ['md:col-span-3', 'md:col-span-6', 'md:col-span-9', 'md:col-span-12'],
                                    'md:col-span-4',
                                    $wrapperClasses
                                );
                            }
                        ?>
                        <div
                            class="<?= $escaper->escapeHtmlAttr($wrapperClasses) ?>"
                            <?= /* @noEscape */ $field->renderAttributes($escaper, 'wrapper') ?>
                        >
                            <?= /* @noEscape */ $field->render() ?>

                            <?php if ($magewire->hasError($field->getId())): ?>
                                <?= /** @noEscape */ $magewire->getError($field->getId()) ?>
                            <?php endif ?>
                        </div>
                    <?php endif ?>
                <?php endforeach ?>
            <?php endif ?>

            <?= /** @noEscape */ $block->getChildHtml('address-form.after') ?>
        </div>

        <div class="flex items-center gap-x-3">
            <button
                type="button"
                class="btn btn-primary"
                x-on:click="validate().then(() => $wire.submit()).catch(() => {})"
                wire:loading.attr="disabled"
            >
                <span><?= $escaper->escapeHtml(__('Save')) ?></span>
            </button>

            <button
                type="button"
                class="btn btn-secondary"
                x-on:click="window.hyva.modal.pop()"
            >
                <span><?= $escaper->escapeHtml(__('Cancel')) ?></span>
            </button>
        </div>
    </form>
</div>
