<?php

declare(strict_types=1);

use Hyva\Checkout\ViewModel\Breadcrumbs\SigninRegister;
use Hyva\Theme\ViewModel\HeroiconsOutline;

/** @var \Magento\Framework\View\Element\Template $block */
/** @var \Hyva\Theme\Model\ViewModelRegistry $viewModels */
/** @var \Hyva\Checkout\Magewire\Checkout\AddressView\MagewireAddressFormInterface $magewire */
/** @var \Magento\Framework\Escaper $escaper */
/** @var \Hyva\Checkout\Model\Form\EntityFormInterface $form */

/** @var SigninRegister $viewModel */
$viewModel = $viewModels->require(SigninRegister::class);

$iconsViewModel = $viewModels->require(HeroiconsOutline::class);
$form = $magewire->getPublicForm();
$namespace = $form->getNamespace();
?>
<form
    <?= /* @noEscape */ $form->renderAttributes($escaper) ?>
    class="space-y-2 address-form"
    x-data="initAddressForm($el, $wire)"
    novalidate
    <?php if ($magewire->canAutoSave()): ?>
        x-init="init()"
        x-on:validate-address-form.window="() => validateAddressForm($event)"
        wire:loading.class="loading"
        wire:target="save"
    <?php endif ?>
>
    <div class="grid grid-cols-12 gap-x-4">
        <?= /** @noEscape */ $block->getChildHtml('address-form.before') ?>

        <?php if ($form->hasFields()): ?>
            <?php foreach ($form->getFields() as $field): ?>
                <?php if ($field->canRender()): ?>

                    <?php if (
                        ($field->getId() === 'email')
                        && ($viewModel->getCustomerSession()->isLoggedIn() === false)
                    ) {
                        continue;
                    } ?>

                    <?php if ($field->getId() === 'email'): ?>
                        <div class="<?= $escaper->escapeHtmlAttr($field->renderWrapperClass(['col-span-12'])) ?>">
                            <?= /** @noEscape */ $block->getChildHtml('checkout.shipping-details.address-form.contact.title') ?>
                        </div>
                    <?php endif; ?>

                    <?php /** @purge: md:col-span-3 md:col-span-4 md:col-span-6 md:col-span-9 md:col-span-12 */ ?>
                    <?php if ($field->hasRelatives()): ?>
                        <?php
                            $wrapperClasses = $field->renderWrapperClass(['col-span-12']);
                            $wrapperClasses = str_replace(
                                ['md:col-span-3', 'md:col-span-6', 'md:col-span-9', 'md:col-span-12'],
                                'md:col-span-4',
                                $wrapperClasses
                            );
                        ?>
                        <?php foreach ($field->getRelatives() as $relative): ?>
                            <div
                                class="<?= $escaper->escapeHtmlAttr($wrapperClasses) ?>"
                                <?= /* @noEscape */ $field->renderAttributes($escaper, 'wrapper') ?>
                            >
                                <?= /* @noEscape */ $relative->render() ?>
                            </div>
                        <?php endforeach ?>
                    <?php endif ?>

                    <?php
                        $wrapperClasses = $field->renderWrapperClass(['col-span-12']);
                        if ($field->getName() === 'street') {
                            $wrapperClasses = str_replace(
                                ['md:col-span-3', 'md:col-span-9', 'md:col-span-12'],
                                'md:col-span-6',
                                $wrapperClasses
                            );
                        } elseif ($field->getName() === 'postcode') {
                            $wrapperClasses = str_replace(
                                ['md:col-span-3', 'md:col-span-6', 'md:col-span-9', 'md:col-span-12'],
                                'md:col-span-4',
                                $wrapperClasses
                            );
                        }
                    ?>
                    <div
                        class="<?= $escaper->escapeHtmlAttr($wrapperClasses) ?>"
                        <?= /* @noEscape */ $field->renderAttributes($escaper, 'wrapper') ?>
                    >
                        <?= /* @noEscape */ $field->render() ?>

                        <?php if ($magewire->hasError($field->getId())): ?>
                            <?= /** @noEscape */ $magewire->getError($field->getId()) ?>
                        <?php endif ?>
                    </div>

                    <?php if ($field->getId() === 'email'): ?>
                        <div class="<?= $escaper->escapeHtmlAttr($field->renderWrapperClass(['col-span-12'])) ?>">
                            <?= /** @noEscape */ $block->getChildHtml('checkout.shipping-details.address-form.address.title') ?>
                        </div>
                    <?php endif; ?>
                <?php endif ?>

            <?php endforeach ?>
        <?php endif ?>

        <?= /** @noEscape */ $block->getChildHtml('address-form.after') ?>
    </div>
</form>
