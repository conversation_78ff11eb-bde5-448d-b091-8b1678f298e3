<?php

declare(strict_types=1);

use Hyva\Checkout\ViewModel\Checkout\Formatter as FormatterViewModel;

/** @var \Magento\Framework\Escaper $escaper */
/** @var \Magento\Framework\View\Element\Template $block */
/** @var \Hyva\Theme\Model\ViewModelRegistry $viewModels */

/** @var FormatterViewModel $formatterViewModel */
$formatterViewModel = $viewModels->require(FormatterViewModel::class);

$quoteItem = $block->getData('quote_item');
$options = $quoteItem['options'] ?? null;
?>
<?php if ($options): ?>
    <div class="text-sm leading-relaxed table-auto">
        <?php foreach ($options as $option): ?>
            <div class="flex text-xs gap-x-2 text-black/60">
                <span><?= $escaper->escapeHtml(__($option['label'])) ?>:</span>
                <span class="font-bold"><?= /* @noEscape */ __($option['value']) ?></span>
            </div>
        <?php endforeach ?>
    </div>
<?php endif ?>
