<?php

declare(strict_types=1);

use Hyva\Checkout\ViewModel\Checkout\PriceSummary\TotalSegments as TotalSegmentsViewModel;

/** @var \Magento\Framework\View\Element\Template $block */
/** @var \Magento\Framework\Escaper $escaper */
/** @var \Hyva\Theme\Model\ViewModelRegistry $viewModels */

/** @var TotalSegmentsViewModel $totalSegmentsViewModel */
$totalSegmentsViewModel = $viewModels->require(TotalSegmentsViewModel::class);
$totals = $totalSegmentsViewModel->getTotals();
$position = 0;
?>
<div class="total-segments leading-loose">
    <?php if ($totals && isset($totals['total_segments'])): ?>
        <?php foreach ($totals['total_segments'] as $segment): ?>
            <?php if (isset($segment['code']) && $child = $totalSegmentsViewModel->getTotalBlock($block, $segment->toArray())): ?>
                <div class="item <?= $escaper->escapeHtmlAttr($segment['code']) ?> <?= $escaper->escapeHtmlAttr($position++) ?>">
                    <?= $child->toHtml() ?>

                    <?php foreach ($totalSegmentsViewModel->getTotalSegmentExtensionAttributes($segment) as $code => $extensionAttribute): ?>
                        <?php if (
                            is_array($extensionAttribute)
                            && $extensionAttributes = $totalSegmentsViewModel->getTotalExtensionAttributesBlock($child, $code, $extensionAttribute)
                        ): ?>
                            <?= $extensionAttributes->toHtml() ?>
                        <?php endif ?>
                    <?php endforeach ?>
                </div>
            <?php endif ?>
        <?php endforeach ?>
    <?php else: ?>
        <p class="border border-red-700 bg-red-500 rounded-md text-white p-4 text-sm">
            <?= $escaper->escapeHtml(__('Something went wrong while collecting the order price summary.')) ?>
        </p>
    <?php endif ?>
</div>
