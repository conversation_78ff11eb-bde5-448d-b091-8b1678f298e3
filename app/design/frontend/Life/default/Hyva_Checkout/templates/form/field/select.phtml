<?php

declare(strict_types=1);

/** @var \Magento\Framework\View\Element\Template $block */
/** @var \Hyva\Checkout\Model\Form\EntityFieldSelectInterface $element */
/** @var \Hyva\Theme\Model\ViewModelRegistry $viewModels */
/** @var \Magento\Framework\Escaper $escaper */
/** @var \Magewirephp\Magewire\Component\Form $magewire */

$element = $block->getData('element');
$attributes = $element->getAttributes();
?>
<div class="block text-xs text-primary <?= $element->isRequired() ? 'required' : 'not-required' ?>">
    <?= /* @noEscape */ $element->getRenderer()->renderLabel($element) ?>
    <?= /* @noEscape */ $element->getRenderer()->renderBefore($element) ?>

    <?php if ($element->hasRelatives()): ?>
        <div class="space-y-2">
    <?php endif ?>

    <div class="flex items-center gap-4">
        <select class="<?= $escaper->escapeHtmlAttr($element->renderClass(['block w-full form-input renderer-select'])) ?>"
                <?php if ($element->hasAttributes()): ?>
                    <?= /* @noEscape */ $element->renderAttributes($escaper) ?>
                <?php endif ?>
        >
            <?php foreach ($element->getOptions() as $key => $option): ?>
                <?php if (is_object($option)): ?>
                    <option value="<?= $escaper->escapeHtmlAttr($option->getValue()) ?>">
                        <?= $escaper->escapeHtml(__($option->getLabel())) ?>
                    </option>
                <?php elseif (is_array($option)): ?>
                    <option value="<?= $escaper->escapeHtmlAttr($option['value']) ?>">
                        <?= $escaper->escapeHtml(__($option['label'])) ?>
                    </option>
                <?php elseif (is_string($option)): ?>
                    <option value="<?= $escaper->escapeHtmlAttr($key) ?>">
                        <?= $escaper->escapeHtml(__($option)) ?>
                    </option>
                <?php endif ?>
            <?php endforeach ?>
        </select>

        <?php if ($element->hasTooltip()): ?>
            <?= /* @noEscape */ $element->getRenderer()->renderTooltip($element) ?>
        <?php endif ?>
    </div>

    <?php if ($element->hasRelatives()): ?>
        <?php foreach ($element->getRelatives() as $relative): ?>
            <?= /* @noEscape */ $relative->render() ?>
        <?php endforeach ?>
    <?php endif ?>

    <?php if ($element->hasRelatives()): ?>
        </div>
    <?php endif ?>

    <?= /* @noEscape */ $element->getRenderer()->renderAfter($element) ?>
</div>
