<?php

declare(strict_types=1);

use Hyva\Checkout\Model\Form\EntityFieldInterface;
use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\SvgIcons;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;

/** @var Template $block */
/** @var EntityFieldInterface $field */
/** @var HeroiconsOutline $iconsViewModel */
/** @var ViewModelRegistry $viewModels */
/** @var Escaper $escaper */

/** @var SvgIcons $svgIcons */
$svgIcons = $viewModels->require(SvgIcons::class);

$element = $block->getData('element');
?>
<?php if ($element): ?>
    <div class="flex items-center gap-2 tooltip"
         x-data="{ tooltipVisible: false }"
         role="tooltip"
    >
        <div x-on:mouseenter.self="tooltipVisible = true"
             x-on:mouseleave.self="tooltipVisible = false"
             x-on:click.outside.away="tooltipVisible = false"
             class="relative hover:cursor-pointer"
        >
            <?= $svgIcons->infoHtml("w-4 h-4") ?>

            <template x-if="tooltipVisible">
                <div class="absolute px-3 pt-3 -right-5 w-80 top-[calc(100%+0.25rem)]">
                    <div class="relative px-3 py-2 text-black rounded-md shadow-lg bg-secondary">
                        <div class="absolute w-3 h-3 origin-bottom-left transform rotate-45 bg-secondary right-3 -top-3"></div>
                        <?= $escaper->escapeHtml(__($element->getTooltip())) ?>
                    </div>
                </div>
            </template>
        </div>
    </div>
<?php endif ?>
