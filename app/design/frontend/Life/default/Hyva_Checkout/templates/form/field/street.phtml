<?php

declare(strict_types=1);

use Hyva\Checkout\ViewModel\Form\Field\Street as StreetFormFieldViewModel;

/** @var \Magento\Framework\View\Element\Template $block */
/** @var \Hyva\Checkout\Model\Form\EntityFieldInterface $element */
/** @var \Hyva\Theme\Model\ViewModelRegistry $viewModels */
/** @var \Magento\Framework\Escaper $escaper */
/** @var \Magewirephp\Magewire\Component\Form $magewire */

/** @Tailwind md:w-1/4 md:w-2/4 md:w-3/4 md:w-4/4 mb-2 */

$streetFormFieldViewModel = $viewModels->require(StreetFormFieldViewModel::class);
$element = $block->getData('element');
$attributes = $element->getAttributes();
?>
<div class="w-full text-xs text-primary <?= /* @noEscape */ $element->isRequired() ? 'required' : 'not-required' ?>">
    <?= /* @noEscape */ $element->getRenderer()->renderLabel($element) ?>
    <?= /* @noEscape */ $element->getRenderer()->renderBefore($element) ?>

    <div class="flex items-center gap-4">
        <input class="<?= $escaper->escapeHtmlAttr($element->renderClass(['block w-full form-input grow renderer-text-grid'])) ?>"
            <?php if ($element->hasAttributes()): ?>
                <?= /* @noEscape */ $element->renderAttributes($escaper) ?>
            <?php endif ?>
        />

        <?php if ($element->hasTooltip()): ?>
            <?= /* @noEscape */ $element->getRenderer()->renderTooltip($element) ?>
        <?php endif ?>
    </div>

    <?= /* @noEscape */ $element->getRenderer()->renderAfter($element) ?>
</div>
