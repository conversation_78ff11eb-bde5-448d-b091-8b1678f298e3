<?php

declare(strict_types=1);

/** @var \Magento\Framework\View\Element\Template $block */
/** @var \Hyva\Checkout\Model\Form\EntityFieldInterface $element */
/** @var \Hyva\Theme\Model\ViewModelRegistry $viewModels */
/** @var \Magento\Framework\Escaper $escaper */
/** @var \Magewirephp\Magewire\Component\Form $magewire */

$element = $block->getData('element');
$attributes = $element->getAttributes();
?>
<div class="flex font-medium text-gray-700 <?= /* @noEscape */ $element->isRequired() ? 'required' : 'not-required' ?>">
    <div class="flex items-center h-5">
        <input class="<?= $escaper->escapeHtmlAttr($element->renderClass(['field checkbox w-4 h-4 renderer-checkbox'])) ?>"
            <?php if ($element->hasAttributes()): ?>
                <?= /* @noEscape */ $element->renderAttributes($escaper) ?>
            <?php endif ?>
        />
    </div>

    <div class="ml-2 space-y-2">
        <label for="<?= $escaper->escapeHtmlAttr($attributes['id'] ?? $element->getId()) ?>">
            <span>
                <?= $escaper->escapeHtml(__($element->getLabel())) ?>
            </span>
        </label>

        <?php if ($element->hasRelatives()): ?>
            <div class="space-y-2">
                <?php foreach ($element->getRelatives() as $relative): ?>
                    <?= /* @noEscape */ $relative->render() ?>
                <?php endforeach ?>
            </div>
        <?php endif ?>
    </div>
</div>
