<?php

declare(strict_types=1);

use Hyva\Checkout\ViewModel\Navigation;

/** @var \Magento\Framework\View\Element\Template $block */
/** @var \Hyva\Theme\Model\ViewModelRegistry $viewModels */
/** @var \Magento\Framework\Escaper $escaper */

/** @var Navigation $viewModel */
$viewModel = $viewModels->require(Navigation::class);

$next = $viewModel->getConfig()->getStepAfter();
?>
<?php if ($next === null): ?>
    <button
        type="button"
        class="hidden btn btn-primar"
        x-spread="buttonPlaceOrder()"
        x-bind="buttonPlaceOrder()"
        @place-order-from-payment.window="$el.click();"
    >
        <span><?= $escaper->escapeHtml(__('Checkout')) ?></span>
    </button>
<?php endif ?>
