<?php

declare(strict_types=1);

use Hyva\Checkout\ViewModel\Navigation;
use Hyva\Theme\ViewModel\SvgIcons;

/** @var \Magento\Framework\View\Element\Template $block */
/** @var \Hyva\Theme\Model\ViewModelRegistry $viewModels */
/** @var \Magento\Framework\Escaper $escaper */

/** @var SvgIcons $svgIcons */
$svgIcons = $viewModels->require(SvgIcons::class);

/** @var Navigation $viewModel */
$viewModel = $viewModels->require(Navigation::class);
$config = $viewModel->getConfig();

?>

<?php if ($config->hasSteps() && $config->isMultiStepper()): ?>
    <?php $current = $config->getStepData(); ?>
    <?php $previous = $config->getStepBefore(); ?>
    <?php $next = $config->getStepAfter(); ?>

    <?php if (is_array($previous) && $viewModel->getSystemConfig()->showNavigationBackButton()): ?>
        <button
            type="button"
            rel="prev"
            class="flex items-center text-link after:hidden before:hover:delay-0 gap-x-2"
            x-spread="buttonPrevious('<?= $escaper->escapeJs($previous['route']) ?>')"
            x-bind="buttonPrevious('<?= $escaper->escapeJs($previous['route']) ?>')"
        >
            <?= $svgIcons->chevronLeftHtml("w-4 h-4") ?>
            <?php if ($previous['name'] === 'shipping'): ?>
                <?= $escaper->escapeHtml(__('Back to address details')) ?>
            <?php else: ?>
                <?= $escaper->escapeHtml(__('Back to %1', [strtolower((string) __($previous['label'] ?? 'previous step'))])) ?>
            <?php endif; ?>
        </button>
    <?php elseif (is_array($current) && isset($current['shipping'])): ?>
        <a class="flex items-center text-link after:hidden before:hover:delay-0 gap-x-2" href="<?= $escaper->escapeUrl($block->getUrl('checkout/cart')); ?>">
            <?= $svgIcons->chevronLeftHtml("w-4 h-4") ?>
            <?= $escaper->escapeHtml(__('Back to shopping cart')) ?>
        </a>
    <?php endif; ?>

    <?php if (is_array($next)): ?>
        <div class="button-wrapper max-sm:fixed max-sm:bottom-0 max-sm:bg-white max-sm:w-full max-sm:left-0 max-sm:px-3 max-sm:py-4 max-sm:z-10 max-sm:shadow-lg">
            <button
                type="button"
                rel="next"
                class="btn btn-primary sm:min-w-[17rem] justify-center max-sm:w-full"
                x-spread="buttonNext('<?= $escaper->escapeJs($next['route']) ?>')"
                x-bind="buttonNext('<?= $escaper->escapeJs($next['route']) ?>')"
                x-on:click="handleEcommerceDatalayerEvent('add_shipping_info')"
            >
                <?php if ($next['name'] === 'payment'): ?>
                    <span><?= $escaper->escapeHtml(__('To payment method')) ?></span>
                <?php else: ?>
                    <span><?= $escaper->escapeHtml(__('Proceed to %1', [strtolower((string) __($next['label'] ?? 'next step'))])) ?></span>
                <?php endif; ?>
            </button>
        </div>
    <?php endif ?>
<?php endif ?>
