<?xml version="1.0"?>
<page
    layout="2columns-right"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd"
>
    <body>
        <referenceContainer name="before.body.end">
            <block class="MagePal\GoogleAnalytics4\Block\Data\Checkout" template="Hyva_Checkout::checkout.phtml">
                <arguments>
                    <argument name="component_name" xsi:type="string">checkOutGa4DataLayer</argument>
                </arguments>
            </block>
        </referenceContainer>

        <referenceContainer name="header.container" htmlClass="page-header"/>

        <referenceBlock name="hyva.checkout.columns" template="Hyva_Checkout::layout/1column.phtml" />

        <referenceBlock name="checkout.guest-details" template="Hyva_Checkout::checkout/guest/form.phtml">
            <block
                name="checkout.guest.address-form.contact.title"
                template="Hyva_Checkout::section/title.phtml"
            >
                <action method="setTitle">
                    <argument name="title" translate="true" xsi:type="string">Contact information</argument>
                </action>
            </block>
        </referenceBlock>

        <referenceBlock name="checkout.shipping-details.address-form">
            <block
                name="checkout.shipping-details.address-form.contact.title"
                template="Hyva_Checkout::section/title.phtml"
            >
                <action method="setTitle">
                    <argument name="title" translate="true" xsi:type="string">Contact information</argument>
                </action>
            </block>
            <block
                name="checkout.shipping-details.address-form.address.title"
                template="Hyva_Checkout::section/title.phtml"
            >
                <action method="setTitle">
                    <argument name="title" translate="true" xsi:type="string">Shipping address</argument>
                </action>
            </block>
        </referenceBlock>
        <referenceBlock name="checkout.shipping-details.address-list.grid">
            <block
                name="checkout.billing-shipping.address-list.grid.title"
                template="Hyva_Checkout::section/title.phtml"
            >
                <action method="setTitle">
                    <argument name="title" translate="true" xsi:type="string">Shipping address</argument>
                </action>
            </block>
        </referenceBlock>
        <referenceBlock name="checkout.shipping-details.address-list.grid">
            <block
                name="checkout.billing-shipping.address-list.grid.title"
                template="Hyva_Checkout::section/title.phtml"
            >
                <action method="setTitle">
                    <argument name="title" translate="true" xsi:type="string">Shipping address</argument>
                </action>
            </block>
        </referenceBlock>
        <referenceBlock name="checkout.payment.methods">
            <block
                name="checkout.payment.methods.title"
                template="Hyva_Checkout::section/title.phtml"
            >
                <action method="setTitle">
                    <argument name="title" translate="true" xsi:type="string">Payment methods</argument>
                </action>
            </block>
            <block
                name="checkout.payment.place-order"
                template="Hyva_Checkout::checkout/payment/place-order.phtml"
            >
                <block
                    name="checkout.payment.terms-conditions"
                    template="Hyva_Checkout::checkout/terms-conditions.phtml"
                    ifconfig="checkout/options/enable_agreements"
                >
                    <arguments>
                        <argument name="magewire" xsi:type="object">
                            \Hyva\Checkout\Magewire\Checkout\TermsConditions
                        </argument>
                    </arguments>
                    <block
                        name="component-messenger-payment-actions"
                        template="Hyva_Checkout::page/messenger.phtml"
                        before="-"
                    >
                        <arguments>
                            <argument name="event_prefix" xsi:type="string">quote:actions</argument>
                        </arguments>
                    </block>
                    <block
                        name="checkout.payment.terms-conditions.list"
                        as="list"
                        template="Hyva_Checkout::checkout/terms-conditions/list.phtml"
                    />
                    <block
                        name="checkout.terms-conditions.message-only"
                        as="message"
                        template="Hyva_Checkout::checkout/terms-conditions/message.phtml"
                    />
                    <block
                        name="checkout.terms-conditions.message-including-page"
                        as="page"
                        template="Hyva_Checkout::checkout/terms-conditions/message.phtml"
                    />
                </block>
            </block>
        </referenceBlock>

        <move
            element="price-summary.total-segments"
            destination="checkout.price-summary.section"
            after="price-summary.cart-items"
        />

        <move element="header.container" destination="main" before="-"/>
        <move element="page.messages" destination="main" after="header.container"/>
        <move element="checkout.quote-summary.section" destination="div.sidebar.additional"/>
        <move element="magewire.loader" destination="main" after="-" />

        <referenceContainer name="page.bottom.container" remove="true" />
        <referenceContainer name="checkout.section.quote-actions" remove="true" />
        <referenceBlock name="checkout.shipping-details.title" remove="true" />
        <referenceBlock name="topmenu_desktop" remove="true" />
        <referenceBlock name="footer" remove="true" />
        <referenceBlock name="usp" remove="true" />
        <referenceBlock name="checkout.quote-summary.title" remove="true" />
        <referenceBlock name="price-summary.total-segment.tax" remove="true" />
        <referenceBlock name="checkout.shipping-summary" remove="true"/>
        <referenceBlock name="magewire.loader.notifications" remove="true" />
        <referenceBlock name="price-summary.total-segment.discount" remove="true" />
    </body>
</page>
