<?xml version="1.0"?>
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd"
>
    <body>
        <referenceBlock name="checkout.payment.methods">
            <!-- Payment Renderer: MultiSafepay Maestro -->
            <block
                name="checkout.payment.method.multisafepay_maestro"
                as="multisafepay_maestro"
            >
                <arguments>
                    <argument name="metadata" xsi:type="array">
                        <item name="icon" xsi:type="array">
                            <item name="svg" xsi:type="string">multisafepay/multisafepay_maestro</item>
                            <item name="attributes" xsi:type="array">
                                <item name="title" xsi:type="string">Maestro</item>
                            </item>
                        </item>
                    </argument>
                </arguments>
            </block>
        </referenceBlock>

        <!-- The hyva.checkout.components block does not really exist. It is only a virtual "container". -->
        <!-- To render a component declared here, it needs to be <move>d to the target column in the step. -->
        <!-- The reason to declare a component here is if it is more complex and will be used in more than one step. -->
        <move element="coupon-code" destination="checkout.payment.section" before="checkout.payment.methods"/>
    </body>
</page>
