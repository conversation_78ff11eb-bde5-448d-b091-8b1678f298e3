<?php

declare(strict_types=1);

use Hyva\Theme\ViewModel\Store;
use Hyva\Theme\ViewModel\SvgIcons;
use Life\ThemeConfigurations\ViewModel\SwiperScript;

/** @var \Magento\Framework\View\TemplateEngine\Php $this */
/** @var \Magento\Catalog\Block\Product\View $block */
/** @var \Magento\Framework\Escaper $escaper */
/** @var \Hyva\Theme\Model\ViewModelRegistry $viewModels */

/** @var Store $viewModelStore */
$viewModelStore = $viewModels->require(Store::class);

/** @var SvgIcons $svgIcons */
$svgIcons = $viewModels->require(SvgIcons::class);

$uniqueId = '_' . uniqid();
$query = $block->getGraphqlQuery();
/**
 * Can be a custom filter, like  "color: { in: [\"yellow\",\"orange\"] },"
 * @var string $productFilters
 */
$productFilters = $block->getProductFilters();
$categoryIds = $block->getCategoryIds();
$priceFrom = $block->getPriceFrom();
$priceTo = $block->getPriceTo();
$sortAttribute = $block->getSortAttribute() ?: 'position';
$sortDirection = $block->getSortDirection() ?: 'ASC';
$title = $block->getTitle();
$type = 'related';

if ($block->getProduct()) { /** @phpstan-ignore-line */
    $productSkus = $block->getProduct()->getSku();
} else {
    $productSkus = '%sku%';
}

$productSkuFilter = sprintf("sku: { in: [\"%s\"] },", join("\",\"", explode(',', $productSkus)));

$categoryFilter = $categoryIds ?
    sprintf("category_id: { in: [\"%s\"] }", join("\",\"", explode(',', $categoryIds))) :
    "";
$priceFilter = $priceFrom || $priceTo ? "price: { from: \"$priceFrom\", to: \"$priceTo\" }," : "";

$productAttributes = "
      sku
      id
      name
      image {
        label
        url
      }
      hover_image {
        label
        url
      }
      url_key
      url_suffix
      add_to_cart_url
      stock_status
      visibility
      status
      price_range {
        minimum_price {
          regular_price {
            value
            currency
          }
          final_price {
            value
            currency
          }
        }
        msrp_price {
          regular_price {
            value
            currency
          }
        }
      }";

$items = "{$type}_products {
        $productAttributes
    }";

$graphqlQuery = $query ?: "
{
  products(
    filter: {
      $productFilters
      $productSkuFilter
      $categoryFilter
      $priceFilter
    }
    sort: {{$sortAttribute}: {$sortDirection}}
  ) {
    items {
        $items
    }
  }
}";
?>
<script defer="defer">
    function initSliderComponent<?= /** @noEscape */$uniqueId ?>() {
        return {
            products: [],
            currency: [],
            isLoading: true,
            productIds: [],
            formKey: hyva.getFormKey(),
            query: <?= /** @noEscape */ json_encode($graphqlQuery) ?>,
            initCarousel(carousel) {
                if (!Swiper) {
                    return;
                }

                // Product carousel
                const initProductCarousel = (slider) => {
                    const swiperElement = slider.querySelector('.swiper');

                    swiperElement.insertAdjacentHTML(
                        'beforeend',
                        '<?= $escaper->escapeJs($block->getBlockHtml('pagebuilder.carousel.nav')) ?>'
                    );

                    const arrows = swiperElement.querySelectorAll('.swiper-nav button');

                    [...arrows].map((arrow) => {
                        arrow.classList.remove('!hidden');
                    })

                    slider.querySelector('.swiper-pagination').classList.remove('!hidden');

                    // Swiper options
                    const swiper = new Swiper(swiperElement, {
                        slidesPerView: 1.3,
                        spaceBetween: 16,
                        loop: false,
                        navigation: {
                            nextEl: swiperElement.querySelector('.swiper-button-next'),
                            prevEl: swiperElement.querySelector('.swiper-button-prev'),
                        },
                        autoplay: false,
                        pagination: {
                            el: slider.querySelector('.swiper-pagination'),
                            type: 'bullets',
                            clickable: true,
                        },
                        breakpoints: {
                            768: {
                                slidesPerView: 2.75,
                                spaceBetween: 24,
                            },
                            1024: {
                                slidesPerView: 3,
                            }
                        }
                    });
                };

                // Init scripts
                carousel.forEach(element => {
                    if (element.dataset.contentType === 'product-carousel-popup') {
                        initProductCarousel(element);
                    }
                });
            },
            getProducts(event, $nextTick) {
                // For list, reset products when adding to cart a new item
                this.products = [];

                // Replace dynamic SKU in query with item SKU from form
                let query = this.query;
                if (event.target && event.target.dataset && event.target.dataset.sku) {
                    query = query.replace('%sku%', event.target.dataset.sku);
                }

                fetch('<?= $escaper->escapeUrl($block->getBaseUrl()) ?>graphql?query=' + encodeURIComponent(
                    query
                ), {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Store': '<?= /* @noEscape */ $viewModelStore->getStoreCode() ?>'
                    },
                })
                .then((response) =>  response.json())
                .then((data) => {
                        this.currency = (data &&
                            data.data &&
                            data.data.currency);
                        return this.products = (
                            data &&
                            data.data &&
                            data.data.products &&
                            data.data.products.items &&
                            data.data.products.items[0] &&
                            data.data.products.items[0]['<?= $escaper->escapeHtml($type) ?>_products'] &&
                            // only use products with visibility set to `Catalog` or `Catalog, Search` (2 or 4)
                            // with status set to '1' (enabled)
                            data.data.products.items[0]['<?= $escaper->escapeHtml($type) ?>_products']
                                .filter(product => {
                                    product.labels = [];
                                    this.productIds.push(product.id);
                                    return (
                                        [2,4].includes(product.visibility) &&
                                        product.status.toString() === '1'
                                    )
                                })
                        ) || [];
                    }
                )
                .finally(() => {
                    let self = this;
                    this.isLoading = false;
                    $nextTick(() => {
                        const carouselElement = document.querySelectorAll(
                            `[data-content-type="product-carousel-popup"]`
                        );

                        // Load Swiper script
                        const swiperScript = document.createElement('script');

                        swiperScript.type = 'text/javascript';
                        swiperScript.src = '<?= SwiperScript::SWIPER_JS ?>';
                        swiperScript.setAttribute('data-swiper-script', 'init');

                        const hasSwiperScript = document.querySelector(`script[data-swiper-script="init"]`);

                        if (!hasSwiperScript) {
                            document.head.appendChild(swiperScript);
                        }

                        // Load Swiper stylesheet
                        const swiperStyle = document.createElement('link');
                        swiperStyle.rel = 'stylesheet';

                        swiperStyle.href = '<?= SwiperScript::SWIPER_CSS ?>';
                        swiperStyle.setAttribute('data-swiper-style', 'init');

                        const hasSwiperStyle = document.querySelector(`script[data-swiper-style="init"]`);

                        if (!hasSwiperStyle) {
                            document.head.appendChild(swiperStyle);
                        }

                        // Load Carousel
                        if (carouselElement.length > 0) {
                            this.initCarousel(carouselElement);
                        }
                    });
                });
            },
            addToCart(container, index) {
                let self = this,
                    formElement = container.closest("form[data-id='"+index+"']"),
                    addToCartBtn = formElement.querySelector(".tocart");

                if (!formElement || !addToCartBtn) {
                    return;
                }

                addToCartBtn.disabled = true;
                addToCartBtn.classList.add('added-to-cart');

                // Extract form data from event & add isAjax parameter
                let formData = new FormData(formElement),
                    data = new URLSearchParams(formData);

                data.append('isAjax', '1');

                fetch(formElement.action, {
                    "headers": {
                        "content-type": "application/x-www-form-urlencoded; charset=UTF-8",
                    },
                    "body": data.toString(),
                    "method": "POST",
                    "mode": "cors",
                    "credentials": "include"
                })
                .then(function (response) {
                    if (response.redirected) {
                        window.location.href = response.url;
                    } else if (response.ok) {
                        return response.json();
                    } else {
                        typeof window.dispatchMessages !== "undefined" && window.dispatchMessages([{
                            type: "warning",
                            text: "<?= $escaper->escapeHtml(__('Could not add product to cart.')) ?>"
                        }], 5000);
                    }
                })
                .then(function (response) {
                    if (response.backUrl) {
                        window.location.href = response.backUrl;
                    } else {
                        window.dispatchEvent(new CustomEvent("reload-customer-section-data"));
                        // remove the cookie to prevent localstorage returning add to cart event twice
                        hyva.setCookie('mage-cache-sessid', '', -1, true);
                        addToCartBtn.classList.add('added');
                    }
                })
            },
            formatPrice(priceValue) {
                let formattedPrice = hyva.formatPrice(priceValue).replace(/\s/g, '');

                return formattedPrice.replace(/\.00$/g, '.-').replace(/\,00$/g, ',-');
            },
            minHeight() {
                return 'min-height: ' + ((this.loading && '650px') || 0);
            }
        }
    }
</script>
<section
    class="relative pl-4 lg:px-6"
    x-data="initSliderComponent<?= /** @noEscape */ $uniqueId?>()"
    @ajax-add-to-cart.window="getProducts($event, $nextTick);"
    :style="minHeight()"
>
    <template x-if="isLoading">
        <?= $block->fetchView((string) $block->getTemplateFile('Hyva_Theme::ui/loading.phtml')) ?>
    </template>
    <template x-if="products && products.length">
        <div data-content-type="product-carousel-popup">
            <div class="swiper relative w-full !py-4 !lg:py-8">
                <h3 class="hidden mb-4 text-base leading-[1.875] sm:block"><?= $escaper->escapeHtml(__('Recommended products')) ?></h3>
                <div class="relative grid w-full swiper-wrapper sm:grid-cols-2 xl:grid-cols-4"
                    <?php if ($title): ?>
                        id="<?= $escaper->escapeHtmlAttr(strtolower(str_replace(' ', '-', trim($title)))) ?>"
                    <?php endif; ?>
                >
                    <template x-for="(product, index) in products">
                        <div class="swiper-slide !h-auto group flex-none py-1">
                            <form method="post" :action='product.add_to_cart_url' :data-id="index" class="flex flex-col h-full bg-white border border-secondary-900/50 rounded-xl item product product-item">
                                <input type="hidden" name="form_key" :value="formKey"/>
                                <input type="hidden" name="product" :value="product.id"/>
                                <div class="relative flex justify-center">
                                    <?php /* Product Image */ ?>
                                    <a
                                        :href="'<?= $escaper->escapeUrl($block->getBaseUrl()) ?>' +
                                            product.url_key + (product.url_suffix || '')"
                                        class="block mx-auto my-3 product photo product-item-photo"
                                        tabindex="-1"
                                    >
                                        <img
                                            class="object-contain max-w-full aspect-[1/0.9]"
                                            :class="{'group-hover:opacity-0 group-hover:invisible': product.hover_image.label}"
                                            :src="product.image.url"
                                            :alt="product.image.label"
                                            :data-id="index"
                                            loading="lazy"
                                            width="500"
                                            height="500"
                                        />
                                        <template x-if="product.hover_image.label">
                                            <img
                                                class="absolute top-0 invisible object-contain h-full transition-all duration-300 opacity-0 group-hover:opacity-100 group-hover:visible"
                                                :src="product.hover_image.url"
                                                :alt="product.hover_image.label"
                                                :data-id="index"
                                                loading="lazy"
                                                width="500"
                                                height="500"
                                            />
                                        </template>
                                    </a>
                                </div>
                                <div class="flex flex-wrap p-4 border-t product-info grow border-secondary-900/50">
                                    <div class="flex justify-between w-full gap-x-4">
                                        <div class="items-center text-base font-semibold text-primary md:text-md">
                                            <a
                                                class="flex items-center h-12 product-item-link line-clamp-2"
                                                :href="'<?= $escaper->escapeUrl($block->getBaseUrl()) ?>' +
                                                    product.url_key + (product.url_suffix || '')"
                                            >
                                                <span x-html="product.name"></span>
                                            </a>
                                        </div>
                                        <div>
                                            <p
                                                class="flex justify-end text-base font-bold slider-price-container md:text-md"
                                                x-text="formatPrice(product.price_range.minimum_price.final_price.value)"
                                            ></p>
                                            <p class="flex items-center justify-end text-xs leading-normal basis-full old-price text-black/60">
                                                <span>
                                                    <?= __('Advise Price') ?>&nbsp;
                                                </span>
                                                <template
                                                    x-if="product.price_range.msrp_price.regular_price.value
                                                        && product.price_range.msrp_price.regular_price.value > product.price_range.minimum_price.final_price.value"
                                                >
                                                    <span
                                                        class="price-wrapper whitespace-nowrap line-through"
                                                        x-text="formatPrice(product.price_range.msrp_price.regular_price.value)"
                                                    ></span>
                                                </template>
                                            </p>
                                        </div>
                                    </div>
                                    <div class="flex flex-wrap items-center justify-center w-full mt-3">
                                        <button
                                            class="justify-center w-full min-h-[40px] px-4 py-0 tocart btn btn-secondary add-to-cart text-primary lg:hover:text-white"
                                            type="button"
                                            aria-label="<?= $escaper->escapeHtml(__('Add to Cart')) ?>"
                                            @click="addToCart($el, index)"
                                            x-show="product.stock_status === 'IN_STOCK'"
                                        >
                                            <?= $svgIcons->plusCartHtml("p-2 plus") ?>
                                            <?= $svgIcons->shoppingCartHtml("h-6 w-6 shopping-cart") ?>
                                            <?= $svgIcons->checkHtml("h-6 w-6 checkmark") ?>
                                        </button>
                                        <span class="mt-3 text-sm tracking-1 text-black/50" x-show="product.stock_status !== 'IN_STOCK'">
                                            <?= $escaper->escapeHtml(__('Temporarily sold out')) ?>
                                        </span>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </template>
                </div>
            </div>
        </div>
    </template>
</section>
