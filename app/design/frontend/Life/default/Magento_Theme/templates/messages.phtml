<?php

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsOutline;
use Hyva\Theme\ViewModel\StoreConfig;

/** @var Magento\Framework\Escaper $escaper */
/** @var ViewModelRegistry $viewModels */
/** @var HeroiconsOutline $heroicons */
/** @var StoreConfig $storeConfig */

$heroicons = $viewModels->require(HeroiconsOutline::class);
$storeConfig = $viewModels->require(StoreConfig::class);

$defaultSuccessMessageTimeout = $storeConfig->getStoreConfig('hyva_theme_general/messages/success_message_timeout');

?>
<script defer="defer">
    <?php if ($defaultSuccessMessageTimeout): ?>
    window.defaultSuccessMessageTimeout = <?= (int) $defaultSuccessMessageTimeout ?>;
    <?php endif; ?>
    function initMessages() {
        "use strict";
        return {
            messages: window.mageMessages || [],
            isEmpty() {
                for (const message of this.messages) {
                    if (undefined !== message) {
                        return false;
                    }
                }
                return true;
            },
            removeMessage(messageIndex) {
                this.messages[messageIndex] = undefined;
            },
            addMessages(messages, hideAfter) {
                if (hideAfter == undefined) {
                    hideAfter = 13000;
                }
                messages.map((message) => {
                    this.messages = this.messages.concat(message);
                    if (hideAfter === undefined && message.type === 'success' && window.defaultSuccessMessageTimeout) {
                        hideAfter = window.defaultSuccessMessageTimeout;
                    }
                    if (hideAfter) {
                        this.setHideTimeOut(this.messages.length -1, hideAfter);
                    }
                });
            },
            setHideTimeOut(messageIndex, hideAfter) {
                setTimeout((messageIndex) => {
                    this.removeMessage(messageIndex);
                }, hideAfter, messageIndex);
            },
            eventListeners: {
                ['@messages-loaded.window'](event) {
                    this.addMessages(event.detail.messages, event.detail.hideAfter)
                },
                ['@private-content-loaded.window'](event) {
                    const data = event.detail.data;
                    if (
                        data.messages &&
                        data.messages.messages &&
                        data.messages.messages.length
                    ) {
                        this.addMessages(data.messages.messages);
                    }
                },
                ['@clear-messages.window']() {
                    this.messages = [];
                }
            }
        }
    }
</script>
<section id="messages"
         x-data="initMessages()"
         x-bind="eventListeners"
         aria-live="assertive"
         role="alert"
>
    <template x-if="!isEmpty()">
        <div class="w-full">
            <div class="messages container mx-auto py-3">
                <template x-for="(message, index) in messages" :key="index">
                    <div>
                        <template x-if="message">
                            <div class="message" :class="message.type"
                                 :ui-id="'message-' + message.type"
                            >
                                <span x-html="message.text" class="font-normal"></span>
                                <button
                                    type="button"
                                    class="text-gray-600 hover:text-black"
                                    aria-label="<?= $escaper->escapeHtml(__('Close message')) ?>"
                                    @click.prevent="removeMessage(index)"
                                >
                                    <?= $heroicons->xHtml('stroke-current', 18, 18, ['aria-hidden' => 'true']); ?>
                                </button>
                            </div>
                        </template>
                    </div>
                </template>
            </div>
        </div>
    </template>
</section>
