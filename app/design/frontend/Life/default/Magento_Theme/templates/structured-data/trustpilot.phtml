<?php

declare(strict_types=1);

use Life\TrustpilotUsp\ViewModel\Trustpilot;
use Redkiwi\RichSnippets\ViewModel\Store;

/** @var \Magento\Framework\View\Element\Template $block */
/** @var \Magento\Framework\Escaper $escaper */
/** @var \Hyva\Theme\Model\ViewModelRegistry $viewModels */

/** @var Trustpilot $trustpilotViewModel */
$trustpilotViewModel = $viewModels->require(Trustpilot::class);

/** @var Store $storeViewModel */
$storeViewModel = $viewModels->require(Store::class);

if (!$trustpilotViewModel->getReviewsNumber() || !$trustpilotViewModel->getScore()) {
    return '';
}
?>
<script type="application/ld+json">
    {
        "@context": "https://schema.org/",
        "@type": "AggregateRating",
        "itemReviewed": {
            "@type": "Store",
            "name": "<?= $storeViewModel->getStoreInfo('name'); ?>"
        },
        "ratingValue": "<?= $trustpilotViewModel->getScore(); ?>",
        "bestRating": "10",
        "ratingCount": "<?= $trustpilotViewModel->getReviewsNumber(); ?>"
    }
</script>
