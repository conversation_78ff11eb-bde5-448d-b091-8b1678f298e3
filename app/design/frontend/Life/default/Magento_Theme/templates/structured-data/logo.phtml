<?php

declare(strict_types=1);

/** @var \Magento\Theme\Block\Html\Header\Logo $block */
/** @var \Magento\Framework\Escaper $escaper */
/** @var \Hyva\Theme\Model\ViewModelRegistry $viewModels */

/** @var \Hyva\Theme\ViewModel\Logo\LogoPathResolver $logoPathResolver */
$logoPathResolver = $block->getData('logoPathResolver');
$logoSrc = $logoPathResolver && method_exists($logoPathResolver, 'getLogoSrc')
    ? $logoPathResolver->getLogoSrc($block->getData('logo_file'))
    : $block->getLogoSrc();
?>
<script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "Organization",
        "url": "<?= $escaper->escapeJs($block->getUrl()) ?>",
        "logo": "<?= $logoSrc ?>"
    }
</script>
