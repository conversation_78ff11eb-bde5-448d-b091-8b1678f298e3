<?php

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsOutline;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;

/** @var Template $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */
/** @var HeroiconsOutline $heroicons */
$heroicons = $viewModels->require(HeroiconsOutline::class);
?>

<div class="block <?= $escaper->escapeHtmlAttr($block->getBlockCss()) ?> card filter-option">
    <button
        type="button"
        class="title <?= $escaper->escapeHtmlAttr($block->getBlockCss()) ?>-title flex justify-between items-center w-full">
        <span class="text-xl leading-9 title">
            <?= $escaper->escapeHtml(__($block->getData('block_title'))) ?>
        </span>
    </button>
    <span class="block w-full my-4 border-b delimiter border-secondary-900/50"></span>
    <div class="content <?= $escaper->escapeHtmlAttr($block->getBlockCss()) ?>-content block" id="<?= $escaper->escapeHtmlAttr($block->getBlockCss()) ?>">
        <?= $block->getChildHtml() ?>
    </div>
</div>
