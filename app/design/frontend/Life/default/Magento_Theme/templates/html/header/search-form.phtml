<?php

declare(strict_types=1);

use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;
use Magento\Search\Helper\Data as SearchHelper;
use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsOutline;

// phpcs:disable Magento2.Templates.ThisInTemplate.FoundThis
// phpcs:disable Magento2.Templates.ThisInTemplate.FoundHelper

/** @var Escaper $escaper */
/** @var Template $block */
/** @var SearchHelper $helper */
/** @var ViewModelRegistry $viewModels */

/** @var HeroiconsOutline $heroicons */
$heroicons = $viewModels->require(HeroiconsOutline::class);

$helper = $this->helper(SearchHelper::class);
?>
<script defer="defer">
    'use strict';

    function initMiniSearch() {
        return {
            minSearchLength: <?= (int) $helper->getMinQueryLength() ?>,
            suggestions: [],
            searchIsFocused: false,
            suggest() {
                const search = this.$refs.searchInput;
                if (search.value.length >= this.minSearchLength) {
                    search.setCustomValidity('');
                    search.reportValidity();
                    this.fetchSuggestions(search.value);
                } else {
                    this.suggestions = [];
                }
            },
            fetchSuggestions(term) {
                fetch(
                    window.BASE_URL + 'search/ajax/suggest?' + new URLSearchParams({q: term}),
                    {
                        headers: {
                            'X-Requested-With': 'XMLHttpRequest'
                        }
                    }
                )
                .then(response => response.json())
                .then(result => this.suggestions = result);
            },
            search(term) {
                const search = this.$refs.searchInput;
                term = term || search.value;
                if (term.length < this.minSearchLength) {
                    search.setCustomValidity('<?= $escaper->escapeJs(
                        __('Minimum Search query length is %1', $helper->getMinQueryLength())
                    ) ?>');
                    search.reportValidity();
                } else {
                    search.setCustomValidity('');
                    search.value = term;
                    this.$refs.form.submit();
                }
            },
            focusElement(element) {
                if (element && element.nodeName === "DIV") {
                    element.focus();
                    return true;
                } else {
                    return false;
                }
            }
        }
    }
</script>
<div class="px-3 pt-3 mt-3 -mx-3 border-t border-secondary-900/50 md:border-none md:p-0 md:my-0" x-data="initMiniSearch()">
     <form class="relative form minisearch" id="search_mini_form" x-ref="form" @submit.prevent="search()"
          action="<?= $escaper->escapeUrl($helper->getResultUrl()) ?>" method="get" role="search">
        <label class="hidden" for="search" data-role="minisearch-label">
            <span><?= $escaper->escapeHtml(__('Search')) ?></span>
        </label>
        <div
            class="after:content-[''] after:fixed after:bg-black/30 after:top-0 after:bottom-0 after:left-0 after:right-0 after:transition-all after:duration-300 after:ease-in-out"
            :class="{'after:opacity-100 after:pointer-events-auto': searchIsFocused, 'after:opacity-0 after:pointer-events-none': !searchIsFocused }"
        >
            <input id="search"
                x-ref="searchInput"
                type="search"
                autocomplete="off"
                name="<?= $escaper->escapeHtmlAttr($helper->getQueryParamName()) ?>"
                value="<?= /** @noEscape */ $helper->getEscapedQueryText() ?>"
                placeholder="<?= $escaper->escapeHtmlAttr(__('What are you looking for?')) ?>"
                maxlength="<?= $escaper->escapeHtmlAttr($helper->getMaxQueryLength()) ?>"
                class="relative w-full"
                :class="{'z-10': searchIsFocused}"
                @focus="searchIsFocused = true"
                @blur="searchIsFocused = false"
                @focus.once="suggest"
                @input.debounce.300="suggest"
                @keydown.arrow-down.prevent="focusElement($root.querySelector('[tabindex]'))"
                @search-open.window.debounce.10="
                        $el.focus()
                        $el.select()
                "
            />
        </div>
        <template x-if="suggestions.length > 0">
             <div class="absolute flex flex-col w-full mt-1 leading-normal transition bg-white appearance-none text-grey-800">
                 <template x-for="suggestion in suggestions">
                     <div class="flex justify-between p-2 mb-1 border cursor-pointer"
                          tabindex="0"
                          @click="search(suggestion.title)"
                          @keydown.enter="search(suggestion.title)"
                          @keydown.arrow-up.prevent="
                              focusElement($event.target.previousElementSibling) || $refs.searchInput.focus()
                          "
                          @keydown.arrow-down.prevent="focusElement($event.target.nextElementSibling)"
                     >
                         <span x-text="suggestion.title"></span>
                         <span x-text="suggestion.num_results"></span>
                     </div>
                 </template>
             </div>
        </template>
        <button type="submit"
                title="<?= $escaper->escapeHtml(__('Search')) ?>"
                class="absolute top-3 right-4 action search"
                :class="{'z-10': searchIsFocused}"
                aria-label="Search"
                @focus="searchIsFocused = true"
                @blur="searchIsFocused = false"
        >
            <?= $heroicons->searchHtml("w-6 h-6") ?>
            <span class="sr-only"><?= $escaper->escapeHtml(__('Search')) ?></span>
        </button>
    </form>
</div>
