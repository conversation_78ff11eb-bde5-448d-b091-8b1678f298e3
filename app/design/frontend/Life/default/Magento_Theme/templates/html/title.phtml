<?php

declare(strict_types=1);

use Magento\Framework\Escaper;
use Magento\Theme\Block\Html\Title;

/** @var Escaper $escaper */
/** @var Title $block */

$cssClass = $block->getCssClass() ? ' ' . $block->getCssClass() : 'text-3xl';
$pageHeading = trim((string)$block->getPageHeading());

if (!$pageHeading) {
    return;
}

?>

<div class="container my-6 <?= /** @noEscape */ $cssClass ?>">
    <h1
        class="heading-2 page-title"
        id="page-title"
        <?php if ($block->getId()): ?> id="<?= $escaper->escapeHtmlAttr($block->getId()) ?>" <?php endif; ?>
    >
        <span class="base" data-ui-id="page-title-wrapper" <?= $block->getAddBaseAttribute() ?>>
            <?= $escaper->escapeHtml($pageHeading) ?>
        </span>
    </h1>
    <?= $block->getChildHtml() ?>
</div>
