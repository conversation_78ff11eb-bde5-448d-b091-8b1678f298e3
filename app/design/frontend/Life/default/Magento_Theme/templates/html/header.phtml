<?php

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\SvgIcons;
use Life\DisableCheckoutExtensions\Helper\Config as DisableCheckoutConfigHelper;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;

/** @var Escaper $escaper */
/** @var Template $block */
/** @var ViewModelRegistry $viewModels */

/** @var SvgIcons $svgIcons */
$svgIcons = $viewModels->require(SvgIcons::class);

/** @var Hyva\Theme\ViewModel\StoreConfig $storeConfig */
$storeConfig = $viewModels->require(Hyva\Theme\ViewModel\StoreConfig::class);
$showMiniCart = $storeConfig->getStoreConfig(\Magento\Checkout\Block\Cart\Sidebar::XML_PATH_CHECKOUT_SIDEBAR_DISPLAY);

/** @var DisableCheckoutConfigHelper $disableCheckoutConfigHelper */
$disableCheckoutConfigHelper = $this->helper(DisableCheckoutConfigHelper::class);
$disableCheckout = $disableCheckoutConfigHelper->getConfig()->getDisableCheckout();
?>
<script defer="defer">
    function initHeader () {
        return {
            cart: {},
            isCartOpen: false,
            menuOpen: false,
            scrollingDown: false,
            scrollPosTop: true,
            alertHideMobileHeader: false,
            uspTicker: document.querySelector('.page-wrapper > .widget--redkiwi--widget-usp'),
            uspTickerVisibleHeight: '0px',
            startingScrollPos: window.pageYOffset,
            getScrollDir() {
                // only trigger when scrolling down or up more then 50 pixels
                const scrollBound = 50;
                if (window.pageYOffset < this.startingScrollPos - scrollBound | window.pageYOffset > this.startingScrollPos + scrollBound) {

                    // reset starting point
                    this.startingScrollPos = window.pageYOffset;

                    let goingDown = false;
                    let scrollPos = window.pageYOffset;

                    if (scrollPos > this.prevScrollPos && window.pageYOffset > 140) {
                        goingDown = true;
                    }

                    this.scrollingDown = goingDown;
                    this.prevScrollPos = scrollPos;
                    this.scrollPosTop = (window.pageYOffset == 0) ? true : false;
                }
            },
            getData(data) {
                if (data.cart) { this.cart = data.cart };
            },
            isCartEmpty() {
                return !this.cart.summary_count;
            },
            toggleCart(event) {
                if (event.detail && event.detail.isOpen !== undefined) {
                    this.isCartOpen = event.detail.isOpen;
                    if (!this.isCartOpen && this.$refs && this.$refs.cartButton) {
                        this.$refs.cartButton.focus();
                    }
                } else {
                    <?php
                    /*
                     * The toggle-cart event was previously dispatched without parameter to open the drawer (not toggle).
                     * Keeping this in here for backwards compatibility.
                     */
                    ?>
                    this.isCartOpen = true;
                }
            },
            toggleMenu(){
                this.menuOpen = !this.menuOpen;
                document.querySelector('body').classList.toggle('overflow-hidden');

                if (this.menuOpen && this.uspTicker) {
                    this.uspTickerVisibleHeight = this.uspTicker.offsetHeight - window.scrollY > 0 ?
                        `${this.uspTicker.offsetHeight - window.scrollY}px` :
                        `0px`;
                }
            },
            getCssVar() {
                return `--tickerVisibileHeight: ${this.uspTickerVisibleHeight}`;
            },
            closeMenu(){
                this.menuOpen = false;
                document.querySelector('body').classList.remove('overflow-hidden');
            },
            hideMobileHeader(event) {
                if (event.detail) {
                    this.alertHideMobileHeader = event.detail.isAlertBar;
                }
            },
        }
    }
    function initCompareHeader() {
        return {
            compareProducts: null,
            itemCount: 0,
            receiveCompareData(data) {
                if (data['compare-products']) {
                    this.compareProducts = data['compare-products'];
                    this.itemCount = this.compareProducts.count;
                }
            }
        }
    }
</script>
<div id="header"
    class="peer z-30 w-full transition-all duration-300 ease-in-out border-b border-opacity-50 border-secondary-900"
    :class="{
        'bg-white md:bg-secondary-300': scrollPosTop,
        'bg-white': !scrollPosTop || menuOpen,
        'max-md:relative': !scrollPosTop && !menuOpen,
        'max-md:invisible max-md:-top-36': scrollingDown && !menuOpen,
        'max-md:top-0': !scrollingDown,
        '!hidden': alertHideMobileHeader,
    }"
    x-data="initHeader()"
    @private-content-loaded.window="getData(event.detail.data)"
    @alert-hide-mobile-header.window="hideMobileHeader($event)"
    @scroll.window.debounce.10="getScrollDir()"
    @click.outside="closeMenu()"
    :style="getCssVar()"
>
    <div class="container flex flex-wrap items-center justify-between w-full py-3">
        <!--Mobile navigation-->
        <?= $block->getChildHtml('topmenu') ?>

        <!--Logo-->
        <?= $block->getChildHtml('logo'); ?>

        <!--Search-->
        <div class="order-3 w-full md:w-auto md:order-2 min-w-[33vw]">
            <?= $block->getChildHtml('header-search'); ?>
        </div>

        <!--Icons-->
        <div class="flex items-center justify-end order-2 w-1/4 md:order-3 md:w-auto gap-x-2 md:gap-x-6">
            <!--Language switcher-->
            <?= $block->getChildHtml('store-language-switcher') ?>

            <!-- Stores Link -->
            <a
                href="<?= $escaper->escapeUrl($block->getUrl($this->helper(\MageWorx\StoreLocator\Helper\Data::class)->getLinkUrl())); ?>"
                class="relative flex flex-col p-1 no-underline transition duration-300 ease-in-out rounded md:hidden focus:ring-secondary-700/50 focus:border-transparent focus:outline-none focus:ring-2 focus:bg-secondary"
            >
                <?= $svgIcons->storeHtml("w-[2.1875rem] h-[2.1875rem] md:w-8 md:h-8") ?>
                <span class="sr-only"><?= $escaper->escapeHtmlAttr(__('Stores')) ?></span>
            </a>

            <!--Customer Icon & Dropdown-->
            <?= $block->getChildHtml('customer') ?>

            <?php if (!$disableCheckout): ?>

                <!--Cart Icon-->
                <?php if ($showMiniCart): ?>
                    <button
                <?php else: ?>
                    <a
                <?php endif ?>
                    id="menu-cart-icon"
                    class="relative flex flex-col p-1 no-underline transition duration-300 ease-in-out rounded focus:ring-secondary-700/50 focus:border-transparent focus:outline-none focus:ring-2"
                    :class="{ 'hover:bg-secondary focus:bg-secondary': scrollPosTop, 'hover:bg-secondary-300 focus:bg-secondary-300': !scrollPosTop }"
                    x-ref="cartButton"
                    :aria-disabled="isCartEmpty()"
                    aria-label="<?= $escaper->escapeHtmlAttr(__('Cart')) ?>"
                    <?php if ($showMiniCart): ?>
                        @click.prevent.stop="() => {
                            $dispatch('toggle-cart', { isOpen: true })
                        }"
                        @toggle-cart.window="toggleCart($event)"
                        :aria-expanded="isCartOpen"
                        aria-haspopup="dialog"
                    <?php else: ?>
                        href="<?= $escaper->escapeUrl($block->getUrl('checkout/cart/index')) ?>"
                    <?php endif ?>
                >
                    <span x-show="isCartEmpty()" class="relative self-center">
                        <?= $svgIcons->cartEmptyHtml("w-[2.35rem] h-[2.35rem] md:w-[2.2rem] md:h-[2.2rem]") ?>
                    </span>
                    <span x-cloak x-show="!isCartEmpty()" class="relative self-center">
                        <?= $svgIcons->cartFilledHtml("w-[2.35rem] h-[2.35rem] md:w-[2.2rem] md:h-[2.2rem]") ?>
                        <span class="absolute top-1 right-1 text-[0.625rem] font-bold text-primary uppercase w-3.5 h-3.5 flex justify-center items-center">
                            <span x-text="cart.summary_count"></span>
                        </span>
                    </span>
                    <span class="hidden mt-0.5 text-xs text-center md:inline"><?= $escaper->escapeHtmlAttr(__('Cart')) ?></span>

                <?php if ($showMiniCart): ?>
                    </button>
                <?php else: ?>
                    </a>
                <?php endif ?>

            <?php endif ?>

        </div>
    </div>

    <?php if (!$disableCheckout): ?>
        <!--Cart Drawer-->
        <?= $block->getChildHtml('cart-drawer'); ?>
    <?php endif ?>

    <!--Authentication Pop-Up-->
    <?= $block->getChildHtml('authentication-popup'); ?>
</div>
