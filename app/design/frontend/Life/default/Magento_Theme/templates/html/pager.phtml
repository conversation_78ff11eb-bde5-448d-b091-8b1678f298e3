<?php

declare(strict_types=1);

use Hyva\Theme\ViewModel\SvgIcons;

/** @var \Magento\Theme\Block\Html\Pager $block */
/** @var \Magento\Framework\Escaper $escaper */
/** @var \Hyva\Theme\Model\LocaleFormatter $localeFormatter */
/** @var \Hyva\Theme\Model\ViewModelRegistry $viewModels */

/** @var SvgIcons $svgIcons */
$svgIcons = $viewModels->require(SvgIcons::class);

$paginationUrlAnchor = $block->hasData('pagination_url_anchor')
    ? '#' . $escaper->escapeHtmlAttr((string) $block->getData('pagination_url_anchor'))
    : '';

$pagerItemClass = "relative inline-flex items-center text-xl leading-7 border border-primary rounded-full text-primary w-12 h-12 flex justify-center items-center";
$pagerItemBtnClass = $pagerItemClass
    . " transition duration-150 ease-in-out"
    . " hover:text-white hover:bg-primary"
    . " focus:z-10 focus:outline-none focus:border-primary-lighter focus:shadow-outline-blue"
    . " active:bg-primary active:text-white";

$block->setJump(2);
$block->setFrameLength(2);

if (!$block->getCollection()->getSize()) {
    return;
}
?>
<?php if ($block->getUseContainer()): ?>
    <div class="grid items-center grid-flow-row grid-cols-4 gap-2 pager sm:grid-cols-8 md:grid-cols-4 lg:grid-cols-8">
<?php endif ?>

<?php if ($block->getShowAmounts()): ?>
    <p class="flex order-3 col-span-2 toolbar-amount sm:order-2 md:order-3 lg:order-2 gap-x-1">
        <span class="text-sm toolbar-number">
        <?php if ($block->getLastPageNum() > 1): ?>
            <?= $escaper->escapeHtml(
                __(
                    'Items %1 to %2 of %3 total',
                    $localeFormatter->formatNumber($block->getFirstNum()),
                    $localeFormatter->formatNumber($block->getLastNum()),
                    $localeFormatter->formatNumber($block->getTotalNum())
                )
            ) ?>
        <?php elseif ($block->getTotalNum() == 1): ?>
            <?= $escaper->escapeHtml(__('%1 Item', $localeFormatter->formatNumber($block->getTotalNum()))) ?>
        <?php else: ?>
            <?= $escaper->escapeHtml(__('%1 Item(s)', $localeFormatter->formatNumber($block->getTotalNum()))) ?>
        <?php endif; ?>
        </span>
    </p>
<?php endif ?>

<div class="flex justify-center order-2 col-span-4">
    <?php if ($block->getLastPageNum() > 1): ?>
        <nav class="inline-flex items-center pages" aria-label="pagination">
            <ol class="relative z-0 inline-flex flex-wrap justify-center gap-2 items pages-items md:gap-x-4 md:gap-y-2">
                <li class="item pages-item-previous">
                    <?php $text = $block->getAnchorTextForPrevious() ? $block->getAnchorTextForPrevious() : ''; ?>
                    <?php if (!$block->isFirstPage()): ?>
                        <a
                            href="<?= $escaper->escapeUrl($block->getPreviousPageUrl()) . /* @noEscape */ $paginationUrlAnchor ?>"
                            class="<?= $escaper->escapeHtmlAttr($text ? 'link ' : 'action ') ?> <?= /* @noEscape */ $pagerItemBtnClass ?> px-3 py-2"
                            <?php if (!$text): ?>
                                aria-label="<?= $escaper->escapeHtmlAttr(__('Previous')) ?>"
                            <?php endif; ?>
                        >
                            <?php if ($text): ?>
                                <span class="sr-only label"><?= $escaper->escapeHtml(__('Page')) ?></span>
                                <span><?= $escaper->escapeHtml($text) ?></span>
                            <?php else: ?>
                                <?= $svgIcons->arrowLeftHtml('', 20, 20, [ 'aria-hidden' => 'true']); ?>
                            <?php endif; ?>
                        </a>
                    <?php else: ?>
                        <a
                            role="link"
                            class="previous <?= /* @noEscape */ $pagerItemClass ?> px-3 py-2 opacity-20"
                            aria-disabled="true"
                            aria-label="<?= $escaper->escapeHtmlAttr(__('Previous')) ?>"
                        >
                            <?= $svgIcons->arrowLeftHtml('', 20, 20, [ 'aria-hidden' => 'true']); ?>
                        </a>
                    <?php endif; ?>
                </li>

                <?php if ($block->isLastPage() && $block->canShowFirst()): ?>
                    <li
                        class="item"
                        aria-label="<?= $escaper->escapeHtml(__('Page') . ' 1') ?>"
                    >
                        <a
                            href="<?= $escaper->escapeUrl($block->getFirstPageUrl()) . /* @noEscape */ $paginationUrlAnchor ?>"
                            class="page first <?= /* @noEscape */ $pagerItemBtnClass ?> px-4 py-2"
                        >
                            <span class="sr-only label"><?= $escaper->escapeHtml(__('Page')) ?></span>
                            <span><?= $escaper->escapeHtml($localeFormatter->formatNumber(1)) ?></span>
                        </a>
                    </li>
                <?php endif; ?>

                <?php if ($block->isLastPage() && $block->canShowPreviousJump()): ?>
                    <li class="-ml-px item">
                        <a
                            href="<?= $escaper->escapeUrl($block->getPreviousJumpUrl()) . /* @noEscape */ $paginationUrlAnchor ?>"
                            class="page previous jump <?= /* @noEscape */ $pagerItemBtnClass ?> !bg-transparent !border-none !items-end !text-primary !w-6"
                            aria-label="<?= $escaper->escapeHtmlAttr(__(
                                'Skip to page %1',
                                $localeFormatter->formatNumber($block->getPreviousJumpPage())
                            )) ?>"
                        >
                            <span aria-label="<?= $escaper->escapeHtml(__('Jump backward')) ?>">...</span>
                        </a>
                    </li>
                <?php endif; ?>

                <?php foreach ($block->getFramePages() as $page): ?>
                    <?php if ($block->isPageCurrent($page)): ?>
                        <li class="-ml-px item">
                            <a
                                href="<?= $escaper->escapeUrl($block->getPageUrl($page)) . /* @noEscape */ $paginationUrlAnchor ?>"
                                class="page <?= /* @noEscape */ $pagerItemBtnClass ?> z-10 px-4 py-2 bg-primary !text-white border-primary"
                                aria-current="page"
                            >
                                <span class="sr-only label">
                                    <?= $escaper->escapeHtml(
                                        __('You\'re currently reading page')
                                    ) ?>
                                </span>
                                <?= $escaper->escapeHtml($localeFormatter->formatNumber($page)) ?>
                            </a>
                        </li>
                    <?php else: ?>
                        <li class="-ml-px item">
                            <a
                                href="<?= $escaper->escapeUrl($block->getPageUrl($page)) . /* @noEscape */ $paginationUrlAnchor ?>"
                                class="page <?= /* @noEscape */ $pagerItemBtnClass ?> px-4 py-2"
                            >
                                <span class="sr-only label"><?= $escaper->escapeHtml(__('Page')) ?></span>
                                <span><?= $escaper->escapeHtml($localeFormatter->formatNumber($page)) ?></span>
                            </a>
                        </li>
                    <?php endif; ?>
                <?php endforeach; ?>

                <?php if ($block->canShowNextJump()): ?>
                    <li class="-ml-px item">
                        <a
                            href="<?= $escaper->escapeUrl($block->getNextJumpUrl()) . /* @noEscape */ $paginationUrlAnchor ?>"
                            class="page next jump <?= /* @noEscape */ $pagerItemBtnClass ?> !bg-transparent !border-none !items-end !text-primary !w-6"
                            aria-label="<?= $escaper->escapeHtmlAttr(__(
                                'Skip to page %1',
                                $localeFormatter->formatNumber($block->getNextJumpPage())
                            )) ?>"
                        >...</a>
                    </li>
                <?php endif; ?>

                <?php if ($block->canShowLast()): ?>
                    <li class="-ml-px text-gray-500 item">
                        <a
                            href="<?= $escaper->escapeUrl($block->getLastPageUrl()) . /* @noEscape */ $paginationUrlAnchor ?>"
                            class="page last <?= /* @noEscape */ $pagerItemBtnClass ?> px-4 py-2"
                        >
                            <span class="sr-only label"><?= $escaper->escapeHtml(__('Page')) ?></span>
                            <span><?= $escaper->escapeHtml($localeFormatter->formatNumber($block->getLastPageNum())) ?></span>
                        </a>
                    </li>
                <?php endif; ?>

                <li class="-ml-px item pages-item-next">
                    <?php if (!$block->isLastPage()): ?>
                        <?php $text = $block->getAnchorTextForNext() ? $block->getAnchorTextForNext() : ''; ?>
                        <a
                            href="<?= $escaper->escapeUrl($block->getNextPageUrl()) . /* @noEscape */ $paginationUrlAnchor ?>"
                            class="<?= $text ? 'link ' : 'action ' ?> next <?= /* @noEscape */ $pagerItemBtnClass ?> px-3 py-2"
                            <?php if (!$text): ?>
                                aria-label="<?= $escaper->escapeHtmlAttr(__('Next')) ?>"
                            <?php endif; ?>
                        >
                            <?php if ($text): ?>
                                <span class="sr-only label"><?= $escaper->escapeHtml(__('Page')) ?></span>
                                <span><?= $escaper->escapeHtml($text) ?></span>
                            <?php else: ?>
                                <?= $svgIcons->arrowRightHtml('', 20, 20, [ 'aria-hidden' => 'true']); ?>
                            <?php endif; ?>
                        </a>
                    <?php else: ?>
                        <a
                            role="link"
                            class="next <?= /* @noEscape */ $pagerItemClass ?> px-3 py-2 opacity-20"
                            aria-disabled="true"
                            aria-label="<?= $escaper->escapeHtmlAttr(__('Next')) ?>"
                        >
                            <?= $svgIcons->arrowRightHtml('', 20, 20, [ 'aria-hidden' => 'true']); ?>
                        </a>
                    <?php endif; ?>
                </li>
            </ol>
        </nav>
    <?php endif; ?>
</div>

<?php if ($block->isShowPerPage()): ?>
    <div class="flex items-center justify-end order-3 col-span-2 limiter sm:order-2 md:order-3 lg:order-2">
        <label class="text-sm label" for="limiter">
            <span class="mr-2" aria-hidden="true"><?= $escaper->escapeHtml(__('Show')) ?></span>
            <select
                id="limiter"
                class="form-select"
                onchange="location.href = this.value"
                aria-label="<?= $escaper->escapeHtmlAttr(__('Show items per page')) ?>"
            >
                <?php foreach ($block->getAvailableLimit() as $key => $limit): ?>
                    <option value="<?= $escaper->escapeUrl($block->getLimitUrl($key)) ?>"
                        <?php if ($block->isLimitCurrent($key)): ?>
                            selected="selected"<?php endif ?>>
                        <?= $escaper->escapeHtml($localeFormatter->formatNumber((int) $limit)) ?>
                    </option>
                <?php endforeach; ?>
            </select>
        </label>
    </div>
<?php endif ?>

<?php if ($block->getUseContainer()): ?>
    </div>
<?php endif ?>
