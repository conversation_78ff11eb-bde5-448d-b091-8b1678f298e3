<?php

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Magento\Framework\Escaper;
use Magento\Theme\Block\Html\Header\Logo;

/** @var Logo $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */

$storeName = $block->getThemeName();

/** @var \Hyva\Theme\ViewModel\Logo\LogoPathResolver $logoPathResolver */
$logoPathResolver = $block->getData('logoPathResolver');
$logoSrc = $logoPathResolver && method_exists($logoPathResolver, 'getLogoSrc')
    ? $logoPathResolver->getLogoSrc($block->getData('logo_file'))
    : $block->getLogoSrc();
?>
<div class="order-1">
    <a
        class="flex items-center justify-center text-xl font-medium tracking-wide text-gray-800 no-underline hover:no-underline font-title"
        href="<?= $escaper->escapeUrl($block->getUrl('')) ?>"
        aria-label="<?= $escaper->escapeHtmlAttr(__('Go to Home page')) ?>"
    >
        <img
            src="<?= $escaper->escapeUrl($logoSrc) ?>"
            alt="<?= $escaper->escapeHtmlAttr($block->getLogoAlt() ? $block->getLogoAlt() : __('Store logo')) ?>"
            width="76"
            height="60"
        />
        <?php if (!$logoSrc): ?>
            <?= $escaper->escapeHtml($storeName) ?>
        <?php endif; ?>
    </a>
</div>
