<?php

declare(strict_types=1);

use Magento\Theme\Block\Html\Breadcrumbs;
use Magento\Framework\Escaper;
use Hyva\Theme\ViewModel\SvgIcons;

/** @var Escaper $escaper */
/** @var Breadcrumbs $block */
/** @var array[] $crumbs */
/** @var Hyva\Theme\Model\ViewModelRegistry $viewModels */

/** @var SvgIcons $svgIcons */
$svgIcons = $viewModels->require(SvgIcons::class);

?>
<?php if ($crumbs && is_array($crumbs)): ?>
<nav class="breadcrumbs" aria-label="Breadcrumb" id="breadcrumbs">
    <div class="container">
        <ol class="flex flex-wrap items-center py-4 text-sm rounded items list-reset text-grey">
            <?php foreach ($crumbs as $crumbName => $crumbInfo): ?>
                <?php $breadCrumbType = preg_replace('/\d/', '', $crumbName); ?>
                <li class="item hidden md:flex <?= $escaper->escapeHtmlAttr($crumbName) ?>">
                <?php if (!$crumbInfo['first']): ?>
                    <span aria-hidden="true" class="hidden px-2 md:block text-primary-lighter">/</span>
                <?php endif; ?>
                <?php if ($crumbInfo['link']): ?>
                    <a href="<?= $escaper->escapeUrl($crumbInfo['link']) ?>"
                       class="flex items-center no-underline gap-x-2"
                       title="<?= $escaper->escapeHtmlAttr($crumbInfo['title']) ?>"
                    >
                        <?php if ($crumbInfo['first']): ?>
                            <?= $svgIcons->homeHtml('hidden md:block w-5 h-5', 16, 16, ['aria-hidden' => 'true'])  ?>
                            <?= $svgIcons->chevronLeftHtml('md:hidden w-5 h-5', 16, 16, ['aria-hidden' => 'true'])  ?>
                            <span class="md:hidden"><?= $escaper->escapeHtml($crumbInfo['label']) ?></span>
                        <?php else: ?>
                            <?= $svgIcons->chevronLeftHtml('md:hidden w-5 h-5', 16, 16, ['aria-hidden' => 'true'])  ?>
                            <?= $escaper->escapeHtml($crumbInfo['label']) ?>
                        <?php endif; ?>
                    </a>
                <?php elseif ($crumbInfo['last']): ?>
                    <span
                       class="text-primary-lighter"
                       aria-current="page"
                    ><?= $escaper->escapeHtml($crumbInfo['label']) ?></span>
                <?php else: ?>
                    <?= $escaper->escapeHtml($crumbInfo['label']) ?>
                <?php endif; ?>
                </li>
            <?php endforeach; ?>
        </ol>
    </div>
</nav>
<?php endif; ?>
