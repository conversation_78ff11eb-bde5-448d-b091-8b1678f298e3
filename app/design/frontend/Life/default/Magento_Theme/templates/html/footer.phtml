<?php

declare(strict_types=1);

use Hyva\Theme\ViewModel\SvgIcons;
use Magento\Framework\View\Element\Template;
use Magento\Framework\Escaper;

/** @var Template $block */
/** @var ViewModelRegistry $viewModels */
/** @var Escaper $escaper */

/** @var SvgIcons $svgIcons */
$svgIcons = $viewModels->require(SvgIcons::class);
?>
<div class="text-white">
    <div class="container relative py-8 mx-auto bg-primary">
        <div class="relative z-10 flex flex-col lg:flex-wrap lg:flex-row lg:gap-x-4 gap-y-2 lg:gap-y-0">
            <?php foreach ($block->getChildNames() as $childName): ?>
                <?= $childName !== 'footer-copyright' ? $block->getBlockHtml($childName) : '' ?>
            <?php endforeach; ?>
        </div>

        <?= $svgIcons->footerBgHtml("max-lg:hidden absolute h-full w-auto top-0 right-0") ?>
    </div>
    <div class="text-xs text-black bg-white rounded-b-xl">
        <div class="container flex flex-wrap items-center justify-between gap-3 py-4 mx-auto">
            <?= $block->getChildHtml('copyright') ?>
        </div>
    </div>
</div>

