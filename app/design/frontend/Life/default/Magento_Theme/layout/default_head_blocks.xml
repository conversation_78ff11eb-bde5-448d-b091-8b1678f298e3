<?xml version="1.0"?>
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <head>
        <link rel="icon" src="Magento_Theme::images/favicon/favicon.ico" sizes="any"/>
        <!-- .svg extension is marked as font asset and rendered as such. To prevent this use .svg+xml -->
        <link rel="icon" src="Magento_Theme::images/favicon/favicon.svg+xml" type="image/svg+xml"/>
        <link rel="apple-touch-icon" src="Magento_Theme::images/favicon/apple-touch-icon.png"/>
        <link rel="manifest" src="Magento_Theme::images/favicon/manifest.json"/>
        <meta name="msapplication-TileImage" content="Magento_Theme::images/favicon/mstile-144x144.png" />
    </head>
</page>
