<?php

declare(strict_types=1);

/** @var \Smile\ElasticsuiteCms\Block\Page\Result $block */
/** @var \Magento\Framework\Escaper $escaper */
?>
<div class="search results">
    <ul>
        <?php foreach ($block->getPageCollection() as $currentPage) : ?>
            <li>
                <a
                    class="block bg-primary-50 text-primary-300 font-bold hover:bg-primary hover:text-white rounded-md p-3 mt-4"
                    href="<?= $escaper->escapeUrl($block->getPageUrl($currentPage->getId())) ?>"
                >
                    <?= $escaper->escapeHtml($currentPage->getTitle()); ?>
                </a>
            </li>
        <?php endforeach; ?>
    </ul>
</div>
