<?php

declare(strict_types=1);

use Hyva\Theme\ViewModel\SvgIcons;
use Life\DisableCatalog\Helper\Config as DisableCatalogConfigHelper;

/** @var \Smile\ElasticsuiteCms\Block\Page\Suggest $block */
/** @var \Magento\Framework\Escaper $escaper */
/** @var \Hyva\Theme\Model\ViewModelRegistry $viewModels */

/** @var SvgIcons $svgIcons */
$svgIcons = $viewModels->require(SvgIcons::class);

/** @var DisableCatalogConfigHelper $disableCatalogConfigHelper */
$disableCatalogConfigHelper = $this->helper(DisableCatalogConfigHelper::class);
$disableCatalog = $disableCatalogConfigHelper->getConfig()->getDisable();
?>
<?php if ($block->canShowBlock()): ?>
   <div class="container search results">
        <div class="w-full max-w-4xl mx-auto lg:w-2/3">
            <?php if (!$disableCatalog): ?>
                <h3><?= __('Results in cms pages:')?></h3>
            <?php endif; ?>

            <ul class="my-6">
                <?php foreach ($block->getPageCollection() as $currentPage) : ?>
                        <li>
                            <a
                                class="flex items-center justify-between p-3 mt-4 text-base font-bold leading-7 text-black transition-all duration-300 ease-in-out bg-white gap-x-2 rounded-xl hover:bg-primary hover:text-white"
                                href="<?= $escaper->escapeUrl($block->getPageUrl($currentPage->getId())) ?>"
                            >
                                <?= $escaper->escapeHtml($currentPage->getTitle()); ?>
                                <?= $svgIcons->arrowRightHtml('w-6 h-6'); ?>
                            </a>
                        </li>
                <?php endforeach; ?>
            </ul>
        </div>
   </div>

    <script defer="defer">
        // Hide products no results messages if there are CMS results
        if (document.querySelector('.catalog-search.container .intro-text')) {
            document.querySelector('.catalog-search.container .intro-text').style.display = 'none';
        }
        if (document.querySelector('.column.main .message.notice')) {
            document.querySelector('.column.main .message.notice').style.display = 'none';
        }
    </script>
<?php endif;
