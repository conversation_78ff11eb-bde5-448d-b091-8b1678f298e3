<?php

declare(strict_types=1);

use Magento\Catalog\Helper\Output;

/** @var \Magento\Catalog\Block\Category\View $block */
?>
<?php if ($bottomDescription = $block->getCurrentCategory()->getBottomDescription()) :?>
    <div class="category-description" id="category-bottom-description">
        <?= /* @noEscape */ $this->helper(Output::class)->categoryAttribute(
            $block->getCurrentCategory(),
            $bottomDescription,
            'bottom_description'
        ) ?>
    </div>
<?php endif; ?>
