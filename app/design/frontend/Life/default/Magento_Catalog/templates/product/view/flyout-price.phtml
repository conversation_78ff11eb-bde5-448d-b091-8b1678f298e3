<?php

declare(strict_types=1);

use Hyva\Theme\ViewModel\ProductPage;
use Hyva\Theme\ViewModel\ProductPrice;
use Life\ProductsConnector\ViewModel\ProductPrice as LifeProductPrice;
use Life\ReplacePriceZeros\ViewModel\Config;
use Magento\Catalog\Pricing\Price\FinalPrice;
use Magento\Catalog\Pricing\Price\RegularPrice;
use Magento\Catalog\Pricing\Price\TierPrice;
use Magento\Msrp\Pricing\Price\MsrpPrice;

/** @var \Magento\Catalog\Block\Product\AbstractProduct $block */
/** @var \Magento\Framework\Escaper $escaper */
/** @var \Hyva\Theme\Model\ViewModelRegistry $viewModels */

/** @var \Magento\Catalog\Model\Product $product */
$product = $block->getProduct();

/** @var ProductPrice $productPriceViewModel */
$productPriceViewModel = $viewModels->require(ProductPrice::class);

/** @var ProductPage $productViewModel */
$productViewModel = $viewModels->require(ProductPage::class);

$regularPrice = $productPriceViewModel->getPriceValue(RegularPrice::PRICE_CODE, $product);
$finalPrice = $productPriceViewModel->getPriceValue(FinalPrice::PRICE_CODE, $product);
$tierPrices = $productPriceViewModel->getTierPrices(TierPrice::PRICE_CODE, $product);
$msrpPrice = iterator_to_array($product->getPriceInfo()->getPrices())[MsrpPrice::PRICE_CODE] ?? null;

if ($productPriceViewModel->displayPriceInclAndExclTax()) {
    $regularPriceExclTax = $productPriceViewModel->getPriceValueExclTax(RegularPrice::PRICE_CODE, $product);
    $finalPriceExclTax = $productPriceViewModel->getPriceValueExclTax(FinalPrice::PRICE_CODE, $product);
}

$displayTax = $productPriceViewModel->displayPriceIncludingTax();

/** @var Config $replaceZeros */
$replaceZeros = $viewModels->require(Config::class);

/** @var LifeProductPrice $lifeProductPriceViewModel */
$lifeProductPriceViewModel = $viewModels->require(LifeProductPrice::class);

$discountPercentage = $lifeProductPriceViewModel->getDiscountPercentage($product);
$discountAmount = $lifeProductPriceViewModel->getDiscountAmount($product);
$pricePercentageThreshold = $lifeProductPriceViewModel->getPricePercentageThresholdAmount();
?>
<script defer="defer">
    function initPrice<?= (int)$product->getId() ?>Flyout() {

        <?php /* All four of these keys are used - they are rendered by PHP */ ?>
        const regularPriceInclTaxKey = 'oldPrice',
            regularPriceExclTaxKey = 'baseOldPrice',
            finalPriceInclTaxKey = 'finalPrice',
            finalPriceExclTaxKey = 'basePrice';

        function calculateCustomOptionPrices(activeCustomOptions, customOptionPrices) {
            return activeCustomOptions.reduce((priceAccumulator, activeCustomOptionId) => {
                const customOptionPrice = customOptionPrices[activeCustomOptionId];
                if (customOptionPrice) {
                    return Number.parseFloat(priceAccumulator) + Number.parseFloat(customOptionPrice);
                }
                return priceAccumulator;
            }, 0);
        }

        return {
            regularPriceKey: <?= $displayTax ? 'regularPriceInclTaxKey' : 'regularPriceExclTaxKey' ?>,
            finalPriceKey: <?= $displayTax ? 'finalPriceInclTaxKey' : 'finalPriceExclTaxKey' ?>,
            activeProductsPriceData: false,
            initialFinalPrice: <?= (float)$finalPrice ?>,
            initialMsrpPrice: <?= (float)$lifeProductPriceViewModel->getMsrpPrice($product) ?>,
            calculatedFinalPrice: false,
            calculatedFinalPriceWithCustomOptions: false,
            initialTierPrices: <?= /** @noEscape */ json_encode($tierPrices) ?>,
            showRegularPriceLabel: <?= ($finalPrice < $regularPrice) ? 'true' : 'false' ?>,
            customOptionPrices: [],
            <?php if ($productPriceViewModel->displayPriceInclAndExclTax()): ?>
            initialBasePrice: <?= (float)$finalPriceExclTax ?>,
            calculatedBasePrice: false,
            customOptionBasePrices: [],
            calculatedBasePriceWithCustomOptions: false,
            <?php endif; ?>
            activeCustomOptions: [],
            qty: 1,
            discountPercentage: <?= /** @noEscape */ $discountPercentage; ?>,
            discountAmount: <?= /** @noEscape */ $discountAmount; ?>,
            showPercentage: <?= ($lifeProductPriceViewModel->getNormalPrice($product) <= $pricePercentageThreshold) ? 1 : 0; ?>,
            updateCustomOptionActive(data) {
                let activeCustomOptions = this.activeCustomOptions;
                const customOptionId = data.customOptionId;

                if (data.active) {
                    if (!activeCustomOptions.includes(customOptionId)) {
                        activeCustomOptions.push(data.customOptionId);
                    }
                } else {
                    if (customOptionId && activeCustomOptions.includes(customOptionId)) {
                        let index = activeCustomOptions.indexOf(customOptionId);
                        activeCustomOptions.splice(index, 1);
                    }
                }
                this.calculateFinalPriceWithCustomOptions()
            },
            updateCustomOptionPrices(prices, basePrices) {
                if (prices) {
                    this.customOptionPrices = prices;
                }

                <?php if ($productPriceViewModel->displayPriceInclAndExclTax()): ?>
                if (basePrices) {
                    this.customOptionBasePrices = basePrices;
                }
                <?php endif; ?>

                this.calculateFinalPriceWithCustomOptions();
            },
            calculateFinalPrice() {
                const findApplicableTierPrice = (initialPrice, withTax) => {
                    if (this.activeProductsPriceData && this.activeProductsPriceData.tierPrices) {
                        const key = withTax ? 'price' : 'basePrice'
                        return this.activeProductsPriceData.tierPrices.reduce((acc, tierPrice) => {
                            if (this.qty >= tierPrice.qty && tierPrice[key] < acc) {
                                return tierPrice[key];
                            }
                            return acc;
                        }, this.activeProductsPriceData[withTax ? finalPriceInclTaxKey : finalPriceExclTaxKey].amount);

                    } else {
                        const key = withTax ? 'price_incl_tax' : 'price_excl_tax';
                        return Object.values(this.initialTierPrices).reduce((acc, tierPrice) => {
                            if (this.qty >= tierPrice.price_qty && tierPrice[key] < acc) {
                                return tierPrice[key];
                            }
                            return acc;
                        }, initialPrice);

                    }
                }

                this.calculatedFinalPrice = findApplicableTierPrice(this.initialFinalPrice, <?= $displayTax ? 'true' : 'false' ?>);
                window.dispatchEvent(new CustomEvent("update-product-final-price", {detail: this.calculatedFinalPrice}));

                <?php if ($productPriceViewModel->displayPriceInclAndExclTax()): ?>
                this.calculatedBasePrice = findApplicableTierPrice(<?= (float) $finalPriceExclTax ?>, false);
                window.dispatchEvent(new CustomEvent("update-product-base-price", {detail: {basePrice: this.calculatedBasePrice}}));
                <?php endif; ?>
            },
            calculatePriceLabelVisibility() {
                this.showRegularPriceLabel =
                    (this.calculatedFinalPrice === this.activeProductsPriceData[this.regularPriceKey].amount) &&
                    this.activeProductsPriceData.isMinimalPrice;
            },
            calculateFinalPriceWithCustomOptions() {
                const finalPrice = this.calculatedFinalPrice || this.initialFinalPrice;
                this.calculatedFinalPriceWithCustomOptions = finalPrice + this.getCustomOptionPrice();
                <?php if ($productPriceViewModel->displayPriceInclAndExclTax()): ?>
                const basePrice = this.calculatedBasePrice || this.initialBasePrice;
                this.calculatedBasePriceWithCustomOptions = basePrice + this.getCustomOptionBasePrice();
                <?php endif; ?>
            },
            getCustomOptionPrice() {
                return calculateCustomOptionPrices(this.activeCustomOptions, this.customOptionPrices);
            },
            <?php if ($productPriceViewModel->displayPriceInclAndExclTax()): ?>
            getCustomOptionBasePrice() {
                return calculateCustomOptionPrices(this.activeCustomOptions, this.customOptionBasePrices);
            },
            <?php endif; ?>
            getFormattedFinalPrice() {
                return this.formatPrice(
                    this.calculatedFinalPriceWithCustomOptions ||
                    this.calculatedFinalPrice ||
                    this.initialFinalPrice
                )
            },
            getFormattedMsrpPrice() {
                let msrpPrice = this.activeProductsPriceData?.msrpPrice?.amount || this.initialMsrpPrice,
                    finalPrice = this.calculatedFinalPriceWithCustomOptions ||
                        this.calculatedFinalPrice ||
                        this.initialFinalPrice;

                if (finalPrice > msrpPrice) {
                    return '';
                }

                return this.formatPrice(msrpPrice)
            },
            <?php if ($productPriceViewModel->displayPriceInclAndExclTax()): ?>
            getFormattedBasePrice() {
                return this.formatPrice(
                    this.calculatedBasePriceWithCustomOptions ||
                    this.calculatedBasePrice ||
                    this.initialBasePrice
                )
            },
            <?php endif; ?>
            isPriceHidden() {
                const finalPrice = this.calculatedFinalPriceWithCustomOptions ||
                    this.calculatedFinalPrice ||
                    this.initialFinalPrice;
                return <?= $product->isSaleable() ? 'false' : 'true' ?> && finalPrice === 0;
            },
            formatPrice(value, showSign) {
                let formatter = new Intl.NumberFormat(
                    document.documentElement.lang,
                    {
                        style: 'currency',
                        currency: '<?= $escaper->escapeHtml($productViewModel->getCurrencyData()['code']) ?>',
                        signDisplay: showSign ? "always" : "auto"
                    }
                );
                let formattedPrice = ((typeof Intl.NumberFormat.prototype.formatToParts) ?
                    formatter.formatToParts(value).map(({type, value}) => {
                        switch (type) {
                            case 'currency':
                                return '<?= $productViewModel->getCurrencyData()['symbol'] ?: ""; ?>' || value;
                            case 'minusSign':
                                return '- ';
                            case 'plusSign':
                                return '+ ';
                            default :
                                return value;
                        }
                    }).reduce((string, part) => string + part) :
                    formatter.format(value)).replace(/\s/g, '');

                if (<?= $replaceZeros->canReplaceZeros() ? 'true' : 'false'; ?>) {
                    return formattedPrice
                        // Replace ,00/.00 with ,-/.-
                        .replace(/\.00$/g, '.-')
                        .replace(/\,00$/g, ',-')
                        // Remove thousands separator
                        .replace(/(,|.)(?=\d{3})/g, '')
                }
                return formattedPrice;
            },
            calculateDiscount() {
                if (
                    this.activeProductsPriceData
                    && this.activeProductsPriceData[this.finalPriceKey]
                    && this.activeProductsPriceData[this.finalPriceKey].amount
                    && this.activeProductsPriceData[this.regularPriceKey]
                    && this.activeProductsPriceData[this.regularPriceKey].amount
                ) {
                    let normalPrice = this.activeProductsPriceData[this.regularPriceKey].amount,
                        finalPrice = this.activeProductsPriceData[this.finalPriceKey].amount;

                    this.discountPercentage = Math.round(((normalPrice - finalPrice) / normalPrice) * 100);
                    this.discountAmount = Math.round(normalPrice - finalPrice);
                    this.showPercentage = (normalPrice <= <?= $pricePercentageThreshold; ?>);
                }
            },
            getDiscount() {
                return (this.showPercentage) ?
                    '-' + this.discountPercentage + '%' :
                    '-' + '<?= $lifeProductPriceViewModel->getCurrencySymbol(); ?>' + this.discountAmount;
            },
            eventListeners: {
                ['@update-prices-<?= (int)$product->getId() ?>.window'](event) {
                    this.activeProductsPriceData = event.detail;

                    this.calculateFinalPrice();
                    this.calculateFinalPriceWithCustomOptions();
                    this.calculatePriceLabelVisibility();
                    this.calculateDiscount();
                },
                ['@update-qty-<?= (int)$product->getId() ?>.window'](event) {
                    this.qty = event.detail;
                    this.calculateFinalPrice();
                    this.calculateFinalPriceWithCustomOptions();
                    this.calculateDiscount();
                },
                ['@update-custom-option-active.window'](event) {
                    this.updateCustomOptionActive(event.detail);
                },
                ['@update-custom-option-prices.window'](event) {
                    this.updateCustomOptionPrices(event.detail);
                },
                <?php if ($productPriceViewModel->displayPriceInclAndExclTax()): ?>
                ['@update-custom-option-base-prices.window'](event) {
                    this.updateCustomOptionPrices(null, event.detail);
                }
                <?php endif; ?>
            }
        }
    }
</script>
<div
    x-data="initPrice<?= (int)$product->getId() ?>Flyout()"
    x-bind="eventListeners"
    class="flex flex-wrap price-box price-final_price gap-x-3 gap-y-1 relative"
>
    <template x-if="discountPercentage">
        <span
            class="flex items-center px-2 py-0.5 text-sm bg-tertiary-300 rounded-xl text-tertiary"
            x-html="getDiscount()"
        ></span>
    </template>
    <template x-if="!activeProductsPriceData && !isPriceHidden()">
        <div class="flex price-container gap-x-4">
            <div
                class="inline-flex items-center final-price text-primary"
                itemprop="offers"
                itemscope=""
                itemtype="http://schema.org/Offer"
            >
                <span class="block text-xs lowercase price-label">
                    <?= ($product->canConfigure() && is_int($product->getPrice())) ?
                        $escaper->escapeHtml(__('As low as')) :
                        '' ?>
                </span>
                <span
                    id="product-price-<?= (int)$product->getId() ?>"
                    class="price-wrapper font-bold no-underline"
                >
                    <span class="price" x-html="getFormattedFinalPrice()">
                        <?= /** @noEscape */ $productViewModel->format($finalPrice) ?>
                    </span>
                </span>
                <meta itemprop="price" content="<?= $escaper->escapeHtmlAttr($finalPrice) ?>">
                <meta
                    itemprop="priceCurrency"
                    content="<?= $escaper->escapeHtmlAttr($productViewModel->getCurrencyData()['code']) ?>"
                >
            </div>
            <?php if ($productPriceViewModel->displayPriceInclAndExclTax()): ?>
                <div class="final-price-excl-tax">
                    <span class="text-gray-900 font-regular">
                        <span><?= $escaper->escapeHtml(__('Excl. Tax')) ?>:</span>
                        <span class="price" x-html="getFormattedBasePrice()">
                            <?= /** @noEscape */ $productViewModel->format($finalPriceExclTax) ?>
                        </span>
                    </span>
                </div>
            <?php endif; ?>
        </div>
    </template>
    <template x-if="activeProductsPriceData &&
        activeProductsPriceData.oldPrice &&
        activeProductsPriceData[finalPriceKey].amount < activeProductsPriceData[regularPriceKey].amount ||
        activeProductsPriceData
    ">
        <div class="flex items-center gap-x-4">
            <template x-if="activeProductsPriceData">
                <div class="flex items-baseline final-price text-primary">
                    <?php if ($product->canConfigure() && is_int($product->getPrice())): ?>
                        <span
                            class="block mr-2 text-xs font-medium lowercase price-label sm:text-base"
                            :class="{ 'hidden' : !showRegularPriceLabel }"
                        >
                            <?= $escaper->escapeHtml(__('As low as')) ?>
                        </span>
                    <?php endif; ?>
                    <span
                        id="product-price-<?= (int)$product->getId() ?>"
                        class="price-wrapper font-bold no-underline"
                    >
                        <span class="price" x-html="getFormattedFinalPrice()"></span>
                    </span>
                </div>
            </template>
        </div>
    </template>
    <?php if ($productPriceViewModel->displayPriceInclAndExclTax()): ?>
        <template x-if="activeProductsPriceData &&
            activeProductsPriceData.oldPrice &&
            activeProductsPriceData[finalPriceKey].amount < activeProductsPriceData[regularPriceKey].amount
        ">
            <div class="old-price-excl-tax">
                <span><?= $escaper->escapeHtml(__('Excl. Tax')) ?>:</span>
                <span class="text-gray-900 font-regular">
                <span
                    class="price"
                    x-html="formatPrice(activeProductsPriceData['baseOldPrice'].amount + getCustomOptionBasePrice())"
                ></span>
            </span>
            </div>
        </template>
        <template x-if="activeProductsPriceData">
            <div class="price-excl-taxinline-block">
                <span><?= $escaper->escapeHtml(__('Excl. Tax')) ?>:</span>
                <span class="text-gray-900 font-regular">
                <span class="price" x-html="getFormattedBasePrice()"></span>
            </span>
            </div>
        </template>
    <?php endif; ?>
    <?php if ($msrpPrice->canApplyMsrp($product)): ?>
        <div class="flex items-center gap-x-[1ch] text-xs leading-normal text-black/60 basis-full old-price">
            <span>
                <?= __('Advise Price') ?>
            </span>
            <span
                id="msrp-product-price-<?= (int)$product->getId() ?>"
                class="price-wrapper title-font"
            >
                <span class="line-through" x-html="getFormattedMsrpPrice()">
                    <?php if ($msrpPrice->isMinimalPriceLessMsrp($product)): ?>
                        <?= /** @noEscape */ $productViewModel->format($lifeProductPriceViewModel->getMsrpPrice($product)) ?>
                    <?php endif; ?>
                </span>
            </span>
        </div>
    <?php endif; ?>
</div>
