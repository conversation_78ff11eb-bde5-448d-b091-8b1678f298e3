<?php

declare(strict_types=1);

use Hyva\Theme\ViewModel\ProductAttributes;
use Hyva\Theme\ViewModel\ProductPage;
use Life\ProductQualityMark\ViewModel\QualityMarks;
use Magento\Catalog\Model\Product;
use Hyva\Theme\ViewModel\SvgIcons;

/** @var \Magento\Framework\View\Element\Template $block */
/** @var \Magento\Framework\Escaper $escaper */
/** @var \Hyva\Theme\Model\ViewModelRegistry $viewModels */

/** @var ProductPage $productViewModel */
$productViewModel = $viewModels->require(ProductPage::class);
/** @var ProductAttributes $attributesViewModel */
$attributesViewModel = $viewModels->require(ProductAttributes::class);
/** @var Product $product */
$product = $productViewModel->getProduct();
/** @var QualityMarks $qualityMarksViewModel */
$qualityMarksViewModel = $viewModels->require(QualityMarks::class);
/** @var SvgIcons $svgIcons */
$svgIcons = $viewModels->require(SvgIcons::class);

$qualityMarks = $qualityMarksViewModel->getQualityMarksData($product);

if (!$qualityMarks) {
    return '';
}

/** @var \Hyva\Theme\ViewModel\Modal $modalViewModel */
$modalViewModel = $viewModels->require(\Hyva\Theme\ViewModel\Modal::class);

?>
<ul class="flex flex-wrap mb-4 gap-x-8 gap-y-4">
    <?php foreach ($qualityMarks as $qualityMark): ?>
        <?php $code = $qualityMark->getData('code'); ?>
        <li x-data="hyva.modal()">
            <button
                @click="show('<?= $escaper->escapeHtmlAttr($code); ?>')"
                aria-label="<?= $escaper->escapeHtml($qualityMark->getData('label')); ?>"
                class="inline-flex items-center gap-2 text-xs"
            >
                <?php if ($image = $qualityMark->getData('image')): ?>
                    <img
                        src="<?= $escaper->escapeUrl($image) ?>"
                        alt=""
                        class="object-cover"
                        width="40"
                        height="40"
                        draggable="false"
                    />
                <?php endif; ?>
                <?= $escaper->escapeHtml($qualityMark->getData('label')); ?>
            </button>

            <?php if ($content = $qualityMark->getData('content')): ?>
                <?= $modalViewModel->createModal()
                    ->withContent(<<<END_OF_CONTENT

<div class="flex justify-between gap-2">
<button @click="hide" type="button" class="flex justify-end items-center text-primary ml-auto min-h-a11y aspect-square top-10 right-10 lg:absolute">
    <span class="sr-only">{$escaper->escapeHtml(__('Close'))}</span>
    {$svgIcons->closeHtml("w-6 h-6")}
</button>
</div>
<div id="{$escaper->escapeHtmlAttr($code)}">{$content}</div>

END_OF_CONTENT
                    )
                    ->withAriaLabelledby($code)
                    ->withDialogRefName($code)
                    ->addContainerClass('!justify-end !items-stretch')
                    ->addDialogClass('info-drawer bg-white w-[90%] md:w-3/4 lg:w-1/2 2xl:w-5/12 h-full relative py-6 px-3 md:px-0 md:py-12 rounded-tl-xl rounded-bl-xl')
                ?>
            <?php endif; ?>
        </li>
    <?php endforeach; ?>
</ul>
