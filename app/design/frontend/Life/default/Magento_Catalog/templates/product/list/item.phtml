<?php

declare(strict_types=1);

use Hyva\Theme\ViewModel\ProductCompare;
use Hyva\Theme\ViewModel\ProductListItem;
use Hyva\Theme\ViewModel\ProductPage;
use Hyva\Theme\ViewModel\Wishlist;
use Life\DisableCheckoutExtensions\Helper\Config as DisableCheckoutConfigHelper;
use Life\ProductsConnector\Setup\Patch\Data\AddProductSubtitleAttribute;
use Life\ProductsConnector\ViewModel\{HoverImage, ProductPrice};
use Life\RemoveCurrencySymbol\ViewModel\Config as RemoveCurrencySymbolConfig;
use Magento\Catalog\Helper\Output as CatalogOutputHelper;
use Magento\Catalog\ViewModel\Product\OptionsData as ProductOptionsData;
use Magento\Framework\View\Element\Template;
use Hyva\Theme\ViewModel\SvgIcons;

/** @var \Magento\Catalog\Block\Product\AbstractProduct $block */
/** @var \Magento\Framework\Escaper $escaper */
/** @var \Hyva\Theme\Model\ViewModelRegistry $viewModels */

/** @var ProductPage $productViewModel */
$productViewModel = $viewModels->require(ProductPage::class);

/** @var ProductListItem $productListItemViewModel */
$productListItemViewModel = $viewModels->require(ProductListItem::class);

/** @var CatalogOutputHelper $catalogOutputHelper */
$catalogOutputHelper = $this->helper(CatalogOutputHelper::class);

/** @var ProductCompare $compareViewModel */
$compareViewModel = $viewModels->require(ProductCompare::class);
/** @var Wishlist $wishlistViewModel */
$wishlistViewModel = $viewModels->require(Wishlist::class);

/** @var SvgIcons $svgIcons */
$svgIcons = $viewModels->require(SvgIcons::class);

$productOptionsViewmodel = $viewModels->require(ProductOptionsData::class);

/** @var DisableCheckoutConfigHelper $disableCheckoutConfigHelper */
$disableCheckoutConfigHelper = $this->helper(DisableCheckoutConfigHelper::class);
$disableCheckout = $disableCheckoutConfigHelper->getConfig()->getDisableCheckout();

/** @var Magento\Catalog\Model\Product $product */
$product = $block->getData('product');
$imageDisplayArea = $block->getData('image_display_area');
$templateType = $block->getData('template_type');
$viewMode = $block->getData('view_mode');
$showDescription = $block->getData('show_description');
$showAddToWishlist = $wishlistViewModel->isEnabled() && !$disableCheckout;
$showAddToCompare = $compareViewModel->showInProductList();
$viewIsGrid = $viewMode === 'grid';

if (!$product) {
    return '';
}
$productId = $product->getId();
$options   = $productOptionsViewmodel->getOptionsData($product);
$uniqueId = '_' . uniqid();

$hideDetails = $block->getData('hide_details') ?: false;
$hideRatingSummary = $block->getData('hide_rating_summary') ?: false;

$imageCustomAttributes = $product->getData('image_custom_attributes')
    ?? $block->getData('image_custom_attributes')
    ?? [];
$productName = $catalogOutputHelper->productAttribute($product, $product->getName(), 'name');

/** @var ProductPrice $productPriceViewModel */
$productPriceViewModel = $viewModels->require(ProductPrice::class);

/** @var RemoveCurrencySymbolConfig $removeCurrencySymbolConfig */
$removeCurrencySymbolConfig = $viewModels->require(RemoveCurrencySymbolConfig::class);

$discount = $productPriceViewModel->getDiscountPercentage($product);

/** @var HoverImage $hoverImage */
$hoverImage = $viewModels->require(HoverImage::class);
$hoverImageId = 'category_page_grid_hover';
$hoverImageUrl = $hoverImage->getImageUrl($product, $hoverImageId);
$uniqueId = '_' . uniqid();

$defaultProduct = $productPriceViewModel->getDefaultProduct($product) ?? $product;
?>

<?php if ($product->isSaleable()) : ?>
    <form
        method="post"
        action="<?= $escaper->escapeUrl($productViewModel->getAddToCartUrl($product, ['useUencPlaceholder' => true])) ?>"
        <?php if ($block->getData('item_relation_type') !== 'crosssell') : ?>
        x-data
        @submit.prevent="$dispatch('ajax-add-to-cart')"
        <?php endif; ?>
        data-sku="<?= $escaper->escapeHtmlAttr($product->getSku()); ?>"
        class="bg-white border border-secondary-900/50 rounded-xl item product product-item product_addtocart_form relative flex flex-col w-full h-full overflow-hidden <?= $viewIsGrid ? '' : 'md:flex-row' ?>"
        <?php if ($product->getOptions()) : ?>
        enctype="multipart/form-data"
        <?php endif; ?>
    >
        <?=
        /** @noEscape */
        $block->getBlockHtml('formkey') ?>
        <input
            type="hidden"
            name="product"
            value="<?= (int)$productId ?>"
        />
        <?php foreach ($options as $optionItem) : ?>
            <input
                type="hidden"
                name="<?= $escaper->escapeHtml($optionItem['name']) ?>"
                value="<?= $escaper->escapeHtml($optionItem['value']) ?>"
            >
        <?php endforeach; ?>
    <?php else : ?>
        <div
            class="bg-white border border-secondary-900/50 rounded-xl item product product-item flex flex-col w-full h-full <?= $viewIsGrid ? '' : 'md:flex-row' ?>">
        <?php endif; ?>
        <div class="relative flex justify-center">
            <?php if ($showAddToWishlist) : ?>
                <button
                    x-data="initWishlist()"
                    @click.prevent="addToWishlist(<?= (int)$productId ?>)"
                    aria-label="<?= $escaper->escapeHtmlAttr(__('Add to Wish List') . ' ' . $productName) ?>"
                    type="button"
                    class="absolute z-10 inline-flex items-center justify-center w-8 h-8 text-white transition rounded-full right-3 top-3 shrink-0 bg-secondary-900 md:hover:bg-white md:hover:shadow-md md:hover:text-primary"
                >
                    <?= $svgIcons->wishlistHtml('p-1.5', 32, 32) ?>
                </button>
            <?php endif; ?>
            <?php /* Product Gallery */ ?>
            <?=
            /* @phpstan-ignore-next-line */
            $block->getLayout()
                ->createBlock(Template::class, 'product.gallery' . $product->getId() . $uniqueId)
                ->setProduct($product)
                ->setTemplate('Magento_Catalog::product/list/gallery.phtml')
                ->toHtml();
            ?>

            <?php if (!$disableCheckout) : ?>
                <div class="absolute flex flex-wrap left-3 bottom-3">
                    <?php if (!$product->isSaleable()) : ?>
                        <div class="w-auto text-sm">
                            <?= $block->getChildBlock('stockstatus')->setData('product', $product)->toHtml() ?>
                        </div>
                    <?php endif; ?>
                </div>
            <?php endif; ?>

            <div class="absolute z-10 inset-x-4 bottom-4 max-md:hidden">
                <span class="w-6/12 pr-3 text-xs text-secondary-900"><?= $escaper->escapeHtml($product->getData(AddProductSubtitleAttribute::ATTRIBUTE_CODE)) ?></span>
                <?=
                /* @phpstan-ignore-next-line */
                $block->getLayout()
                    ->createBlock(Template::class, 'product.colors' . $product->getId() . $uniqueId)
                    ->setProduct($product)
                    ->setTemplate('Magento_Catalog::product/list/colors.phtml')
                    ->toHtml();
                ?>
            </div>

            <?php if ($product->isAvailable() && !$hideDetails) : ?>
                <?= $block->getProductDetailsHtml($product) ?>
            <?php endif; ?>
        </div>

        <div class="flex border-t product-info grow border-secondary-900/50">
            <div class="flex flex-col w-full p-4">
                <div class="flex items-start justify-between flex-grow gap-2">
                    <?php $productNameStripped = $block->stripTags($product->getName(), null, true); ?>
                    <div class="items-center text-primary font-semibold text-base md:text-md <?= $viewIsGrid ? '' : 'md:text-left' ?>">
                        <a
                            class="product-item-link line-clamp-2"
                            href="<?= $escaper->escapeUrl($product->getProductUrl()) ?>"
                            :id="`slide-desc-<?= $escaper->escapeHtmlAttr($product->getId()) ?>-${$id('slider-id')}`"
                            data-link-product-id="<?= $escaper->escapeHtmlAttr($product->getId()) ?>"
                        >
                            <?= /* @noEscape */ $productName ?>
                        </a>
                    </div>

                    <?php if (!$disableCheckout) : ?>
                        <div
                            class="flex justify-end text-base text-gray-900 md:text-md"
                            data-price-wrapper="product-id-<?= $product->getId() ?>"
                        >
                            <?php
                            /**
                             * This can appear in cart (checkout route) where currency should appear, so an extra
                             * currency replacement is needed for item price
                             */
                            ?>
                            <?= /* @noEscape */ $removeCurrencySymbolConfig->replaceCurrencySymbolInContent(
                                $productListItemViewModel->getProductPriceHtml($product)
                            ) ?>
                        </div>
                    <?php endif; ?>
                </div>

                <div class="flex justify-end md:hidden">
                    <span class="w-6/12 pr-3 text-xs text-secondary-900">
                        <?= $escaper->escapeHtml($product->getData(AddProductSubtitleAttribute::ATTRIBUTE_CODE)) ?>
                    </span>
                    <?=
                    /* @phpstan-ignore-next-line */
                    $block->getLayout()
                        ->createBlock(Template::class, 'product.colors.mobile' . $product->getId() . $uniqueId)
                        ->setProduct($product)
                        ->setTemplate('Magento_Catalog::product/list/colors.phtml')
                        ->toHtml();
                    ?>
                </div>

                <?php if ($block->getData('item_relation_type') === 'crosssell') : ?>
                    <div class="flex flex-wrap mt-4">
                        <button
                            type="submit"
                            class="justify-center w-full text-base font-semibold btn btn-primary sm:w-auto"
                        >
                            <span class="sm:ml-2 sm:inline">
                                <?= $escaper->escapeHtml(__('Add to cart')) ?>
                            </span>

                            <?= $svgIcons->addToCartHtml('ml-2 text-white', 24, 24); ?>
                        </button>
                    </div>
                <?php endif; ?>
            </div>
        </div>
        <?php if ($product->isSaleable()) : ?>
    </form>
<?php else : ?>
    </div>
<?php endif; ?>
