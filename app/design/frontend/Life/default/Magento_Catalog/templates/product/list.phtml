<?php

declare(strict_types=1);

use Hyva\Theme\ViewModel\CurrentCategory;
use Hyva\Theme\ViewModel\ProductListItem;
use Magento\Framework\View\Element\Template;

/** @var \Magento\Catalog\Block\Product\ListProduct $block */
/** @var \Magento\Framework\Escaper $escaper */
/** @var \Hyva\Theme\Model\ViewModelRegistry $viewModels */

/** @var ProductListItem $productListItemViewModel */
$productListItemViewModel = $viewModels->require(ProductListItem::class);

/** @var CurrentCategory $currentCategoryViewModel */
$currentCategoryViewModel = $viewModels->require(CurrentCategory::class);

$eagerLoadImagesCount = (int) ($block->getData('eager_load_images_count') ?? 3);
$productCollection = $block->getLoadedProductCollection();
$productCollection->addMediaGalleryData();

$categoryCards = [];
$layer = $block->getLayer();

// Show cards when there are more than 5 products
if (
    ($productCollection->count() > 5)
    && ($category = $layer->getCurrentCategory())
) {
    $categoryCards = $category->getData('signposting_cards') ?? [];
    if (is_string($categoryCards)) {
        $categoryCards = [];
    }
}
?>
<?php if (!$productCollection->count()) : ?>
    <div class="container">
        <div class="message info empty">
            <div>
                <?= $escaper->escapeHtml(__('We can\'t find products matching the selection.')) ?>
            </div>
        </div>
    </div>
<?php else : ?>
    <section
        class="container pb-8"
        id="product-list"
        aria-label="<?= $escaper->escapeHtmlAttr(__('Product list')) ?>"
        tabindex="-1"
    >
        <?= $block->getChildHtml('catalog.leftnav'); // Category filters
        ?>
        <?= $block->getChildHtml('catalogsearch.leftnav'); // Search filters
        ?>
        <?= $block->getAdditionalHtml() ?>
        <?php
        if ($block->getMode() == 'grid') {
            $viewMode         = 'grid';
            $imageDisplayArea = 'category_page_grid';
            $showDescription  = false;
            $templateType     = \Magento\Catalog\Block\Product\ReviewRendererInterface::SHORT_VIEW;
        } else {
            $viewMode         = 'list';
            $imageDisplayArea = 'category_page_list';
            $showDescription  = true;
            $templateType     = \Magento\Catalog\Block\Product\ReviewRendererInterface::FULL_VIEW;
        }
        /**
         * Position for actions regarding image size changing in vde if needed
         */
        $pos = $block->getPositioned();
        ?>
        <div
            class="relative products wrapper mode-<?= /* @noEscape */ $viewMode ?> products-<?= /* @noEscape */ $viewMode ?>"
            x-data=""
            x-intersect.margin.-50%="$dispatch('products-visible', { visible: true })"
            x-intersect:leave.margin.-50%="$dispatch('products-visible', { visible: false })"
        >
            <?= $block->fetchView((string) $block->getTemplateFile('Hyva_Theme::ui/products-loading.phtml')) ?>
            <div class="mx-auto pb-12 grid gap-4 grid-cols-1
                <?= /* @noEscape */ $viewMode === 'grid'
                    ? 'sm:grid-cols-2 xl:grid-cols-3 3xl:grid-cols-4'
                    : '' ?>">
                <?php
                /** @var \Magento\Catalog\Model\Product $product */
                $position = 1;
                foreach (array_values($productCollection->getItems()) as $i => $product) {
                    if ($i < $eagerLoadImagesCount) {
                        $product->setData('image_custom_attributes', ['loading' => 'eager', 'fetchpriority' => 'high']);
                    }
                ?>
                    <div
                        class="relative product-item-wrapper"
                        style="order: <?= (int)($position++ * 10); ?>"
                    >
                        <?= $productListItemViewModel->getItemHtml(
                            $product,
                            $block,
                            $viewMode,
                            $templateType,
                            $imageDisplayArea,
                            $showDescription
                        );
                        ?>
                    </div>
                <?php } ?>

                <?php $cardNumber = 0; ?>
                <?php foreach ($categoryCards as $cardPosition => $card) : ?>
                    <?php $uniqueId = '_' . uniqid(); ?>
                    <?=
                    /* @phpstan-ignore-next-line */
                    $block->getLayout()
                        ->createBlock(Template::class, 'category.card.' . $uniqueId)
                        ->setCurrentCard($card)
                        /**
                         * As example: Card 1: pos 3, Card 2: pos 9, Card 3: pos 12
                         * For 2nd card with position 9, position should be 8, because previous pos 3 card was already inserted.
                         */
                        ->setPosition($cardPosition - $cardNumber + 1)
                        ->setTemplate('Life_CategoryCards::category-card.phtml')
                        ->toHtml();
                    ?>
                <?php endforeach; ?>

                <?= $block->fetchView($block->getTemplateFile('Life_CategoryUsp::category-usps.phtml')) ?>
            </div>
            <?= $block->getChildHtml('product.list.active.filters') ?>
        </div>
        <?= $block->getChildBlock('toolbar')->setIsBottom(true)->toHtml() ?>
    </section>

    <?= $block->fetchView($block->getTemplateFile('Hyva_SmileElasticsuite::catalog/layer/filter/js/attribute-filter-js.phtml')) ?>
    <?= $block->fetchView($block->getTemplateFile('Hyva_SmileElasticsuite::catalog/layer/filter/js/slider-filter-js.phtml')) ?>

    <script defer="defer">
        function initLayeredSwatch() {
            return {
                getSwatchType(typeNumber) {
                    switch (typeNumber) {
                        case "1":
                            return "color";
                        case "2":
                            return "image";
                        default:
                            return "text";
                    }
                },
                getSwatchBackgroundStyle(type, value, image) {
                    if (this.getSwatchType(type) === "color") {
                        return 'background-color:' + value;
                    } else if (this.getSwatchType(type) === "image") {
                        return "background: #ffffff url('" + image + "') no-repeat center";
                    } else {
                        return '';
                    }
                },
                activeTooltipItem: false,
                tooltipPositionElement: false,
                isVisualSwatch() {
                    return this.getSwatchType(this.activeTooltipItem.type) !== 'text'
                }
            }
        }

    </script>

    <script defer="defer">
        function initToolbar(options) {
            return {
                options: options.productListToolbarForm || {},
                getUrlParams: function () {
                    let decode = window.decodeURIComponent,
                        urlPaths = this.options.url.split('?'),
                        urlParams = urlPaths[1] ? urlPaths[1].split('&') : [],
                        params = {},
                        parameters, i;

                    for (i = 0; i < urlParams.length; i++) {
                        parameters = urlParams[i].split('=');
                        params[decode(parameters[0])] = parameters[1] !== undefined ?
                            decode(parameters[1].replace(/\+/g, '%20')) :
                            '';
                    }

                    return params;
                },
                getCurrentLimit: function () {
                    return this.getUrlParams()[this.options.limit] || this.options.limitDefault;
                },
                getCurrentPage: function () {
                    return this.getUrlParams()[this.options.page] || 1;
                },
                changeUrl(paramName, paramValue, defaultValue, sorting = '') {
                    let urlPaths = this.options.url.split('?'),
                        baseUrl = urlPaths[0],
                        paramData = this.getUrlParams(),
                        currentPage = this.getCurrentPage(),
                        newPage;

                    /**
                     * calculates the page on which the first item of the current page will
                     * be with the new limit and sets that number as the new page
                     */
                    if (currentPage > 1 && paramName === this.options.limit) {
                        newPage = Math.floor(this.getCurrentLimit() * (currentPage - 1) / paramValue) + 1;

                        if (newPage > 1) {
                            paramData[this.options.page] = newPage;
                        } else {
                            delete paramData[this.options.page];
                        }
                    }

                    paramData[paramName] = paramValue;

                    if (sorting) {
                        paramData['product_list_dir'] = sorting;
                    }

                    if (this.options.post) {
                        hyva.postForm({
                            action: baseUrl,
                            data: paramData,
                            skipUenc: true
                        });
                    } else {
                        if (paramValue === defaultValue.toString()) {
                            delete paramData[paramName];
                            if (sorting) {
                                delete paramData['product_list_dir'];
                            }
                        }
                        paramData = Object.keys(paramData).length === 0 ?
                            '' :
                            '?' + (new URLSearchParams(paramData));
                        location.href = baseUrl + paramData
                    }
                }
            }
        }

    </script>
<?php endif; ?>
