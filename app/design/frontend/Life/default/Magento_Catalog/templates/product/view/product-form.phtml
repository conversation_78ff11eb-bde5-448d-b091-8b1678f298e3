<?php

declare(strict_types=1);

/** @var \Magento\Catalog\Block\Product\View $block */
/** @var \Magento\Framework\Escaper $escaper */

/** @var \Magento\Catalog\Model\Product $product */
$product = $block->getProduct();

/**
 * Add to cart url is set on 'product.info' block by
 * @see \Magento\Checkout\Block\Cart\Item\Configure::_prepareLayout
 */
$productInfoBlock = $block->getLayout()->getBlock('product.info');

$formContent = $block->getChildHtml();
?>

<?php if ($product->isSaleable()): ?>
    <form
        method="post"
        action="<?= $escaper->escapeUrl($productInfoBlock->getSubmitUrl($product)) ?>"
        x-data="{...validationForAjaxAddToCart($el), ...hyva.formValidation($el)}"
        @submit.prevent.stop="validateAndSubmit"
        data-sku="<?= $escaper->escapeHtmlAttr($product->getSku()); ?>"
        id="product_addtocart_form"
        <?php if ($product->getOptions()): ?> enctype="multipart/form-data"<?php endif; ?>
    >
        <input type="hidden" name="product" value="<?= (int)$product->getId() ?>" />
        <input type="hidden" name="selected_configurable_option" value=""  />
        <input type="hidden" name="related_product" id="related-products-field" value="" />
        <input type="hidden" name="item"  value="<?= (int)$block->getRequest()->getParam('id') ?>">
        <?= $block->getBlockHtml('formkey') ?>

        <?= $formContent ?>
    </form>
<?php endif; ?>
<script defer="defer">
    function validationForAjaxAddToCart(formElement) {
        return {
            validateAndSubmit() {
                new Promise(() => this.validate().then(() => {
                    window.dispatchEvent(
                        new CustomEvent("ajax-add-to-cart", {detail: {target: formElement}})
                    );
                }).catch(() => {}))
            }
        }
    }
</script>
