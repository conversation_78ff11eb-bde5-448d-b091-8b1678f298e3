<?php

declare(strict_types=1);

use Hyva\Theme\ViewModel\SvgIcons;

/** @var \Magento\Catalog\Block\Product\ProductList\Toolbar $block */
/** @var \Magento\Framework\Escaper $escaper */
/** @var \Hyva\Theme\Model\ViewModelRegistry $viewModels */

/** @var SvgIcons $svgIcons */
$svgIcons = $viewModels->require(SvgIcons::class);

// Make sure that for search page, the 'relevance' is always the top option
$availableOrders = $block->getAvailableOrders();
if (isset($availableOrders['relevance'])) {
    $relevance = $availableOrders['relevance'];
    unset($availableOrders['relevance']);
    $availableOrders = array_merge(['relevance' => $relevance], $availableOrders);
}
?>
<div class="flex items-center justify-end order-1 col-span-3 toolbar-sorter sorter sm:col-span-6 md:col-span-3 lg:col-span-6">
    <div class="relative">
        <select
            data-role="sorter"
            class="!pr-12 form-select sorter-options max-sm:w-48"
            aria-label="<?= $escaper->escapeHtml(__('Sort By')) ?>"
            @change="changeUrl(
                'product_list_order',
                $event.currentTarget.options[$event.currentTarget.selectedIndex].value,
                options.orderDefault,
                $event.currentTarget.options[$event.currentTarget.selectedIndex].dataset.sort,
            )"
        >
            <?php foreach ($availableOrders as $orderCode => $orderLabel): ?>
                <?php if ($orderCode === 'price'): ?>
                    <option
                        value="<?= $escaper->escapeHtmlAttr($orderCode) ?>"
                        data-sort="desc"
                    <?php if ($block->isOrderCurrent($orderCode) && ($block->getCurrentDirection() === 'desc')): ?>
                        selected="selected"
                    <?php endif; ?>
                    >
                        <?= $escaper->escapeHtml(__($orderLabel)) ?> (<?= $escaper->escapeHtml(__('high to low')); ?>)
                    </option>
                    <option
                        value="<?= $escaper->escapeHtmlAttr($orderCode) ?>"
                        data-sort="asc"
                    <?php if ($block->isOrderCurrent($orderCode) && ($block->getCurrentDirection() === 'asc')): ?>
                        selected="selected"
                    <?php endif; ?>
                    >
                        <?= $escaper->escapeHtml(__($orderLabel)) ?> (<?= $escaper->escapeHtml(__('low to high')); ?>)
                    </option>
                <?php else: ?>
                    <option
                        value="<?= $escaper->escapeHtmlAttr($orderCode) ?>"
                        data-sort="<?= ($orderCode === 'views') ? 'desc' : 'asc'; ?>"
                    <?php if ($block->isOrderCurrent($orderCode)): ?>
                        selected="selected"
                    <?php endif; ?>
                    >
                        <?= $escaper->escapeHtml(__($orderLabel)) ?>
                    </option>
                <?php endif; ?>
            <?php endforeach; ?>
        </select>
        <?= $svgIcons->arrowDownHtml("w-6 h-6 text-black pointer-events-none absolute right-5 top-3") ?>
    </div>
</div>
