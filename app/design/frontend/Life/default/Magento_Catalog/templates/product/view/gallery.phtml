<?php

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\SvgIcons;
use Magento\Catalog\Block\Product\View\Gallery;
use Magento\Catalog\Helper\Image;
use Magento\Framework\Escaper;

/** @var Escaper $escaper */
/** @var Gallery $block */
/** @var ViewModelRegistry $viewModels */

/** @var SvgIcons $svgIcons */
$svgIcons = $viewModels->require(SvgIcons::class);

$images = $block->getGalleryImages()->getItems();
$mainImage = current(array_filter($images, function ($img) use ($block) {
    return $block->isMainImage($img);
}));

if (!empty($images) && empty($mainImage)) {
    $mainImage = $block->getGalleryImages()->getFirstItem();
}

/** @var Image $helper */
$helper = $block->getData('imageHelper');
$mainImageData = $mainImage ?
    $mainImage->getData('medium_image_url') :
    $helper->getDefaultPlaceholderUrl('image');

$smallWidth = $block->getImageAttribute('product_page_image_small', 'width', '120');
$smallHeight = $block->getImageAttribute('product_page_image_small', 'height', '120');
$mediumWidth = $block->getImageAttribute('product_page_image_medium', 'width', '450');
$mediumHeight = $block->getImageAttribute('product_page_image_medium', 'height', '450');

$productName = $block->getProduct()->getName();

$iconBtnCls = 'inline-flex w-[2.75rem] aspect-square !p-0 items-center justify-center rounded-full border border-current text-primary bg-white';

?>

<div
    id="gallery"
    x-data="initGallery()"
    x-init="setActive(0)"
    x-bind="eventListeners"
    :class="{
        'w-full h-full fixed top-0 left-0 bg-white z-50 flex': fullscreen,
        'col-span-7 md:h-auto flex flex-col px-4 sm:px-0': !fullscreen
    }"
    @resize.window.debounce="calcPageSize()"
>
    <div
        class="md:sticky md:top-36"
        :class="{ 'w-full' : fullscreen }"
    >
        <div
            class="absolute top-0 right-0 z-30 items-center pt-4 pr-4 touch-none"
            :class="{ 'hidden': !fullscreen, 'flex': fullscreen }"
        >
            <button
                @click="toggleFullscreen(false)"
                type="button"
                class="<?= $iconBtnCls ?>"
                aria-label="<?= $escaper->escapeHtml(__('Close')) ?>"
            >
                <svg class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path
                        stroke-linecap="round" stroke-linejoin="round"
                        stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                </svg>
            </button>
        </div>
        <div
            class="relative flex w-full"
            :class="{ 'h-full' : fullscreen }"
            x-transition:enter="ease-out duration-500"
            x-transition:enter-start="opacity-0"
            x-transition:enter-end="opacity-100"
        >
            <div
                class="flex flex-no-wrap flex-1 w-full overflow-x-scroll overflow-y-hidden snap snap-x snap-center snap-mandatory gallery-snap"
                x-ref="gallery"
                @scroll.debounce="calcActive()"
            >
                <template x-for="(image, index) in images" :key="index">
                    <div
                        class="relative z-10 flex items-center flex-shrink-0 object-contain object-center w-full gallery-image snap-center touch-none touch-pan-y touch-pan-x"
                        :class="{
                            'pb-28' : fullscreen,
                            'bg-white rounded-xl overflow-clip' : !fullscreen,
                            'overflow-auto' : zoom,
                            'overflow-hidden' : !zoom
                        }"
                    >
                        <img
                            :alt="image.caption || '<?= $escaper->escapeJs($productName) ?>'"
                            :title="image.caption || '<?= $escaper->escapeJs($productName) ?>'"
                            class="z-10 flex-shrink-0 object-contain object-center w-full max-h-full gallery-image rounded-xl"
                            :class="{
                                'cursor-pointer aspect-[3/2]': !fullscreen,
                                'h-auto w-auto mx-auto': fullscreen,
                                'absolute inset-0': (images[index]?.type === 'video'),
                                '!max-w-none !max-h-[none]' : zoom && images[index]?.type != 'video',
                                'cursor-auto' : fullscreen && !zoomable,
                                'cursor-pointer' : fullscreen && zoomable && !zoom,
                                'cursor-grab' : !dragging && zoom,
                                'cursor-grabbing' : dragging && zoom
                            }"
                            loading="lazy"
                            draggable="false"
                            :src="fullscreen ? image.full : image.img"
                            role="button"
                            @click="handleImgClick()"
                            :data-width="image.width"
                            :data-height="image.height"
                        />
                        <div
                            class="relative z-20 hidden w-full h-full bg-white nonmobile"
                            :class="{ 'cursor-pointer max-h-screen-75': !fullscreen, 'max-h-screen-70 pt-8': fullscreen, 'hidden': activeVideoType !== 'youtube' }"
                        >
                            <div :id="'youtube-player-'+index" class="w-full h-full"></div>
                        </div>
                        <div
                            class="relative z-20 hidden w-full h-full bg-white"
                            :class="{ 'cursor-pointer max-h-screen-75': !fullscreen, 'max-h-screen-70 pt-8': fullscreen, 'hidden': activeVideoType !== 'vimeo' }"
                        >
                            <div :id="'vimeo-player-'+index" class="w-full h-full"></div>
                        </div>
                    </div>
                </template>
            </div>
        </div>

        <menu
            class="flex items-center gap-2 absolute bottom-[7rem] left-1/2 -translate-x-1/2 z-30 touch-none transition-opacity duration-300"
            :class="{ 'opacity-0 pointer-events-none': !fullscreen || !zoomable }"
        >
            <li>
                <button
                    @click="updateZoom(false)"
                    class="<?= $iconBtnCls ?>"
                    :class="{ 'opacity-50 pointer-events-none cursor-not-allowed' : !zoom }"
                >
                    <span class="sr-only"><?= $escaper->escapeHtml(__('Zoom out')) ?></span>
                    <?= $svgIcons->zoomOutHtml("w-5 h-5", 20, 20, ['aria-hidden' => 'true']) ?>
                </button>
            </li>
            <li>
                <button
                    @click="updateZoom(true)"
                    class="<?= $iconBtnCls ?>"
                    :class="{ 'opacity-50 pointer-events-none cursor-not-allowed' : zoom }"
                >
                    <span class="sr-only"><?= $escaper->escapeHtml(__('Zoom in')) ?></span>
                    <?= $svgIcons->zoomInHtml("w-5 h-5", 20, 20, ['aria-hidden' => 'true']) ?>
                </button>
            </li>
        </menu>

        <div
            id="thumbs"
            class="z-30 flex items-center mx-auto my-4 overflow-auto transition-all lg:mb-0 place-content-evenly"
            :class="{ 'fixed justify-center bottom-0 left-0 right-0 mx-6 mb-3 min-h-[6.25rem]': fullscreen, 'max-w-[43rem]': !fullscreen}"
            x-show="images.length > 1"
            x-cloak
        >
            <button
                class="mx-4 rounded-full outline-none focus:outline-none"
                :class="{
                    'opacity-20': firstSlideActive(),
                    'opacity-100': !firstSlideActive(),
                    'fixed top-1/2 left-4 bg-white lg:block' : fullscreen
                }"
                x-show="showNavigation()"
                @click="slide('left', 1)"
                :disabled="firstSlideActive()"
            >
                <span class="sr-only"><?= __('Previous slide') ?></span>
                <?= $svgIcons->arrowCircleLeftHtml("w-12 h-12") ?>
            </button>
            <div
                class="flex flex-no-wrap overflow-x-scroll overflow-y-hidden gap-3 lg:overflow-hidden lg:pb-0"
                x-ref="thumbnails"
            >
                <template x-for="(image, index) in images" :key="index" hidden>
                    <div class="flex-none w-16 aspect-square relative md:w-[4.5rem]">
                        <button
                            @click.prevent="setActive(index)"
                            class="absolute inset-0 rounded-xl overflow-clip"
                            :class="{'border border-primary': active === index}"
                        >
                            <img
                                :src="image.thumb"
                                alt="product thumbnail"
                                class="absolute inset-0 object-cover h-full"
                            />
                        </button>
                    </div>
                </template>
            </div>
            <button
                class="mx-4 rounded-full outline-none focus:outline-none"
                :class="{
                    'opacity-20': lastSlideActive(),
                    'opacity-100': !lastSlideActive(),
                    'fixed top-1/2 right-4 bg-white lg:block' : fullscreen
                }"
                x-show="showNavigation()"
                @click="slide('right', 1)"
                :disabled="lastSlideActive()"
            >
                <span class="sr-only"><?= __('Next slide') ?></span>
                <?= $svgIcons->arrowCircleRightHtml("w-12 h-12") ?>
            </button>
        </div>
    </div>
</div>
<script defer="defer">
    function initGallery() {
        return {
            "active": 0,
            "activeStep": 0,
            "activeStepInit": null,
            "fullscreen": false,
            "hideAllControls": true,
            "initialImages": <?= /* @noEscape */ $block->getGalleryImagesJson() ?>,
            "images": <?= /* @noEscape */ $block->getGalleryImagesJson() ?>,
            "appendOnReceiveImages": <?= $block->getVar(
                'gallery_switch_strategy',
                'Magento_ConfigurableProduct'
            ) === 'append' ? 'true' : 'false' ?>,
            "zoomable": false,
            "zoom": false,
            "dragging": false,
            receiveImages(images) {
                let self = this;

                if (this.appendOnReceiveImages) {
                    this.images = [].concat(this.initialImages, images);
                } else {
                    this.images = images;
                }
                this.calcPageSize();

                // Let some time for images to be rendered
                setTimeout(() => {
                    window.dispatchEvent(new CustomEvent("gallery-updated", {}));
                }, 300);
            },
            initActive() {
                let active = this.images.findIndex(function(image) {
                    return image.isMain === true
                });
                if (active === -1) {
                    active = 0;
                }
                this.setActive(active);
            },
            setActive(index) {
                if (index === this.activeStepInit) {
                    return;
                }
                this.activeStepInit = index;

                var pixelsToScroll = (index - this.active) * this.getGallerySlider().clientWidth;
                this.getGallerySlider().scrollBy({
                    left: pixelsToScroll,
                    behavior: "smooth"
                });

                this.active = index;
                let activeThumb = document.querySelectorAll('#thumbs img')[this.active];
                if (activeThumb) {
                    activeThumb.scrollIntoView({ behavior: "smooth", block: 'nearest'})
                }
                if (window.youtubePlayer) {
                    window.youtubePlayer.stopVideo();
                }
                if (this.vimeoPlayer) {
                    this.vimeoPlayer.contentWindow.postMessage(JSON.stringify({
                        "method": "pause"
                    }), "*");
                }
                if (this.images[index]?.type === 'video') {
                    this.activateVideo();
                } else {
                    this.activeVideoType = false;
                }
                this.checkZoom();
            },
            eventListeners: {
                ['@keydown.window.escape']() {
                    this.toggleFullscreen(false)
                },
                ['@update-gallery.window'](event) {
                    this.receiveImages(event.detail);
                }
            },
            slide(direction, steps) {
                const thumbnailBaseElement = this.$refs.thumbnails.getElementsByTagName('div')[0];
                if (!thumbnailBaseElement) {
                    return;
                }

                const galleryBaseElement = this.getGallerySlider().querySelector('.gallery-image');
                if (!galleryBaseElement) {
                    return;
                }

                if (direction === 'left' && this.active > 0) {
                    this.setActive(this.active - steps);
                } else if (direction === 'right' && this.active + 1 < this.images.length) {
                    this.setActive(this.active + steps);
                }

                const thumbnailPixelsToScroll = direction === 'left' ?
                    -thumbnailBaseElement.scrollWidth * steps :
                    thumbnailBaseElement.scrollWidth * steps;
                this.$refs.thumbnails.scrollBy({
                    left: thumbnailPixelsToScroll,
                    behavior: "smooth"
                })
            },
            handleImgClick() {

                // enable fullscreen
                if (!this.fullscreen) {
                    this.toggleFullscreen(true);
                    return;
                }

                // toggle zoom
                this.zoom ?  this.updateZoom(false) : this.updateZoom(true);
            },
            toggleFullscreen(bool) {
                const doc = document.documentElement;
                this.fullscreen = bool;
                this.fullscreen ? doc.classList.add('overflow-hidden') : doc.classList.remove('overflow-hidden');

                let self = this;
                setTimeout(function() {
                    self.getGallerySlider().scrollTo(self.active * self.getGallerySlider().clientWidth, 0);
                    self.checkZoom();
                }, 50);
            },
            getGallerySlider() {
                return this.$refs.gallery;
            },
            pageSize: 1,
            // calculate size of each gallery item
            calcPageSize() {
                var slider = this.getGallerySlider();
                if (slider) {
                    this.pageSize = Math.round(slider.clientWidth / slider.querySelector('.gallery-image').clientWidth);
                }
            },
            // calculate current visible item
            calcActive(event) {
                var slider = this.getGallerySlider();
                if (slider) {
                    this.active = Math.ceil(
                        Math.round(
                            slider.scrollLeft / (slider.scrollWidth / this.images.length)
                        ) / this.pageSize
                    ) * this.pageSize;
                    this.setActive(this.active);
                }
            },
            showNavigation() {
                return this.images.length > 3;
            },
            firstSlideActive() {
                if (this.active === 0) {
                    return true;
                }
                return false;
            },
            lastSlideActive() {
                if (this.active === this.images.length - 1) {
                    return true;
                }
                return false;
            },
            checkZoom() {
                this.zoom = false;
                this.zoomable = false;
                const slideContainer = this.getGallerySlider().querySelector(`.gallery-image:nth-child(${this.active + 2})`);
                const img = this.getGallerySlider().querySelector(`.gallery-image:nth-child(${this.active + 2}) > img`);

                if (!slideContainer) return;

                // get sizes
                const sliderSize = [slideContainer.offsetWidth, slideContainer.offsetHeight];
                const naturalImgSize = [img.dataset.width, img.dataset.height];

                // if the original image size is larger then the slide
                if (naturalImgSize[0] > sliderSize[0] || naturalImgSize[1] > sliderSize[1]) {
                    this.zoomable = true;
                }

                // enable mouse drag scroll
                let mouseDown = false;
                let startX, scrollLeft;
                let startY, scrollTop;

                const startDragging = (e) => {
                    this.dragging = true;
                    mouseDown = true;
                    startX = e.pageX - slideContainer.offsetLeft;
                    startY = e.pageY - slideContainer.offsetTop;
                    scrollLeft = slideContainer.scrollLeft;
                    scrollTop = slideContainer.scrollTop;
                }

                const stopDragging = (e) => {
                    this.dragging = false;
                    mouseDown = false;
                }

                const move = (e) => {
                    e.preventDefault();
                    if (!mouseDown) return;
                    const x = e.pageX - slideContainer.offsetLeft;
                    const y = e.pageY - slideContainer.offsetTop;
                    const scrollX = x - startX;
                    const scrollY = y - startY;
                    slideContainer.scrollLeft = scrollLeft - scrollX;
                    slideContainer.scrollTop = scrollTop - scrollY;
                }

                // Add the event listeners
                slideContainer.addEventListener('mousemove', move, false);
                slideContainer.addEventListener('mousedown', startDragging, false);
                slideContainer.addEventListener('mouseup', stopDragging, false);
                slideContainer.addEventListener('mouseleave', stopDragging, false);

            },
            updateZoom(bool) {
                const slide = this.getGallerySlider().querySelector(`.gallery-image:nth-child(${this.active + 2})`);
                this.zoom = bool;

                if (this.zoom) {
                    setTimeout(function() {
                        const slideSize = [slide.offsetWidth, slide.offsetHeight];
                        const slideScrollSize = [slide.scrollWidth, slide.scrollHeight];
                        slide.scrollTo((slideScrollSize[0] - slideSize[0]) / 2, (slideScrollSize[1] - slideSize[1]) / 2)
                    }, 5);
                } else {
                    slide.scrollTo(0, 0);
                }
            }
        }
    }
</script>
