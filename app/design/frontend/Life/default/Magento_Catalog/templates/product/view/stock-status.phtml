<?php

declare(strict_types=1);

use Hyva\Theme\ViewModel\CurrentProduct;
use Magento\Catalog\Model\Product;

/** @var \Magento\Framework\View\Element\Template $block */
/** @var \Magento\Framework\Escaper $escaper */
/** @var \Hyva\Theme\Model\ViewModelRegistry $viewModels */

/** @var CurrentProduct $currentProduct */
$currentProduct = $viewModels->require(CurrentProduct::class);

/** @var Product $product */
$product = $block->hasData('product')
    ? $block->getData('product')
    : $currentProduct->get();

if (!$product || !$product->getId()) {
    return;
}
?>
<div class="text-right">
    <?php if ($product->getIsSalable()): ?>
        <p class="flex items-center justify-end align-middle available gap-x-2 stock"
           title="<?= $escaper->escapeHtmlAttr(__('Availability')) ?>">
            <span class="w-3 h-3 bg-green-500 rounded-full shrink-0"></span>
            <span><?= $escaper->escapeHtml(__('In stock')) ?></span>
        </p>
    <?php else: ?>
        <p class="flex items-center justify-end align-middle gap-x-2 unavailable stock"
           title="<?= $escaper->escapeHtmlAttr(__('Availability')) ?>">
            <span class="w-3 h-3 bg-red-500 rounded-full shrink-0"></span>
            <span><?= $escaper->escapeHtml(__('Out of stock')) ?></span>
        </p>
    <?php endif; ?>
</div>
