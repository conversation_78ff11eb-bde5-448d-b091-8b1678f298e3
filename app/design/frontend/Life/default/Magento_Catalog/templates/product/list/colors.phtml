<?php

declare(strict_types=1);

use Life\ProductsConnector\ViewModel\ProductColors;

/** @var \Magento\Framework\View\Element\Template $block */
/** @var \Magento\Framework\Escaper $escaper */
/** @var \Hyva\Theme\Model\ViewModelRegistry $viewModels */

/** @var ProductColors $productColorsViewModel */
$productColorsViewModel = $viewModels->require(ProductColors::class);

/** @var Magento\Catalog\Model\Product $product */
$product = $block->getData('product');
$productId = $product->getId();

$colors = $productColorsViewModel->getColors($product);

if (!$colors) {
    return '';
}

$displayColors = array_slice($colors, 0, 4);
$rest = count($colors) - count($displayColors);
?>
<button
    class="flex gap-2 mt-1 sm:mt-3 !mb-0 !pl-0 items-center"
    :class="{'cursor-pointer max-md:pointer-events-none': hasSwatchColors}"
    x-data="{show: true, hasSwatchColors: false, moreOptions: <?= ($rest > 0) ? 'true' : 'false'; ?>}"
    @click="$dispatch('toggle-swatch-colors', { productId: <?= /* @noEscape */ $productId; ?> }); show = false;"
    @toggle-colors-<?= /* @noEscape */ $productId; ?>.window="show = !show"
    @has-swatch-colors-<?= /* @noEscape */ $productId; ?>.window="hasSwatchColors = true"
    x-show="show || !hasSwatchColors"
>
    <span class="flex items-center">
        <?php foreach ($displayColors as $swatchItem): ?>
            <span class="w-6 h-6 relative rounded-full border border-secondary-900/30 shadow-sm overflow-hidden [&:not(:first-child)]:-ml-2">
                <?php if ($swatchItem['image']): ?>
                    <img
                        src="<?= $escaper->escapeUrl($swatchItem['image'] ?? '');?>"
                        alt="<?= $escaper->escapeHtmlAttr($swatchItem['label'] ?? ''); ?>"
                        class="absolute inset-0 block object-cover"
                    >
                <?php else: ?>
                    <span
                        style="background-color: <?= $escaper->escapeUrl($swatchItem['color'] ?? '');?>"
                        title="<?= $escaper->escapeHtmlAttr($swatchItem['label'] ?? ''); ?>"
                        class="absolute inset-0 block object-cover"
                    ></span>
                <?php endif; ?>
            </span>
        <?php endforeach; ?>
        <span
            class="relative w-6 h-6 -ml-2 overflow-hidden border border-white rounded-full shadow-sm"
            x-show="moreOptions || hasSwatchColors"
        >
            <span class="absolute inset-0 flex items-center justify-center object-cover text-xs bg-white">+
                <span x-show="moreOptions"><?= $escaper->escapeHtml($rest); ?></span>
            </span>
        </span>
    </span>
    <span x-show="hasSwatchColors" class="text-xs underline text-black/50 max-md:hidden">
        <?= $escaper->escapeHtml(__('more options')); ?>
    </span>
</button>
