<?php

declare(strict_types=1);

/** @var \Magento\Catalog\Block\Product\View\Details $block */
/** @var \Magento\Framework\Escaper $escaper */

?>

<div class="rounded-xl bg-white grid lg:grid-cols-2 lg:gap-[15%] my-2 mx-4 lg:m-12 p-4 lg:p-[3.5rem]">
    <?php foreach ($block->getGroupSortedChildNames('detailed_info', '') as $sectionName) : ?>
        <?php
        /** @var \Magento\Framework\View\Element\BlockInterface&\Magento\Framework\DataObject|bool $sectionBlock */
        $sectionBlock  = $block->getLayout()->getBlock($sectionName);
        $sectionHtml   = (string)$sectionBlock->toHtml();
        ?>
        <section
            id="<?= $escaper->escapeHtmlAttr($sectionBlock->getNameInLayout()) ?>"
            class="pb-8 prose"
        >
            <?= /* @noEscape */ $sectionHtml ?>
        </section>
    <?php endforeach ?>
</div>
