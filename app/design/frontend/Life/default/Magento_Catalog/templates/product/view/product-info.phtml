<?php

declare(strict_types=1);

use Hyva\Theme\ViewModel\ProductAttributes;
use Hyva\Theme\ViewModel\ProductPage;
use Life\DisableCheckoutExtensions\Helper\Config as DisableCheckoutConfigHelper;

/** @var \Magento\Catalog\Block\Product\View $block */
/** @var \Magento\Framework\Escaper $escaper */
/** @var \Hyva\Theme\Model\ViewModelRegistry $viewModels */

/** @var ProductPage $productViewModel */
$productViewModel = $viewModels->require(ProductPage::class);
/** @var ProductAttributes $attributesViewModel */
$attributesViewModel = $viewModels->require(ProductAttributes::class);
/** @var \Magento\Catalog\Model\Product $product */
$product = $productViewModel->getProduct();
/** @var \Hyva\Theme\ViewModel\HeroiconsOutline $heroicons */
$heroicons = $viewModels->require(\Hyva\Theme\ViewModel\HeroiconsOutline::class);

/** @var DisableCheckoutConfigHelper $disableCheckoutConfigHelper */
$disableCheckoutConfigHelper = $this->helper(DisableCheckoutConfigHelper::class);
$disableCheckout = $disableCheckoutConfigHelper->getConfig()->getDisableCheckout();
?>

<div class="flex flex-col w-full gap-2 mb-2">
    <div class="px-4 pb-4 bg-white sm:px-8 sm:pb-8 rounded-b-xl">
        <?= $block->getChildHtml("product.quality.marks") ?>

        <?php if ($subtitle = $productViewModel->getProduct()->getData('subtitle_pdp')): ?>
            <p class="leading-loose product-description line-clamp-3 opacity-60 lg:mt-1 lg:text-sm lg:leading-loose">
                <?= /* @noEscape */ $productViewModel->productAttributeHtml(
                    $productViewModel->getProduct(),
                    $subtitle,
                    'subtitle_pdp')
                ?>
            </p>
        <?php endif; ?>

        <?php if ($shortDescription = $productViewModel->getShortDescription()): ?>
            <p
                id="product-description"
                class="mt-3 text-sm leading-loose product-description line-clamp-2 lg:text-base lg:leading-loose max-lg:hidden"
            >
                <?= /* @noEscape */ $shortDescription ?>
            </p>
            <a
                href="#description"
                class="text-sm leading-none font-semibold underline lowercase lg:text-base lg:leading-loose max-lg:hidden"
            >
                <?= $escaper->escapeHtml(__('Read more')); ?>
            </a>
        <?php endif; ?>

        <?php if ($product->getTypeId() === 'configurable'): ?>
            <div class="mt-6 lg:hidden">
                <div
                    class="flex grow-0 shrink basis-0"
                    role="group"
                    aria-label="<?= $escaper->escapeHtmlAttr('Price') ?>"
                >
                    <?= $block->getChildHtml("product.info.price") ?>
                </div>

                <div class="inline-block mt-2 bg-primary-100/30 text-primary text-sm rounded-[0.25rem] overflow-clip">
                    <?= $block->getChildHtml("product.delivery.time") ?>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>

<div
    x-data="initStickyCta()"
    @resize.window.debounce="init()"
>
    <?= $block->getChildHtml('product.info.review') ?>
    <?= $block->getChildHtml("product.info.stockstatus") ?>
    <?= $block->getChildHtml('product.info.form') ?>
    <?php if (!$disableCheckout): ?>
        <?php if ($tierPriceBlock = $block->getChildHtml("product.price.tier")): ?>
            <div class="py-4 my-2 tier-price-container">
                <?= /** @noEscape */ $tierPriceBlock ?>
            </div>
        <?php endif; ?>
        <div
            :class="{ 'hidden' : !sticky }"
            :style="`height: ${height}`"
        ></div>
        <div
            class="product-wrapper"
            x-ref="productWrapper"
        >
            <div
                class="bg-white sm:flex"
                :class="{
                    'px-4 py-3 slide-in fixed bottom-0 left-0 right-0 z-30 border-t border-secondary-900/50 sm:px-0' : sticky,
                    'mt-2 p-4 rounded-xl lg:p-6' : !sticky
                }"
            >
                <div
                    class="justify-between sm:container sm:flex gap-x-4"
                    :class="{
                        '!p-0' : !sticky
                    }"
                >
                    <div
                        class="items-center justify-between gap-x-4 hidden"
                        :class="{
                            'md:flex' : sticky
                        }"
                    >
                        <div
                            class="max-w-[92px] rounded-xl overflow-hidden"
                            x-data="initStickyImage()"
                            x-bind="eventListeners"
                        >
                            <template x-if="!loadImage">
                                <?= $block->getImage($product, 'product_page_image_small') /** @phpstan-ignore-line */
                                    ->setTemplate('Magento_Catalog::product/list/image.phtml')
                                    ->setProductId($product->getId())
                                    ->toHtml(); ?>
                            </template>
                            <template x-if="loadImage">
                                <img :src="mainImage?.thumb" alt="<?= $escaper->escapeHtml($product->getName()); ?>" title="<?= $escaper->escapeHtml($product->getName()); ?>" class="w-full" />
                            </template>
                        </div>
                        <h3 class="text-lg font-light">
                            <?= $escaper->escapeHtml($product->getName()); ?>
                        </h3>
                    </div>

                    <div
                        class="flex justify-between gap-x-2 sm:gap-x-6"
                        :class="{
                            'items-start md:items-center' : sticky,
                            'w-full flex-col' : !sticky
                        }"
                    >
                        <div
                            class="flex grow-0 shrink basis-0 mb-3 px-1.5 lg:px-2"
                            :class="{ '[&_[x-data^=initPrice]]:mb-0' : sticky }"
                            role="group"
                            aria-label="<?= $escaper->escapeHtmlAttr('Price') ?>"
                        >
                            <?= $block->getChildHtml("product.info.price") ?>
                        </div>

                        <div
                            class="max-w-sm xl:w-96 product-action"
                            :class="{
                                'sm:w-60' : sticky,

                            }"
                        >
                            <span
                                class="block mb-3 text-primary rounded-[0.25rem] overflow-clip"
                                :class="{
                                    'text-sm' : !sticky,
                                    'text-xs [&_.product-delivery-time]:justify-end lg:text-sm' : sticky
                                }"
                            >
                                <?= $block->getChildHtml("product.delivery.time") ?>
                            </span>

                            <?php if ($product->isSaleable()): ?>
                                <div>
                                    <?= $block->getChildHtml("product.info.quantity") ?>
                                </div>

                                <?= $block->getChildHtml("product.info.addtocart") ?>
                            <?php endif; ?>
                        </div>
                        <div class="mt-3" :class="{ 'hidden' : sticky }">
                            <?= $block->getChildHtml("product.info.usps") ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>
    <div class="relative p-4 mt-2 overflow-hidden showroom-notice md:p-8 rounded-xl bg-secondary-500">
        <div class="relative z-10"><?= $block->getChildHtml('product.info.bottom') ?></div>
        <div class="w-48 h-[15rem] absolute top-3/4 -right-8 -translate-y-1/2 bg-style1 bg-no-repeat blur-sm opacity-10"></div>
    </div>
    <?php if ($product->isSaleable()): ?>
        <div class="flex justify-end">
            <?= $block->getChildHtml('addtocart.shortcut.buttons') ?>
        </div>
    <?php endif; ?>
    <?= $block->getChildHtml('product.info.additional.actions'); ?>

    <?= $block->getChildHtml("product.info.additional") ?>
</div>

<script defer="defer">
    function initStickyCta() {
        return {
            sticky: false,
            height: '0px',
            init() {
                this.observeVisibility();

                <?php 
                // watch sticky variable and update reserved height if CTA bar is not sticky
                ?>
                this.$watch('sticky', value => { !value && this.calcCtaHeight() });

                <?php
                // initially, set the reserved space height for sticky CTA
                ?>
                setTimeout(() => {
                    if (!this.sticky) {
                        this.calcCtaHeight();
                    }
                }, 50);

                <?php 
                // watch sticky variable and update reserved height if CTA bar is not sticky 
                ?>
                this.$watch('sticky', value => { !value && this.calcCtaHeight() });
            },
            calcCtaHeight() {
                this.height = `${this.$refs.productWrapper.getBoundingClientRect().height}px`;
            },
            observeVisibility() {
                const observerOptions = {
                    root: null,
                    rootMargin: '0px',
                    threshold: 0
                };

                const observer = new IntersectionObserver(entries => {
                    entries.forEach(entry => {
                        <?php
                        // Check if the bottom of the element is above the top of the viewport
                        ?>
                        this.sticky = !entry.isIntersecting && entry.boundingClientRect.bottom < 0;
                    });
                }, observerOptions);

                observer.observe(this.$refs.productWrapper);
            }
        }

    }
    function initStickyImage() {
        return {
            loadImage: false,
            mainImage: [],
            receiveImages(images) {
                if (images.length) {
                    this.mainImage = images.find((i) => i.isMain);

                    if (this.mainImage?.isMain) {
                        this.loadImage = true;
                    }
                }
            },
            eventListeners: {
                ['@update-gallery.window'](event) {
                    this.receiveImages(event.detail);
                }
            }
        }
    }
</script>
