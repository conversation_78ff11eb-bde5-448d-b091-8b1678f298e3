<script defer="defer">
    function initListItemGallery(data) {
        return {
            "active": 0,
            "activeStep": 0,
            "activeStepInit": null,
            "hideAllControls": true,
            "initialImages": data.initialImages ?? [],
            "images": data.images ?? [],
            "additionalImages": data.additionalImages ?? [],
            "appendOnReceiveImages": data.appendOnReceiveImages ?? 'false',
            "currentGallery": null,
            "defaultProductId": data.defaultProductId ?? null,
            "productId": data.productId ?? null,

            initialize(element) {
                this.currentGallery = element;
                if (this.defaultProductId) {
                    this.updateImages(this.defaultProductId);
                }
                this.images.sort((a, b) => {
                    return parseInt(a.position, 10) - parseInt(b.position, 10);
                });
                this.initialImages.sort((a, b) => {
                    return parseInt(a.position, 10) - parseInt(b.position, 10);
                });
                this.setActive(0);
            },

            receiveImages(images) {
                let self = this;

                if (this.appendOnReceiveImages) {
                    this.images = [].concat(this.initialImages, images);
                } else {
                    this.images = images;
                }

                // Scroll to start
                setTimeout(function() {
                    self.setActive(0);
                    self.slide('left', Math.max(self.active, self.images.length));
                }, 300);
            },

            initActive() {
                let active = this.images.findIndex(function(image) {
                    return image.isMain === true
                });
                if (active === -1) {
                    active = 0;
                }
                this.setActive(active);
            },

            setActive(index) {
                if (index === this.activeStepInit) {
                    return;
                }
                this.activeStepInit = index;

                var pixelsToScroll = (index - this.active) * this.getGallerySlider().clientWidth;
                this.getGallerySlider().scrollBy({
                    left: pixelsToScroll,
                    behavior: "smooth"
                });

                this.active = index;
            },

            slide(direction, steps) {
                if (direction === 'left' && this.active > 0) {
                    this.setActive(this.active - steps);
                } else if (direction === 'right' && this.active + 1 < this.images.length) {
                    this.setActive(this.active + steps);
                }
            },

            getGallerySlider() {
                return this.currentGallery.querySelector('.gallery');
            },

            showNavigation() {
                return this.images.length > 1;
            },

            firstSlideActive() {
                if (this.active === 0) {
                    return true;
                }
                return false;
            },

            lastSlideActive() {
                if (this.active === this.images.length - 1) {
                    return true;
                }
                return false;
            },

            updateImages(productId) {
                if (productId) {
                    let images = this.initialImages,
                        currentProductId = this.productId;

                    if (this.additionalImages[productId]) {
                        images = this.additionalImages[productId];
                        currentProductId = productId
                    }

                    images.sort((a, b) => {
                        return parseInt(a.position, 10) - parseInt(b.position, 10);
                    });

                    this.images = images;

                    if (currentProductId != this.currentProductId) {
                        this.currentProductId = currentProductId;
                    }
                }
            },

            calcActive(event) {
                var slider = this.getGallerySlider();
                if (slider) {
                    this.active = Math.ceil(
                        Math.round(
                            slider.scrollLeft / (slider.scrollWidth / this.images.length)
                        )
                    );
                    this.setActive(this.active);
                }
            }
        }
    }
</script>
