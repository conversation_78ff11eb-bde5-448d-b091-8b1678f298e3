<?php
declare(strict_types=1);

use Magento\Catalog\Block\Product\Image;

/** @var Image $block */
/** @var \Magento\Framework\Escaper $escaper */

$hasLoadingAttribute = ($block->getCustomAttributes() ?: [])['loading'] ?? false;
$hoverImageUrl = $block->getData('hover_image') ?: false;
?>
<img
    class="object-contain w-full <?= $escaper->escapeHtmlAttr($block->getClass()) ?><?=
        ($hoverImageUrl) ? ' group-hover:opacity-0 group-hover:invisible' : ''; ?>"
    x-data=""
    @update-gallery-<?= (int)$block->getProductId() ?>.window="$root.src = $event.detail"
    <?php foreach ($block->getCustomAttributes() as $name => $value): ?>
        <?= $escaper->escapeHtmlAttr($name) ?>="<?= $escaper->escapeHtmlAttr($value) ?>"
    <?php endforeach; ?>
    src="<?= $escaper->escapeUrl($block->getImageUrl()) ?>"
    <?php if (! $hasLoadingAttribute): ?>
    loading="lazy"
    <?php endif; ?>
    width="<?= $escaper->escapeHtmlAttr($block->getWidth()) ?>"
    height="<?= $escaper->escapeHtmlAttr($block->getHeight()) ?>"
    alt="<?= $escaper->escapeHtmlAttr($block->getLabel()) ?>"
    title="<?= $escaper->escapeHtmlAttr($block->getLabel()) ?>"
/>
<?php if ($hoverImageUrl): ?>
    <img src="<?= $escaper->escapeUrl($hoverImageUrl); ?>"
         alt="<?= $escaper->escapeHtmlAttr($block->getLabel()) ?>"
         class="w-full absolute top-0 invisible object-contain h-full transition-all duration-300 opacity-0 group-hover:opacity-100 group-hover:visible rounded-xl"
    >
<?php endif; ?>
