<?php

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Magento\Catalog\Model\Product;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;
use Hyva\Theme\ViewModel\ProductPage;

/** @var Escaper $escaper */
/** @var Template $block */
/** @var ViewModelRegistry $viewModels */

/** @var ProductPage $productViewModel */
$productViewModel = $viewModels->require(ProductPage::class);
/** @var Product $product */
$product = $productViewModel->getProduct();

if (!$product->getDescription()) {
    return;
}

$cssClasses = $block->getData('css_classes') ?? 'prose';

?>
<h3 class="<?=$escaper->escapeHtmlAttr($cssClasses) ?> heading-3 mt-12 mb-10">
    <span class="base">
        <?= $escaper->escapeHtml(__('Product description')) ?>
    </span>
</h3>
<div class="<?= $escaper->escapeHtmlAttr($cssClasses) ?>">
    <?= $productViewModel->productAttributeHtml($product, $product->getDescription(), 'description') ?>
</div>
