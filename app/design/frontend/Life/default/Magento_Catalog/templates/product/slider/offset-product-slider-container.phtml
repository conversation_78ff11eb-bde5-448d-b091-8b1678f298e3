<?php

declare(strict_types=1);

use Hyva\Theme\ViewModel\SvgIcons;
use Hyva\Theme\ViewModel\ProductListItem;
use Hyva\Theme\ViewModel\ProductPage;
use Hyva\Theme\ViewModel\Store;
use Life\ThemeConfigurations\ViewModel\SwiperScript;
use Magento\Catalog\Block\Product\ReviewRendererInterface as ProductReviewRenderer;
use Magento\Framework\View\Element\Template;

// phpcs:disable Generic.Files.LineLength.TooLong

/** @var \Magento\Framework\View\Element\Template $block */
/** @var \Magento\Framework\Escaper $escaper */
/** @var \Hyva\Theme\Model\ViewModelRegistry $viewModels */

/** @var Store $viewModelStore */
$viewModelStore = $viewModels->require(Store::class);
/** @var ProductPage $productViewModel */
$productViewModel = $viewModels->require(ProductPage::class);
/** @var SvgIcons $svgIcons */
$svgIcons = $viewModels->require(SvgIcons::class);
/** @var ProductListItem $productListItemViewModel */
$productListItemViewModel = $viewModels->require(ProductListItem::class);

$viewMode = 'grid';
$imageDisplayArea = 'category_page_grid';
$showDescription = false;

$title = (string) $block->getTitle();
$description = (string) $block->getDescription();
$backgroundColor = (string) $block->getBackgroundColor();
$headingTag = $block->getData('heading_tag') ?: 'h3';

$items = $block->getItems() ?? [];
if (is_object($items) && $items instanceof Iterator) {
    $items = iterator_to_array($items);
}
if (!$itemCount = count($items)) {
    return '';
}

$sliderIndex = 1;
$sliderItemRenderer = $block->getLayout()->getBlock('product_list_item')
    ?: $block->getChildBlock('slider.item.template')
    ?: $block->getLayout()->createBlock(Template::class);

$hideRatingSummary = (bool) $block->getData('hide_rating_summary');
$hideDetails       = (bool) $block->getData('hide_details');

$sliderItemRenderer->setData('hide_details', $hideDetails);
$sliderItemRenderer->setData('hide_rating_summary', $hideRatingSummary);

// The slider item renderer block is often a shared instance.
// If a specific item template is set for this slider, the previously set template must be reset later
// so the item template is only replaced for the one slider it is specified on.
$sharedItemRendererTemplate = null;
$isSharedItemRenderer       = $sliderItemRenderer !== $block->getChildBlock('slider.item.template');
if ($isSharedItemRenderer && $block->getChildBlock('slider.item.template')) {
    $sharedItemRendererTemplate = $sliderItemRenderer->getTemplate();
    $sliderSpecificItemTemplate = $block->getChildBlock('slider.item.template')->getTemplate();
    $sliderItemRenderer->setTemplate($sliderSpecificItemTemplate);
}

?>
<script defer="defer">
    'use strict';

    (() => {
        const initCarousels = (elements) => {
            if (!Swiper) {
                return;
            }

            // Product carousel
            const initProductCarousel = (slider) => {
                const swiperElement = slider.querySelector('.swiper');

                // Add Swiper pagination & arrows
                slider.insertAdjacentHTML(
                    'beforeend',
                    '<?= $escaper->escapeJs($block->getBlockHtml('pagebuilder.carousel.nav')) ?>'
                );

                const arrows = slider.querySelectorAll('.swiper-nav button');

                [...arrows].map((arrow) => {
                    arrow.classList.remove('!hidden');
                })

                // Swiper options
                const swiper = new Swiper(swiperElement, {
                    slidesPerView: 1,
                    spaceBetween: 24,
                    loop: true,
                    autoplay: false,
                    navigation: {
                        nextEl: slider.querySelector('.swiper-button-next'),
                        prevEl: slider.querySelector('.swiper-button-prev'),
                    },
                    breakpoints: {
                        768: {
                            slidesPerView: 2,
                        },
                        1024: {
                            slidesPerView: 2.25,
                        },
                        1440: {
                            slidesPerView: 3.25,
                        },
                    }
                });
            };


            // Init scripts
            elements.forEach(element => {
                if (element.dataset.contentType === 'product-carousel') {
                    initProductCarousel(element);
                }
            });
        };

        window.addEventListener('init-external-scripts', () => {
            const carouselElements = document.querySelectorAll(
                `[data-content-type="product-carousel"]`
            );

            // Load Swiper script
            const swiperScript = document.createElement('script');

            swiperScript.type = 'text/javascript';
            swiperScript.src = '<?= SwiperScript::SWIPER_JS ?>';
            swiperScript.setAttribute('data-swiper-script', 'init');

            const hasSwiperScript = document.querySelector(`script[data-swiper-script="init"]`);

            if (!hasSwiperScript) {
                document.head.appendChild(swiperScript);
            }

            // Load Swiper stylesheet
            const swiperStyle = document.createElement('link');
            swiperStyle.rel = 'stylesheet';

            swiperStyle.href = '<?= SwiperScript::SWIPER_CSS ?>';
            swiperStyle.setAttribute('data-swiper-style', 'init');

            const hasSwiperStyle = document.querySelector(`script[data-swiper-style="init"]`);

            if (!hasSwiperStyle) {
                document.head.appendChild(swiperStyle);
            }

            // Load Carousels
            if (carouselElements.length > 0) {
                swiperScript.addEventListener('load', () => {
                    initCarousels(carouselElements);
                });
            }
        }, {once: true, passive: true});
    })();
</script>

<section
    class="<?= $escaper->escapeHtmlAttr($block->getData('maybe_purged_tailwind_section_classes'))
        ?: 'max-lg:!p-4 lg:!py-8 xl:!py-12 rounded-xl flex max-lg:flex-col relative';
    ?>"
    role="group"
    aria-roledescription="<?= $escaper->escapeHtmlAttr(__('Carousel')) ?>"
    aria-label="<?= $escaper->escapeHtmlAttr(__('Carousel %1', $title)) ?>"
    data-background-color-type="<?= $escaper->escapeHtmlAttr($backgroundColor); ?>"
>
    <?php if ($items): ?>
        <div class="slider-description max-lg:order-2 max-lg:!mb-6 max-lg:mt-8 max-lg:text-center lg:absolute lg:h-full lg:top-0 lg:z-10 lg:w-1/3 !mb-0 text-black lg:px-8 xl:px-12 lg:flex lg:items-end lg:pb-32 lg:pointer-events-none">
            <div>
                <?php if ($title): ?>
                    <<?= /* @noEscape */ $headingTag ?> class="mb-2 text-3xl font-medium leading-normal md:text-5xl title-font">
                    <?= $escaper->escapeHtml($title); ?>
                    </<?= /* @noEscape */ $headingTag ?>>
                <?php endif; ?>
                <?php if ($description): ?>
                    <?= /* @noEscape */ $description; ?>
                <?php endif; ?>
            </div>
        </div>
        <div class="relative lg:pl-[33.33%] max-lg:w-full max-lg:pb-20 w-full" data-content-type="product-carousel">
            <div class="">
                <div class="max-lg:!container relative flex-none w-screen -ml-2 overflow-x-hidden swiper sm:-mx-3 sm:w-full">
                    <div
                        class="relative grid mx-auto swiper-wrapper sm:grid-cols-2 xl:grid-cols-3 3xl:grid-cols-4"
                    >
                        <?php foreach ($items as $product): ?>
                            <div
                                class="swiper-slide !h-auto"
                                role="group"
                                aria-label="<?= $escaper->escapeHtmlAttr(__('Item %1', $sliderIndex++)) ?>"
                            >
                                <?php $product->setData('item_relation_type', $block->getData('item_relation_type')); ?>
                                <?= /** @noEscape */ $productListItemViewModel->getItemHtmlWithRenderer(
                                    $sliderItemRenderer,
                                    $product,
                                    $block,
                                    $viewMode,
                                    ProductReviewRenderer::SHORT_VIEW,
                                    $imageDisplayArea,
                                    $showDescription
                                ) ?>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
    <?php endif; ?>
</section>
<?php

if ($sharedItemRendererTemplate) {
    $sliderItemRenderer->setTemplate($sharedItemRendererTemplate);
}
?>
