<?php

declare(strict_types=1);

/** @var \Magento\Framework\Escaper $escaper */
/** @var \Magento\Catalog\Block\Product\ProductList\Toolbar $block */
/** @var \Hyva\Theme\Model\LocaleFormatter $localeFormatter */

$additionalOptions = ['page' => 'p'];
?>
<?php if ($block->getCollection()->getSize()): ?>
    <?php
        $countLabel = '';
        if ((int)$block->getTotalNum() === 1) {
            $countLabel = $escaper->escapeHtml(
                __('%1 result', $localeFormatter->formatNumber($block->getTotalNum()))
            );
        } elseif ((int)$block->getTotalNum() > 1) {
            $countLabel = $escaper->escapeHtml(
                __('%1 results', $localeFormatter->formatNumber($block->getTotalNum()))
            );
        }
   ?>
    <div
        x-data='initToolbar(<?= /* @noEscape */ $block->getWidgetOptionsJson($additionalOptions) ?>)'
        x-init="$dispatch('show-results-count', { label: '<?= /** @noEscape */ $countLabel; ?>' })"
        class="toolbar toolbar-products"
        id="toolbar<?php if ($block->getIsBottom()): ?>-bottom<?php endif ?>"
    >
        <?php if ($block->getIsBottom()): ?>
            <?= $block->getPagerHtml() ?>
        <?php else: ?>
            <?php if ($block->isExpanded()): ?>
                <?= /** @noEscape */ $block->fetchView($block->getTemplateFile('Magento_Catalog::product/list/toolbar/sorter.phtml')) ?>
            <?php endif ?>
        <?php endif ?>
    </div>
<?php endif ?>
