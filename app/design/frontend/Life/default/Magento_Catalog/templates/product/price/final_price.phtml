<?php

declare(strict_types=1);

use Hyva\Theme\ViewModel\ProductPage;
use Life\ProductsConnector\ViewModel\ProductPrice;
use Magento\Msrp\Pricing\Price\MsrpPrice;

/** @var \Magento\Catalog\Pricing\Render\FinalPriceBox $block */
/** @var \Hyva\Theme\Model\ViewModelRegistry $viewModels */
/** @var \Magento\Framework\Escaper $escaper */

/** ex: \Magento\Catalog\Pricing\Price\RegularPrice */
/** @var \Magento\Framework\Pricing\Price\PriceInterface $priceModel */
$priceModel = $block->getPriceType('regular_price');

/** ex: \Magento\Catalog\Pricing\Price\FinalPrice */
/** @var \Magento\Framework\Pricing\Price\PriceInterface $finalPriceModel */
$finalPriceModel = $block->getPriceType('final_price');
$idSuffix = $block->getIdSuffix() ? $block->getIdSuffix() : '';
$schema = ($block->getZone() == 'item_view') ? true : false;

$product = $block->getSaleableItem();
$product->getPriceInfo()->getPrice('final_price')->getAmount();

/** @var ProductPrice $productPriceViewModel */
$productPriceViewModel = $viewModels->require(ProductPrice::class);
$discountPercentage = $productPriceViewModel->getDiscountPercentage($product);
$discountAmount = $productPriceViewModel->getDiscountAmount($product);
$pricePercentageThreshold = $productPriceViewModel->getPricePercentageThresholdAmount();
$msrpPrice = iterator_to_array($product->getPriceInfo()->getPrices())[MsrpPrice::PRICE_CODE] ?? null;

/** @var ProductPage $productViewModel */
$productViewModel = $viewModels->require(ProductPage::class);
?>
<?php if ($discountAmount > 0): ?>
    <span
        class="flex items-center px-1 py-px my-1 text-xs bg-tertiary-300 rounded-xl text-tertiary min-w-fit empty:hidden"
        data-price-type="discountPrice"
    >
        <?php if ($productPriceViewModel->getNormalPrice($product) > $pricePercentageThreshold): ?>
            -<?= /* @noEscape */ $productPriceViewModel->getFormattedPrice($discountAmount) ?>
        <?php elseif ($discountPercentage > 0): ?>
            -<?= /* @noEscape */ $discountPercentage ?>%
        <?php endif; ?>
    </span>
<?php endif; ?>
<span class="flex gap-x-3 items-center">
    <span class="special-price">
        <span class="price-container price-final_price tax weee" data-price-type="finalPrice">
            <?= /** @noEscape */ $productViewModel->format($product->getData('final_price')) ?>
        </span>
    </span>
</span>
<?php if ($msrpPrice->canApplyMsrp($product)): ?>
    <div class="flex items-center justify-end text-xs leading-normal basis-full old-price text-black/60">
        <span class="text-black/60">
            <?= __('Advise Price') ?>
        </span>
        <span data-price-type="msrpPrice">&nbsp;
            <span class="line-through">
                <?php if ($msrpPrice->isMinimalPriceLessMsrp($product)): ?>
                    <?= /** @noEscape */ $productViewModel->format(
                        $productPriceViewModel->getMsrpPrice($product)) ?>
                <?php endif; ?>
            </span>
        </span>
    </div>
<?php endif; ?>

<?php if ($block->showMinimalPrice()) :?>
    <?php if ($block->getUseLinkForAsLowAs()) :?>
        <a href="<?= $escaper->escapeUrl($product->getProductUrl()) ?>" class="minimal-price-link">
            <?= /* @noEscape */ $block->renderAmountMinimal() ?>
        </a>
    <?php else :?>
        <span class="minimal-price-link">
            <?= /* @noEscape */ $block->renderAmountMinimal() ?>
        </span>
    <?php endif?>
<?php endif; ?>
