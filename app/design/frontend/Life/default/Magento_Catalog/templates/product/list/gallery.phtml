<?php

declare(strict_types=1);

use Hyva\Theme\ViewModel\SvgIcons;
use Life\ProductsConnector\ViewModel\ConfigurableProductGallery;
use Magento\Catalog\Block\Product\View\Gallery;

/** @var \Magento\Catalog\Block\Product\AbstractProduct $block */
/** @var \Magento\Framework\Escaper $escaper */
/** @var \Hyva\Theme\Model\ViewModelRegistry $viewModels */

/** @var SvgIcons $svgIcons */
$svgIcons = $viewModels->require(SvgIcons::class);

/** @var \Magento\Catalog\Model\Product $product */
$product = $block->getData('product');

/** @var ConfigurableProductGallery $galleryViewModel */
$galleryViewModel = $viewModels->require(ConfigurableProductGallery::class);
/** @var Gallery $galleryBlock */
$galleryBlock = $block->getLayout()->createBlock(Gallery::class);
$galleryBlock->setProduct($product);

$productImages = $galleryBlock->getGalleryImages()->getItems();
$associatedProductImages = $galleryViewModel->getGallery($product, $galleryBlock);

$productName = $product->getName();
$defaultProductId = $galleryViewModel->getDefaultProductId($product);

$uniqueId = '_' . uniqid();

try {
    $galleryData = json_encode([
        'initialImages' => json_decode($galleryBlock->getGalleryImagesJson(), true),
        'images' => json_decode($galleryBlock->getGalleryImagesJson()),
        'additionalImages' => json_decode($associatedProductImages),
        'appendOnReceiveImages' => $galleryBlock->getVar(
            'gallery_switch_strategy',
            'Magento_ConfigurableProduct'
        ) === 'append' ? 'true' : 'false',
        'defaultProductId' => $defaultProductId,
        'productId' => $product->getId()
    ]);
} catch (\Throwable $exception) {
    $galleryData = '{}';
}
?>

<div
    class="object-contain w-full"
    x-data='initListItemGallery(<?= /* @noEscape */ $galleryData; ?>)'
    x-init="initialize($el)"
    @update-list-images-<?= $product->getId() ?>.window="updateImages($event.detail)"
>
    <div class="relative">
        <div
            class="relative flex w-full"
            x-transition:enter="ease-out duration-500"
            x-transition:enter-start="opacity-0"
            x-transition:enter-end="opacity-100"
        >
            <div
                class="flex flex-no-wrap flex-1 w-full overflow-x-scroll overflow-y-hidden gallery snap snap-x snap-center snap-mandatory gallery-snap"
            >
                <template x-for="(image, index) in images" :key="index">
                    <div class="relative z-10 flex items-center flex-shrink-0 object-contain object-center w-full gallery-image snap-center touch-none touch-pan-y touch-pan-x"
                    >
                        <a
                            href="<?= $escaper->escapeUrl($product->getProductUrl()) ?>"
                            data-link-product-id="<?= $escaper->escapeHtmlAttr($product->getId()) ?>"
                        >
                            <img
                                :alt="image.caption || '<?= $escaper->escapeJs($productName) ?>'"
                                :title="image.caption || '<?= $escaper->escapeJs($productName) ?>'"
                                class="object-cover object-center w-full max-h-full gallery-image aspect-[3/2]"
                                draggable="false"
                                :src="image.img"
                                :data-width="image.width"
                                :data-height="image.height"
                                width="615"
                                height="447"
                            />
                        </a>
                    </div>
                </template>
            </div>
        </div>

        <div
            class="absolute z-10 flex justify-between w-full transform -translate-y-1/2 pointer-events-none top-1/2"
            x-show="images.length > 1"
            x-cloak
        >
            <button
                class="p-2 rounded-full outline-none pointer-events-auto"
                :class="{
                    'opacity-20': firstSlideActive(),
                    'opacity-100': !firstSlideActive()
                }"
                x-show="showNavigation()"
                @click="slide('left', 1)"
                :disabled="firstSlideActive()"
            >
                <span class="sr-only"><?= __('Previous slide') ?></span>
                <?= $svgIcons->chevronLeftHtml("w-6 h-6") ?>
            </button>
            <button
                class="p-2 rounded-full outline-none pointer-events-auto"
                :class="{
                    'opacity-20': lastSlideActive(),
                    'opacity-100': !lastSlideActive()
                }"
                x-show="showNavigation()"
                @click="slide('right', 1)"
                :disabled="lastSlideActive()"
            >
                <span class="sr-only"><?= __('Next slide') ?></span>
                <?= $svgIcons->chevronRightHtml("w-6 h-6") ?>
            </button>
        </div>
    </div>
</div>
