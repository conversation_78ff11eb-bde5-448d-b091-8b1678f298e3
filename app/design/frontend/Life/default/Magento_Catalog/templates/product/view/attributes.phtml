<?php

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\SvgIcons;
use Life\Catalog\ViewModel\ProductAttributes;
use Magento\Catalog\Helper\Output as CatalogOutputHelper;

/** @var \Hyva\Theme\Model\ViewModelRegistry $viewModels */
/** @var \Magento\Framework\Escaper $escaper */
/** @var \Magento\Catalog\Block\Product\View\Attributes $block */
/** @var \Magento\Catalog\Model\Product $product */

/** @var SvgIcons $svgIcons */
$svgIcons = $viewModels->require(SvgIcons::class);

/** @var CatalogOutputHelper */
$catalogOutputHelper = $this->helper(CatalogOutputHelper::class);
$product = $block->getProduct();
$productAttributes = [];

/** @var ViewModelRegistry $viewModels */
$childProductAttributes = $viewModels->require(ProductAttributes::class);
/** @var ProductAttributes $childProductAttributes */
$productAttributes = $childProductAttributes->getChildProductAttributes($product);
$productAttributes['default'] = [
    'sku' =>
        [
            'code' => 'sku',
            'label' => __('Sku'),
            'value' => $product->getSku(),
        ],
    ...$block->getAdditionalData()
];

$setItemsSkus = $childProductAttributes->getSetItemsSkus($product);

$tabListBtnCls = 'toggle-button py-2 px-6 bg-secondary-300 rounded-3xl whitespace-nowrap';
?>

<div
    x-data='productSpecifications(
        <?= /* @noEscape */ json_encode($productAttributes) ?>,
        <?= /* @noEscape */ json_encode($childProductAttributes->getSetItemsByProduct($product)) ?>
    )'
    x-on:resize.window="isMobile = (window.innerWidth < 768) ? true : false"
    @configurable-selection-changed.window="updateSpecifications($event)"
>
    <div class="flex items-start justify-between">
        <div
            id="pdp-specs-menu"
            class="flex gap-2 flex-wrap"
            :aria-expanded="expandedMenu || !isMobile"
            :class="{
                'max-h-10 overflow-hidden' : !expandedMenu && isMobile
            }"
        >
            <button
                class="<?= $tabListBtnCls ?>"
                :class="{'bg-primary text-white': (selectedSpecificationsId == 'default')}"
                data-product-id="<?= $product->getId() ?>"
                @click="updateSpecificationsBySetItem('default')"
            ><?= __('General') ?></button>

            <template x-for="(setItemSku, index) in currentSetItems" :key="index">
                <button
                    class="<?= $tabListBtnCls ?>"
                    :class="{'bg-primary text-white': (selectedSpecificationsId == index)}"
                    :data-product-id="setItemSku"
                    :tabIndex="expandedMenu ? false : '-1'"
                    @click="updateSpecificationsBySetItem(index)"
                    x-html="setItemSku"
                ></button>
            </template>
        </div>
        <button
            x-show="Object.keys(currentSetItems).length > 0 && isMobile"
            @click="toggleMenu()"
            class="btn btn-secondary shrink-0 bg-secondary-300"
            aria-controls="pdp-specs-menu"
        >
            <span
                class="inline-flex transition-transform"
                :class="{
                    'rotate-90' : !expandedMenu,
                    '-rotate-90' : expandedMenu
                }"
            >
                <?= $svgIcons->chevronRightHtml('w-4 h-4', 32, 32, [ 'aria-hidden' => 'true']); ?>
            </span>
        </button>
    </div>
    <div
        class="table-wrapper overflow-x-auto py-6"
        id="product-attributes-<?= $product->getId() ?>"
    >
        <table class="additional-attributes border-t border-gray-300 text-sm w-full">
            <template x-for="(specification, index) in currentSpecifications" :key="index">
                <tr class="border-b border-gray-300">
                    <th
                        class="col label w-1/4 py-4 text-left text-gray-700 font-normal product-attribute-label"
                        scope="row"
                        x-html="specification.label"
                    ></th>
                    <td
                        class="col data w-3/4 py-4 pl-2 text-left text-gray-500 product-attribute-value text-right"
                        :data-th="specification.label"
                        x-html="specification.value"
                    ></td>
                </tr>
            </template>
        </table>
    </div>
</div>
<script defer="defer">
    function productSpecifications(specifications, setItems) {
        return {
            specifications: specifications,
            setItems: setItems,
            currentSpecifications: specifications?.default,
            currentSetItems: setItems?.default,
            currentProductId: 'default',
            selectedSpecificationsId: 'default',
            expandedMenu: false,
            isMobile: (window.innerWidth < 768) ? true : false,

            updateSpecifications(event) {
                let productId = event?.detail?.productIndex;

                this.currentSpecifications = this.specifications?.default;
                this.currentSetItems = this.setItems?.default;
                if (productId && this.specifications[productId]) {
                    this.currentProductId = productId;
                    this.currentSpecifications = this.specifications[productId];
                    this.selectedSpecificationsId = 'default';
                }

                if (productId && this.setItems[productId]) {
                    this.currentProductId = productId;
                    this.currentSetItems = this.setItems[productId];
                }
            },

            updateSpecificationsBySetItem(productId) {
                this.currentSpecifications = this.specifications?.default;
                this.selectedSpecificationsId = productId;

                // Use product ID from selected configurations
                if (productId === 'default') {
                    productId = this.currentProductId;
                }

                if (productId && this.specifications[productId]) {
                    this.currentSpecifications = this.specifications[productId];
                }
            },

            toggleMenu() {
                this.expandedMenu = !this.expandedMenu
            }
        }
    }
</script>
