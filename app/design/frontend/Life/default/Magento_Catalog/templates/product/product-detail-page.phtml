<?php

declare(strict_types=1);

use Magento\Catalog\Block\Product\View;

/** @var View $block */

$product = $block->getProduct();
?>
<section class="relative max-sm:overflow-x-hidden">
    <div class="sm:container md:py-6">
        <div class="flex gap-4 flex-col lg:grid lg:grid-cols-12 lg:gap-6">
            <?php if ($cylindoGallery = $block->getChildHtml('cylindo.gallery')): ?>
                <?= $cylindoGallery ?>
            <?php else: ?>
                <?= $block->getChildHtml('product.media') ?>
            <?php endif; ?>
            <div class="mx-4 sm:mx-0 lg:col-span-5">
                <div class="px-4 pt-4 bg-white sm:px-8 sm:pt-8 rounded-t-xl">
                    <?= $block->getChildHtml('product.title') ?>
                </div>
                <?= $block->getChildHtml('product.info') ?>
            </div>
        </div>
    </div>
    <div class="pdp-main-leafs w-[32rem] h-full -scale-x-100 rotate-[32deg] bg-style8 bg-contain bg-no-repeat absolute -top-36 -left-36 -z-1 opacity-5 blur-[6px] pointer-event-none md:h-[64rem]"></div>
</section>
<section>
    <?= $block->getChildHtml('product_options_wrapper_bottom') ?>
</section>

<?= $block->getChildHtml('product.features'); ?>
<?= $block->getChildHtml('product.info.details'); ?>

<section>
    <?= $block->getChildHtml('product.info.details.after') ?>
    <?= $block->getChildHtml('related') ?>
    <?= $block->getChildHtml('serie_products') ?>
    <?= $block->getChildHtml('upsell') ?>
    <?= $block->getChildHtml('review_list') ?>
    <?= $block->getChildHtml('review_form') ?>
</section>
