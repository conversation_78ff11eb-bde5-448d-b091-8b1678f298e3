<?php

declare(strict_types=1);

use Magento\Framework\Escaper;
use Magento\Theme\Block\Html\Title;

/** @var Escaper $escaper */
/** @var Title $block */

$pageHeading = trim((string)$block->getPageHeading());

if (!$pageHeading) {
    return;
}

?>

<h1 class="heading-3">
    <span class="base" data-ui-id="page-title-wrapper" <?= $block->getAddBaseAttribute() ?>>
        <?= $escaper->escapeHtml($pageHeading) ?>
    </span>
</h1>
<?= $block->getChildHtml() ?>
