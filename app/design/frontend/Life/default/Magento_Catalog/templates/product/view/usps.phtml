<?php

/** @var Redkiwi\WidgetDoorways\Block\Widget\Doorways $block */
/** @var Magento\Framework\Escaper $escaper */

?>

<?php if ($doorways = $block->getDoorways()) : ?>
    <ul class="flex flex-col gap-1">
        <?php /** @var array{image: string, text: array{title: string, subtitle: string}, link: array{url: string, target: string}} $doorway */ ?>
        <?php foreach ($doorways as $doorway) : ?>
            <?php $block->setCurrentDoorway($doorway) ?>
            <li class="flex gap-[0.156rem]">
                <?php if ($link = trim($doorway['link']['url'] ?? '')) : ?>
                    <a class="flex gap-[0.156rem]" href="<?= $escaper->escapeHtmlAttr($link) ?>" target="<?= $escaper->escapeHtmlAttr($doorway['link']['target']) ?>">
                        <?= $block->fetchView((string)$block->getTemplateFile('Life_WidgetUspExtensions::widget/usp/usp-ticker.phtml')) ?>
                    </a>
                <?php else : ?>
                    <?= $block->fetchView((string)$block->getTemplateFile('Life_WidgetUspExtensions::widget/usp/usp-ticker.phtml')) ?>
                <?php endif ?>
            </li>
        <?php endforeach ?>
    </ul>
<?php endif ?>
