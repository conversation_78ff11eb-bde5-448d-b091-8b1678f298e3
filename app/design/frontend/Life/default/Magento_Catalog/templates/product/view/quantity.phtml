<?php

declare(strict_types=1);

use Hyva\Theme\ViewModel\ProductStockItem;
use Magento\Catalog\Model\Product;

// phpcs:disable Generic.WhiteSpace.ScopeIndent.Incorrect

/** @var \Magento\Catalog\Block\Product\View $block */
/** @var \Magento\Framework\Escaper $escaper */
/** @var \Hyva\Theme\Model\ViewModelRegistry $viewModels */

/** @var Product $product */
$product = $block->getProduct();

/** @var ProductStockItem $stockItemViewModel */
$stockItemViewModel = $viewModels->require(ProductStockItem::class);
$minSalesQty = $stockItemViewModel->getMinSaleQty($product);
$maxSalesQty = $stockItemViewModel->getMaxSaleQty($product);

$maxSalesQtyLength  = ($maxSalesQty ? strlen((string) $maxSalesQty) : 4)
    + (/* add one if decimal for separator */
    (int) $stockItemViewModel->isQtyDecimal($product));

$step = $stockItemViewModel->getQtyIncrements($product)
    ? $stockItemViewModel->getQtyIncrements($product)
    : null;

?>
<?php if ($block->shouldRenderQuantity()): ?>
    <script defer="defer">
        function initQtyField() {

            function findPathParam(key) {
                // get all path pairs after BASE_URL/front_name/action_path/action
                const baseUrl = (BASE_URL.substring(0, 2) === '//' ? 'http:' : '') + BASE_URL;
                const baseUrlParts = (new URL(baseUrl)).pathname.replace(/\/$/, '').split('/');
                const pathParts = window.location.pathname.split('/').slice(baseUrlParts.length + 3);
                for (let i = 0; i < pathParts.length; i += 2) {
                    if (pathParts[i] === key && pathParts.length > i) {
                        return pathParts[i + 1];
                    }
                }
            }

            return {
                qty: <?= $block->getProductDefaultQty() * 1 ?>,
                itemId: (new URLSearchParams(window.location.search)).get('id') || findPathParam('id'),
                productId: '<?= (int)$product->getId() ?>',
                <?php /* populate the qty when editing a product from the cart */ ?>
                onGetCartData: function onGetCartData(data, $dispatch) {
                    const cart = data && data.data && data.data.cart;
                    if (this.itemId && cart && cart.items) {
                        const cartItem = cart.items.find((item) => {
                            return item.item_id === this.itemId && item.product_id === this.productId;
                        });
                        if (cartItem && cartItem.qty) {
                            this.qty = cartItem.qty;
                            $dispatch('update-qty-' + this.productId, this.qty);
                        }
                    }
                }
            };
        }
    </script>
    <div x-data="initQtyField()"
        class="">
        <?php if ($product->isSaleable()): ?>
            <div class="mr-2">
                <label for="qty[<?= (int)$product->getId() ?>]"
                    class="sr-only"
                >
                    <?= $escaper->escapeHtml(__('Quantity')) ?>
                </label>
                <input name="qty"
                    @private-content-loaded.window="onGetCartData($event.detail, $dispatch)"
                    id="qty[<?= (int)$product->getId() ?>]"
                    form="product_addtocart_form"
                    <?php if ($stockItemViewModel->isQtyDecimal($product)): ?>
                    type="hidden"
                    pattern="[0-9]+(\.[0-9]{1,<?= /** @noEscape */ $maxSalesQtyLength ?>})?"
                    inputmode="decimal"
                    <?php else: ?>
                    type="hidden"
                    pattern="[0-9]{0,<?= /** @noEscape */ $maxSalesQtyLength ?>}"
                    inputmode="numeric"
                    <?php if ($minSalesQty): ?>min="<?= /** @noEscape */ $minSalesQty ?>"<?php endif; ?>
                    <?php if ($maxSalesQty): ?>max="<?= /** @noEscape */ $maxSalesQty ?>"<?php endif; ?>
                    <?php if ($step): ?>step="<?= /** @noEscape */ $step ?>"<?php endif; ?>
                    <?php endif; ?>
                    :value="qty"
                    class="form-input px-2 py-2 w-20 text-center invalid:ring-2 invalid:ring-red-500"
                    x-model.number="qty"
                    @change="$dispatch('update-qty-<?= (int)$product->getId() ?>', qty)"
                />
            </div>
        <?php endif; ?>
    </div>
<?php endif; ?>
