<?xml version="1.0"?>
<page
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd"
    layout="1column"
>
    <body>
        <referenceBlock name="head.additional">
            <block template="Magento_Theme::head/htmx.phtml" />
        </referenceBlock>
        <referenceBlock name="page.main.title">
            <arguments>
                <argument name="css_class" xsi:type="string">flex justify-center</argument>
            </arguments>
        </referenceBlock>
        <referenceContainer name="content">
            <block
                class="Magento\Catalog\Block\Category\View"
                name="category.bottom.description"
                template="Magento_Catalog::category/bottom-description.phtml"
                after="category.products"
            />
        </referenceContainer>
    </body>
</page>
