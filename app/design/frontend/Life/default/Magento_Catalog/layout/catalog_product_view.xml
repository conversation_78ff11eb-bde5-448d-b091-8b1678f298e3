<?xml version="1.0" ?>
<page
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd"
>
    <update handle="hyva_modal" />
    <update handle="hyva_form_validation" />
    <body>
        <referenceBlock name="page.main.title" template="Magento_Catalog::product/title.phtml" />
        <referenceContainer name="product.info.main">
            <container
                name="product.info.detail"
                label="Product Info Detail"
                htmlTag="section"
                htmlClass="product-info-detail"
                after="product.info.overview"
            />
        </referenceContainer>

        <referenceBlock
            name="related"
            class="Magento\Catalog\Block\Product\View"
            template="Magento_Theme::elements/slider-for-popup.phtml"
        >
            <arguments>
                <argument name="type" xsi:type="string">related</argument>
                <argument name="title" xsi:type="string" translate="true">Recommended products</argument>
            </arguments>
        </referenceBlock>

        <referenceBlock name="product.info">
            <block name="product.quality.marks" template="Magento_Catalog::product/view/quality_marks.phtml" />
            <container name="product.info.bottom" label="Product Info Bottom" htmlTag="div" />
            <block
                name="product.info.usps"
                ifconfig="redkiwi__widget_usp/product_usp/show_usp"
                class="Redkiwi\WidgetDoorways\Block\Widget\Doorways"
                template="Magento_Catalog::product/view/usps.phtml"
            >
                <arguments>
                    <argument name="doorways" xsi:type="helper" helper="Life\CatalogProductUsp\Helper\Config::getDoorways" />
                </arguments>
            </block>
        </referenceBlock>

        <referenceContainer name="before.body.end">
            <block name="ajax-add-to-cart-modal" template="Redkiwi_Popup::product/ajax-add-to-cart-modal.phtml" />
        </referenceContainer>

        <referenceBlock name="product.detail.page">
            <container name="product.info.details.after"/>
            <block
                name="serie_products"
                class="Magento\Catalog\Block\Product\View"
                template="Magento_Catalog::product/slider/offset-product-slider.phtml"
            >
                <arguments>
                    <argument name="type" xsi:type="string">serie</argument>
                    <argument name="heading_tag" xsi:type="string">h2</argument>
                    <argument name="title" xsi:type="string" translate="true">Other sets in this series</argument>
                </arguments>
            </block>
        </referenceBlock>
        <referenceBlock name="upsell">
            <arguments>
                <argument name="title" xsi:type="string" translate="true">Related Products</argument>
                <argument name="heading_tag" xsi:type="string">h2</argument>
            </arguments>
        </referenceBlock>
        <referenceBlock name="product.info.options.configurable">
            <block
                class="Magento\Catalog\Block\Product\Price"
                name="product.flyout.price"
                template="Magento_Catalog::product/view/flyout-price.phtml"
            />
        </referenceBlock>

        <move element="related" destination="ajax-add-to-cart-modal" after="-" />
    </body>
</page>
