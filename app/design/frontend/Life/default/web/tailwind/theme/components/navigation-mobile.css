.mobile-nav {
    height: calc(100vh - 11rem);

    @supports (height: 100dvh) {
        height: calc(100dvh - 11rem);
    }

    &:has(.submenu-open) .main_secondary {
        @apply hidden;
    }

    .customer-menu,
    .customer-label {
        @apply !inline-block;
    }

    .customer-dropdown {
        @apply left-0 right-auto bottom-full;
    }

    &.scrolled-down {
        height: calc(100vh - 8.75rem);

        @supports (height: 100dvh) {
            height: calc(100dvh - 8.75rem);
        }
    }

    /* disable overflow when submenu it active */
    .mobile-nav-inner:has(.submenu-active) {
        @apply overflow-hidden;
    }

    .main {
        .__list.submenu-open {
            @apply overflow-visible;
        }
    }

    .__list {
        @apply relative left-0 px-3;
        @apply motion-safe:transition-all motion-safe:duration-300 motion-safe:ease-in-out;
        @apply motion-reduce:delay-[10ms];

        &.submenu-open {
            @apply -left-full;
        }

        .__item {
            @apply border-b border-secondary-900/50 last:border-none;

            &.__item--parent {
                .__link {
                    @apply pointer-events-none;
                }

                &.submenu-active {
                    .__inner-list--level1 {
                        @apply opacity-100 pointer-events-auto delay-0;
                    }
                }
            }

            .__inner-list {
                @apply !columns-auto !w-full;
            }
        }

        .__item.collection {
            @apply pointer-events-none;

            > span {
                @apply hidden;
            }

            .__inner-list {
                &--level1 {
                    @apply relative left-0 h-auto p-0 overflow-hidden opacity-100;

                    &.subsubmenu-open {
                        @apply overflow-visible;
                    }

                    > li:first-child {
                        @apply hidden;
                    }
                }

                &--level2 {
                    @apply !w-[calc(100%+1.5rem)] left-[calc(100%+0.75rem)];
                }

                &--level3 {
                    @apply hidden;
                }
            }

            .__inner-item {
                &--level1 {
                    .__inner-item-wrapper {
                        @apply pointer-events-auto;
                    }

                    &.subsubmenu-active {
                        .__inner-list--level3 {
                            @apply block;
                        }
                    }
                }
            }
        }

        .__item:not(.collection) {
            .__inner-list {
                .__inner-item {
                    &--level1 {
                        .__inner-link {
                            @apply pl-4;
                        }

                        &.break-column {
                            .__inner-link {
                                @apply pl-0;
                            }
                        }
                    }
                }
            }
        }

        .__item.gardenstyles {
            .__inner-list {
                .__inner-item {
                    @apply mb-2 border-none;

                    .__inner-link {
                        @apply flex items-center w-full p-6 bg-white border rounded-xl border-secondary-900/50 text-base leading-[1.875rem] relative overflow-hidden h-16;
                    }

                    img {
                        @apply absolute w-20 h-20 p-0 rounded-full -left-4 -top-2;
                    }

                    span.title {
                        @apply text-base pl-14;
                    }

                    &.hide-mobile {
                        @apply max-md:hidden;
                    }

                    &.all-gardenstyles {
                        @apply w-full mt-4;

                        .__inner-item-wrapper {
                            span {
                                @apply justify-center;

                                .__inner-link {
                                    @apply w-full btn btn-secondary;
                                }
                            }
                        }

                        .__inner-link {
                            @apply justify-center h-auto p-0 bg-transparent border-none;

                            span.title {
                                @apply pl-0;
                            }
                        }
                    }
                }
            }
        }
    }

    .__inner-list {
        @apply w-full !min-h-0;

        &--level1,
        &--level2 {
            @apply absolute top-0 px-3 overflow-x-hidden overflow-y-auto opacity-0 pointer-events-none left-full;
            @apply motion-safe:transition-all motion-safe:duration-300 motion-safe:ease-in-out motion-safe:delay-300;
            @apply motion-reduce:delay-[10ms];
            height: calc(calc(100vh - 14.8rem) - var(--tickerVisibileHeight));

            @supports (height: 100dvh) {
                height: calc(calc(100dvh - 14.8rem) - var(--tickerVisibileHeight));
            }
        }

        &--level1 {
            &.subsubmenu-open {
                @apply left-0 overflow-visible;
            }
        }

        &--level2 {
            @apply flex flex-col gap-x-3;
        }

        &--level3 {
            button {
                @apply hidden;
            }
        }

        .__inner-item {
            @apply border-b border-secondary-900/50 last:border-none;

            &--level1 {
                @apply flex items-center gap-x-3;

                &.__inner-item--parent {
                    &.subsubmenu-active {
                        .__inner-list--level2 {
                            @apply opacity-100 pointer-events-auto delay-0;
                        }
                    }
                }

                > .__inner-item-wrapper {
                    > span {
                        > .__inner-link {
                            @apply flex items-center gap-x-2;
                        }
                    }
                }

                .__inner-link {
                    @apply order-2;

                    > img {
                        @apply w-10;
                    }
                }

                svg {
                    @apply order-3;
                }
            }

            &--level2 {
                > span {
                    @apply hidden;
                }
            }

            &--level3 {
                .__inner-link {
                    @apply pl-4;
                }

                &.break-column,
                &[class*="font-bold"] {
                    .__inner-link {
                        @apply pl-0;
                    }
                }
            }

            &--parent {
                > span {
                    > .__inner-link {
                        @apply pointer-events-none;
                    }
                }
            }
        }

        .doorways {
            @apply py-6 -mx-3 bg-secondary-300;

            [data-content-type='column-line'] {
                @apply gap-6;
            }

            [data-content-type='column']:nth-child(2n + 1) {
                @apply basis-[calc(40%-0.75rem)];
            }

            [data-content-type='column']:nth-child(2n + 2) {
                @apply basis-[calc(60%-0.75rem)] !justify-center;
            }

            figure[data-content-type='image'] {
                img {
                    @apply p-0 rounded-full;
                }
            }

            [data-content-type='text'] {
                p {
                    @apply mb-1;

                    a {
                        &:before,
                        &:after {
                            @apply hidden;
                        }

                        &:hover {
                            @apply underline;
                        }
                    }
                }
            }
        }
    }

    .__link,
    .__inner-link {
        @apply block w-full py-4;
    }

    .__bg-dropdown {
        @apply hidden;
    }

    .__inner-item--all {
        @apply !hidden;
    }

    .main_secondary {
        @apply border-t border-secondary-900/50;
    }
}
