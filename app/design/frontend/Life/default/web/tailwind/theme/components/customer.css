.account {
    .page-main {
        @apply my-12;
    }

    .columns {
        @apply container;
    }
}

.customer-account-edit,
.customer-address-form {
    .form {
        .form-input,
        .form-select {
            @apply w-full;
        }

        .postcode-warning {
            @apply mt-1 text-xs text-red-500;
        }
    }
}

.account-nav {
    ul {
        li {
            a,
            span.active {
                @apply inline-flex justify-between mb-4 text-sm leading-5;
            }

            &.authorization-link {
                a {
                    @apply flex items-center justify-between mb-0;
                }
            }
        }
    }
}

.actions-toolbar {
    @apply inline-flex items-center justify-start mt-6 gap-x-4;
}


@layer components {
    .actions-toolbar .primary button {
        @apply btn btn-primary;
    }
}

.customer-account-login,
.customer-account-forgotpassword,
.customer-account-createpassword,
.customer-account-create,
.customer-address-form {

    .page-wrapper {
        @apply relative overflow-x-hidden;
    }

    .page-main {
        @apply container my-12;
    }

    form {
        .field {
            @apply mb-6;

            .label {
                @apply text-sm;
            }
        }

        .form-input {
            @apply w-full;
        }
    }

    #password-strength-meter-container {
        @apply w-full mt-2 text-xs;
    }

    .field-error {
        .messages {
            @apply text-xs;
        }
    }
}

.customer-account-login {
    #customer-login-container {
        @apply block max-w-xl mx-auto card lg:p-8;
    }

    .account-usps {
        @apply text-sm;
        
        li {
            @apply flex items-center justify-center mb-3 gap-x-2;
        }
    }
}

.customer-account-login,
.customer-account-createpassword,
.customer-account-create {
    fieldset {
        .control {
            @apply relative;
        }

        .form-input + button {
            @apply absolute top-0 right-0 -translate-y-0.5;
        }
    }
}