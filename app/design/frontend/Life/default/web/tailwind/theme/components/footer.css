.page-footer {
    @apply container pb-3 md:pb-6 lg:pb-12;

    .footer-menu {
        @apply w-full;
        
        .__list {
            @apply lg:justify-between lg:flex lg:gap-x-4;

            .footer-menu__item--parent {
                @apply text-sm lg:w-1/5;
            }
        }

        .__item {
            @apply mb-5 lg:mb-0;
            
            button {
                @apply relative w-full pb-3 mb-3 text-left lg:pb-0 lg:pointer-events-none lg:w-auto max-lg:border-b border-primary-100/40;
                font-weight: bold;

                &.menu-open {
                    svg {
                        @apply transform rotate-180;
                    }
                }

                &:focus-visible {
                    @apply rounded-sm outline outline-1 outline-white/50 outline-offset-2 before:hidden;
                }
            }
        }

        &__inner-list--level1 {
            @apply lg:!block mb-8 lg:mb-0;
        }

        &__inner-item--level1 {
            @apply mb-4;

            &.view-all {
                .footer-menu__inner-link {
                    @apply after:content-[''];

                    &::after {
                        @apply absolute left-0 block w-full h-px bg-white -bottom-0.5;
                        transition: 1.1s var(--ease-out-expo);
                        transform-origin: right;
                        transition-delay: 0.25s;

                        @media (prefers-reduced-motion: reduce) {
                            transition: none !important;
                        }
                    }

                    &:hover {
                        &::before {
                            transition-delay: 0.25s;
                        }

                        &::after {
                            transform: scaleX(0);
                            transition-delay: 0s;
                        }
                    }

                    &:focus-visible {
                        @apply after:hidden;
                    }
                }
            }
        }

        &__inner-link {
            @apply relative before:content-[''];
    
            &::before {
                @apply absolute left-0 block w-full h-px bg-white -bottom-0.5;
                transition: 1.1s var(--ease-out-expo);

                @media (prefers-reduced-motion: reduce) {
                    transition: none !important;
                }
            }

            &::before {
                transform: scaleX(0);
                transform-origin: left;
            }
    
            &:hover {
                &::before {
                    transform: scaleX(1);
                }
            }

            &:focus-visible {
                @apply rounded-sm outline outline-1 outline-white/50 outline-offset-2 before:hidden;
            }
        }
    }

    .footer-social,
    .footer-trustmarks {
        @apply lg:w-[calc(20%-0.75rem)] border border-primary-100/40 rounded-xl p-3 text-xs flex;

        .__list {
            @apply flex flex-wrap gap-4 gap-y-2 lg:gap-2 xl:gap-3;
        }

        .__item {
            @apply flex items-end;
            
            &.footer-title {
                @apply items-start w-full mb-4;
                font-weight: bold;

                .title {
                    @apply block;
                }
            }

            .title {
                @apply hidden;
            }
        }

        &__link {
            @apply block;
            
            &:focus-visible {
                @apply rounded-sm outline outline-1 outline-white/50 outline-offset-2;
            }
        }
    }

    .footer-social {
        &__link {
            img {
                @apply w-8;
            }
        }
    }

    .footer-trustmarks {
        &__link {
            img {
                @apply w-8 xl:w-10;
            }
        }
    }

    .footer-newsletter {
        @apply lg:w-[calc(40%-0.75rem)] flex;

        .__list {
            @apply flex flex-col w-full lg:flex-row gap-y-2 lg:gap-x-4;
        }

        .__item {
            @apply flex w-full p-3 text-sm border lg:w-1/2 border-primary-100/40 rounded-xl;
        }

        &__link,
        button {
            @apply hidden;
        }

        &__inner-list--level1 {
            @apply !flex flex-col lg:justify-between;
        }

        &__inner-item--level1 {
            &.footer-title {
                @apply mb-4 text-xs;
                font-weight: bold;
            }
        }

        a.footer-newsletter__inner-link {
            &:hover {
                @apply underline;
            }

            &:focus-visible {
                @apply rounded-sm outline outline-1 outline-white/50 outline-offset-2 before:hidden;
            }
        }
    }

    .column {
        @apply lg:w-[calc(20%-0.75rem)] flex items-end;

        label {
            @apply block mt-4 mb-4 text-xs font-bold lg:mt-0;
        }

        select {
            @apply text-white bg-transparent border border-primary-100/40 rounded-xl px-3 py-1.5 w-full min-h-0;

            &:focus-visible {
                @apply border-white;
            }

            option {
                @apply bg-primary;
            }
        }
    }

    .footer-privacy {
        &__list {
            @apply flex flex-wrap items-center gap-y-2 gap-x-8;

            a {
                @apply relative before:content-[''];
    
                &::before {
                    @apply absolute left-0 block w-full h-px bg-black -bottom-0.5;
                    transition: 1.1s var(--ease-out-expo);
                }
    
                &::before {
                    transform: scaleX(0);
                    transform-origin: left;
                }
        
                &:hover {
                    &::before {
                        transform: scaleX(1);
                    }
                }
    
                &:focus-visible {
                    @apply rounded-sm outline outline-1 outline-white/50 outline-offset-2 before:hidden;
                }
            }
        }
    }

    .footer-payment {
        &__list {
            @apply flex items-center gap-x-2;
        }

        &__link {
            @apply flex items-center justify-center w-8 h-6 p-1 border rounded border-secondary-900/50;

            .title {
                @apply sr-only;
            }
        }
    }
}

.page-bottom {
    .contact-section {
        @apply pb-6 rounded-b-none mx-[var(--container-padding)];
    }
}
