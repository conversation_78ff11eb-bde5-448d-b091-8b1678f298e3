.desktop-nav {
    .__list {
        @apply flex items-center md:gap-x-4 lg:gap-x-10;

        /**
        * General styles
        */
        &.show-dropdown {
            .__item--parent {
                &:hover {
                    .__inner-list--level1 {
                        @apply top-full;
                    }

                    .__bg-dropdown {
                        @apply top-full;
                    }

                    .__bg-dropdown-overlay {
                        @apply opacity-100;
                    }
                }
            }
        }

        .__item {
            .__inner-item {
                @apply py-3;
            }

            .__link {
                @apply block py-3 text-sm;
            }

            .__inner-link {
                @apply text-sm;
            }
            
            a.__inner-link {
                &:hover {
                    @apply underline;
                }
            }
        }

        .__bg-dropdown {
            @apply absolute left-0 w-full bg-white -top-[100vh] -z-20 rounded-b-3xl;
            @apply motion-safe:transition-all motion-safe:duration-500 motion-safe:ease-in-out;
            @apply motion-reduce:delay-[10ms];
        }

        .__bg-dropdown-overlay {
            @apply absolute left-0 right-0 h-screen opacity-0 pointer-events-none bg-black/40 -z-30;
            @apply motion-safe:transition-all motion-safe:duration-500 motion-safe:ease-in-out;
            @apply motion-reduce:delay-[10ms];
        }

        /**
        * Collection dropdown 
        */
        .__item.collection {
            .__inner-list {
                @apply absolute left-0 right-0;
                @apply motion-safe:transition-all motion-safe:duration-500 motion-safe:ease-in-out;
                @apply motion-reduce:delay-[10ms];
    
                &--level1 {
                    @apply container -top-[100vh] -z-10 min-h-screen-50 !columns-auto !w-full pt-4;

                    .__inner-item {
                        &--level1 {
                            @apply flex items-center w-1/4 p-0 2xl:w-1/5 gap-x-4;

                            > .__inner-item-wrapper {
                                @apply relative p-2 mb-2 bg-white rounded-full;
                                @apply motion-safe:transition-all motion-safe:duration-300 motion-safe:ease-in-out;
                                @apply motion-reduce:delay-[10ms];
                            }

                            > .__inner-item-wrapper {
                                > span {
                                    > .__inner-link {
                                        @apply flex items-center p-0 gap-x-2;
                                    }
                                }
                            }

                            img {
                                @apply flex-shrink-0;
                            }

                            svg {
                                @apply order-3 lg:mr-4;
                            }

                            .__inner-link {
                                @apply text-base leading-[1.875rem] order-2 z-10;

                                word-break: break-word;
                            }
                        }

                        &--level2 {
                            @apply p-0;

                            .__inner-link {
                                @apply text-sm;
                            }
                        }
                    }
                }
    
                &--level2 {
                    @apply container top-0 w-3/4 2xl:w-4/5 p-0 pl-12 opacity-0 pointer-events-none left-1/4 2xl:left-[20%] min-h-screen-50 !columns-auto flex flex-col;

                    .__inner-item {
                        @apply break-inside-avoid-column;

                        &--level2 {
                            &:not(.doorways) {
                                @apply flex-grow py-4 pl-6;
                            }

                            &.doorways {
                                @apply border-l bg-secondary-300 border-secondary-900/50 rounded-br-3xl;

                                .__inner-item--level3 {
                                    @apply py-10;
                                }

                                .__inner-list--level3 {
                                    @apply !w-auto;
                                }

                                [data-content-type='row'] {
                                    @apply max-w-6xl;
                                }

                                [data-content-type='column-line'] {
                                    @apply gap-x-6;
                                }

                                [data-content-type='column'] {
                                    @apply !justify-center;
                                }

                                figure[data-content-type='image'] {
                                    a {
                                        @apply flex justify-center;
                                    }

                                    img {
                                        @apply rounded-full xl:!max-w-[11.75rem];
                                        clip-path: circle();
                                    }
                                }

                                [data-content-type='text'] {
                                    p {
                                        @apply mb-1;

                                        a {
                                            &:before,
                                            &:after {
                                                @apply hidden;
                                            }

                                            &:hover {
                                                @apply underline;
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        
                        &.break-column {
                            @apply break-before-column;
                        }
                    }
                }

                &--level3 {
                    @apply relative max-lg:!w-full;
                }
            }

            .__inner-item--level1 {
                > .__inner-item-wrapper:hover {
                    @apply bg-secondary-500 before:content-[''];

                    &:before {
                        @apply w-full h-48 absolute -right-[2.3125rem] lg:-right-[1.625rem] 2xl:-right-5 top-1/2 z-1 -translate-y-1/2;

                        -webkit-clip-path: polygon(0 50%,100% 100%,100% 0);
                        clip-path: polygon(0 50%,100% 100%,100%0);
                    }

                    + script + .__inner-list--level2 {
                        @apply opacity-100 pointer-events-auto;
                    }
                }

                &:last-child {
                    > .__inner-item-wrapper:hover {
                        &:before {
                            @apply bottom-0 top-auto transform-none;

                            -webkit-clip-path: polygon(0 100%,100% 100%,100% 0);
                            clip-path: polygon(0 100%,100% 100%,100%0);
                        }
                    }
                }

                &:hover {
                    > .__inner-item-wrapper {
                        @apply bg-secondary-500;
                    }
                    
                    .__inner-list--level2 {
                        @apply opacity-100 pointer-events-auto;
                    }
                }
            }

            .__bg-dropdown {
                &:after {
                    @apply absolute top-0 w-px h-full pointer-events-none bg-secondary-900/50 left-[calc(25%+3rem)] 2xl:left-[calc(20%+3rem)];
                    content: '';
                }
            }
        }

        /**
        * Simple dropdown 
        */
        .__item:not(.collection) {
            .__inner-list {
                @apply absolute -top-[100vh] -z-10 pt-6 pb-10 max-xl:left-6 !w-[calc(100%-3rem)];
                @apply motion-safe:transition-all motion-safe:duration-500 motion-safe:ease-in-out;
                @apply motion-reduce:delay-[10ms];
            }

            .__inner-item {
                @apply break-inside-avoid-column;

                &.break-column {
                    @apply break-before-column;
                }
            }
        }

        /**
        * Garden styles dropdown 
        */
        .__item.gardenstyles {   
            .__inner-list {
                @apply left-0 !w-full;
    
                &--level1 {
                    @apply container flex flex-wrap gap-6 py-12;
                }

                .__inner-item {
                    &--level1 {
                        @apply w-full md:w-[calc(50%-0.75rem)] lg:w-[calc(25%-1.125rem)] p-0;

                        .__inner-item-wrapper {
                            @apply block;
                        }

                        .__inner-link {
                            @apply flex items-center w-full p-6 bg-white border rounded-xl border-secondary-900/50 text-base leading-[1.875rem] relative overflow-hidden h-24 xl:h-32;

                            img {
                                @apply absolute rounded-full w-36 lg:w-32 xl:w-44 -left-6 lg:-left-12 -top-6 lg:-top-4 xl:-top-6;
                            }

                            span.title {
                                @apply md:pl-32 lg:pl-[4.5rem] xl:pl-[7.5rem] text-sm xl:text-base;
                            }
                        }
                    }

                    &.all-gardenstyles {
                        @apply w-full;

                        .__inner-item-wrapper {
                            span {
                                @apply justify-center;

                                .__inner-link {
                                    @apply w-auto px-20 btn btn-secondary;
                                }
                            }
                        }

                        .__inner-link {
                            @apply h-auto p-0 bg-transparent border-none;

                            span.title {
                                @apply pl-0;
                            }
                        }
                    }
                }
            }

            .__bg-dropdown {
                @apply bg-secondary-300;
            }
        }
    }

    .__inner-item--all {
        @apply !hidden;
    }
}
