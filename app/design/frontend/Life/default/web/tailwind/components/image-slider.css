/* Image Slider */
[data-appearance='image_slider'] {
    [data-appearance='category-doorway'] {
        &[data-slide-width='narrow'] {
            @apply md:w-[18rem];

            img {
                @apply aspect-[1/1.2] md:aspect-[1/1.5];
            }
        }

        &[data-slide-width='wide'] {
            @apply md:w-[36rem];

            img {
                @apply aspect-square md:aspect-[1/0.75];
            }
        }

        [data-element='empty_link'] {
            @apply max-md:flex max-md:justify-center;
        }

        img {
            @apply object-cover rounded-xl max-md:w-full;
        }
    }
}

.image_slider {
    @apply max-lg:!p-4 lg:!py-8 xl:!py-12 bg-primary rounded-xl flex max-lg:flex-col relative;

    h3 {
        @apply max-lg:order-2 max-lg:!mb-6 max-lg:mt-16 max-lg:text-center lg:absolute lg:h-full lg:top-0 lg:z-10 lg:w-1/3 !mb-0 text-white lg:px-8 xl:px-12 lg:flex lg:items-end lg:pb-32 lg:pointer-events-none;
    }

    .swiper {
        @apply lg:pl-[33.33%] max-lg:w-full max-lg:pb-20 w-full;
    }

    .swiper-container {
        @apply overflow-hidden;
    }

    .swiper-wrapper {
        @apply max-lg:items-end;
    }

    .swiper-nav {
        .swiper-button-next,
        .swiper-button-prev {
            @apply text-white;
        }
    }

    .swiper-nav {
        @apply left-1/2 max-lg:transform max-lg:-translate-x-1/2 bottom-px lg:left-8 xl:left-12 lg:max-w-[calc(33.33%-4rem)];
    }

    .swiper-pagination {
        @apply flex-wrap gap-y-4 min-w-[6.25rem];
    }

    [data-element="empty_link"] {
        picture.pagebuilder-doorway__image:not(:only-child) {
            @apply hidden lg:block;
        }

        picture.pagebuilder-doorway__hover-image {
            @apply lg:hidden;
        }
    }
}

[data-appearance="category-doorway"] .image-wrapper {
    @apply max-h-80;
}
