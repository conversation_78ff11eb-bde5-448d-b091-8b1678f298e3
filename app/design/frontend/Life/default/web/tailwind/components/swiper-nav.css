.swiper-nav {
    @apply absolute z-10 flex items-center justify-center gap-x-6;

    .swiper-button-prev,
    .swiper-button-next {
        @apply relative flex-shrink-0 w-12 h-12 text-black rounded-full;
        
        &.swiper-button-disabled {
            @apply !opacity-25 cursor-default;
        }

        &:after {
            @apply hidden;
        }
    }

    .swiper-button-prev {
        @apply left-auto;
    }

    .swiper-button-next {
        @apply right-auto;
    }

    .swiper-pagination {
        @apply relative flex justify-center px-2 select-none gap-x-4 ;
        
        .swiper-pagination-bullet {
            @apply block !w-1.5 !h-1.5 bg-white rounded-full cursor-pointer opacity-100 !m-0;
        
            &-active {
                @apply outline outline-white outline-1 outline-offset-8;
            }
        }
    }
}
