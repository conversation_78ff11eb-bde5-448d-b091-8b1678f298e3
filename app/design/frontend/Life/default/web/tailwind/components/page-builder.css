/**
 * Layout
 */

 :root {
    --ease-out-expo: cubic-bezier(0.19, 1, 0.22, 1);
 }

/* Rows */
[data-content-type='row'] {
    @apply box-border;

    &[data-appearance='contained'] {
        @apply container;
        @apply box-border ml-auto mr-auto;

        [data-element='inner'] {
            @apply box-border;
            background-attachment: scroll !important;
        }
    }

    &[data-appearance='full-bleed'] {
        background-attachment: scroll !important;
    }

    &[data-appearance='full-width'] {
        background-attachment: scroll !important;

        > .row-full-width-inner {
            @apply w-full mx-auto;
        }
    }
}

/* Column Groups (both needed for pagebuider backwards compatibiliy) */
[data-content-type='column-group'], [data-content-type='column-line'] {
    @apply flex-wrap md:flex-nowrap;
}

/* Columns */
[data-content-type='column'] {
    @apply box-border w-full max-w-full;
    background-attachment: scroll !important;
    flex-basis: 100%;

    @screen md {
        flex-basis: auto;
    }
}

/* Tabs/Tab Item */
[data-content-type='tabs'] {
    .tabs-navigation {
        @apply block p-0 -mb-px shadow;

        li.tab-header {
            @apply relative inline-block max-w-full my-0 mr-0 -ml-px break-words bg-gray-100 border border-b-0 border-gray-300 border-solid rounded-bl-none rounded-br-none;

            &:first-child {
                @apply ml-0;
            }

            &.active {
                @apply z-20 bg-white;
                border-bottom: 1px solid white;
            }

            a.tab-title {
                @apply relative block px-5 py-3 text-sm font-semibold whitespace-normal align-middle cursor-pointer;

                span {
                    @apply font-semibold leading-5 text-black;
                }
            }
        }
    }

    .tabs-content {
        @apply box-border relative z-10 overflow-hidden border border-gray-300 border-solid rounded-sm shadow;

        [data-content-type='tab-item'] {
            @apply box-border p-8;
            min-height: inherit;
            background-attachment: scroll !important;
        }
    }

    &.tab-align-left {
        .tabs-content {
            border-top-left-radius: 0 !important;
        }
    }

    &.tab-align-right {
        .tabs-content {
            border-top-right-radius: 0 !important;
        }
    }
}

/**
 * Elements
 */

 @layer components {
    [data-content-type='row'] {
        h1, h2, h3, h4, h5, h6 {
            @apply mb-2;
        }

        ul, ol,
        table,
        p {
            @apply mb-6;
        }

        ol, ul {
            @apply pl-5;
        }

        ol {
            @apply list-decimal;
        }

        ul {
            @apply list-disc;
        }

        ul,
        ol {
            li:not(:last-child) {
                @apply mb-2;
            }
        }

        p,
        li {
            a:not(.btn) {
                @apply underline underline-offset-[0.375em] hover:text-primary;

                @screen md {
                    @media (hover: hover) and (pointer: fine) {
                        @apply inline-block no-underline relative before:content-[''] after:content-[''];

                        &::before,
                        &::after {
                            @apply absolute left-0 block w-full h-px bg-black -bottom-0.5;
                            transition: 1.1s var(--ease-out-expo);
                        }
                        &::before {
                            transform: scaleX(0);
                            transform-origin: left;
                        }
        
                        &::after {
                            transform-origin: right;
                            transition-delay: 0.25s;
                        }
        
                        &:hover {
                            &::before {
                                transform: scaleX(1);
                                transition-delay: 0.25s;
                            }
        
                            &::after {
                                transform: scaleX(0);
                                transition-delay: 0s;
                            }
                        }
                    }
                }


                &:focus-visible {
                    @apply rounded-sm outline outline-1 outline-offset-2 outline-primary/50 before:hidden after:hidden;
                }
            }
        }

        table {
            tr:nth-child(odd) {
                @apply bg-secondary
            }

            td {
                @apply py-1.5 px-3;
            }
        }
    }
}

/* Text */
[data-content-type='text'] {
    @apply break-words;

    img {
        @apply rounded-xl;
    }

    table {
      @apply block overflow-x-auto;
    }
}

/* Heading */
[data-content-type='heading'] {
    @apply break-words;
}

/* Buttons/Button Item */
[data-content-type='buttons'] {
    @apply grid gap-2;

    [data-content-type='button-item'] {
        [data-element='link'],
        [data-element='empty_link'] {
            @apply max-w-full break-words;
        }

        [data-element='empty_link'] {
            @apply cursor-default;
        }

        a,
        button,
        div {
            @apply inline-block;

            &.pagebuilder-button-link {
                @apply box-border;
            }
        }
    }
}

a,
button,
div {
    &.pagebuilder-button-primary {
        @apply btn btn-primary;
    }
    &.pagebuilder-button-secondary {
        @apply btn btn-secondary;
    }
}

/* HTML Code */
[data-content-type='html'] {
    @apply break-words;

    iframe {
        @apply rounded-xl;
    }
}

/**
 * Media
 */

/* Image */
[data-content-type='image'] {
    @apply box-border;

    > [data-element='link'],
    > [data-element='link'] img,
    > img {
        @apply rounded-xl;
    }

    .pagebuilder-mobile-hidden {
        @apply hidden md:block;
    }

    .pagebuilder-mobile-only {
        @apply md:hidden;
    }

    figcaption {
        @apply break-words;
    }
}

/* Video */
[data-content-type='video'] {
    font-size: 0;

    .pagebuilder-video-inner {
        @apply box-border inline-block w-full;
    }

    .pagebuilder-video-container {
        @apply relative overflow-hidden;
        border-radius: inherit;
        padding-top: 56.25%;
    }

    iframe,
    video {
        @apply absolute top-0 left-0 w-full h-full rounded-xl;
    }
}

/* Map */
[data-content-type='map'] {
    @apply box-border h-72;
}

[data-content-type='column'] {
    [data-content-type='map'] {
        @apply md:h-full;
    }
}

/**
 * Add Content
 */

/* Block */
[data-content-type$='block'] {
    .block {
        p:last-child {
            @apply mt-0 mb-4;
        }
    }
}

/* Fix for block in row */
[data-content-type='row'] [data-content-type='block'] [data-content-type='row']:not([data-appearance="blog-doorways"]) {
    margin: 0;
    padding: 0;
}


/* Dynamic Block */
[data-content-type='dynamic_block'] {
    [data-content-type='image'] img {
        @apply inline;
    }

    .block-banners .banner-item-content,
    .block-banners-inline .banner-item-content {
        @apply mb-auto;
    }
}

/* Products */
[data-content-type='products'][data-appearance='carousel'] {
    [data-role='glider-content'] {
        grid-template-columns: repeat(100, calc(50% - 1rem));

        @screen md {
            grid-template-columns: repeat(100, calc(33% - 1rem));
        }

        @screen lg {
            grid-template-columns: repeat(100, calc(25% - 1rem));
        }
    }
}

/**
 * Glider
 */

.glider-contain {
    @apply relative w-full mx-auto my-0;
}

.glider {
    @apply overflow-y-hidden;

    &.draggable {
        @apply select-none;
        cursor: grab;

        .glider-slide img {
            @apply pointer-events-none select-none;
        }
    }

    &.drag {
        cursor: grabbing;
    }

    &::-webkit-scrollbar {
        @apply h-0 opacity-0;
    }

    .glider-track {
        @apply z-10 flex w-full p-0 m-0;
    }
}

.glider-slide {
    @apply content-center justify-center w-full select-none;
    min-width: 150px;

    img {
        @apply max-w-full;
    }
}

@media (max-width: 36em) {
    .glider::-webkit-scrollbar {
        @apply w-2 h-1 opacity-100 appearance-none;
    }

    .glider::-webkit-scrollbar-thumb {
        @apply opacity-100;
        border-radius: 99px;
        background-color: rgba(156, 156, 156, .25);
        -webkit-box-shadow: 0 0 1px rgba(255, 255, 255, .25);
        box-shadow: 0 0 1px rgba(255, 255, 255, .25)
    }
}

[data-show-arrows='false'] {
    .glider-prev,
    .glider-next {
        @apply hidden;
    }
}

[data-show-dots='false'] {
    .glider-dots {
        @apply hidden;
    }
}

[data-content-type="banner"]>.pagebuilder-banner-main-wrapper{
    @apply bg-none my-1.5;
}

.pagebuilder-doorways-button {
    span {
        @apply flex items-center after:inline-block after:w-4 after:h-4 after:ml-3;
    }
}

[data-background-color-type="dark"] {
    @apply text-white bg-primary;

    .pagebuilder-overlay::after {
        @apply opacity-90;
    }

    [data-appearance="blog"],
    [data-appearance="usp"] {
        @apply text-secondary-300;
    }

    [data-element="content"] p a:not(.btn) {
        @apply hover:text-inherit;
        @apply focus-visible:text-inherit;

        &::before,
        &::after {
            @apply bg-current;
        }
    }

    .pagebuilder-doorways-button {
        span {
            @apply after:content-[url('../svg/chevron-right-primary.svg')];
        }

        &:hover {
            span {
                @apply md:after:content-[url('../svg/chevron-right-white.svg')];
            }
        }
    }
}

[data-background-color-type="light"],
[data-background-color-type="white"] {
    .pagebuilder-overlay::after {
        @apply opacity-20;
    }

    [data-appearance="blog"],
    [data-appearance="usp"] {
        @apply text-primary;
    }

    .pagebuilder-doorways-button {
        span {
            @apply after:content-[url('../svg/chevron-right-white.svg')];
        }

        &:hover {
            span {
                @apply md:after:content-[url('../svg/chevron-right-primary.svg')];
            }
        }
    }
}

[data-background-color-type="light"] {
    @apply bg-secondary;
}

[data-background-color-type="white"] {
    @apply bg-white;
}

[data-background-color-type="transparent"]{
    @apply bg-transparent;

    .pagebuilder-overlay::after {
        @apply opacity-10;
    }
}

[data-margin-orientation="both"][data-margin-size="small"] {
    @apply m-2 md:m-4;
}

[data-margin-orientation="both"][data-margin-size="medium"] {
    @apply m-4 md:m-8;
}

[data-margin-orientation="both"][data-margin-size="large"] {
    @apply m-8 md:m-16;
}

[data-margin-orientation="horizontal"][data-margin-size="small"] {
    @apply mx-2 md:mx-4;
}

[data-margin-orientation="horizontal"][data-margin-size="medium"] {
    @apply mx-4 md:mx-8;
}

[data-margin-orientation="horizontal"][data-margin-size="large"] {
    @apply mx-8 md:mx-16;
}

[data-margin-orientation="vertical"][data-margin-size="small"] {
    @apply my-2 md:my-4;
}

[data-margin-orientation="vertical"][data-margin-size="medium"] {
    @apply my-4 md:my-8;
}

[data-margin-orientation="vertical"][data-margin-size="large"] {
    @apply my-8 md:my-16;
}

[data-padding-orientation="both"][data-padding-size="small"] {
    @apply p-2 md:p-4;
}

[data-padding-orientation="both"][data-padding-size="medium"] {
    @apply p-4 md:p-8;
}

[data-padding-orientation="both"][data-padding-size="large"] {
    @apply p-8 md:p-16;
}

[data-padding-orientation="horizontal"][data-padding-size="small"] {
    @apply px-2 md:px-4;
}

[data-padding-orientation="horizontal"][data-padding-size="medium"] {
    @apply px-4 md:px-8;
}

[data-padding-orientation="horizontal"][data-padding-size="large"] {
    @apply px-8 md:px-16;
}

[data-padding-orientation="vertical"][data-padding-size="small"] {
    @apply py-2 md:py-4;
}

[data-padding-orientation="vertical"][data-padding-size="medium"] {
    @apply py-4 md:py-8;
}

[data-padding-orientation="vertical"][data-padding-size="large"] {
    @apply py-8 md:py-16;
}

[data-border-radius-type="medium"],
[data-border-radius-type="medium"] [data-element="empty_link"],
[data-border-radius-type="medium"] [data-element="link"]{
    @apply overflow-hidden rounded-md;
}

[data-border-radius-type="large"],
[data-border-radius-type="large"] [data-element="empty_link"],
[data-border-radius-type="large"] [data-element="link"]{
    @apply overflow-hidden rounded-xl;
}

[data-background-style^="style_"] .pagebuilder-overlay::after {
    content: '';
    @apply w-3/4 h-full absolute z-0 top-0 blur-sm bg-[length:450px_450px] bg-no-repeat;
}

.pagebuilder-collage-content a:not(.pagebuilder-banner-button) {
    @apply w-full text-center;

    @screen md {
        @apply w-auto text-left;
    }
}

.pagebuilder-collage-content .btn span {
    @apply w-full text-center max-sm:inline;

    @screen md {
        @apply text-left;
    }
}

[data-background-style="style_1"] .pagebuilder-overlay::after {
    @apply bg-style1;
}

[data-background-style="style_2"] .pagebuilder-overlay::after {
    @apply bg-style2;
}

[data-background-style="style_3"] .pagebuilder-overlay::after {
    @apply bg-style3;
}

[data-background-style="style_4"] .pagebuilder-overlay::after {
    @apply bg-style4;
}

[data-background-style="style_5"] .pagebuilder-overlay::after {
    @apply bg-style5;
}

[data-background-style="style_6"] .pagebuilder-overlay::after {
    @apply bg-style6;
}

[data-background-style="style_7"] .pagebuilder-overlay::after {
    @apply bg-style7;
}

[data-background-style="style_8"] .pagebuilder-overlay::after {
    @apply bg-style8;
}

.contact-section ul {
    @apply p-0 m-0 list-none;
}

.contact-section ul li {
    @apply p-0;
}

.faq-content {
    > div {
        > [data-content-type="row"][data-appearance="contained"] {
            @apply px-0;

            [data-content-type="text"] {
                p {
                    &:last-child {
                        @apply mb-0;
                    }
                }
            }
        }
    }
}

.faq-search-index {
    .page-title {
        @apply w-full max-w-4xl mx-auto text-2xl md:text-5xl;
    }
}

[data-appearance="blog-doorways"],
[data-appearance="doorways"] {
    [data-element="inner"] {
        @apply order-2 !grid gap-4 sm:gap-8 grid-cols-1 sm:grid-cols-3 md:grid-cols-5 relative z-1;

        &[data-desktop-columns="2"] {
            @apply sm:grid-cols-2 md:grid-cols-2;
        }

        &[data-desktop-columns="3"] {
            @apply sm:grid-cols-1 md:grid-cols-3;
        }

        &[data-desktop-columns="4"] {
            @apply sm:grid-cols-2 md:grid-cols-4;
        }

        &[data-desktop-columns="6"] {
            @apply sm:grid-cols-3 md:grid-cols-6;
        }

        &[data-desktop-columns="7"] {
            @apply sm:grid-cols-3 md:grid-cols-7;
        }

        &[data-desktop-columns="8"] {
            @apply sm:grid-cols-4 md:grid-cols-8;
        }
    }
}

[data-appearance="blog-doorways"] {
    @apply container box-border relative grid w-auto px-4 py-8 mx-3 my-12 overflow-hidden md:py-10 md:px-6 lg:px-12 rounded-xl md:mx-6 lg:m-12;

    .pagebuilder-doorways-header {
        @apply order-1 !mb-4 md:!mb-20 md:text-center z-1;

        h3 {
            @apply mb-0 text-2xl md:text-5xl;
        }
    }

    &[data-background-color-type="light"] {
        @apply text-primary;
    }

    [data-element="inner"] {
        .pagebuilder-banner-main-wrapper {
            @apply flex items-center min-h-full my-0 sm:block;

            .banner-image {
                @apply w-auto h-auto mb-4 mr-4 overflow-hidden rounded-full sm:mb-8 sm:w-auto sm:mx-auto;

                .pagebuilder-banner-wrapper {
                    @apply static min-w-full sm:transform-none;
                }
            }

            .pagebuilder-overlay {
                @apply w-10/12 p-0 text-sm sm:w-auto sm:text-center after:content-none;
            }
        }

        [data-appearance="usp"] {
            .banner-image {
                @apply mb-0 sm:mb-6 sm:max-w-[70px];

                .pagebuilder-banner-wrapper {
                    @apply h-[40px] w-[40px] md:h-[70px] md:w-[70px];
                }
            }

            .pagebuilder-overlay {
                p {
                    @apply mb-0 sm:mb-4;
                }
            }
        }

        [data-appearance="blog"] {
            .pagebuilder-banner-main-wrapper {
                @apply block;

                .pagebuilder-overlay {
                    @apply w-auto p-0 text-sm text-center;

                    p {
                        @apply mb-0 sm:mb-4;
                    }
                }

                .banner-image {
                    @apply w-auto h-auto max-w-[15rem] sm:max-w-sm aspect-square;

                    .pagebuilder-banner-wrapper {
                        @apply w-full h-full transition translate-y-0;
                    }
                }

                .pagebuilder-banner-button {
                    @apply bg-transparent absolute top-0 left-0 w-full h-full !rounded-none after:hidden;

                    &:hover + .pagebuilder-overlay {
                        .pagebuilder-blog-content {
                            p {
                                @apply underline;
                            }
                        }
                    }
                }
            }

            &:hover {
                .banner-image {
                    .pagebuilder-banner-wrapper {
                        @apply md:scale-105;
                    }
                }
            }
        }
    }

    .pagebuilder-doorways-footer {
        @apply flex justify-center order-3 mt-4 md:mt-8 z-1;

        .pagebuilder-doorways-button {
            @apply !opacity-100 !visible w-full sm:w-auto justify-center;
        }
    }

    &[data-background-style^="style_"]:after {
        content: '';
        @apply w-full md:w-7/12 h-full absolute z-0 -right-[35%] md:-right-[26%] -top-[13%] blur-md md:blur-lg -rotate-[83deg] bg-[length:388px_587px] md:bg-[length:1094px_1063px] bg-no-repeat opacity-10;
    }

    &[data-background-style="style_1"]:after {
        @apply bg-style1;
    }

    &[data-background-style="style_2"]:after {
        @apply bg-style2;
    }

    &[data-background-style="style_3"]:after {
        @apply bg-style3;
    }

    &[data-background-style="style_4"] {
        @apply mx-0 rounded-none md:rounded-xl md:mx-6 lg:m-12;

        .pagebuilder-doorways-header {
            @apply mb-8 text-center;
        }

        .pagebuilder-banner-main-wrapper {
            .banner-image {
                @apply mx-auto;
            }
        }

        .pagebuilder-doorways-footer {
            .pagebuilder-doorways-button {
                @apply w-auto;
            }
        }

        &:after {
            @apply bg-style4;
        }
    }

    &[data-background-style="style_5"]:after {
        @apply bg-style5;
    }

    &[data-background-style="style_6"]:after {
        @apply bg-style6;
    }

    &[data-background-style="style_7"]:after {
        @apply bg-style7;
    }

    &[data-background-style="style_8"]:after {
        @apply bg-style8;
    }
}

[data-appearance="doorways"] {
    @apply container relative my-12;

    .pagebuilder-doorways-header {
        @apply flex items-center justify-between sm:mb-5 z-1;

        h3 {
            @apply text-3xl md:text-5xl text-primary;
        }

        .pagebuilder-doorways-button {
            @apply max-sm:hidden;
            
            &.pagebuilder-button-primary {
                span {
                    @apply flex flex-full-text after:content-[url('../svg/chevron-right-white.svg')];
                }

                &:hover {
                    span {
                        @apply md:after:content-[url('../svg/chevron-right-white.svg')];
                    }
                }
            }

            &.pagebuilder-button-secondary {
                span {
                    @apply after:content-[url('../svg/chevron-right-primary.svg')];
                }

                &:hover {
                    span {
                        @apply md:after:content-[url('../svg/chevron-right-white.svg')];
                    }
                }
            }
        }
    }

    [data-element="inner"] {
        @apply gap-3 sm:gap-5;

        > div {
            @apply overflow-hidden rounded-xl;

            &[data-background-style^="style_"] {
                .pagebuilder-overlay:after {
                    @apply w-3/4 h-full absolute z-10 -right-20 -bottom-4 bg-[length:250px_250px] md:top-auto md:-right-36 md:-bottom-96 -rotate-[36deg] blur-sm md:bg-[length:450px_450px] bg-no-repeat;
                }
            }

            &[data-background-color-type="light"] {
                .pagebuilder-banner-button {
                    @apply bg-white;
                }
            }

            &[data-background-color-type="dark"] {
                .pagebuilder-banner-main-wrapper {
                    .pagebuilder-overlay {

                        &:after {
                            @apply w-full h-full absolute z-20 top-auto -right-4 -bottom-20 bg-[length:250px_250px] md:-right-40 md:-bottom-48 md:w-[650px] -rotate-[31deg] blur-md md:bg-[length:650px_650px] bg-no-repeat;
                        }

                        .pagebuilder-garden-style-content {
                            @apply relative z-30;

                            p {
                                strong {
                                    @apply font-bold text-primary-100;
                                }
                            }
                        }

                        .pagebuilder-banner-button {
                            @apply bg-primary-100;
                        }
                    }
                }
            }

            &:hover {
                .banner-image {
                    .pagebuilder-banner-wrapper {
                        @apply md:scale-105;
                    }
                }
            }
        }

        .pagebuilder-banner-main-wrapper {
            @apply flex items-center min-h-full my-0 sm:block;

            .banner-image {
                @apply w-4/12 z-1 mb-0 sm:mb-5 sm:w-full h-auto aspect-[.95/1.2] sm:aspect-[.95/1] sm:mx-auto overflow-hidden rounded-br-[50px] md:rounded-br-[200px];

                .pagebuilder-banner-wrapper {
                    @apply top-0 w-full h-full min-w-full min-h-full transition translate-y-0 rounded-none;
                }
            }

            .pagebuilder-overlay {
                @apply w-9/12 p-4 text-sm sm:w-full;

                .pagebuilder-garden-style-content {
                    @apply relative z-20 flex items-end justify-between sm:items-center;

                    p {
                        @apply mb-2;
                    }

                    .pagebuilder-banner-button {
                        @apply static flex justify-center items-center rounded-full w-full h-full max-w-[36px] h-[36px] sm:max-w-[48px] sm:max-h-[48px] sm:h-12 p-0 ml-4 before:absolute before:left-0 before:right-0 before:top-0 before:bottom-0 z-10 after:content-none;

                        span {
                            @apply flex justify-center items-center after:content-[url('../svg/chevron-right-primary.svg')] -indent-[9999px] after:indent-0 after:float-left after:h-5;
                        }

                        &:hover {
                            @apply md:bg-primary;

                            span {
                                @apply md:after:content-[url('../svg/chevron-right-white.svg')];
                            }
                        }
                    }
                }
            }
        }
    }

    .pagebuilder-doorways-mobile {
        @apply relative flex justify-center mt-8 transition-all duration-1000 opacity-0 -bottom-14 sm:hidden;

        &.reveal.to-reveal {
            @apply bottom-0 opacity-100;
        }

        .pagebuilder-doorways-button {
            span {
                @apply flex flex-full-text after:content-[url('../svg/chevron-right-white.svg')];
            }

            &:hover {
                span {
                    @apply md:after:content-[url('../svg/chevron-right-white.svg')];
                }
            }
        }
    }
}

/* elements that should be revealed on scroll */
.to-reveal .pagebuilder-doorways-header > *,
.to-reveal[data-appearance="blog-doorways"] [data-element="inner"] > div,
.to-reveal[data-appearance="doorways"] [data-element="inner"] > div,
.to-reveal .pagebuilder-doorways-footer {
    @apply relative duration-1000;
    @apply motion-safe:-bottom-28 motion-safe:!opacity-0;
}

.to-reveal[data-appearance=doorways] .pagebuilder-doorways-header .pagebuilder-doorways-button {
    @apply duration-1000 sm:relative;
    @apply motion-safe:-bottom-28 motion-safe:!opacity-0;
}

.to-reveal[data-appearance="blog-doorways"],
.to-reveal[data-appearance="doorways"] {
    &[data-background-color-type="light"],
    &[data-background-color-type="dark"] {
        @apply duration-1500;
        @apply motion-safe:!opacity-0 motion-safe:bg-transparent;
    }
}

.reveal.to-reveal {
    &[data-appearance="doorways"],
    &[data-appearance="blog-doorways"] {
        &[data-background-color-type="light"] {
            @apply bg-secondary !opacity-100;
        }

        &[data-background-color-type="dark"] {
            @apply bg-primary !opacity-100;
        }

        .pagebuilder-doorways-header > *,
        .pagebuilder-doorways-header .pagebuilder-doorways-button,
        .pagebuilder-doorways-footer {
            @apply bottom-0 !opacity-100;
        }
    }
}

[data-appearance="doorways"],
[data-appearance="blog-doorways"] {
    [data-element="inner"] > .reveal.to-reveal {
        @apply bottom-0 !opacity-100;
    }
}

[data-appearance="doorways"] {
    @apply flex flex-col;

    .pagebuilder-doorways-header {
        @apply order-1;
    }

    .pagebuilder-doorways-mobile {
        @apply order-3;
    }

    .pagebuilder-doorways-items {
        @apply order-2;
    }
}

.page-main {
    .carousel {
        @apply relative my-8 md:my-12;

        > .product-carousel {
            @apply !px-0;
        }

        [data-content-type="products"][data-appearance="carousel"] {
            @apply block !px-0;

            .product-carousel-header {
                @apply mb-6 text-primary;
            }

            .widget-product-carousel {
                @apply block !px-0;
            }
        }
    }
}

.pagebuilder-column {
    [data-content-type="products"][data-appearance="carousel"] {
        .product-carousel-header,
        .products-container {
            @apply px-0;
        }

        .product-carousel-header {
            @apply lg:pr-36;
        }

        .widget-product-carousel {
            @apply max-lg:!px-0;
        }

        .swiper-nav {
            @apply top-0 right-0;
        }
    }
}

.page-main-full-width {
    [data-content-type="products"][data-appearance="carousel"] {
        .product-carousel-header {
            min-height: 3.75rem;
        }
    }
}

[data-content-type="products"][data-appearance="carousel"],
.product-slider,
.ajax-addtocart-modal {
    @apply relative;

    .swiper-nav {
        @apply static w-full lg:w-auto lg:absolute lg:right-[var(--container-padding)] lg:top-5;

        .swiper-button-prev,
        .swiper-button-next {
            @apply mt-0;
        }

        .swiper-pagination {
            @apply static;

            .swiper-pagination-bullet {
                @apply bg-primary;

                &.swiper-pagination-bullet-active {
                    @apply outline-primary;
                }
            }
        }
    }

    &.product-slider {
        .swiper-nav {
            @apply mt-4 lg:mt-0;

            .swiper-pagination {
                @apply hidden;
            }
        }
    }

    &.ajax-addtocart-modal {
        .swiper-nav {
            @apply lg:mt-4 lg:w-full lg:static;

            .swiper-button-prev,
            .swiper-button-next {
                @apply absolute right-0 hidden top-2 lg:block;

                &.swiper-button-prev {
                    @apply right-20;
                }

                &.swiper-button-lock {
                    @apply hidden;
                }
            }

            .swiper-pagination {
                @apply mt-4 lg:hidden lg:mt-0;
            }
        }
    }
}

.faq-search-index {
    .columns {
        @apply container;
    }
}

.faq-content {
    [data-content-type="buttons"] {
        @apply mt-4;
    }
}
