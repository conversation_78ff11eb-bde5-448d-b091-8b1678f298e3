@layer utilities {
    /* This class only exists for backwards compatibility, */
    /* The default is applied in tailwind.config.js since Hyvä 1.2.0 */
    .text-sans-serif {
        font-family: "Poppins", Arial, sans-serif;
    }
}

html {
    @apply antialiased;
}

body {
    @apply text-base leading-normal tracking-normal text-black text-sans-serif;
}

@font-face {
    font-family: 'Poppins';
    src: url('../fonts/Poppins-Regular.woff2') format('woff2'),
        url('../fonts/Poppins-Regular.ttf') format('truetype');
    font-weight: 400;
    font-style: normal;
    font-display: swap;
    font-kerning: normal;
    font-synthesis: none;
}

@font-face {
    font-family: 'Poppins';
    src: url('../fonts/Poppins-Bold.woff2') format('woff2'),
        url('../fonts/Poppins-Bold.ttf') format('truetype');
    font-weight: 700;
    font-style: normal;
    font-display: swap;
    font-kerning: normal;
    font-synthesis: none;
}

h1, .heading-1 {
    @apply text-[2rem] leading-[2.3rem] md:text-[3.5rem] md:leading-tight;
}

h2, .heading-2 {
    @apply text-[1.625rem] leading-10 md:text-5xl md:leading-tight;
}

h3, .heading-3 {
    @apply text-[1.375rem] leading-8 md:text-[2.5rem] md:leading-tight;
}

h4, .heading-4 {
    @apply text-xl leading-[1.875rem] md:text-[2rem] md:leading-tight;
}

h5, .heading-5 {
    @apply text-lg leading-[1.625rem] md:text-[1.625rem] md:leading-tight;
}

h6 .heading-6 {
    @apply text-base md:text-xl;
}

address {
    @apply not-italic;
}