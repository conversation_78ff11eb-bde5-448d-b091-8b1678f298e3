#category-view-container {
    @apply flex-col max-w-full lg:max-w-[calc(100%-6rem)] border-b lg:flex px-0 border-secondary-900/50;

    .category-description {
        @apply pb-6 mx-auto;

        .carousel {
            @apply lg:!px-0 !pb-0;
        }

        [data-content-type='text'] {
             @apply max-w-5xl mx-auto;
        }
    }
}

.toolbar-products {
    .modes-mode {
        @apply w-6 h-6;

        span {
            @apply sr-only;
        }

        &.mode-grid {
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" /></svg>');
        }

        &.mode-list {
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16" /></svg>');
        }
    }

    .sorter-options {
        @apply border-none bg-secondary leading-[1.875rem];
    }
}
