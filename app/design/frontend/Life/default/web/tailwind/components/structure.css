body {
    overflow-y: scroll;
}

.clearfix::after {
    content: "";
    display: block;
    clear: both;
}

.page-main {
    @apply my-6;
}

.columns {
    @apply grid grid-cols-1 gap-x-8 gap-y-4;
    grid-template-rows: auto minmax(0, 1fr);

    .main {
        @apply order-2;
    }

    .sidebar {
        @apply order-3;
    }

    .product-main-full-width & {
        @apply max-w-none;
    }

    .page-main-full-width & {
        @apply px-0 max-w-none;
    }
}

.column-title {
    @apply -mt-4;
}
.column-title-sm {
    @apply pb-0 -mt-4;
}

.column-right {
    @apply pl-12 rounded-r-xl -mt-11;
}

.column-right-header {
    @apply -ml-6 pt-11;
}

.column-left {
    @apply pr-12 -mr-6 rounded-l-xl pl-14;
}

.column-button {
    @apply -ml-6 pr-5 rounded-[18.75rem];
}

.column-desc {
    @apply w-[91%] -ml-5 leading-8;

}

.column-desc-sm {
    @apply w-[91%] leading-7;
}

.page-with-filter {
    .columns {
        .sidebar-main {
            @apply order-1;
        }
    }
}

@screen sm {
    .page-layout-2columns-left,
    .page-layout-2columns-right,
    .page-layout-3columns {
        .columns {
            @apply grid-cols-2;

            .main {
                @apply col-span-2;
            }

            .sidebar {
                @apply order-3;
            }
        }
    }

    .page-with-filter {
        .columns {
            .sidebar-main {
                @apply order-1 col-span-2;
            }
        }
    }
}

@screen md {
    .page-layout-2columns-left,
    .page-layout-2columns-right,
    .page-layout-3columns {
        .columns {
            @apply grid-cols-3;

            .main {
                @apply row-span-2;
            }

            .sidebar {
                @apply col-span-1;
            }
        }
    }

    .page-layout-2columns-left,
    .page-layout-3columns {
        .columns {
            .main {
                @apply col-start-2 !important;
            }
            .sidebar {
                @apply order-1;
            }

            .sidebar ~ .sidebar-additional {
                @apply order-3;
            }
        }
    }

    .page-layout-2columns-right,
    .page-layout-2columns-right.page-with-filter {
        .sidebar-main {
            @apply order-3;
        }
    }

    .column-right {
        @apply -ml-6;
    }

    .column-right-header {
        @apply pt-20 -ml-6;
    }

    .column-title {
        @apply pt-0 pb-24 -mt-4;
    }

    .column-desc {
        @apply leading-10;
    }

    .column-button {
        @apply pr-5;
    }
}

@screen lg {
    .page-layout-2columns-left,
    .page-layout-2columns-right,
    .page-layout-3columns {
        .columns {
            @apply grid-cols-4;
        }
    }

    .page-layout-2columns-left,
    .page-layout-2columns-right {
        .columns {
            .main {
                @apply col-span-3;
            }
        }
    }
    .page-layout-3columns {
        .columns {
            .sidebar-additional {
                @apply col-start-4;
            }
        }
    }
}

.product-image-container {
    width: 100% !important;

    img {
        width: 100%;
    }
}
