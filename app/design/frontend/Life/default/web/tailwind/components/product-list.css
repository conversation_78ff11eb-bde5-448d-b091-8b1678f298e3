
.product-item {
    .price-box {
        @apply flex items-end justify-end gap-y-1 lg:items-start gap-x-3;

        &.price-final_price {
            @apply flex-wrap;
            
            .price-label {
                @apply hidden;
            }
        }
    }
    .price-container {
        @apply block;

        .price {
            @apply text-lg font-semibold;
        }

        .price-label {
            @apply text-sm;
        }
    }

    .special-price .price-container .price-label {
        @apply sr-only;
    }

    .old-price .price-container {
        @apply text-primary-800;

        .price {
            @apply text-base font-normal;
        }
    }

    .gallery-snap {
        scrollbar-width: none;
    }
}