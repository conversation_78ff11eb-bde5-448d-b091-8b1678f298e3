.modal {
  /*
   * TODO: add tailwind classes used for the cart and modal styles.
   * This will make the modal and off-canvas styles theme specific and more adjustable.
   */
}

.backdrop {
    @apply fixed inset-0 flex bg-black bg-opacity-25;
}

/* add to cart */
.ajax-addtocart-modal .add-to-cart svg {
    @apply relative z-10;
}

/* not added to cart */
.ajax-addtocart-modal .add-to-cart:not(.added) .checkmark {
    @apply hidden;
}

/* added to cart */
.ajax-addtocart-modal .add-to-cart.added {
    @apply bg-primary-100 text-primary;
}

.ajax-addtocart-modal .add-to-cart.added::after {
    @apply hidden;
}

.ajax-addtocart-modal .add-to-cart.added .shopping-cart {
    @apply hidden;
}
