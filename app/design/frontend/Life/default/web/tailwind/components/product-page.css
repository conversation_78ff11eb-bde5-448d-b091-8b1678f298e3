
.page-product-bundle {
    .price-final_price {
        .price-from,
        .price-to {

            .price-container {
                @apply block mb-4 text-gray-700;

                .price-label {
                    @apply block text-lg font-medium;
                }

                .price {
                    @apply text-2xl font-semibold leading-tight text-gray-800;
                }
            }

            .price-including-tax + .price-excluding-tax {
                @apply mt-1;

                .price {
                    @apply text-base;
                }
            }

            .old-price {
                .price-container {
                    .price,
                    .price-label {
                        @apply inline text-base font-normal text-gray-500;
                    }
                }
            }
        }
    }
}

.showroom-notice [data-content-type='row'][data-appearance='contained'] {
    @apply p-0;

    h2,
    h3,
    h4,
    h5,
    h6 {
        &:first-child {
            @apply flex items-center gap-2 mb-4 text-sm font-semibold text-primary;

            img {
                @apply object-contain w-6 h-6;
            }
        }
    }

    p:last-child {
        @apply mb-0 text-sm;
    }
}

.catalog-product-view {
    .page-main {
        @apply my-0;
    }

    .page-footer {
        @apply pb-20 sm:pb-28 lg:pb-32;
    }

    .info-drawer {
        h2 + p {
            img {
                @apply w-full;
            }
        }

        [data-appearance='slider'] {
            @apply !min-h-0;
        }

        .swiper.slider {
            .swiper-nav {
                @apply justify-center pb-4 lg:pb-0 aspect-video;
            }

            .pagebuilder-overlay {
                @apply !hidden;
            }
        }

        [data-background-type='video']{
            video {
                @apply !w-full !h-auto !m-0 !transform-none aspect-video;
            }
        }

        .visual-swatch {
            @apply w-10 h-10 m-0;
        }

        [data-appearance='collage-left'] {
            .pagebuilder-slide-wrapper {
                @apply aspect-video;
            }
        }
    }

    .slide-in {
        animation: .3s ease-out slide-in;
        
        @keyframes slide-in {
            from {
                transform: translateY(100%);
            }
            to {
                transform: translateY(0);
            }
        }
    }
}

.delivery-wrapper {
    &[data-bg-color] {
        @apply bg-primary-100/30;
    }

    &[data-bg-color="orange"],
    &[data-bg-color="red"] {
        @apply bg-[#EA8255];
    }

    &[data-bg-color="red"] {
        @apply font-bold;
    }
}

@media screen and (max-width: 767px) {
    .catalog-product-view #thumbs > button {
        display: none;
    }
}

#product-config-flyout {
    .field.field-reserved {
        @apply absolute opacity-0;
    }
}