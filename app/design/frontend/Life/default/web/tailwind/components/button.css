@layer components {
    .btn {
        @apply relative inline-flex items-center px-5 py-3 overflow-hidden text-base text-white transition-all duration-500 ease-in-out !rounded-full bg-primary;
        @apply after:content-[''] after:absolute after:top-0 after:-left-full after:w-full after:h-full after:rounded-full;
        @apply motion-safe:after:transition-all motion-safe:after:duration-500 motion-safe:after:ease-in-out;

        svg {
            @apply inline-flex;
        }

        span {
            @apply relative z-10 align-middle;
        }

        svg {
            @apply relative z-10;
        }

        &:hover {
            @apply lg:after:left-0;
        }

        &:focus-visible {
             @apply border-transparent outline-none ring-4 ring-primary/50;
        }
    }

    .btn-primary {
        @apply text-white bg-primary after:bg-primary-600;

        &:hover {
            @apply after:bg-primary-600;

            svg {
                @apply text-primary;
            }
        }
    }

    .btn-secondary {
        @apply text-primary bg-secondary after:bg-secondary-600;

        &:focus-visible {
            @apply ring-secondary-700/50;
       }
    }

    .btn-size-lg {
        @apply px-10 py-4 text-lg;
    }

    .btn-size-sm {
        @apply px-2 py-2 text-sm;
    }
    
    .btn-size-xs {
        @apply px-2 py-2 text-xs;
    }

    .text-link {
        @apply relative hover:text-primary before:content-[''] after:content-[''];

        &::before,
        &::after {
            @apply absolute left-0 block w-full h-px bg-black -bottom-0.5;
            transition: 1.1s var(--ease-out-expo);
        }

        &::before {
            transform: scaleX(0);
            transform-origin: left;
        }

        &::after {
            transform-origin: right;
            transition-delay: 0.25s;
        }

        &:hover {
            &::before {
                transform: scaleX(1);
                transition-delay: 0.25s;
            }

            &::after {
                transform: scaleX(0);
                transition-delay: 0s;
            }
        }

        &:focus-visible {
            @apply rounded-sm outline outline-1 outline-primary/50 outline-offset-2 before:hidden after:hidden;
        }
    }
}

