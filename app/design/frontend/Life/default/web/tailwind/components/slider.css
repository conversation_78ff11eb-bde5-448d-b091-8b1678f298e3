/* Slider */
[data-appearance='slider'] {
    @apply relative flex;

    [data-appearance='collage-left'] {
        [data-element='content'] {
            @apply xl:text-white;
        }
    }
}

.slider {
    @apply rounded-2xl bg-secondary xl:bg-transparent;

    .swiper-nav {
        @apply w-full aspect-video md:aspect-[16/7] xl:pr-12 pb-2 px-4 xl:pb-14 pointer-events-none lg:aspect-video xl:aspect-[1/0.5] 2xl:aspect-[1/0.4] items-end xl:justify-end max-xl:gap-4;

        .swiper-button-prev {
            @apply top-0 hidden text-white pointer-events-auto xl:order-2 xl:block;
        }

        .swiper-button-next {
            @apply top-0 text-white pointer-events-auto xl:order-3 max-xl:w-6 max-xl:h-6 ;

            svg {
                @apply max-xl:!w-6 max-xl:!h-6;
            }
        }

        .swiper-pagination {
            @apply w-auto pointer-events-auto xl:order-1 max-xl:flex-grow max-xl:justify-evenly max-xl:gap-2 py-0.5 xl:py-3 max-xl:!px-0;

            .swiper-pagination-bullet {
                @apply max-xl:!h-0.5 max-xl:!w-full relative;

                &:after {
                    @apply content-[''] w-full h-6 absolute left-0 xl:hidden top-1/2 transform -translate-y-1/2;
                }

                &.swiper-pagination-bullet-active {
                    @apply max-xl:outline-none max-xl:bg-tertiary-300;
                }
            }
        }
    }

    .pagebuilder-collage-content p:last-child {
        @apply max-sm:text-center;
    }

    [data-appearance='collage-left'] .pagebuilder-slide-wrapper {
        @apply !bg-right;
    }
}

[data-appearance='collage-left'] {
    &:not(.swiper-slide) {
        @apply hidden w-full first:block;
    }

    &.swiper-slide-active {
        .pagebuilder-overlay {
            @apply max-lg:opacity-100 xl:left-0;
        }
    }
}

@layer components {
    /* Slide */
    [data-appearance='collage-left'] {
        @apply box-border overflow-hidden leading-5 xl:bg-transparent;
        min-height: inherit;

        @media (max-width: 80rem) {
            &[data-content-type='slide'] {
                @apply h-auto;

                [data-element='empty_link'] {
                    @apply flex flex-col;
                }

                .pagebuilder-overlay {
                    @apply flex-grow;

                    .pagebuilder-collage-content {
                        @apply h-full;

                        [data-element='content'] {
                            @apply grid items-start h-full;
                            grid-template-rows: auto 1fr;

                            p:last-child:has(a) {
                                @apply self-end;

                                a:last-child {
                                    @apply mb-0;
                                }
                            }
                        }
                    }
                }
                .pagebuilder-slide-wrapper {
                    &[data-background-type='image'] {
                        background-position: 80% 100%!important;
                    }
                }
            }
        }

        & > [data-element='link'],
        & > [data-element='empty_link'] {
            @apply h-full;

            color: inherit;
            min-height: inherit;
            text-decoration: inherit;

            &:hover {
                color: inherit;
                text-decoration: inherit;
            }
        }

        .pagebuilder-slide-wrapper {
            @apply box-border break-words aspect-video md:aspect-[16/7] lg:aspect-video xl:aspect-[1/0.5] 2xl:aspect-[1/0.4];

            video {
                @apply max-lg:object-cover max-xl:!max-w-full max-xl:!max-h-full max-xl:!m-0 max-xl:!transform-none;
            }

            .pagebuilder-slide-button {
                @apply max-w-full m-0 mt-5 break-words transition-opacity;
                text-align: inherit;
            }

            .pagebuilder-poster-content {
                @apply w-full;
            }

            [data-element='content'] p a:not(.btn) {
                font-size: inherit;
                line-height: inherit;
            }
        }

        .pagebuilder-overlay {
            @apply z-10 flex items-end w-full px-4 py-6 ease-in-out rounded-tl-2xl rounded-tr-2xl;
            @apply md:rounded-tr-none md:rounded-bl-2xl;
            @apply xl:w-auto xl:h-full xl:px-6 xl:py-12 xl:bg-transparent xl:absolute xl:bottom-0 xl:text-inherit;
            @apply motion-safe:delay-200 motion-safe:transition-all motion-safe:duration-300;
            @apply motion-safe:max-lg:opacity-0 motion-reduce:!opacity-100;
            @apply motion-safe:xl:-left-1/2;

            &.pagebuilder-poster-overlay {
                @apply flex items-center justify-center;
                min-height: inherit;
            }

            &:not(.pagebuilder-poster-overlay) {
                @apply max-w-none xl:max-w-[41.67%];
            }

            .pagebuilder-collage-content {
                @apply w-full;
            }

            [data-element='content'] {
                
                h1 {
                    @apply mb-4;
                }

                p {
                    @apply mb-4 text-base xl:mb-8 last:mb-0;

                    a {
                        @apply text-base;

                        &.btn {
                            @apply w-full mb-4 sm:w-auto sm:mb-0 sm:mr-4 max-xl:btn-primary after:!hidden;
                        }

                        &:not(.btn) {
                            @apply sm:w-auto;

                            &:before,
                            &:after {
                                @apply !bg-white;
                            }

                            &:hover {
                                @apply text-white;
                            }
                        }
                    }
                }

                [style*="color"] {
                    @media (max-width: 1023px) {
                        @apply !text-inherit;
                    }
                }
            }
        }
    }

    [data-gradient-overlay='true'] {
        &:after {
            @apply xl:inset-0 xl:absolute xl:content-[''] xl:pointer-events-none;
            background: linear-gradient(221.08deg, rgba(0, 0, 0, 0) 47.03%, rgba(0, 0, 0, 0.6) 85.25%);
        }
    }

    .slider {
        [data-appearance='collage-left'] .pagebuilder-overlay [data-element='content'] p {
            @apply flex flex-wrap items-center xl:text-xl xl:leading-9;
        }
    }
}
