.product-filter {
    .filter-option {
        @apply relative;
        
        .filter-options-title {
            button {
                @apply relative rounded-full bg-secondary py-2.5 px-5;
            }
        }

        .swatch-attribute-options {
            @apply max-w-[18.75rem];
        }
    }

    .all-filters {
        @apply text-white cursor-pointer bg-primary rounded-full py-2.5 px-5 flex items-center;
    }

    .filter-content {
        .filter-option {
            @apply mb-10;
            
            .filter-options-title {
                button {
                    @apply p-0 bg-transparent;
                }
            }
        }
    }

    .filter-modal {
        .swatch-attribute-options {
            @apply max-w-none;
        }
    }

    .htmx-indicator-small {
        @apply opacity-0 pointer-events-none;

        &.htmx-request {
            @apply opacity-100 pointer-events-auto;
        }
    }    
}
