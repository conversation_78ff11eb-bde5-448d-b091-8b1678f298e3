.hyva_checkout-index-index {
    #header {
        @apply mb-8 shadow-md;
        
        :is(.container) {
            @apply lg:max-w-[calc(var(--max-w-container)/2)] lg:mr-0;
        }
    }

    #messages {
        @apply lg:max-w-[calc(var(--max-w-container)/2)] lg:ml-auto;
    }

    .page-main {
        @apply m-0;
    }

    &.page-layout-2columns-right .columns {
        @apply lg:grid-cols-[repeat(2,1fr)];
    }

    .columns {
        @apply block;
        @apply lg:grid lg:grid-rows-1 lg:min-h-screen;

        .column.main {
            @apply w-full bg-white;
            @apply lg:col-start-1 lg:col-span-1;
        }

        .sidebar {
            @apply w-full mb-20 py-8 px-[var(--container-padding)];
            @apply lg:max-w-[calc(var(--max-w-container)/2)] lg:mb-0 lg:pt-32 lg:pl-8 lg:pr-16 lg:col-start-2 lg:col-span-1;
            @apply 2xl:pl-24 2xl:pr-32;

            #quote-summary {
                @apply sticky top-32;
            }
        }
    }

    .step-shipping,
    .step-payment {
        @apply container pb-6;
    }

    .step-payment {
        .checkout-login {
            @apply hidden;
        }
    }

    #payment {
        > header.section-title {
            @apply hidden;
        }
    }

    #payment-methods {
        .payment-icon {
            svg {
                @apply max-h-6;
            }
        }

        .terms-and-conditions {
            a {
                @apply underline sm:no-underline sm:text-link;
            }
        }
    }

    .btn,
    .btn-secondary {
        @apply font-normal border-none shadow-none;
    }

    .btn-secondary {
        @apply text-primary bg-secondary after:bg-primary;

        &:hover {
            @apply lg:text-white;
        }

        &:focus-visible {
            @apply ring-secondary-700/50;
       }
    }

    div.required > label > span:after {
        @apply !text-sm !-right-2;
    }

    .postcode-warning {
        @apply mt-1 text-red-500;
    }

    #hyva-checkout-main {
        @apply mr-0;
        @apply lg:max-w-[calc(var(--max-w-container)/2)];

        .breadcrumbs {
            .item {
                @apply inline-flex relative items-center text-black 
                after:content-[''] after:h-4 after:w-4 after:rounded-full after:bg-secondary-300 after:absolute after:-bottom-6 after:left-1/2 after:transform after:-translate-x-1/2 after:border after:border-secondary-900/50 after:z-1
                before:content-[''] before:h-px before:absolute before:right-1/2 before:w-[50vw] before:-bottom-4;
                
                &.active {
                    @apply space-x-1 font-bold after:border-primary after:bg-primary before:bg-primary;
                }
                &.locked {
                    @apply cursor-not-allowed text-black/70;
                }
                &.completed {
                    @apply text-black after:border-primary after:bg-primary before:bg-primary;
                }
            }
        }
    }
}
