.offset-product-slider {
    section {
        &[data-background-color-type="dark"] {
            @apply bg-primary;

            .slider-description {
                @apply !text-white;
            }

            .swiper-nav {
                .swiper-button-next,
                .swiper-button-prev {
                    @apply text-white;
                }
    
                .swiper-pagination {
                    .swiper-pagination-bullet {
                        @apply bg-white;
                        
                        &-active {
                            @apply outline-white;
                        }
                    }
                }
            }
        }

        &[data-background-color-type="light"] {
            @apply bg-secondary;

            .slider-description {
                @apply !text-black;
            }
        }

        .swiper-container {
            @apply overflow-hidden;
        }
    
        .swiper-nav {
            @apply left-1/2 max-lg:transform max-lg:-translate-x-1/2 bottom-px lg:left-8 xl:left-12 lg:max-w-[calc(33.33%-4rem)];

            .swiper-button-next,
            .swiper-button-prev {
                @apply text-black;
            }

            .swiper-pagination {
                @apply flex-wrap gap-y-4 min-w-[6.25rem];

                .swiper-pagination-bullet {
                    @apply bg-black;
                    
                    &-active {
                        @apply outline-black;
                    }
                }
            }
        }
    }
}