const colors = require('tailwindcss/colors');
const hyvaModules = require('@hyva-themes/hyva-modules');

const themeColors = {
    transparent: 'transparent',
    current: 'currentColor',
    black: '#1D1D1D',
    white: colors.white,

    primary: {
        DEFAULT: '#00281E',
        '100': '#B4C6B9',
        '300': '#19B99C',
        '500': '#00281E',
        '600': '#033B2C',
        '700': '#DBE2DD',
        '800': '#5E5E5E',
        '900': '#d2dcd6'
    },
    secondary: {
        DEFAULT: '#E6DFD4',
        '300': '#F5F2EE',
        '500': '#E6DFD4',
        '600': '#D5C7B3',
        '700': '#C2A88F',
        '900': '#9c978e',
    },
    tertiary: {
        DEFAULT: '#F36C2C',
        '300': '#FFDBC3',
        '500': '#F36C2C',
    },
    pink: {
        DEFAULT: '#FFA5AC',
    },
    yellow: {
        DEFAULT: '#F8D073',
    },
    blue: {
        DEFAULT: '#81D7DE',
    },
    purple: {
        DEFAULT: '#A49BC5',
    },
    error: {
        DEFAULT: '#EFBFC1',
        '700': '#EA545A'
    }
};

module.exports = hyvaModules.mergeTailwindConfig({
    content: [
        // this theme's phtml and layout XML files
        '../../**/*.phtml',
        '../../*/layout/*.xml',
        '../../*/page_layout/override/base/*.xml',
        // parent theme in Vendor (if this is a child-theme)
        '../../../../../../../vendor/hyva-themes/magento2-default-theme/**/*.phtml',
        '../../../../../../../vendor/hyva-themes/magento2-default-theme/*/layout/*.xml',
        '../../../../../../../vendor/hyva-themes/magento2-default-theme/*/page_layout/override/base/*.xml',
        // app/code phtml files (if need tailwind classes from app/code modules)
        '../../../../../../../app/code/**/*.phtml',
    ],
    plugins: [
        require('@tailwindcss/forms'),
        require('@tailwindcss/line-clamp')
    ],
    theme: {
        extend: {
            colors: themeColors,
            container: {
                center: true,
                padding: {
                    DEFAULT: 'var(--container-padding)',
                },
                screens: {
                    'sm': '100%',
                    'md': '100%',
                    'lg': '100%',
                    'xl': '100%',
                    '2xl': '100%',
                }
            },
            fontFamily: {
                sans: ['Poppins', 'Arial', 'sans-serif'],
            },

            // for existing components
            backgroundColor: {
                container: {
                    lighter: colors.white,
                    'DEFAULT': themeColors.secondary[300],
                    darker: themeColors.secondary,
                }
            },

            backgroundImage: {
                'style1': 'url("../images/BannerStyle/style1.svg")',
                'style2': 'url("../images/BannerStyle/style2.svg")',
                'style3': 'url("../images/BannerStyle/style3.svg")',
                'style4': 'url("../images/BannerStyle/style4.svg")',
                'style5': 'url("../images/BannerStyle/style5.svg")',
                'style6': 'url("../images/BannerStyle/style6.svg")',
                'style7': 'url("../images/BannerStyle/style7.svg")',
                'style8': 'url("../images/BannerStyle/style8.svg")'
            },
            // for existing components
            borderColor: {
                container: {
                    lighter: colors.gray[300],
                    'DEFAULT': colors.gray[100],
                    darker: colors.gray[500],
                }
            },
            boxShadow: {
                'md': '0 4px 10px 0 rgba(0, 0, 0, 0.04)',
                'lg': '0 -4px 20px rgba(0, 0, 0, 0.15)',
            },

            // for existing components
            textColor: {
                orange: colors.orange,
                primary: {
                    lighter: colors.gray['500'],
                    darker: colors.gray['900'],
                },
                secondary: {
                    lighter: colors.gray['500'],
                    darker: colors.gray['900'],
                },
            },

            minHeight: {
                a11y: '48px',
                'screen-25': '25vh',
                'screen-50': '50vh',
                'screen-75': '75vh',
            },
            maxHeight: {
                '0': '0',
                'screen-25': '25vh',
                'screen-50': '50vh',
                'screen-75': '75vh',
            },
            maxWidth: {
                container: 'var(--max-w-container)'   
            },
            transitionDelay: {
                '0': '0ms',
            },
            zIndex: {
                1: '1',
                2: '2',
                '19': '19'
            },
            fontSize: {
                '3xl': ['1.6rem', { lineHeight: '2.438' }],
            },
            transitionDuration: {
                '1500': '1500ms',
            },
            flex: {
                'full-text': '0 0 auto',
            },
            scale: {
                200: '2',
                250: '2.5'
            },
            screens: {
                '3xl': '1780px',
            }
        },
    },
    safelist: [
        'btn',
        { pattern: /btn-./ },
    ],
    future: {
        hoverOnlyWhenSupported: true,
    },
});
