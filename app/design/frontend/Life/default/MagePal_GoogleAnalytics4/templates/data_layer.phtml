<?php

/** @var MagePal\GoogleAnalytics4\Block\DataLayer $block */
?>
<!-- Start Google Analytics 4 for Google Tag Manager by MagePal -->
<script defer="defer" type="text/javascript">
    (function () {
        let globalDatalayer = window.<?= $block->getDataLayerName() ?> = window.<?= $block->getDataLayerName() ?> || [];

        function init() {
            const data = <?= $block->getDataLayerJson() ?>;
            if (data) {
                let storedData = null

                data.forEach((item) => {
                    // Set category data on product detail page
                    if (item?.event === 'view_item') {
                        if (storedData === null) {
                            storedData = JSON.parse(window.localStorage.getItem("product-click-ga4"));
                        }

                        item?.ecommerce?.items?.forEach((product) => {
                            if (storedData?.index) {
                                product.index = storedData.index
                            }

                            if (storedData?.item_list_name) {
                                product.item_list_name = storedData.item_list_name;
                                product.item_list_id = storedData.item_list_id;
                            }
                        })
                    }

                    globalDatalayer.push(item);
                })
            }
        }

        init();

        <?php if ($block->hasImpressionList()) : ?>
        // Track product clicks
        const productsList = <?= $block->getImpressionListJson() ?>

        productsList.forEach((list) => {
            document.querySelectorAll(list?.class_name).forEach((productLink) => {
                productLink.addEventListener("click", () => {
                    productClick(productLink, list)
                })
            })
        })

        function productClick(element, list) {
            const container = element.closest(list?.container_class)

            // Find product url
            let productUrl = null
            if (container && container.querySelector('a.product-item-link')) {
                productUrl = container.querySelector('a.product-item-link').href
            } else if (container && container.querySelector('a.product-item-photo')) {
                productUrl = container.querySelector('a.product-item-photo').href
            } else {
                productUrl = element.href
            }

            const autoRedirectTimer = setTimeout(function () {
                document.location = productUrl
            }, 3000);

            if (!container) {
                return true;
            }

            const priceBox = container.querySelector("[data-product-id]")
            if (priceBox) {
                const productId = priceBox.dataset.productId

                const productCollections = globalDatalayer.find((layer) => layer?.ecommerce?.items).ecommerce?.items
                const product = productCollections.find((product) => product.p_id == productId)

                const productClickData = {
                    pid: productId,
                    item_id: product.item_id,
                    item_list_name: list.item_list_name,
                    item_list_id: list.item_list_id,
                    index: product.index
                }
                window.localStorage.setItem("product-click-ga4", JSON.stringify(productClickData))

                globalDatalayer.push({
                    'event': 'select_item',
                    'ecommerce': {
                        'items': [product]
                    },
                    'eventCallback': function () {
                        clearTimeout(autoRedirectTimer);
                        document.location = productUrl
                    },
                    '_clear': true
                });

                return false
            }

            return true
        }
        <?php endif; ?>
    })();
</script>
<!-- End Google Analytics 4 for Google Tag Manager by MagePal -->
