<?php

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\SvgIcons;
use Life\CategoryLayeredNavigationSpecialPage\ViewModel\SpecialCategoryPage;
use Life\ThemeConfigurations\ViewModel\HtmxUpdate;
use Magento\Framework\Escaper;
use Magento\LayeredNavigation\Block\Navigation\State;

/** @var State $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */

/** @var SvgIcons $svgIcons */
$svgIcons = $viewModels->require(SvgIcons::class);

/** @var SpecialCategoryPage $specialCategoryPageViewModel */
$specialCategoryPageViewModel = $viewModels->require(SpecialCategoryPage::class);
?>
<?php $filters = $block->getActiveFilters() ?>
<?php $activeFiltersCount = 0; ?>
<div class="w-full" id="active-filters" x-data="initActiveFilters()">
    <?php if (!empty($filters)): ?>
        <div class="flex flex-wrap items-center mb-2 gap-x-4 gap-y-2">
            <span class="text-sm"><?= $escaper->escapeHtml(__('Active filtering')) ?></span>
            <div
                id="active-filtering-content"
                class="flex flex-wrap items-center gap-2"
                aria-labelledby="active-filtering-heading"
                role="region"
            >
                <?php foreach ($filters as $filter): ?>
                    <?php
                        $filterName = $filter->getName();
                        $filterLabel = $block->stripTags($filter->getLabel());

                        // Category is pre-selected in the background for special category page, so it should not appear as active
                        if (
                            (string)$filterName === (string)__('Category')
                            && $specialCategoryPageViewModel->isSpecialCategoryPage()
                        ) {
                            continue;
                        }
                    ?>
                    <div
                        class="items-center justify-between px-2 py-1 bg-white item rounded-xl gap-x-2 md:flex"
                        :class="{ 'hidden [&:nth-child(-n+2)]:flex': !showAll, 'flex': showAll }"
                    >
                        <span>
                            <span class="block text-xs text-black/50"><?= $escaper->escapeHtml(__($filterName)) ?></span>
                            <span class="block text-sm filter-value">
                                <?= $escaper->escapeHtml($filterLabel) ?>
                            </span>
                        </span>
                        <span>
                            <button class="block action remove"
                                hx-get="<?= $escaper->escapeUrl($filter->getRemoveUrl()) ?>"
                                hx-indicator="#products-loader, #products-loader-small"
                                hx-select=".products-grid"
                                hx-select-oob="<?= HtmxUpdate::CATEGORY_ELEMENTS_ID; ?>"
                                hx-target=".products-grid"
                                hx-swap="outerHTML"
                                hx-replace-url="true"
                                title="<?= /* @noEscape */ $escaper->escapeHtmlAttr(__('Remove active %1 filter: %2', $filterName, $filterLabel)) ?>">
                                <?= $svgIcons->closeHtml("w-4 h-4") ?>
                            </button>
                        </span>
                    </div>
                    <?php
                        $activeFiltersCount++;
                    ?>
                <?php endforeach; ?>
                <?php if ($activeFiltersCount > 2): ?>
                    <div
                        @click="showAll = !showAll"
                        class="flex items-center justify-between px-2 py-2.5 bg-white item rounded-xl gap-x-2 md:hidden"
                    >
                        <span><span x-text="showAll ? '-' : '+'"></span> <?= $escaper->escapeHtml($activeFiltersCount - 2) ?></span>
                        <span class="transition-all duration-300 ease-in-out" :class="{ 'transform rotate-180': showAll }"><?= $svgIcons->arrowDownHtml("w-6 h-6") ?></span>
                    </div>
                <?php endif; ?>
            </div>
            <?php if ($block->getLayer()->getState()->getFilters()): ?>
                <div class="block-actions filter-actions">
                    <button
                        hx-get="<?= $escaper->escapeUrl($block->getClearUrl()) ?>"
                        hx-indicator="#products-loader, #products-loader-small"
                        hx-select=".products-grid"
                        hx-select-oob="<?= HtmxUpdate::CATEGORY_ELEMENTS_ID; ?>"
                        hx-target=".products-grid"
                        hx-swap="outerHTML"
                        hx-replace-url="true"
                        class="text-base font-bold text-link"
                    ><?= $escaper->escapeHtml(__('Clear All')) ?></button>
                </div>
            <?php endif; ?>
        </div>
    <?php endif; ?>

    <script defer="defer">
        function initActiveFilters() {
            return {
                showAll: false,
            }
        }
    </script>
</div>
