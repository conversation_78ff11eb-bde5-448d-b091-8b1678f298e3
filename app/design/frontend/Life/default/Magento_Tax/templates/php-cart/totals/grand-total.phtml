<?php
/**
 * The AlpineJS scope of this file is the method `initCartTotals()` in Magento_Checkout::php-cart/totals.phtml
 */
?>
<!-- display subtotal excluding tax -->
<template x-if="segment.code === 'grand_total' && checkoutConfig.includeTaxInGrandTotal">
    <div>
        <div class="flex py-2 pt-6 mx-auto my-2 text-xl border-b md:grid md:grid-cols-2 md:w-full border-container">
            <div class="w-7/12 text-base leading-[1.875rem] text-left font-bold md:w-auto"
                 x-html="`${segment.title} ${includingTaxMessage}` "
            ></div>
            <div class="w-5/12 text-right text-base font-bold leading-[1.875rem] md:w-auto"
                 x-text="formatPrice(segment.value, true)"
            ></div>
        </div>

        <div class="flex py-2 mx-auto my-2 mb-12 text-md md:grid md:grid-cols-2 md:w-full">
            <div class="w-7/12 text-base leading-[1.875rem] text-left font-bold md:w-auto"
                 x-html="`${segment.title} ${excludingTaxMessage}` "
            ></div>
            <div class="w-5/12 text-right text-base font-bold leading-[1.875rem] md:w-auto"
                 x-text="formatPrice(totalsData.grand_total, true)"
            ></div>
        </div>
    </div>
</template>

<!-- display subtotal including tax -->
<template x-if="segment.code === 'grand_total' && !checkoutConfig.includeTaxInGrandTotal">
    <div class="flex pt-4 pb-6 mx-auto mb-6 text-2xl border-b border-secondary-900/50 md:grid md:grid-cols-2 md:w-full">

        <div class="w-7/12 text-base leading-[1.875rem] text-left font-bold md:w-auto"
             x-html="`${segment.title}`"
        ></div>
        <div class="w-5/12 text-right text-base font-bold leading-[1.875rem] md:w-auto"
             x-text="formatPrice(segment.value, true)"
        ></div>


    </div>
</template>
