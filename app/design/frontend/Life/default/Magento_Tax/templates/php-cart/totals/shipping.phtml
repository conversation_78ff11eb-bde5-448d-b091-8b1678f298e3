<?php

declare(strict_types=1);

use Hyva\Theme\ViewModel\SvgIcons;

/**
 * The AlpineJS scope of this file is the method `initCartTotals()` in Magento_Checkout::php-cart/totals.phtml
 */

/** @var \Magento\Framework\View\Element\Template $block */
/** @var \Hyva\Theme\Model\ViewModelRegistry $viewModels */

/** @var SvgIcons $svgIcons */
$svgIcons = $viewModels->require(SvgIcons::class);

?>
<!-- display shipping including and excluding tax -->
<template x-if="segment.code === 'shipping' && quoteData.is_virtual === 0 && !!shippingMethod && 'both' === checkoutConfig.reviewShippingDisplayMode">
    <div>
        <div class="flex mb-2 text-sm md:grid md:grid-cols-2 md:w-full">
            <div class="w-7/12 text-left md:w-auto"
                 x-html="`${segment.title} ${excludingTaxMessage}` "
            ></div>
            <div class="w-5/12 text-right md:w-auto"
                 x-text="formatPrice(segment.value, true)"
            ></div>
        </div>

        <div class="flex mb-2 text-sm md:grid md:grid-cols-2 md:w-full">
            <div class="w-7/12 text-left md:w-auto"
                 x-html="`${segment.title} ${includingTaxMessage}` "
            ></div>
            <div class="w-5/12 text-right md:w-auto"
                 x-text="formatPrice(totalsData.shipping_incl_tax, true)"
            ></div>
        </div>
    </div>
</template>

<!-- display shipping excluding tax -->
<template x-if="segment.code === 'shipping' && quoteData.is_virtual === 0 && !!shippingMethod && 'excluding' === checkoutConfig.reviewShippingDisplayMode">
    <div class="flex mb-2 text-sm md:grid md:grid-cols-2 md:w-full">
        <div class="w-7/12 text-left md:w-auto"
             x-html="`${segment.title}` "
        ></div>
        <div class="w-5/12 text-right md:w-auto"
             x-text="formatPrice(segment.value, true)"
        ></div>
    </div>
</template>

<!-- display shipping including tax -->
<template x-if="segment.code === 'shipping' && quoteData.is_virtual === 0 && !!shippingMethod && 'including' === checkoutConfig.reviewShippingDisplayMode">
    <div class="flex mb-2 text-sm md:grid md:grid-cols-2 md:w-full">
        <div class="flex items-center w-7/12 text-left md:w-auto gap-x-2">
            <div x-html="`${segment.title}`"></div>
            <template x-if="segment.info">
                <div x-data="{show: false}">
                    <span class="cursor-pointer" @click="show = true">
                        <?= $svgIcons->infoHtml("w-4 h-4") ?>
                    </span>
                    <div
                        class="w-full fixed top-0 bottom-0 right-0 z-50 flex justify-end transition duration-500 ease-in-out bg-black/30"
                        x-show="show"
                        x-transition:enter-start="opacity-0"
                        x-transition:enter-end="opacity-100"
                        x-transition:leave-start="opacity-100"
                        x-transition:leave-end="opacity-0"
                    >
                        <div
                            class="info-drawer bg-white w-[90%] md:w-3/4 lg:w-1/2 2xl:w-5/12 h-full relative py-6 px-3 md:px-0 md:py-12 rounded-tl-xl rounded-bl-xl"
                        >
                            <div
                                class="absolute cursor-pointer top-6 right-6 md:top-12 md:right-12 text-primary"
                                @click="show = false"
                            >
                                <?= $svgIcons->closeHtml("w-6 h-6") ?>
                            </div>
                            <div
                                @click.outside="show = false"
                                class="h-full overflow-y-auto"
                                x-html="segment.info"
                            ></div>
                        </div>
                    </div>
                </div>
            </template>
        </div>
        <div class="w-5/12 text-right md:w-auto">
            <template x-if="segment.old_price">
                <span class="line-through old-price mr-2" x-text="formatPrice(segment.old_price, true)"></span>
            </template>
            <span x-text="formatPrice(totalsData.shipping_incl_tax, true)"></span>
        </div>
    </div>
</template>
