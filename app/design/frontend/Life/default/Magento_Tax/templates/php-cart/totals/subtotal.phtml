<?php
/**
 * The AlpineJS scope of this file is the method `initCartTotals()` in Magento_Checkout::php-cart/totals.phtml
 */
?>
<!-- display subtotal including and excluding tax -->
<template x-if="segment.code === 'subtotal' && 'both' === checkoutConfig.reviewTotalsDisplayMode">
    <div>
        <div class="flex mb-2 text-sm md:grid md:grid-cols-2 md:w-full">
            <div class="w-7/12 text-left md:w-auto"
                 x-html="`${segment.title} (${excludingTaxMessage})` "
            ></div>
            <div class="w-5/12 text-right md:w-auto"
                 x-text="hyvaformatPrice(totalsData.subtotal, true)"
            ></div>
        </div>

        <div class="flex mb-2 text-sm md:grid md:grid-cols-2 md:w-full">
            <div class="w-7/12 text-left md:w-auto"
                 x-html="`${segment.title} (${includingTaxMessage})` "
            ></div>
            <div class="w-5/12 text-right md:w-auto"
                 x-text="formatPrice(totalsData.subtotal_incl_tax, true)"
            ></div>
        </div>
    </div>
</template>

<!-- display subtotal excluding tax -->
<template x-if="segment.code === 'subtotal' && 'excluding' === checkoutConfig.reviewTotalsDisplayMode">
    <div class="flex mb-2 text-sm md:grid md:grid-cols-2 md:w-full">
        <div class="w-7/12 text-left md:w-auto"
             x-html="`${segment.title}` "
        ></div>
        <div class="w-5/12 text-right md:w-auto"
             x-text="formatPrice(totalsData.subtotal, true)"
        ></div>
    </div>
</template>

<!-- display subtotal including tax -->
<template x-if="segment.code === 'subtotal' && 'including' === checkoutConfig.reviewTotalsDisplayMode">
    <div class="flex mb-2 text-sm md:grid md:grid-cols-2 md:w-full">
        <div class="w-7/12 text-left md:w-auto"
             x-html="`${segment.title}`"
        ></div>
        <div class="w-5/12 text-right md:w-auto"
             x-text="formatPrice(totalsData.subtotal_incl_tax, true)"
        ></div>
    </div>
</template>
