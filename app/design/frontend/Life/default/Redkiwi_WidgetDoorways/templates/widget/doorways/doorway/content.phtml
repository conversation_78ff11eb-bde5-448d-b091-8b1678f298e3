<?php

/** @var \Magento\Framework\Escaper $escaper */
/** @var \Redkiwi\WidgetDoorways\Block\Widget\Doorways $block */

/** @var array $doorway */
$doorway = $block->getCurrentDoorway();

$title = trim($doorway['text']['title'] ?? '');
$subtitle = trim($doorway['text']['subtitle'] ?? '');

?>

<?php if ($title || $subtitle) : ?>
    <span class="html-block doorway-text">
        <?php if ($title) : ?>
            <span class="html-block doorway-text-title">
                <?= $escaper->escapeHtml($block->parseMarkdownLine($title), ['em', 'strong']) ?>
            </span>
        <?php endif ?>
        <?php if ($subtitle) : ?>
            <span class="html-block doorway-text-subtitle">
                <?= /* @noEscape */ $block->parseMarkdownText(html_entity_decode($subtitle)) ?>
            </span>
        <?php endif ?>
    </span>
<?php endif ?>
