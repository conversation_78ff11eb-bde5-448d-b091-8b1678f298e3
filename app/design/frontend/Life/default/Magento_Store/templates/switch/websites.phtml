<?php

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\Store;
use Hyva\Theme\ViewModel\SvgIcons;
use Life\ThemeConfigurations\ViewModel\WebsiteCountry;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;

/** @var Template $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */

/** @var Store $storeViewModel */
$storeViewModel = $viewModels->require(Store::class);

/** @var WebsiteCountry $websiteCountryViewModel */
$websiteCountryViewModel = $viewModels->require(WebsiteCountry::class);

/** @var SvgIcons $svgIcons */
$svgIcons = $viewModels->require(SvgIcons::class);
?>
<?php if (count($websiteCountryViewModel->getWebsites()) > 1): ?>
    <div class="w-full sm:w-1/2 md:w-full">
        <label class="label" for="website-selector">
            <?= $escaper->escapeHtml(__('Select your country')) ?>
        </label>
        <div class="relative">
            <select id="website-selector" class="w-auto form-select" onchange="location.href = this.value">
                <?php foreach ($websiteCountryViewModel->getWebsites() as $website): ?>
                    <option
                        value="<?= $escaper->escapeUrl($websiteCountryViewModel->getWebsiteUrl($website)) ?>"
                        <?php if ($website->getId() == $storeViewModel->getWebsiteId()): ?>selected="selected"<?php endif ?>
                    >
                        <?= $escaper->escapeHtml($websiteCountryViewModel->getWebsiteCountry($website)) ?>
                    </option>
                <?php endforeach; ?>
            </select>
            <?= $svgIcons->arrowDownHtml("w-5 h-5 text-white pointer-events-none absolute right-3 top-1.5") ?>
        </div>
    </div>
<?php endif; ?>
