<?php

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\Store;
use Hyva\Theme\ViewModel\StoreSwitcher;
use Life\ThemeConfigurations\ViewModel\StoreLanguage;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;
use Magento\Store\ViewModel\SwitcherUrlProvider;

/** @var Template $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */

/** @var SwitcherUrlProvider $switcherUrlProvider */
$switcherUrlProvider = $viewModels->require(SwitcherUrlProvider::class);

/** @var Store $storeViewModel */
$storeViewModel = $viewModels->require(Store::class);

/** @var StoreSwitcher $storeSwitcherViewModel */
$storeSwitcherViewModel = $viewModels->require(StoreSwitcher::class);

/** @var StoreLanguage $storeLanguageViewModel */
$storeLanguageViewModel = $viewModels->require(StoreLanguage::class);
?>
<?php if (count($storeSwitcherViewModel->getStores()) > 1): ?>
    <nav class="w-full sm:w-1/2 md:w-full" aria-label="<?= __('Language switcher') ?>">
        <?php foreach ($storeSwitcherViewModel->getStores() as $lang): ?>
            <?php if ($lang->getId() != $storeViewModel->getStoreId()): ?>
                <a href="<?= $escaper->escapeUrl($switcherUrlProvider->getTargetStoreRedirectUrl($lang)) ?>"
                   class="block px-4 py-2 lg:px-5 lg:py-2 hover:bg-gray-100"
                >
                    <?= $escaper->escapeHtml($storeLanguageViewModel->getStoreLanguage($lang)) ?>
                </a>
            <?php else: ?>
                <span><?= $escaper->escapeHtml($storeLanguageViewModel->getCurrentLanguage()) ?></span>
            <?php endif; ?>
        <?php endforeach; ?>
    </nav>
<?php endif; ?>
