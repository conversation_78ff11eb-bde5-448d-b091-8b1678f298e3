<?php

declare(strict_types=1);

/**
 * @var Magento\Framework\Escaper $escaper
 * @var Magento\Framework\View\Element\Template $block
 */
?>

<template x-if="searchResult.type == 'term'">
    <a
        class="inline-block mx-4 mb-2 text-sm text-link after:hidden before:hover:delay-0"
        x-bind:href="'<?= $escaper->escapeUrl($block->getData('suggestion_url')) ?>' + searchResult.title"
        :title="searchResult.title"
    >
        <span x-text="searchResult.title"></span>
        <span x-text="'(' + searchResult.num_results + ')'"></span>
    </a>
</template>
