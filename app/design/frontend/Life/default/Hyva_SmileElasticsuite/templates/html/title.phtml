<?php

declare(strict_types=1);

use Magento\Search\Helper\Data as SearchHelper;

/** @var \Magento\Framework\Escaper $escaper */
/** @var \Magento\Theme\Block\Html\Title $block */

$cssClass = $block->getCssClass() ? ' ' . $block->getCssClass() : 'text-3xl';

$searchHelper = $this->helper(SearchHelper::class);
if (!$searchHelper->getEscapedQueryText()) {
    return;
}
?>

<div class="container my-6 <?= /** @noEscape */ $cssClass ?>">
    <h1
        class="heading-2 page-title"
        <?php if ($block->getId()): ?> id="<?= $escaper->escapeHtmlAttr($block->getId()) ?>" <?php endif; ?>
    >
        <span class="base" data-ui-id="page-title-wrapper" <?= $block->getAddBaseAttribute() ?>>
            <?= $escaper->escapeHtml(__("You looked up for: '%1'", $searchHelper->getEscapedQueryText())) ?>
        </span>
    </h1>
    <?= $block->getChildHtml() ?>
</div>
