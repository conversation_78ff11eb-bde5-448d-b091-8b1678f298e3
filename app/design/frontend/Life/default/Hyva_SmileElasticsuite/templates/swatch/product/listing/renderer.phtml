<?php

declare(strict_types=1);

use Hyva\Theme\ViewModel\SwatchRenderer;
use Life\ProductsConnector\ViewModel\ConfigurableProductGallery;
use Magento\Swatches\ViewModel\Product\Renderer\Configurable as ConfigurableViewModel;

/** @var \Magento\Swatches\Block\Product\Renderer\Listing\Configurable $block */
/** @var \Magento\Framework\Escaper $escaper */
/** @var \Hyva\Theme\Model\ViewModelRegistry $viewModels */

/** @var ConfigurableViewModel $configurableViewModel */
$configurableViewModel = $viewModels->require(ConfigurableViewModel::class);

$product = $block->getProduct();
$productId = $product->getId();

$attributes = $block->decorateArray($block->getAllowAttributes());

/** @var SwatchRenderer $swatchRendererViewModel */
$swatchRendererViewModel = $viewModels->require(SwatchRenderer::class);

$layout = $block->getLayout();
$swatchItemBlock = $layout->getBlock('product.swatch.item');
/**
 * This is already set in layout, but because of a weird caching issue, the default
 * "Magento_Swatches::product/swatch-item.phtml" seems to be used for some products.
 * Reset this here, so it's always using the correct list swatch template!
 */
$swatchItemBlock->setTemplate('Magento_Swatches::product/list/swatch-item.phtml');
$swatchItemBlock->setData('product_id', $productId);

/** @var ConfigurableProductGallery $galleryViewModel */
$galleryViewModel = $viewModels->require(ConfigurableProductGallery::class);
$preselectedSwatchData = $galleryViewModel->getPreselectedSwatchData($product, $attributes);
?>
<?php if ($product->isSaleable() && count($attributes)): ?>
    <div
        x-data='initListConfigurableSwatchOptions($el, <?= /* @noEscape */ json_encode([
            'config' => json_decode($block->getJsonConfig(), true),
            'swatchConfig' => json_decode($block->getJsonSwatchConfig(), true),
            'preselectedSwatchData' => $preselectedSwatchData,
            'productId' => $productId,
            'productLink' => $escaper->escapeUrl($product->getProductUrl())
        ]); ?>)'
        x-init="findAllowedAttributeOptions();"
        @private-content-loaded.window="onGetCartData($event.detail.data)"
        @resize.window="resizeEvent()"
        @click.outside="show = false; $dispatch('toggle-colors-<?= /* @noEscape */ $productId; ?>', {})"
        class="absolute z-10 inset-x-4 bottom-4 max-md:hidden"
        x-show="show"
        x-cloak
    >
        <div class="flex flex-col gap-1">
            <?php foreach ($attributes as $attribute): ?>
                <?php $attributeId = $attribute->getAttributeId(); ?>
                <?php $productAttribute = $attribute->getProductAttribute(); ?>

                <?php if (
                    !$productAttribute->getUsedInProductListing()
                    || !$swatchRendererViewModel->isSwatchAttribute($productAttribute)
                ) {
                    continue;
                } ?>
                <div class="swatch-attribute <?=
                    $escaper->escapeHtmlAttr($productAttribute->getAttributeCode()) ?>"
                >
                    <div class="w-full swatch-attribute-options">
                        <div
                            class="flex items-center gap-2 px-2 py-1 transition-all bg-white border rounded-lg border-secondary-900/50 before:content-[attr(aria-label)] before:text-xs before:text-black/60 before:font-semibold"
                            role="radiogroup"
                            x-spread="scrollEvents"
                            aria-label="<?= $escaper->escapeHtml($productAttribute->getStoreLabel()) ?>"
                        >
                            <div class="flex flex-wrap items-center gap-y-1">
                                <template
                                    x-for="(item, index) in optionConfig.attributes[<?= (int) $attributeId ?>].options"
                                    :key="item.id"
                                >
                                    <?= /* @noEscape */ $swatchItemBlock->setData('attribute_id', $attributeId)->toHtml(); ?>
                                </template>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
<?php endif; ?>
