<section
    x-data='preselectFilteredOptions()'
    @preselect-filtered-options.window="updateSelectedFilters($event.detail)"
></section>
<script defer="defer">
    function preselectFilteredOptions() {
        return {
            updateSelectedFilters(filterOptions) {
                setTimeout(function() {
                    for (let attributeId in filterOptions) {
                        document.querySelectorAll('input[name="super_attribute[' + attributeId + ']"][aria-label="'
                            + filterOptions[attributeId] + '"]').forEach((swatchInput) => {
                            if (!swatchInput.checked) {
                                swatchInput.click();
                            }
                        });
                    }
                }, 300);
            }
        }
    }
</script>
