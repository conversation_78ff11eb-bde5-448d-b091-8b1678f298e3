<?php

declare(strict_types=1);

/** @var \Magento\LayeredNavigation\Block\Navigation\State $block */
/** @var \Magento\Framework\Escaper $escaper */

$activeFilters = [];
foreach ($block->getActiveFilters() as $filter) {
    if ($filter->getName() == __('Category')) {
        continue;
    }

    $values = $filter->getValue();
    if (
        ($filterModel = $filter->getFilter())
        && ($attributeModel = $filterModel->getAttributeModel())
        && ($attributeModel->getAttributeId())
    ) {
        // Take the last applied filter value
        $activeFilters[$attributeModel->getAttributeId()] = array_pop($values);
    }
}
?>
<section
    x-data="preselectFilteredOptions()"
    x-init='updateSelectedFilters(<?= /* @noEscape */ json_encode($activeFilters); ?>)'
></section>
