<?php

declare(strict_types=1);

use Hyva\Theme\ViewModel\ProductPage;
use Life\ProductsConnector\ViewModel\ProductPrice as LifeProductPrice;
use Life\ReplacePriceZeros\ViewModel\Config;

/** @var \Magento\Framework\View\Element\Template $block */
/** @var \Magento\Framework\Escaper $escaper */
/** @var \Hyva\Theme\Model\ViewModelRegistry $viewModels */

/** @var ProductPage $productViewModel */
$productViewModel = $viewModels->require(ProductPage::class);

/** @var Config $replaceZeros */
$replaceZeros = $viewModels->require(Config::class);

/** @var LifeProductPrice $lifeProductPriceViewModel */
$lifeProductPriceViewModel = $viewModels->require(LifeProductPrice::class);
$pricePercentageThreshold = $lifeProductPriceViewModel->getPricePercentageThresholdAmount();
?>
<script defer="defer">
    function initListConfigurableSwatchOptions(element, data) {
        const configurableOptionsComponent = initConfigurableOptions(
            data.productId ?? null,
            data.config ?? []
        );
        const swatchOptionsComponent = initSwatchOptions(data.swatchConfig ?? []);
        const preselectedSwatchData = data.preselectedSwatchData ?? [];

        return Object.assign(
            configurableOptionsComponent,
            swatchOptionsComponent,
            preselectedSwatchData,
            {
                mouseDown: false,
                startX: 0,
                maxScroll: 0,
                scrollLeft: null,
                slider: null,
                show: false,
                productLink: data.productLink ?? '',

                init() {
                    let self = this;

                    setTimeout(function() {
                        window.dispatchEvent(new CustomEvent('has-swatch-colors-' + data.productId, {}))
                    }, 300);

                    setTimeout(function() {
                        for (let key in preselectedSwatchData) {
                            self.changeOption(key, preselectedSwatchData[key]);
                        }
                    }, 300);

                    window.addEventListener('toggle-swatch-colors', function(event) {
                        let productId = event?.detail?.productId;

                        if (productId == data.productId) {
                            self.show = !self.show;
                        }
                    });
                },

                changeOption(optionId, value, skipUpdateGallery) {
                    this.selectedValues[optionId] = value;
                    this.findSimpleIndex();
                    this.findAllowedAttributeOptions();
                    this.updatePrices();
                    this.updateLinks();
                    !skipUpdateGallery && this.updateGallery();

                    if (this.optionConfig?.optionPrices[this.productIndex]) {
                        this.updateListPrices(this.productId, this.optionConfig.optionPrices[this.productIndex]);
                    }
                },

                updateLinks() {
                    if (!this.productId || !this.productLink) {
                        return;
                    }

                    let urlQueryParams = new URLSearchParams();
                    this.selectedValues.forEach((value, key) => {
                        urlQueryParams.set(key, value);
                    });

                    if (!urlQueryParams.toString().length) {
                        return;
                    }

                    Array.from(document.querySelectorAll('[data-link-product-id="' + this.productId + '"]')).map(link => {
                        link.href = this.productLink + '?' + urlQueryParams.toString();
                    });
                },

                updateGallery() {
                    if (!this.productIndex) {
                        return;
                    }

                    window.dispatchEvent(
                        new CustomEvent(
                            "update-list-images-" + data.productId,
                            {detail: this.productIndex}
                        )
                    );
                },

                preselectQuerystringItems() {
                    // pre-select option like ?size=167
                    const urlQueryParams = new URLSearchParams(window.location.search.replace('?', ''));
                    Object.values(this.optionConfig.attributes).map(attribute => {
                        const skipUpdateGallery = false;
                        if (urlQueryParams.get(attribute.code) !== null) {
                            let optionId = urlQueryParams.get(attribute.code);
                            let found = false;
                            Object.values(attribute.options).map(option => {
                                if (found === false && option.label === optionId) {
                                    optionId = option.id;
                                }
                            });
                            this.changeOption(attribute.id, optionId, skipUpdateGallery);
                        }
                    });
                },

                scrollEvents: {
                    ['@mousedown'](e) {
                        this.slider = e.target.closest('.snap');
                        if (!this.slider) {
                            return;
                        }
                        this.maxScroll = this.slider.scrollWidth - this.slider.offsetWidth;
                        this.startX = e.pageX - this.slider.offsetLeft;
                        this.scrollLeft = this.slider.scrollLeft;
                        this.mouseDown = true;
                    },
                    ['@mouseout.self']() {
                        this.mouseDown = false;
                    },
                    ['@mouseup']() {
                        this.mouseDown = false;
                    },
                    ['@mousemove'](e) {
                        e.preventDefault();
                        if (!this.mouseDown) {
                            return;
                        }
                        const x = e.pageX - this.slider.offsetLeft;
                        const scroll = x - this.startX;
                        const scrollLeft = this.scrollLeft - scroll;

                        if (scrollLeft > this.maxScroll) {
                            this.slider.scrollLeft = this.maxScroll;
                            return
                        }
                        this.slider.scrollLeft = this.scrollLeft - scroll;
                    },
                    ['@onselectstart']() {
                        return false;
                    }
                },

                resizeEvent() {
                    Array.from(this.$el.querySelectorAll('.snap')).forEach(slider => {
                        slider.scrollLeft = 0;
                    })
                },

                updateListPrices(productId, priceData) {
                    const priceWrapper = document.querySelector('[data-price-wrapper="product-id-' + productId + '"]');

                    if (!priceWrapper) {
                        return;
                    }

                    const priceBox = priceWrapper.querySelector('[data-price-type="finalPrice"] .price');
                    if (!priceBox || !priceData) {
                        return;
                    }

                    const msrpPriceBox = priceWrapper.querySelector('[data-price-type="msrpPrice"] span.line-through');
                    let msrpPrice = priceData?.msrpPrice?.amount,
                        finalPrice = priceData?.finalPrice?.amount,
                        oldPrice = priceData?.oldPrice?.amount;

                    priceBox.innerHTML = this.formatPrice(finalPrice);

                    if (msrpPriceBox) {
                        msrpPriceBox.innerHTML = (msrpPrice > finalPrice) ? this.formatPrice(msrpPrice) : '';
                    }

                    const discountPriceBox = priceWrapper.querySelector('[data-price-type="discountPrice"]');
                    if (discountPriceBox) {
                        this.discountPercentage = Math.round(((oldPrice - finalPrice) / oldPrice) * 100);
                        this.discountAmount = Math.round(oldPrice - finalPrice);
                        this.showPercentage = (oldPrice <= <?= $pricePercentageThreshold; ?>);

                        let discountContent = '';
                        if (this.showPercentage && this.discountPercentage > 0) {
                            discountContent = '-' + this.discountPercentage + '%';
                        }
                        if (!this.showPercentage && this.discountAmount > 0) {
                            discountContent = '-' + '<?= $lifeProductPriceViewModel->getCurrencySymbol(); ?>' + this.discountAmount;
                        }

                        discountPriceBox.innerHTML = discountContent;
                    }
                },

                formatPrice(value, showSign) {
                    let formatter = new Intl.NumberFormat(
                        document.documentElement.lang,
                        {
                            style: 'currency',
                            currency: '<?= $escaper->escapeHtml($productViewModel->getCurrencyData()['code']) ?>',
                            signDisplay: showSign ? "always" : "auto"
                        }
                    );
                    let formattedPrice = ((typeof Intl.NumberFormat.prototype.formatToParts) ?
                        formatter.formatToParts(value).map(({type, value}) => {
                            switch (type) {
                                case 'currency':
                                    return '<?= $productViewModel->getCurrencyData()['symbol'] ?: ""; ?>' || value;
                                case 'minusSign':
                                    return '- ';
                                case 'plusSign':
                                    return '+ ';
                                default :
                                    return value;
                            }
                        }).reduce((string, part) => string + part) :
                        formatter.format(value)).replace(/\s/g, '');

                    if (<?= $replaceZeros->canReplaceZeros() ? 'true' : 'false'; ?>) {
                        return formattedPrice
                            // Replace ,00/.00 with ,-/.-
                            .replace(/\.00$/g, '.-')
                            .replace(/\,00$/g, ',-')
                            // Remove thousands separator
                            .replace(/(,|.)(?=\d{3})/g, '')
                    }
                    return formattedPrice;
                }
            }
        );
    }
</script>
