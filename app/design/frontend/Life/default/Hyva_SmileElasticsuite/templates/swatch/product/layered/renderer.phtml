<?php

declare(strict_types=1);

use Life\CategoryLayeredNavigationSpecialPage\ViewModel\SpecialCategoryPage;
use Life\ThemeConfigurations\ViewModel\HtmxUpdate;
use Magento\Swatches\Block\LayeredNavigation\RenderLayered;
use Magento\Swatches\ViewModel\Product\Renderer\Configurable as ConfigurableViewModel;

/** @var RenderLayered $block */
/** @var \Magento\Framework\View\Helper\SecureHtmlRenderer $secureRenderer */
/** @var \Magento\Framework\Escaper $escaper */
/** @var \Hyva\Theme\Model\ViewModelRegistry $viewModels */

/** @var ConfigurableViewModel $configurableViewModel */
$configurableViewModel = $viewModels->require(ConfigurableViewModel::class);

/** @var SpecialCategoryPage $specialCategoryPageViewModel */
$specialCategoryPageViewModel = $viewModels->require(SpecialCategoryPage::class);

$mappedCategoryUrl = $currentCategoryUrl = null;
$currentCategory = $specialCategoryPageViewModel->getCurrentCategory();
if ($currentCategory && $specialCategoryPageViewModel->isSpecialCategoryPage()) {
    $mappedCategoryUrl = $specialCategoryPageViewModel->getMappedCategory()->getUrl();
    $currentCategoryUrl = $currentCategory->getUrl();
}

$swatchData = $block->getSwatchData();
?>

<div class="swatch-attribute swatch-layered <?= $escaper->escapeHtmlAttr($swatchData['attribute_code']) ?>"
     x-data="initLayeredSwatch()"
>
    <div class="clearfix swatch-attribute-options">
        <?php foreach ($swatchData['options'] as $option => $label): ?>
        <?php
            $link = $label['link'];
            if ($mappedCategoryUrl && $currentCategoryUrl) {
                $link = str_replace($currentCategoryUrl, $mappedCategoryUrl, $link);
            }
            if (str_contains($label['custom_style'], 'selected')) {
                $label['custom_style'] = 'selected !border-primary-500';
            }
        ?>
            <button
                hx-get="<?= $escaper->escapeUrl($link) ?>"
                hx-indicator="#products-loader, #products-loader-small"
                hx-select=".products-grid"
                hx-select-oob="<?= HtmxUpdate::CATEGORY_ELEMENTS_ID; ?>"
                hx-target=".products-grid"
                hx-swap="outerHTML"
                hx-replace-url="true"
                rel="nofollow"
                aria-label="
                <?= $escaper->escapeHtmlAttr(__("%1, %2 filter",$label['label'], $swatchData['attribute_label'])); ?>
                <?php if (str_contains($label['custom_style'], 'selected')): ?>
                  <?= ', ' . $escaper->escapeHtml(__('filter selected')); ?>
                <?php endif; ?>
                "
                class="swatch-option-link-layered swatch-option !rounded-full !w-9 !h-9 cursor-pointer relative select-none !bg-[length:2.25rem_2.25rem]
                <?= $block->escapeHtmlAttr($label['custom_style']) ?>"
                :style="getSwatchBackgroundStyle(
                    '<?= (string) $swatchData['swatches'][$option]['type'] ?>',
                    '<?= (string) $swatchData['swatches'][$option]['value'] ?>',
                    '<?= $escaper->escapeUrl($block->getSwatchPath(
                   'swatch_image',
                   $swatchData['swatches'][$option]['value']
                )) ?>'
                )"
                <?php if ($configurableViewModel->getShowSwatchTooltip()): ?>
                    <?php $swatchThumbPath = $block->getSwatchPath(
                        'swatch_thumb',
                        $swatchData['swatches'][$option]['value']
                    ); ?>
                    @mouseenter.self="activeTooltipItem = {
                            attribute: '<?= $escaper->escapeHtmlAttr($swatchData['swatches'][$option]['value']) ?>',
                            type: '<?= $escaper->escapeHtmlAttr($swatchData['swatches'][$option]['type']) ?>',
                            id: '<?= $escaper->escapeHtmlAttr($option) ?>',
                            label: '<?= $escaper->escapeHtmlAttr($label['label']) ?>',
                            thumb: '<?= $escaper->escapeUrl($swatchThumbPath) ?>',
                            value: '<?= $escaper->escapeHtmlAttr($swatchData['swatches'][$option]['value']) ?>'
                        }; tooltipPositionElement = $event.target;"
                    @mouseleave.self="activeTooltipItem = false"
                <?php endif; ?>
            >
                <?php if ((string)$swatchData['swatches'][$option]['type'] === "0"): ?>
                    <?= $escaper->escapeHtml($swatchData['swatches'][$option]['value'] ?: $label['label']) ?>
                <?php endif; ?>
            </button>
        <?php endforeach; ?>
    </div>
</div>
