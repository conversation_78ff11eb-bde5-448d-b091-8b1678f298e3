<?php

/** @var \Magento\Framework\View\Element\Template $block */
/** @var \Magento\Framework\Escaper $escaper */

/** @var \Hyva\Theme\Model\ViewModelRegistry $viewModels */
$storeViewModel = $viewModels->require(\Hyva\SmileElasticsuite\ViewModel\Store::class);
/** @var \Hyva\SmileElasticsuite\ViewModel\Store $storeViewModel */
$currencyCode = $storeViewModel ? $storeViewModel->getStore()->getCurrentCurrency()->getCode() : '';

$isPriceSlider = (strpos((string)$block->getDataRole(), 'price') !== false);
?>

<script defer="defer">
    function rangeSlider(options, jsConfig, priceSlider) {
        "use strict";

        return {
            options: jsConfig,
            priceSlider,
            lastThumbUpdatedMax: true,
            minfocused: false,
            maxfocused: false,
            sliderZindex: 20,
            messageTemplates: {
                "displayOne": '<span class="msg"><?= __('1 item') ?></span>',
                "displayCount": '<span class="msg"><%- count %> <?= __('items') ?></span>',
                "displayEmpty": '<span class="msg-error"><?= __('No items in the current range.') ?></span>'
            },
            minprice: 0,
            maxprice: 0,
            fromLabel: '',
            toLabel: '',
            min: 0,
            max: 0,
            minZindex: 0,
            maxZindex: 0,
            minthumb: 0,
            maxthumb: 0,
            message: '',
            url: '',
            intervals: [],

            initialize: function () {
                this.options = Object.assign({}, this.options, options);
                this.url = this.options.urlTemplate;

                if (this.options.sliderZindex !== undefined) {
                    this.sliderZindex = this.options.sliderZindex;
                }

                this.showAdaptiveSlider = false;
                if (this.options.showAdaptiveSlider !== undefined) {
                    this.showAdaptiveSlider = this.options.showAdaptiveSlider;
                }

                if (this.options.maxLabelOffset === undefined) {
                    this.options.maxLabelOffset = 0.01;
                }

                if (this.options.rate === undefined) {
                    this.options.rate = 1.0000;
                }

                if (this.showAdaptiveSlider) {
                    this._initAdaptiveSliderValues();
                } else {
                    this._initSliderValues();
                }

                this.mintrigger();
                this.maxtrigger();
            },
            _initSliderValues: function () {
                this.rate = parseFloat(this.options.rate);
                this.minprice = Math.floor(this.options.currentValue.from * this.rate);
                this.maxprice = Math.round(this.options.currentValue.to * this.rate);
                this.intervals = this.options.intervals.map(
                    function (item) {
                        item.value = Math.round(item.value * this.rate);
                        return item;
                    }.bind(this)
                );
                this.min = Math.floor(this.options.minValue * this.rate);
                this.max = Math.round(this.options.maxValue * this.rate);
            },

            _initAdaptiveSliderValues: function () {
                this.intervals = this.options.adaptiveIntervals;
                this.options.adaptiveIntervals[0].value = Math.floor(this.options.adaptiveIntervals[0].value);
                this.minprice = this._getAdaptiveValue(Number(this.options.currentValue.from));
                this.maxprice = this._getAdaptiveValue(Number(this.options.currentValue.to));
                this.rate = parseFloat(this.options.rate);
                this.intervals = this.intervals.map(
                    function (item) {
                        item.originalValue = Math.ceil(item.originalValue * this.rate);
                        return item;
                    }.bind(this)
                );
                this.min = this.intervals[0].value;
                this.max = this.intervals[this.intervals.length - 1].value;
            },
            mintrigger: function () {
                this.lastThumbUpdatedMax = false;
                this.validation();
                this.minprice = this._getClosestAdaptiveValue(Math.min(this.minprice, this.maxprice));
                this.minthumb = ((this.minprice - this.min) / (this.max - this.min)) * 100;
                this._onChangeAfter();
            },
            maxtrigger: function () {
                this.lastThumbUpdatedMax = true;
                this.validation();
                this.maxprice = this._getClosestAdaptiveValue(Math.max(this.maxprice, this.minprice));
                this.maxthumb = ((this.maxprice - this.min) / (this.max - this.min)) * 100;
                this._onChangeAfter();
            },
            updateFocusState: function (targetName, stateValue) {
                this[`${targetName}focused`] = stateValue;
            },
            _onChangeAfter: function () {
                this._updateSliderZindexes();
                this._applyRange();
                this._refreshDisplay();
                htmx.process(this.$refs.button);
            },
            validation: function () {
                if (/^[\d.]*$/.test(this.minprice)) {
                    if (this.minprice > this.max) {
                        this.minprice = this._getAdaptiveValue(Number(this.options.currentValue.from));
                    }
                    if (this.minprice < this.min) {
                        this.minprice = this.min;
                    }
                } else {
                    this.minprice = this._getAdaptiveValue(Number(this.options.currentValue.from));
                }
                if (/^[\d.]*$/.test(this.maxprice)) {
                    if (this.maxprice > this.max) {
                        this.maxprice = this.max;
                    }
                    if (this.maxprice < this.min) {
                        this.maxprice = this._getAdaptiveValue(Number(this.options.currentValue.to));
                    }
                } else {
                    this.maxprice = this._getAdaptiveValue(Number(this.options.currentValue.to))
                }
            },
            getItemCount: function () {
                var from = this.minprice,
                    to = this.maxprice,
                    intervals = this.intervals;
                return intervals.map(function (item) {
                        return item.value >= from && item.value < to ? item.count : 0;
                    })
                    .reduce(function (a, b) {
                        return a + b;
                    });
            },
            _refreshDisplay: function () {
                this.count = this.getItemCount();
                let minprice = this._getOriginalValue(this.minprice);
                if (minprice >= this._getOriginalValue(this.max)) {
                    minprice = this._getOriginalValue(this.max) - this.options.maxLabelOffset;
                }
                this.fromLabel = this._formatLabel(minprice);

                let maxprice = this._getOriginalValue(this.maxprice) - this.options.maxLabelOffset;
                if (maxprice < this._getOriginalValue(this.min)) {
                    maxprice = this._getOriginalValue(this.maxprice);
                }
                this.toLabel = this._formatLabel(maxprice);

                var messageTemplate = this.messageTemplates[this.count > 0 ? (this.count > 1 ? 'displayCount' :
                    'displayOne') : 'displayEmpty'];
                this.message = this._unescape(messageTemplate.replace('<%- count %>', this.count));
            },
            _formatLabel: function (value) {
                var formattedValue = value;

                if (this.priceSlider && this.options.fieldFormat) {
                    formattedValue = this._formatPrice(value);
                } else if (this.options.fieldFormat) {
                    formattedValue = this.options.fieldFormat.pattern.replace(
                        '%s',
                        Number.parseFloat(formattedValue).toFixed(this.options.fieldFormat.precision)
                    );
                }

                return formattedValue;
            },
            _formatPrice: function (value) {
                var formatter = new Intl.NumberFormat(
                    document.documentElement.lang, {
                        style: 'currency',
                        currency: '<?= $currencyCode ?>',
                    }
                );

                return formatter.format(value);
            },
            _applyRange: function () {
                var range = {
                    from: this._getOriginalValue(this.minprice) * (1 / this.rate),
                    to: this._getOriginalValue(this.maxprice) * (1 / this.rate),
                };

                this.url = this._unescape(this.options.urlTemplate.replace('<%- from %>', range.from).replace(
                    '<%- to %>', range.to));
            },
            _updateSliderZindexes: function () {
                this.minZindex = this.sliderZindex;
                this.maxZindex = this.sliderZindex;
                if (this.minprice == this.maxprice) {
                    if (this.minprice === this.min || (this.minprice !== this.min && this.lastThumbUpdatedMax)) {
                        this.maxZindex = this.sliderZindex + 1;
                    }

                    if (this.maxprice === this.max || (this.maxprice !== this.max && !this.lastThumbUpdatedMax)) {
                        this.minZindex = this.sliderZindex + 1;
                    }

                }
            },
            _getAdaptiveValue: function (value) {
                if (!this.showAdaptiveSlider) {
                    return value;
                }

                var adaptiveValue = this.intervals[0].value;
                var found = false;
                this.intervals.forEach(function (item) {
                    if (found === false && item.originalValue === value) {
                        adaptiveValue = item.value;
                        found = true;
                    }

                    if (found === false && item.originalValue < value) {
                        adaptiveValue = item.value;
                    }
                });

                return adaptiveValue;
            },

            _getClosestAdaptiveValue: function (value) {
                if (!this.showAdaptiveSlider) {
                    return value;
                }

                var closestValue = this.intervals[0].value;
                var found = false;
                this.intervals.forEach(function (item) {
                    if (item.value === value) {
                        closestValue = value;
                        found = true;
                    }

                    if (found === false && item.value < value) {
                        closestValue = item.value;
                    }
                });

                return closestValue;
            },

            _getOriginalValue: function (value) {
                if (!this.showAdaptiveSlider) {
                    return value;
                }

                var originalValue = null;
                this.intervals.forEach(function (item) {
                    if (item.value === value) {
                        originalValue = item.originalValue;
                    }
                });

                return originalValue;
            },

            _unescape: function (str) {
                return str.replace(/&lt;%|%3C%/g, '<%').replace(/%&gt;|%%3E/g, '%>');
            },
        }
    }

</script>
