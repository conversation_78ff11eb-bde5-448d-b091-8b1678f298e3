<?php

declare(strict_types=1);

use Hyva\Theme\ViewModel\SvgIcons;
use Life\CategoryLayeredNavigationSpecialPage\ViewModel\SpecialCategoryPage;
use Life\ThemeConfigurations\ViewModel\HtmxUpdate;
use Magento\Catalog\Helper\Data;

/** @var \Magento\LayeredNavigation\Block\Navigation $block */
/** @var \Magento\Framework\Escaper $escaper */
/** @var ViewModelRegistry $viewModels */

/** @var Data $catalogHelper */
$catalogHelper = $this->helper(Data::class);

/** @var SvgIcons $svgIcons */
$svgIcons = $viewModels->require(SvgIcons::class);

/** @var SpecialCategoryPage $specialCategoryPageViewModel */
$specialCategoryPageViewModel = $viewModels->require(SpecialCategoryPage::class);

$activeFiltersName = [];
$activeFiltersCount = 0;
$activeFiltersByAttribute = [];
foreach ($block->getLayer()->getState()->getFilters() as $activeFilter) {
    $filterName = (string)$activeFilter->getName();

    // Category is pre-selected in the background for special category page, so it should not appear as active
    if (
        $filterName === (string)__('Category')
        && $specialCategoryPageViewModel->isSpecialCategoryPage()
    ) {
        continue;
    }

    $activeFiltersName[] = $filterName;
    if (isset($activeFiltersByAttribute[$filterName])) {
        $activeFiltersByAttribute[$filterName] = $activeFiltersByAttribute[$filterName] + 1;
    } else {
        $activeFiltersByAttribute[$filterName] = 1;
    }
    $activeFiltersCount++;
}
$activeFilters = json_decode($block->getActiveFilters());

$quickViewLimit = 3;
?>
<?php if ($block->canShowBlock()) : ?>
    <div class="relative product-filter">
        <!-- Layered navigation quick view -->
        <?php $filterIndex = 0; ?>
        <div class="flex flex-wrap justify-between gap-2 mb-2 md:gap-y-4">
            <div
                class="flex gap-2"
                id="quick-filters"
            >
                <div
                    class="flex-wrap gap-2 flex"
                    x-data="{ openedIndex: -1 }"
                >
                    <?php foreach ($block->getFilters() as $filter) : ?>
                        <?php
                        // For category pages there is no need to have category filter
                        if ($catalogHelper->getCategory() && ((string)$filter->getName() === (string)__('Category'))) {
                            continue;
                        }

                        if ($filterIndex > 2) {
                            break;
                        }
                        ?>
                        <?php if ($filter->getItemsCount() || in_array($filter->getName(), $activeFiltersName)) : ?>
                            <?php
                            $filterRenderedContent = $block->getChildBlock('renderer')->render($filter);
                            if (empty(trim($filterRenderedContent))) {
                                continue;
                            }
                            ?>

                            <div
                                role="group"
                                aria-labelledby="filter-option-<?= $filterIndex ?>-title"
                                class="filter-option hidden md:block"
                                id="<?= $filter->getData('attribute_model')?->getData('attribute_code') ?>"
                            >
                                <div
                                    id="filter-option-<?= $filterIndex ?>-title"
                                    class="filter-options-title"
                                >
                                    <button
                                        class="flex items-center justify-between w-full cursor-pointer gap-x-2"
                                        @click="openedIndex == <?= $filterIndex ?> ? openedIndex = -1 : openedIndex = <?= $filterIndex ?>"
                                        aria-controls="filter-option-<?= $filterIndex ?>-content"
                                        :aria-expanded="openedIndex == <?= $filterIndex ?>"
                                    >
                                        <span class="flex items-center text-base leading-7 title gap-x-2">
                                            <?= $escaper->escapeHtml(__($filter->getName())) ?>
                                            <?php if (
                                                $activeFiltersByAttribute
                                                && !empty($activeFiltersByAttribute[(string)$filter->getName()])
                                            ) : ?>
                                                <span
                                                    class="flex items-center justify-center w-6 h-6 bg-white border rounded-full border-secondary-900/50 text-[0.6875rem]"
                                                >
                                                    <?= $activeFiltersByAttribute[(string)$filter->getName()] ?>
                                                </span>
                                            <?php endif; ?>
                                            <span class="sr-only"><?= $escaper->escapeHtml(__(' filter')) ?></span>
                                        </span>
                                        <span>
                                            <?= $svgIcons->arrowDownHtml("w-6 h-6") ?>
                                        </span>
                                    </button>
                                </div>
                                <div
                                    id="filter-option-<?= $filterIndex ?>-content"
                                    class="absolute left-0 z-[25] p-5 bg-white shadow-lg rounded-xl min-w-max top-[calc(100%+0.5rem)]"
                                    x-show="openedIndex == <?= $filterIndex ?>"
                                    style="display: none;"
                                >
                                    <?= /* @noEscape */ $filterRenderedContent ?>
                                    <?= $block->fetchView((string) $block->getTemplateFile('Hyva_Theme::ui/products-loading-small.phtml')) ?>
                                </div>
                            </div>
                            <?php
                            $filterIndex++;
                            ?>
                        <?php endif; ?>
                    <?php endforeach; ?>
                    <div x-data="{ defaultBtnVisible: null, productsVisible: null }" @products-visible.window="productsVisible = $event.detail.visible">
                        <div
                            role="group"
                            class="all-filters<?php if ($filterIndex < $quickViewLimit) : ?> md:hidden<?php endif; ?>"
                            x-intersect.full="defaultBtnVisible = true"
                            x-intersect:leave.full="defaultBtnVisible = false"
                            @click.prevent.stop="$dispatch('toggle-all-filters')"
                        >
                            <span class="flex items-center gap-x-2">
                                <span class="max-md:hidden"><?= $escaper->escapeHtml(__('All filters')); ?></span>
                                <span class="md:hidden"><?= $escaper->escapeHtml(__('Filters')); ?></span>
                                <?php if ($activeFiltersCount) : ?>
                                    <span
                                        class="flex items-center justify-center w-6 h-6 bg-white border rounded-full border-secondary-900/50 text-[0.6875rem] text-black"
                                    ><?= $escaper->escapeHtml($activeFiltersCount) ?></span>
                                <?php endif; ?>
                                <?= $svgIcons->filterHtml("w-6 h-6 text-white") ?>
                            </span>
                        </div>

                        <!-- Filter button copy -->
                        <div
                            role="group"
                            class="fixed z-50 motion-safe:transition-all ease-in-out transform -translate-x-1/2 all-filters md:!hidden duration-250 left-1/2"
                            @click.prevent.stop="$dispatch('toggle-all-filters')"
                            :class="{ '-bottom-12': defaultBtnVisible || !productsVisible, 'bottom-6': !defaultBtnVisible && productsVisible }"
                        >
                            <span class="flex items-center gap-x-2">
                                <span class="max-md:hidden"><?= $escaper->escapeHtml(__('All filters')); ?></span>
                                <span class="md:hidden"><?= $escaper->escapeHtml(__('Filters')); ?></span>
                                <?php if ($activeFiltersCount) : ?>
                                    <span
                                        class="flex items-center justify-center w-6 h-6 bg-white border rounded-full border-secondary-900/50 text-[0.6875rem] text-black"
                                    ><?= $escaper->escapeHtml($activeFiltersCount) ?></span>
                                <?php endif; ?>
                                <?= $svgIcons->filterHtml("w-6 h-6 text-white") ?>
                            </span>
                        </div>
                    </div>
                </div>

                
            </div>

            <?= $block->getParentBlock()->getChildHtml('toolbar') ?>
            <?= $block->getChildHtml('state') ?>
        </div>

        <div
            role="region"
            aria-label="<?= $escaper->escapeHtmlAttr(__('Product filters')) ?>"
            class="
                fixed top-0 bottom-0 left-0 w-[100vw] z-50 block
                motion-safe:transition motion-safe:duration-500 motion-safe:ease-in-out
            "
            x-data="initLayeredNavigation()"
            @toggle-all-filters.window="visible = !visible"
            x-show="visible"
            x-cloak
            x-transition:enter-start="opacity-0"
            x-transition:enter-end="opacity-100"
            x-transition:leave-start="opacity-100"
            x-transition:leave-end="opacity-0"
        >
            <div
                @click="visible = false"
                class="absolute top-0 bottom-0 left-0 right-0 bg-black/30"
            ></div>

            <div
                class="fixed z-10 h-[100dvh] bg-white w-[90%] md:w-3/4 lg:w-5/12 2xl:w-1/4 py-6 px-3 md:px-10 md:py-12 rounded-tr-xl rounded-br-xl filter-modal">
                <div class="flex items-center justify-between pb-2 mb-4 border-b border-b-secondary-900/50">
                    <h4><?= $escaper->escapeHtml(__('Shop By')) ?></h4>
                    <span
                        @click="visible = false"
                        class="cursor-pointer"
                    ><?= $svgIcons->closeHtml("w-6 h-6") ?></span>
                </div>
                <div
                    id="filters-content"
                    class="pt-3 block-content filter-content overflow-y-auto h-[calc(100vh-5.625rem)] pb-20"
                >
                    <?php $filterIndex = 0; ?>
                    <a
                        href="#products-list"
                        class="block px-12 py-4 text-center text-white bg-blue-600 rounded-sm sr-only focus:not-sr-only"
                    >
                        <?= $escaper->escapeHtml(__('Skip to product list')) ?>
                    </a>

                    <?php foreach ($block->getFilters() as $filter) : ?>
                        <?php
                        // For category pages there is no need to have category filter
                        if ($catalogHelper->getCategory() && ((string)$filter->getName() === (string)__('Category'))) {
                            continue;
                        }
                        ?>
                        <?php if ($filter->getItemsCount() || in_array($filter->getName(), $activeFiltersName)) : ?>
                            <?php
                            $filterRenderedContent = $block->getChildBlock('renderer')->render($filter);
                            if (empty(trim($filterRenderedContent))) {
                                continue;
                            }

                            $showOpen = in_array($filterIndex, $activeFilters);
                            ?>
                            <div
                                role="group"
                                aria-labelledby="filter-option-<?= $filterIndex ?>-title"
                                class="filter-option"
                                id="<?= $filter->getData('attribute_model')?->getData('attribute_code') ?>"
                            >
                                <div
                                    id="filter-option-<?= $filterIndex ?>-title"
                                    class="filter-options-title"
                                >
                                    <div class="flex items-center justify-between w-full">
                                        <span class="text-lg">
                                            <?= $escaper->escapeHtml(__($filter->getName())) ?>
                                        </span>
                                    </div>
                                </div>
                                <div
                                    id="filter-option-<?= $filterIndex ?>-content"
                                    class="pt-3 filter-options-content"
                                >
                                    <?= /* @noEscape */ $filterRenderedContent ?>
                                </div>
                            </div>
                            <?php $filterIndex++ ?>
                        <?php endif; ?>
                    <?php endforeach; ?>

                    <?php if ($block->getLayer()->getState()->getFilters()) : ?>
                        <div
                            class="absolute bottom-0 left-0 flex justify-center w-full gap-3 px-3 py-6 bg-gradient-to-b from-white/0 to-white">
                            <button
                                hx-get="<?= $escaper->escapeUrl($block->getClearUrl()) ?>"
                                hx-indicator="#products-loader, #products-loader-small"
                                hx-select=".products-grid"
                                hx-select-oob="<?= HtmxUpdate::CATEGORY_ELEMENTS_ID; ?>"
                                hx-target=".products-grid"
                                hx-swap="outerHTML"
                                hx-replace-url="true"
                                class="text-sm btn btn-secondary"
                            ><span><?= $escaper->escapeHtml(__('Clear')) ?></span></button>
                            <button
                                class="text-sm btn btn-primary"
                                @click="visible = false"
                            ><span><?= $escaper->escapeHtml(__("Show %1 product(s)", $block->getLayer()->getProductCollection()->getSize())) ?></span></button>
                        </div>
                    <?php endif; ?>

                    <?= $block->fetchView((string) $block->getTemplateFile('Hyva_Theme::ui/products-loading-small.phtml')) ?>
                </div>
            </div>
        </div>
    </div>

    <script defer="defer">
        function initLayeredNavigation() {
            return {
                visible: false,
            }
        }

    </script>
<?php endif; ?>
