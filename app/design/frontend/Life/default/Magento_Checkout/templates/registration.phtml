<?php

declare(strict_types=1);

use Magento\Checkout\Block\Registration;
use Magento\Framework\Escaper;

/** @var Registration $block */
/** @var Escaper $escaper */
?>
<div class="container mt-10">
    <p><?= $escaper->escapeHtml(__('You can track your order status by creating an account.')) ?></p>
    <p><?= $escaper->escapeHtml(__('Email Address:')) ?> <?= $escaper->escapeHtml($block->getEmailAddress()) ?></p>
    <div class="my-4">
        <a class="inline-block btn btn-primary" href="<?= $escaper->escapeUrl($block->getCreateAccountUrl()) ?>">
            <span><?= $escaper->escapeHtml(__('Create an Account')) ?></span>
        </a>
    </div>
</div>
