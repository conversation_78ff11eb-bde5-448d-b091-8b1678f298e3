<?php

declare(strict_types=1);

use Hyva\Theme\ViewModel\SvgIcons;

/** @var \Magento\Checkout\Block\Cart\Item\Renderer\Actions\Remove $block */
/** @var \Hyva\Theme\Model\ViewModelRegistry $viewModels */
/** @var \Magento\Framework\Escaper $escaper */

/** @var SvgIcons $svgIcons */
$svgIcons = $viewModels->require(SvgIcons::class);
$item = $block->getItem();

if ($item->getProductType() === 'configurable') {
    $productOptions = $item->getProduct()->getTypeInstance(true)->getOrderOptions($item->getProduct());
    $productName = $productOptions["simple_name"];
} else {
    $productName = $item->getName();
}
?>
<button
    class="flex items-center action action-delete gap-x-1 text-link after:hidden before:hover:delay-0"
    x-data="{}"
    @click.prevent='hyva.postForm(<?= /* @noEscape */ $block->getDeletePostJson() ?>)'
    aria-label="<?= $escaper->escapeHtmlAttr(__('Remove %1', $productName)) ?>"
    type="button"
>
    <?= $svgIcons->trashHtml('w-4 h-4', 16, 16, ['aria-hidden' => 'true']) ?>
    <span class="max-md:hidden"><?= $escaper->escapeHtml(__('Remove')) ?></span>
</button>
