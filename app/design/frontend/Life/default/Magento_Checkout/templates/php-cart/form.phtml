<?php

declare(strict_types=1);

use Hyva\Theme\ViewModel\Cart\ItemOutput;
use Hyva\Theme\ViewModel\Currency;
use Hyva\Theme\ViewModel\Modal;
use Life\HyvaCheckoutExtension\ViewModel\ShippingInfo;
use Magento\Checkout\ViewModel\Cart as CartViewModel;

/** @var \Magento\Checkout\Block\Cart\Grid $block */
/** @var \Hyva\Theme\Model\ViewModelRegistry $viewModels */
/** @var \Magento\Framework\Escaper $escaper */

/** @var CartViewModel $cartViewModel */
$cartViewModel = $viewModels->require(CartViewModel::class);

/** @var Modal $modalViewModel */
$modalViewModel = $viewModels->require(Modal::class);

/** @var ItemOutput $cartItemOutputViewModel */
$cartItemOutputViewModel = $viewModels->require(ItemOutput::class);

/** @var Currency $currencyViewModel */
$currencyViewModel = $viewModels->require(Currency::class);

/** @var ShippingInfo $shippingInfo */
$shippingInfo = $viewModels->require(ShippingInfo::class);
$freeLabel = $shippingInfo->getShippingFreeCostLabel();
?>
<?php $mergedCells = ($cartItemOutputViewModel->isItemPriceDisplayBoth() ? 2 : 1); ?>
<?= $block->getChildHtml('form_before') ?>
<form
    action="<?= $escaper->escapeUrl($block->getUrl('checkout/cart/updatePost')) ?>"
    x-data="{isLoading: false}"
    x-ref="cartForm"
    @submit.prevent="isLoading = true; hyva.postCart($event.target)"
    @change-qty.this="isLoading = true; hyva.postCart($event.target)"
    method="post"
    id="form-validate"
    class="float-left w-full form form-cart"
>
    <template x-if="isLoading">
        <?= $block->fetchView((string) $block->getTemplateFile('Hyva_Theme::ui/loading.phtml')) ?>
    </template>
    <?= $block->getBlockHtml('formkey') ?>
    <div class="cart table-wrapper<?= $mergedCells == 2 ? ' detailed' : '' ?>">
        <?php if ($block->getPagerHtml()): ?>
            <div class="cart-products-toolbar cart-products-toolbar-top toolbar">
                <?= $block->getPagerHtml() ?>
            </div>
        <?php endif ?>
        <div id="shopping-cart-table" class="w-full cart items data">
            <div class="table-caption sr-only">
                <?= $escaper->escapeHtml(__('Shopping Cart Items')) ?>
            </div>
            <?php foreach ($block->getItems() as $item): ?>
                <?= $block->getItemHtml($item) ?>
            <?php endforeach ?>
        </div>
        <?php if ($block->getPagerHtml()): ?>
            <div class="cart-products-toolbar cart-products-toolbar-bottom toolbar">
                <?= $block->getPagerHtml() ?>
            </div>
        <?php endif ?>
    </div>
</form>
<?= $block->getChildHtml('checkout.cart.order.actions') ?>
<?= $block->getChildHtml('shopping.cart.table.after') ?>
<script defer="defer">
    function formatPrice(priceValue, showCurrency) {
        if (parseFloat(priceValue) === 0) {
            return '<?= $escaper->escapeHtml($freeLabel); ?>';
        }

        let formattedPrice = hyva.formatPrice(priceValue).replace(/\s/g, ''),
            currency = "<?= $escaper->escapeJs($currencyViewModel->getCurrentCurrencySymbol()) ?>";

        if (!showCurrency) {
            formattedPrice = formattedPrice.replace(currency, '');
        }

        return formattedPrice.replace(/\.00$/g, '.-').replace(/\,00$/g, ',-');
    }
</script>
