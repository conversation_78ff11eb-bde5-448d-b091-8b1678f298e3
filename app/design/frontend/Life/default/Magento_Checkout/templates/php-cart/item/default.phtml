<?php

declare(strict_types=1);

use Hyva\Theme\ViewModel\ProductListItem;
use Hyva\Theme\ViewModel\ProductStockItem;
use Hyva\Theme\ViewModel\SvgIcons;
use Life\DeliveryTime\Observer\SetDeliveryTime;
use Life\ProductsConnector\ViewModel\ProductPrice;
use Magento\Framework\Pricing\Helper\Data as PricingHelper;

/** @var \Magento\Checkout\Block\Cart\Item\Renderer $block */
/** @var \Magento\Framework\Escaper $escaper */
/** @var \Hyva\Theme\Model\ViewModelRegistry $viewModels */

/** @var SvgIcons $svgIcons */
$svgIcons = $viewModels->require(SvgIcons::class);

$item = $block->getItem();
$childItem = $item;
$childItems = $item->getChildren();
if (count($childItems)) {
    $childItem = current($childItems);
}

$product = $item->getProduct();
$isVisibleProduct = $product->isVisibleInSiteVisibility();

/** @var ProductStockItem $stockItemViewModel */
$stockItemViewModel = $viewModels->require(ProductStockItem::class);
$step = (int)($stockItemViewModel->getQtyIncrements($product) ?: 1);

/** @var ProductListItem $productListItemViewModel */
$productListItemViewModel = $viewModels->require(ProductListItem::class);

/** @var ProductPrice $productPriceViewModel */
$productPriceViewModel = $viewModels->require(ProductPrice::class);

$discountAmount = $productPriceViewModel->getDiscountAmountForCartItem($childItem->getProduct(), $item);

/** @var PricingHelper $pricingHelper */
$pricingHelper = $this->helper(PricingHelper::class);
?>
<div class="relative mb-2 bg-white cart item rounded-xl">
    <div class="flex justify-end text-left align-top md:justify-between max-md:flex-wrap item-info lg:text-right">
        <div
            data-th="<?= $escaper->escapeHtml(__('Item')) ?>"
            class="flex w-full gap-8 px-4 pt-4 pb-6 text-left xl:px-6 col item lg:w-auto"
        >
            <?php if ($block->hasProductUrl()): ?>
                <a
                    href="<?= $escaper->escapeUrl($block->getProductUrl()) ?>"
                    title="<?= $escaper->escapeHtmlAttr($block->getProductName()) ?>"
                    tabindex="-1"
                    class="product-item-photo max-w-[5rem] md:max-w-[8rem] xl:max-w-[13rem] rounded-xl overflow-hidden flex-shrink-0"
                >
            <?php else: ?>
                <span class="product-item-photo max-w-[5rem] md:max-w-[8rem] xl:max-w-[13rem] rounded-xl overflow-hidden flex-shrink-0">
            <?php endif;?>
            <?= $block->getImage($block->getProductForThumbnail(), 'cart_page_product_thumbnail') /** @phpstan-ignore-line */
                ->setTemplate('Magento_Catalog::product/image.phtml')
                ->toHtml() ?>
            <?php if ($block->hasProductUrl()): ?>
                </a>
            <?php else: ?>
                </span>
            <?php endif; ?>
            <div class="product-item-details grow">
                <strong class="break-all product-item-name">
                    <?php if ($block->hasProductUrl()): ?>
                        <a href="<?= $escaper->escapeUrl($block->getProductUrl()) ?>" class="text-base leading-[1.875rem]">
                            <?= $escaper->escapeHtml($block->getProductName()) ?>
                        </a>
                    <?php else: ?>
                        <span class="text-base leading-[1.875rem]"><?= $escaper->escapeHtml($block->getProductName()) ?></span>
                    <?php endif; ?>
                </strong>
                <?php if ($options = $block->getOptionList()): ?>
                    <dl class="clearfix w-full mt-2 text-sm break-all item-options text-black/60">
                        <?php foreach ($options as $option): ?>
                            <?php $formatedOptionValue = $block->getFormatedOptionValue($option) ?>
                            <dt class="float-left mb-1 mr-2 text-xs clear-left">
                                <?= $escaper->escapeHtml($option['label']) ?>:
                            </dt>
                            <dd class="float-left text-xs font-bold">
                                <?php if (isset($formatedOptionValue['full_view'])): ?>
                                        <?= $escaper->escapeHtml($formatedOptionValue['full_view']) ?>
                                    <?php else: ?>
                                        <?= $escaper->escapeHtml($formatedOptionValue['value'], ['span', 'a']) ?>
                                <?php endif; ?>
                            </dd>
                        <?php endforeach; ?>
                    </dl>
                <?php endif;?>
                <?php $addInfoBlock = $block->getProductAdditionalInformationBlock(); ?>
                <?php if ($addInfoBlock): ?>
                    <?= $addInfoBlock->setItem($item)->toHtml() ?>
                <?php endif;?>

                <?php if ($messages = $block->getMessages()): ?>
                    <div class="messages">
                        <?php foreach ($messages as $message): ?>
                            <div class="cart item message <?= $escaper->escapeHtmlAttr($message['type']) ?>">
                                <div><?= $escaper->escapeHtml($message['text']) ?></div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php elseif (
                    $childItem
                    && ($deliveryTimeMessage = $childItem->getData(SetDeliveryTime::ATTRIBUTE_CODE . '_message'))
                ): ?>
                    <div class= "cart item message">
                        <div><?= $escaper->escapeHtml($deliveryTimeMessage) ?></div>
                    </div>
                <?php endif;?>
            </div>
        </div>

        <div class="flex items-center justify-end md:items-start">
            <div
                class="flex w-full pt-4 pb-4 pl-4 sm:px-4 md:pb-6 col qty"
                x-data="{qty: <?= $block->getQty() ?>}"
            >
                <div class="!mt-0 field qty">
                    <div class="control qty">
                        <label for="cart-<?= $escaper->escapeHtmlAttr($item->getId()) ?>-qty" class="flex mb-0">
                            <span class="sr-only label"><?= $escaper->escapeHtml(__('Qty')) ?></span>
                            <button
                                type="button"
                                x-on:click="qty = Math.max(0, qty - <?= $step ?>);
                                    $refs.qtyInput.value = qty;
                                    $refs.cartForm.dispatchEvent(new Event('change-qty', { qty: qty }))"
                                class="relative flex items-center justify-center w-8 h-8 border border-secondary-900/50 -right-px rounded-l-md"
                            >
                                <span class="sr-only">
                                    <?= $escaper->escapeHtml(__('Decrease by 1')) /** @phpstan-ignore-line */ ?>
                                </span>
                                <?= $svgIcons->minusHtml("h-4 w-4") ?>
                            </button>
                            <input
                                id="cart-<?= $escaper->escapeHtmlAttr($item->getId()) ?>-qty"
                                name="cart[<?= $escaper->escapeHtmlAttr($item->getId()) ?>][qty]"
                                value="<?= $escaper->escapeHtmlAttr($block->getQty()) ?>"
                                type="number"
                                size="4"
                                step="any"
                                title="<?= $escaper->escapeHtmlAttr(__('Qty')) ?>"
                                class="w-8 h-8 !min-h-full !p-0 !rounded-none qty text-center"
                                required="required"
                                min="0"
                                data-role="cart-item-qty"
                                x-ref="qtyInput"
                                x-model.number="qty"
                                x-on:change="$refs.cartForm.dispatchEvent(new Event('change-qty', { qty: qty }))"
                            />
                            <button
                                type="button"
                                x-on:click="qty = Math.max(<?= $step ?>, qty + <?= $step ?>);
                                    $refs.qtyInput.value = qty;
                                    $refs.cartForm.dispatchEvent(new Event('change-qty', { qty: qty }));"
                                class="relative flex items-center justify-center w-8 h-8 border border-secondary-900/50 -left-px rounded-r-md"
                            >
                                <span class="sr-only">
                                    <?= $escaper->escapeHtml(__('Increase by 1')) /** @phpstan-ignore-line */ ?>
                                </span>
                                <?= $svgIcons->plusHtml("h-4 w-4") ?>
                            </button>
                        </label>
                    </div>
                </div>
            </div>

            <div class="flex items-end xl:items-baseline w-full px-4 pt-4 pb-4 md:pb-6 gap-x-4 col price w-32 xl:w-auto xl:min-w-[13rem] justify-end">

                <span class="flex flex-wrap justify-end gap-x-4">

                    <span
                        class="text-sm font-bold price"
                        x-html="formatPrice(<?= $item->getRowTotalInclTax() ?>, false)"
                    ></span>
                </span>
            </div>
        </div>
    </div>
    <div class="absolute item-actions bottom-6 left-4 md:left-auto md:right-4 md:bottom-4">
        <?= /* @noEscape */ $block->getActions($item) ?>
    </div>
</div>
