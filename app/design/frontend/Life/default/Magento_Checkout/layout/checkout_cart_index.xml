<?xml version="1.0"?>
<page
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd"
>
    <head>
        <meta name="robots" content="NOINDEX,NOFOLLOW"/>
    </head>
    <body>
        <referenceBlock name="crosssell">
            <arguments>
                <argument name="title" xsi:type="string" translate="true">The nicest accessories</argument>
            </arguments>
        </referenceBlock>

        <referenceContainer name="cart.discount" remove="true" />
        <referenceContainer name="checkout.cart.coupon" remove="true" />
        <referenceBlock name="checkout.cart.shipping" remove="true" />
        <referenceBlock name="tax" remove="true" />
        <referenceBlock name="discount" remove="true" />

        <move element="crosssell" destination="page.bottom.container" before="-" />
    </body>
</page>
