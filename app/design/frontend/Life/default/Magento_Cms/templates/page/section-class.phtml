<script rel="preconnect" src="//cdnjs.cloudflare.com/ajax/libs/ScrollMagic/2.0.7/ScrollMagic.min.js"></script>

<script defer="defer">
    const controller = new ScrollMagic.Controller();
    const revealElements = document.querySelectorAll('.column > div, .pagebuilder-doorways-mobile, .widget.block-static-block > div');
    const revealChildElements = document.querySelectorAll('[data-content-type="banner"]');
    const screenHeight = screen.height;
    const toRevealCls = 'to-reveal';
    const revealCls = 'reveal';

    for (el of revealElements) {
        el.classList.add(toRevealCls);

        if (screenHeight > el.getBoundingClientRect().y) {
            el.classList.add(revealCls);
            continue;
        }

        new ScrollMagic.Scene({
            triggerElement: el,
            offset: 50,
            triggerHook: 0.9,
        })
        .setClassToggle(el, "reveal")
        .addTo(controller);
    }

    for (el of revealChildElements) {
        el.classList.add(toRevealCls);

        if (screenHeight > el.getBoundingClientRect().y) {
            el.classList.add(revealCls);
            continue;
        }

        new ScrollMagic.Scene({
            triggerElement: el,
            offset: 50,
            triggerHook: 1.5,
        })
        .setClassToggle(el, "reveal")
        .addTo(controller);
    }
</script>
