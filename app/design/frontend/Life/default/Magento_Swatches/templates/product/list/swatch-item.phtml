<?php

declare(strict_types=1);

use Magento\Swatches\ViewModel\Product\Renderer\Configurable as ConfigurableViewModel;

/** @var \Magento\Framework\View\Element\Template $block */
/** @var \Magento\Framework\Escaper $escaper */
/** @var \Hyva\Theme\Model\ViewModelRegistry $viewModels */

/** @var ConfigurableViewModel $configurableViewModel */
$configurableViewModel = $viewModels->require(ConfigurableViewModel::class);

$productId = $block->getProductId();
$attributeId = $block->getAttributeId();

if (!$productId || !$attributeId) {
    return '';
}
?>
<template x-if="optionIsEnabled(<?= (int) $attributeId ?>, item.id)">
<div x-id="['attribute-option-<?= (int) $productId ?>-'+item.id]" class="flex items-center justify-center w-6 h-6">
    <template x-if="optionIsActive(<?= (int) $attributeId ?>, item.id)">
        <label
            :for="$id('attribute-option-<?= (int) $productId ?>-'+item.id)"
            class="relative cursor-pointer select-none swatch-option product-option-value-label border-none ring-offset-1 ring-1"
            :title="getSwatchText(<?= $escaper->escapeHtmlAttr($attributeId) ?>, item.id)"
            :class="{
                'ring-secondary-900/80':
                    (selectedValues[<?= (int)$attributeId ?>] === item.id),
                'ring-secondary-600/50':
                    (selectedValues[<?= (int)$attributeId ?>] !== item.id),
                'w-5 h-5' : !isTextSwatch(<?= (int) $attributeId ?>, item.id),
                'border-container-lighter ring ring-primary/75' : focusedLabel === item.id
            }"
            :style="getSwatchBackgroundStyle('<?= (int) $attributeId ?>',item.id)"
        >
            <input
                :id="$id('attribute-option-<?= (int) $productId ?>-'+item.id)"
                :value="item.id"
                name="super_attribute[<?= (int) $attributeId ?>]"
                type="radio"
                class="hidden product-option-value-input"
                style="z-index:-1"
                x-on:focus="focusLabel(item.id)"
                x-on:blur="blurLabel()"
                x-on:change="changeOption(<?= (int) $attributeId ?>, $event.target.value)"
                x-model="selectedValues[<?= (int) $attributeId ?>]"
                :aria-label="getSwatchText(<?= $escaper->escapeHtmlAttr($attributeId) ?>, item.id)"
                aria-describedby="attribute-label-<?= $escaper->escapeHtmlAttr($productId . '-' . $attributeId) ?>"
            >
            <template x-if="isTextSwatch(<?= (int) $attributeId ?>, item.id)">
                <div
                    x-html="getSwatchText(<?= (int) $attributeId ?>, item.id)"
                    class="pointer-events-none select-none whitespace-nowrap"
                    aria-hidden="true"
                ></div>
            </template>
        </label>
    </template>

    <template x-if="!optionIsActive(<?= (int) $attributeId ?>, item.id)">
        <div
            class="relative border-2 shadow-sm opacity-50 cursor-not-allowed select-none border-container-darker swatch-option bg-container-lighter"
            :class="{'w-5 h-5' : !isTextSwatch(<?= (int) $attributeId ?>, item.id)}"
            :style="getSwatchBackgroundStyle('<?= (int) $attributeId ?>',item.id)"
        >
            <div
                x-html="getSwatchText(<?= (int) $attributeId ?>, item.id)"
                class="whitespace-nowrap"
                :class="{ 'sr-only' : !isTextSwatch(<?= (int) $attributeId ?>, item.id) }"
            ></div>
            <svg class="absolute inset-0 w-full h-full text-gray-500 bg-white/25">
                <line
                    x1="0"
                    y1="100%"
                    x2="100%"
                    y2="0"
                    class="stroke-current stroke-1"
                ></line>
            </svg>
        </div>
    </template>
</div>
</template>
