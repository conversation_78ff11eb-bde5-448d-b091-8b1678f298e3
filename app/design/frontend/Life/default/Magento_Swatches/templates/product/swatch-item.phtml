<?php

declare(strict_types=1);

use Magento\Swatches\ViewModel\Product\Renderer\Configurable as ConfigurableViewModel;
use Hyva\Theme\ViewModel\SvgIcons;

// phpcs:disable Generic.Files.LineLength.TooLong

/** @var \Magento\Framework\View\Element\Template $block */
/** @var \Magento\Framework\Escaper $escaper */
/** @var \Hyva\Theme\Model\ViewModelRegistry $viewModels */

/** @var ConfigurableViewModel $configurableViewModel */
$configurableViewModel = $viewModels->require(ConfigurableViewModel::class);

/** @var SvgIcons $svgIcons */
$svgIcons = $viewModels->require(SvgIcons::class);

$productId = $block->getProductId();
$attributeId = $block->getAttributeId();
$attributeCode = $block->getAttributeCode();

if (!$productId || !$attributeId) {
    return '';
}
?>

<div
    x-id="['attribute-option-<?= (int) $productId ?>-'+item.id]"
    class="w-full"
    :class="{
        'sm:w-auto':
            isTextSwatch(<?= (int) $attributeId ?>, item.id)
    }"
    isVisualSwatch(<?= (int) $attributeId ?>,
    item.id)
>
    <template x-if="optionIsEnabled(<?= (int) $attributeId ?>, item.id)">
        <label
            :for="$id('attribute-option-<?= (int) $productId ?>-'+item.id)"
            class="relative transition duration-300 cursor-pointer swatch-option product-option-value-label lg:hover:bg-secondary-300 swatch-option--<?= $attributeCode?>"
            :class="{
                'border-primary selected':
                    (selectedValues[<?= (int)$attributeId ?>] === item.id),
                'border-secondary-900/50':
                    (selectedValues[<?= (int)$attributeId ?>] !== item.id),
                'pointer-events-none bg-secondary-500/25': !optionIsActiveForCurrentSelection(<?= (int) $attributeId ?>, item.id),
                '!px-4' : isTextSwatch(<?= (int) $attributeId ?>, item.id)
            }"
        >
            <input
                :id="$id('attribute-option-<?= (int) $productId ?>-'+item.id)"
                :value="item.id"
                name="super_attribute[<?= (int) $attributeId ?>]"
                type="radio"
                class="absolute inline-block p-0 border-0 focus:border-0 focus:ring-0 product-option-value-input"
                style="z-index:-1"
                x-on:focus="focusLabel(item.id)"
                x-on:blur="blurLabel()"
                x-on:change="changeOption(<?= (int) $attributeId ?>, $event.target.value)"
                x-model="selectedValues[<?= (int) $attributeId ?>]"
                :required="getAllowedAttributeOptions(<?= (int) $attributeId ?>).filter(
                    attributeOption => selectedValues[attributeOption]
                ).length === 0"
                :aria-label="getSwatchText(<?= $escaper->escapeHtmlAttr($attributeId) ?>, item.id)"
                aria-describedby="attribute-label-<?= $escaper->escapeHtmlAttr($productId . '-' . $attributeId) ?>"
            >
            <template x-if="isVisualSwatch(<?= (int) $attributeId ?>, item.id)">
                <div class="relative flex items-center swatch-image shrink-0">
                    <template x-if="isImageSwatch(<?= (int) $attributeId ?>, item.id)">
                        <img
                            class="w-24 h-16 rounded-[0.25rem] overflow-clip"
                            :src="getSwatchImage('<?= (int) $attributeId ?>',item.id)"
                            alt=""
                        />
                    </template>
                    <template x-if="isColorSwatch(<?= (int) $attributeId ?>, item.id)">
                        <span
                            class="inline-block w-24 h-16 rounded-[0.25rem]"
                            :style="getSwatchColor('<?= (int) $attributeId ?>',item.id)"
                            alt=""
                        ></span>
                    </template>
                </div>
            </template>

            <div
                class="label grow"
                :class="isVisualSwatch(<?= (int) $attributeId ?>, item.id) ? 'col-start-2' : 'col-span-2'"
            >
                <div
                    x-html="getLabel(<?= (int) $attributeId ?>, item.id)"
                    class="text-sm font-bold grow"
                ></div>
                <template x-if="extraLabel(item.id)">
                    <div
                        class="text-xs text-black/50"
                        x-html="extraLabel(item.id)"
                    >Vlechtwerk</div>
                </template>
            </div>

            <div
                x-html="getOptionPriceDifference(<?= (int) $attributeId ?>, item.id)"
                class="additional-price text-sm white-space-nowrap shrink-0 justify-self-end"
            ></div>

            <template x-if="qualityMarkContent(item.id)">
                <div>
                    <button
                        type="button"
                        @click="showQualityMarkContent(item.id); $dispatch('init-page-builder-elements', {})"
                        class="info"
                        :class="{'opacity-50': (selectedValues[<?= (int)$attributeId ?>] !== item.id)}"
                    >
                        <span class="sr-only"><?= $escaper->escapeHtml(__('More info')) ?></span>
                        <?= $svgIcons->infoHtml("w-4 h-4") ?>
                    </button>
                    <div
                        class="fixed top-0 bottom-0 right-0 z-50 flex justify-end w-full cursor-default bg-black/30 motion-safe:transition motion-safe:duration-500 motion-safe:ease-in-out"
                        x-show="isQualityMarkContentVisible(item.id)"
                        x-transition:enter-start="opacity-0"
                        x-transition:enter-end="opacity-100"
                        x-transition:leave-start="opacity-100"
                        x-transition:leave-end="opacity-0"
                    >
                        <div
                            class="info-drawer bg-white w-[90%] md:w-3/4 lg:w-1/2 2xl:w-5/12 h-full relative py-6 px-3 md:px-0 md:py-12 rounded-tl-xl rounded-bl-xl">
                            <button
                                type="button"
                                class="absolute cursor-pointer top-6 right-6 md:top-12 md:right-12"
                                @click="hideQualityMarkContent(item.id)"
                            >
                                <span class="sr-only"><?= $escaper->escapeHtml(__('Close modal')) ?></span>
                                <?= $svgIcons->closeHtml("w-6 h-6") ?>
                            </button>
                            <div
                                x-html="qualityMarkContent(item.id)"
                                class="h-full overflow-y-auto"
                            ></div>
                        </div>
                        <div @click="hideQualityMarkContent(item.id)" class="absolute top-0 left-0 w-[10%] h-screen lg:w-1/2 md:w-1/4 2xl:w-7/12"></div>
                    </div>
                </div>
            </template>

            <template x-if="!optionIsActiveForCurrentSelection(<?= (int) $attributeId ?>, item.id)">
                <svg
                    class="absolute inset-0 w-full h-full rounded-xl"
                    :id="'inactive-<?= (int) $productId ?>-'+item.id"
                >
                    <line
                        x1="0"
                        y1="100%"
                        x2="100%"
                        y2="0"
                        class="stroke-current stroke-1"
                    ></line>
                </svg>
            </template>
        </label>
    </template>
</div>
