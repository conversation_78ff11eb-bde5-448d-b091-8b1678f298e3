<?php

declare(strict_types=1);

use Magento\Swatches\ViewModel\Product\Renderer\Configurable as ConfigurableViewModel;
use Hyva\Theme\ViewModel\SvgIcons;

/** @var \Magento\Framework\View\Element\Template $block */
/** @var \Magento\Framework\Escaper $escaper */
/** @var \Hyva\Theme\Model\ViewModelRegistry $viewModels */

/** @var ConfigurableViewModel $configurableViewModel */
$configurableViewModel = $viewModels->require(ConfigurableViewModel::class);

/** @var SvgIcons $svgIcons */
$svgIcons = $viewModels->require(SvgIcons::class);

$productId = $block->getProductId();
$attributeId = $block->getAttributeId();
$attributeLabel = $block->getAttributeLabel();
$categoryId = $block->getData('category_id');

if (!$productId || !$attributeId) {
    return '';
}

?>

<div x-id="['attribute-option-<?= (int) $productId ?>-'+item.id]"
    :class="{
        'w-auto':
            isVisualSwatch(<?= (int) $attributeId ?>, item.id),
        'w-full sm:w-auto':
            isTextSwatch(<?= (int) $attributeId ?>, item.id)
    }"
>
    <template x-if="optionIsEnabled(<?= (int) $attributeId ?>, item.id)">
        <label
            :for="$id('attribute-option-<?= (int) $productId ?>-'+item.id)"
            class="relative mb-0 transition duration-300 cursor-pointer swatch-option swatch-option--fabric product-option-value-label"
            :class="{
                'border-primary selected':
                    (selectedValues[<?= (int)$attributeId ?>] === item.id),
                'border-secondary-900/50':
                    (selectedValues[<?= (int)$attributeId ?>] !== item.id),
                'pointer-events-none bg-secondary-500/25': !optionIsActiveForCurrentSelection(<?= (int) $attributeId ?>, item.id)
            }"
            data-option-category="<?= $escaper->escapeHtmlAttr($attributeLabel ?? ''); ?>"
            :data-option-id="item.id"
        >
            <input
                :id="$id('attribute-option-<?= (int) $productId ?>-'+item.id)"
                :value="item.id"
                name="super_attribute[<?= (int) $attributeId ?>]"
                type="radio"
                class="absolute inline-block p-0 border-0 focus:border-0 focus:ring-0 product-option-value-input"
                style="z-index:-1"
                x-on:focus="focusLabel(item.id)"
                x-on:blur="blurLabel()"
                x-on:change="changeOption(<?= (int) $attributeId ?>, $event.target.value)"
                x-model="selectedValues[<?= (int) $attributeId ?>]"
                :required="getAllowedAttributeOptions(<?= (int) $attributeId ?>).filter(
                    attributeOption => selectedValues[attributeOption]
                ).length === 0"
                :aria-label="getSwatchText(<?= $escaper->escapeHtmlAttr($attributeId) ?>, item.id)"
                aria-describedby="attribute-label-<?= $escaper->escapeHtmlAttr($productId . '-' . $attributeId) ?>"
            >
            <template x-if="isTextSwatch(<?= (int) $attributeId ?>, item.id)">
                <div class="flex items-center mx-1 text-sm">
                    <template x-if="optionIsActiveForCurrentSelection(<?= (int) $attributeId ?>, item.id)">
                        <div
                            class="flex items-center justify-center w-4 h-4 mr-2 bg-white rounded-full"
                            :class="{'hidden': (selectedValues[<?= (int)$attributeId ?>] !== item.id)}"
                        >
                            <?= $svgIcons->checkHtml("text-black w-3 h-3") ?>
                        </div>
                    </template>
                    <div
                        x-html="getSwatchText(<?= (int) $attributeId ?>, item.id)"
                        class="whitespace-nowrap"
                        aria-hidden="true"
                    ></div>
                </div>
            </template>

            <template x-if="isVisualSwatch(<?= (int) $attributeId ?>, item.id)">
                <div class="relative flex items-center shrink-0" :data-swatch-image="item.id">
                    <template x-if="isImageSwatch(<?= (int) $attributeId ?>, item.id)">
                        <img
                            class="visual-swatch rounded-full w-9 h-9 border-2 border-white outline"
                            :class="selectedValues[<?= (int)$attributeId ?>] == item.id ? 'outline-2 outline-primary' : 'outline-1 outline-secondary-900/50'"
                            :src="getSwatchImage('<?= (int) $attributeId ?>',item.id)"
                            alt=""
                            @click="
                                selectedFabric.label = item.label;
                                selectedFabric.category = '<?= $attributeLabel ?? ''; ?>'
                                activePanel = '<?= $escaper->escapeHtmlAttr($categoryId) ?>'
                            "
                        />
                    </template>
                    <template x-if="isColorSwatch(<?= (int) $attributeId ?>, item.id)">
                        <span
                            class="visual-swatch inline-block rounded-full w-9 h-9"
                            :style="getSwatchColor('<?= (int) $attributeId ?>',item.id)"
                            alt=""
                        ></span>
                    </template>
                </div>
            </template>

            <template x-if="!optionIsActiveForCurrentSelection(<?= (int) $attributeId ?>, item.id)">
                <svg class="absolute inset-0 w-full h-full rounded-xl" :id="'inactive-<?= (int) $productId ?>-'+item.id">
                    <line x1="0" y1="100%" x2="100%" y2="0" class="stroke-current stroke-1"></line>
                </svg>
            </template>
        </label>
    </template>
</div>
