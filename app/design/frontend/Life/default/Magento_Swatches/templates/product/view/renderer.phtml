<?php

declare(strict_types=1);

use Life\ProductQualityMark\ViewModel\QualityMarks;
use Magento\Swatches\ViewModel\Product\Renderer\Configurable as ConfigurableViewModel;
use Hyva\Theme\ViewModel\SvgIcons;
use Hyva\Theme\ViewModel\SwatchRenderer;

// phpcs:disable Generic.Files.LineLength.TooLong

/** @var \Magento\Swatches\Block\Product\Renderer\Configurable $block */
/** @var \Magento\Framework\Escaper $escaper */
/** @var \Hyva\Theme\Model\ViewModelRegistry $viewModels */

/** @var ConfigurableViewModel $configurableViewModel */
$configurableViewModel = $viewModels->require(ConfigurableViewModel::class);

/** @var SwatchRenderer $swatchRendererViewModel */
$swatchRendererViewModel = $viewModels->require(SwatchRenderer::class);

/** @var \Magento\Catalog\Model\Product $product */
$product = $block->getProduct();
$productId = $product->getId();

$outOfStockProducts = [];
foreach ($block->getAllowProducts() as $allowProduct) {
    if (!$allowProduct->isSalable()) {
        $outOfStockProducts[] = $allowProduct->getId();
    }
}

$attributes = $block->decorateArray($block->getAllowAttributes());

$layout = $block->getLayout();
$swatchItemBlock = $layout->getBlock('product.swatch.item');
$swatchItemBlock->setData('product_id', $productId);

$tooltipBlockHtml = $block->getBlockHtml('product.swatch.tooltip');

/** @var QualityMarks $qualityMarksViewModel */
$qualityMarksViewModel = $viewModels->require(QualityMarks::class);
$swatchQualityMarks = $qualityMarksViewModel->getSwatchAttributesMarksJson($product, $block->getJsonSwatchConfig());

/** @var SvgIcons $svgIcons */
$svgIcons = $viewModels->require(SvgIcons::class);

$groupListAtts = $block->getBlockHtml("life.product.group.list");
$loopIncrement = $groupListAtts ? 1 : 0;

?>
<?php if ($product->isSaleable() && count($attributes)): ?>
    <script defer="defer">
        function initConfigurableSwatchOptions_<?= (int) $productId ?>() {
            const configurableOptionsComponent = initConfigurableOptions(
                '<?= (int) $productId ?>',
                <?= /* @noEscape */ $block->getJsonConfig() ?>
            );
            const swatchOptionsComponent = initSwatchOptions(
                <?= /* @noEscape */ $block->getJsonSwatchConfig() ?>,
                <?= /* @noEscape */ $swatchQualityMarks ?>
            );
            const outOfStockProducts = <?= json_encode($outOfStockProducts); ?>;
            const addToCartButton = document.getElementById('product-addtocart-button');

            return Object.assign(
                configurableOptionsComponent,
                swatchOptionsComponent,
                {
                    flyoutActive: false,
                    activeStep: 1,
                    lastFocusedEl: null,
                    preselectedOptions: [],
                    canAddToCart: true,
                    imagePreview: {
                        src: '',
                        width: 0,
                        height: 0
                    },
                    toggleFlyout(open = true, step, event) {
                        const bodyCls = ['overflow-clip'];
                        if (open) {
                            let self = this;
                            // Give time for gallery component to be initialized after flyout is opened
                            setTimeout(() => {
                                window.dispatchEvent(
                                    new CustomEvent("flyout-opened", {
                                        detail: {
                                            selectedValues: self.preselectedOptions
                                        }
                                    })
                                );
                            }, 500);

                            this.flyoutActive = true;
                            this.lastFocusedEl = event?.target || null;
                            document.body.classList.add('flyout-active');
                            document.body.classList.add(bodyCls);
                        } else {
                            new Promise(() => this.validate().then(() => {

                                // removes config param from url
                                const url = new URL(window.location.href);
                                const params = new URLSearchParams(url.search);
                                params.delete('config', 'true');
                                url.search = params.toString();
                                history.pushState(null, '', url.toString());

                                this.flyoutActive = false;
                                document.body.classList.remove('flyout-active');
                                document.body.classList.remove(...bodyCls);
                                this.lastFocusedEl && this.lastFocusedEl.focus();
                                this.lastFocusedEl = null;
                            }).catch(() => {
                            }));
                        }
                        step && this.navigateToStep(step);
                    },
                    navigateToStep(step) {
                        this.activeStep = step;
                    },
                    preselectQueryStringOptions() {
                        // Pre-select option like ?612=167
                        const urlQueryParams = new URLSearchParams(window.location.search.replace('?',''));
                        Object.values(this.optionConfig.attributes).map(attribute => {
                            urlQueryParams.get(attribute.id) &&
                            this.changeOption(attribute.id, urlQueryParams.get(attribute.id));
                        });
                    },
                    initFlyout() {
                        const configParam = new URLSearchParams(window.location.search).get('config');

                        if (configParam) {
                            
                            <?php 
                            // if the config param is set, user is redirected from a flyout on an other PDP
                            ?>
                            this.toggleFlyout(true, 0);

                            <?php
                            // this is needed to show the swatches instantly
                            // without this, the swatches are not shown on mobile
                            ?>
                            this.showSwatches = true;
                        }

                        <?php // set event listener to escape key ?>
                        window.addEventListener('keydown', (event) => {
                            if (event.key === 'Escape' && this.flyoutActive) {
                                this.toggleFlyout(false);
                            }
                        });
                    },
                    setPreselectedOptions(event) {
                        this.preselectedOptions = event?.detail?.selectedValues;

                        if (!addToCartButton) {
                            return;
                        }

                        let selectedProductId = event?.detail?.productIndex;
                        if (selectedProductId && outOfStockProducts.includes(selectedProductId)) {
                            this.canAddToCart = false;
                            addToCartButton.disabled = true;
                        } else {
                            this.canAddToCart = true;
                            addToCartButton.disabled = false;
                        }
                    },
                    updateImagePreview(event) {
                        const galleryPreviewImg = document.querySelector('#gallery img.gallery-image');
                        if (!galleryPreviewImg) return;
                        this.imagePreview.src = galleryPreviewImg.src;
                        this.imagePreview.width = galleryPreviewImg.width;
                        this.imagePreview.height = galleryPreviewImg.height;
                    }
                }
            );
        }
    </script>

    <div
        x-data="initConfigurableSwatchOptions_<?= (int) $productId ?>()"
        x-init="init(); initShowSwatchesIntersect(); setTimeout(function() {preselectQueryStringOptions()}, 500); initFlyout()"
        @private-content-loaded.window="setTimeout(function() {onGetCartData($event.detail.data)}, 200);"
        @configurable-selection-changed.window="setPreselectedOptions($event)"
        @gallery-updated.window="updateImagePreview()"
        class="relative skip-defer"
    >
        <div class="grid gap-2 my-6 lg:my-0 lg:p-4 lg:bg-white lg:rounded-xl">
            <h2 class="text-lg">
                <?= $escaper->escapeHtml(__('Assemble')) ?>:
            </h2>
            <ol class="grid gap-2">
                <?php $counter = 0 + $loopIncrement; ?>
                <?php $liCls = 'flex px-4 py-5 bg-white rounded-xl lg:py-5 lg:bg-transparent lg:rounded-none lg:border-b lg:border-secondary-900/20' ?>

                <?php if ($groupListAtts): ?>
                    <li class="<?= $liCls ?>">
                        <div class="flex w-full gap-2 relative">
                            <div class="flex pl-8 items-center flex-wrap gap-x-2">
                                <span class="flex items-center justify-center w-6 h-6 absolute top-px left-0 text-xs border rounded-full swatch-number bg-secondary-300 border-secondary-900">
                                    <?= $counter ?>
                                </span>
                                <span class="lg:text-lg">
                                    <?= $escaper->escapeHtml(__('Version')) ?>
                                </span>
                            </div>
                            <button
                                @click.prevent="toggleFlyout(true, <?= $counter ?>, event)"
                                class="flex gap-0.5 ml-auto items-center text-sm lg:text-base after:absolute after:inset-0"
                            >
                                <span class="opacity-50">
                                    <?= $escaper->escapeHtml(__('Change')) ?>
                                </span>
                                <?= $svgIcons->arrowRightHtml("", 20, 20, ['aria-hidden' => 'true']) ?>
                            </button>
                        </div>
                    </li>
                <?php endif; ?>
                <?php foreach ($attributes as $attribute): ?>
                    <?php $attributeId = $attribute->getAttributeId(); ?>
                    <?php $productAttribute = $attribute->getProductAttribute();  ?>
                    <?php if ($swatchRendererViewModel->isSwatchAttribute($productAttribute)): ?>
                        <li class="<?= $liCls ?> swatch-attribute <?= $escaper->escapeHtmlAttr($productAttribute->getAttributeCode()) ?>">
                            <template x-if="showSwatches">
                                <div class="flex w-full gap-2 relative">
                                    <div class="flex pl-8 items-center flex-wrap gap-x-2">
                                        <span class="flex items-center justify-center w-6 h-6 absolute top-px left-0 text-xs border rounded-full swatch-number bg-secondary-300 border-secondary-900">
                                            <?= ++$counter ?>
                                        </span>
                                        <span class="lg:text-lg">
                                            <?= $escaper->escapeHtml($productAttribute->getStoreLabel()) ?>
                                        </span>
                                        <span
                                            class="text-sm text-primary font-bold lg:text-lg"
                                            x-html="getSwatchLabel(<?= (int)$attributeId ?>, selectedValues[<?= (int)$attributeId ?>]) || ''"
                                        ></span>
                                    </div>
                                    <button
                                        @click.prevent="toggleFlyout(true, <?= $counter ?>, event)"
                                        class="flex gap-0.5 ml-auto items-center text-sm lg:text-base after:absolute after:inset-0"
                                    >
                                        <span class="opacity-50">
                                            <?= $escaper->escapeHtml(__('Change')) ?>
                                            <span class="sr-only"> <?= $escaper->escapeHtml($productAttribute->getStoreLabel()) ?></span>
                                        </span>
                                        <?= $svgIcons->arrowRightHtml("", 20, 20, ['aria-hidden' => 'true']) ?>
                                    </button>
                                </div>
                            </template>
                        </li>
                    <?php else: ?>
                        <li class="<?= $liCls ?>">
                            <label
                                class="w-1/2 text-left text-gray-700 label"
                                for="attribute<?= $escaper->escapeHtmlAttr($attribute->getAttributeId()) ?>"
                            >
                                <span>
                                    <?= $escaper->escapeHtml($attribute->getProductAttribute()->getStoreLabel()) ?>
                                </span>
                            </label>
                            <div class="w-1/2 ml-2 text-left text-gray-900">
                                <select
                                    name="super_attribute[<?= $escaper->escapeHtmlAttr($attribute->getAttributeId()) ?>]"
                                    id="attribute<?= $escaper->escapeHtmlAttr($attribute->getAttributeId()) ?>"
                                    class="form-select super-attribute-select"
                                    x-on:change="changeOption(<?= (int) $attribute->getAttributeId() ?>, event.target.value)"
                                    required="required"
                                >
                                    <option value="">
                                        <?= $escaper->escapeHtml(__('Choose an Option...')) ?>
                                    </option>
                                    <template
                                        x-for="(item, index) in getAllowedAttributeOptions(<?= (int) $attribute->getAttributeId() ?>)"
                                        :key="item.id"
                                    >
                                        <option
                                            :value="item.id"
                                            x-html="item.label"
                                            :selected="selectedValues[<?= $escaper->escapeHtmlAttr($attribute->getAttributeId()) ?>] === item.id">
                                        </option>
                                    </template>
                                </select>
                            </div>
                        </li>
                    <?php endif; ?>
                <?php endforeach; ?>
            </ol>
        </div>
        <div
            id="product-config-flyout"
            class="flex fixed inset-0 z-40 isolate"
            x-cloak
            x-show="flyoutActive"
            x-show="flyoutActive"
            x-transition.opacity
        >
            <div class="flex flex-col w-full min-h-[calc(100%-4.7rem)] max-h-[calc(100%-0.5rem)] mt-auto ml-auto bg-white rounded-t-2xl md:max-w-md md:h-full md:max-h-full md:rounded-none">
                <header class="flex pt-2 px-3 relative md:py-4 md:absolute md:top-0.5 md:right-0 md:z-2">
                    <button
                        @click.prevent="toggleFlyout(false)"
                        class="ml-auto text-primary relative after:inline-block after:w-12 after:aspect-square after:absolute after:top-1/2 after:left-1/2 after:-translate-x-1/2 after:-translate-y-1/2 after:rounded-full"
                    >
                        <?= $svgIcons->closeHtml("w-5 h-5 md:w-6 md:h-6", 24, 24, ['aria-hidden' => 'true']) ?>
                        <span class="sr-only">
                            <?= $escaper->escapeHtml(__('Close')) ?>
                        </span>
                    </button>
                </header>

                <?php // media container ?>
                <?php if ($cylindoGallery = $block->getChildHtml('cylindo.config.preview.gallery')): ?>
                    <div class="aspect-[3/2] md:hidden">
                        <?= $cylindoGallery ?>
                    </div>
                <?php else: ?>
                    <div
                        x-cloak
                        x-show="imagePreview.src"
                        class="aspect-[3/2] md:hidden flex items-center justify-center"
                    >
                        <img
                            :src="imagePreview.src"
                            :width="imagePreview.width"
                            :height="imagePreview.height"
                            alt="<?= $escaper->escapeHtmlAttr($product->getName()); ?>"
                            loading="lazy"
                            draggable="false"
                        >
                    </div>
                <?php endif; ?>

                <?php // step container ?>
                <div class="relative grow basis-72">
                    <?php $counter = 0 + $loopIncrement; ?>
                    <?php if ($groupListAtts): ?>
                        <div
                            x-cloak
                            x-show="activeStep === <?= $counter ?>"
                            class="swatch-attribute absolute inset-0 overflow-y-auto"
                        >
                            <?= $groupListAtts ?>
                        </div>
                    <?php endif; ?>
                    <?php foreach ($attributes as $attribute): ?>
                        <?php
                        $counter++;
                        $attributeId = $attribute->getAttributeId();
                        $productAttribute = $attribute->getProductAttribute();
                        ?>
                        <?php if ($swatchRendererViewModel->isSwatchAttribute($productAttribute)): ?>
                            <div
                                x-cloak
                                x-show="activeStep === <?= $counter ?>"
                                class="swatch-attribute absolute inset-0 overflow-y-auto <?= $escaper->escapeHtmlAttr($productAttribute->getAttributeCode()) ?>"
                            >
                                <template x-if="showSwatches">
                                    <div class="grid gap-2 pb-6 md:gap-4">
                                        <h3 class="pt-1 px-3 bg-white md:pt-3 md:pr-12 md:text-3xl md:leading-normal">
                                            <span>
                                                <?= $escaper->escapeHtml(__('Choose a')) ?>
                                            </span>
                                            <span class="lowercase">
                                                <?= $escaper->escapeHtml($productAttribute->getStoreLabel()) ?>
                                            </span>
                                        </h3>
                                        <div class="product-option-values">
                                            <div
                                                class="swatch-attribute-options"
                                                role="radiogroup"
                                                aria-label="<?= $escaper->escapeHtmlAttr($productAttribute->getStoreLabel()) ?>"
                                            >
                                                <?php if ($productAttribute->getAttributeCode() === 'color_fabric'): ?>
                                                    <?= $block
                                                        ->assign([
                                                            'attributeId' => $attributeId,
                                                            'product' => $product,
                                                            'productAttribute' => $productAttribute,
                                                            'swatchItemBlock' => $swatchItemBlock
                                                        ])
                                                        ->fetchView(
                                                            (string)$block->getTemplateFile('Magento_Swatches::product/view/renderer-fabric.phtml'))
                                                    ?>
                                                <?php elseif ($productAttribute->getAttributeCode() === 'dimensions' ): ?>  
                                                    <div class="grid grid-cols-2 gap-2 px-3">
                                                        <template
                                                            x-for="(item, index) in optionConfig.attributes[<?= (int) $attributeId ?>].options"
                                                            :key="item.id"
                                                        >
                                                            <?= /* @noEscape */ $swatchItemBlock->setData('attribute_id', $attributeId)
                                                                ->setData('attribute_code', $productAttribute->getAttributeCode())
                                                                ->setData('product', $product)
                                                                ->toHtml(); ?>
                                                        </template>
                                                    </div>
                                                <?php else: ?>
                                                    <div class="grid gap-2 px-3">
                                                        <template
                                                            x-for="(item, index) in optionConfig.attributes[<?= (int) $attributeId ?>].options"
                                                            :key="item.id"
                                                        >
                                                            <?= /* @noEscape */ $swatchItemBlock->setData('attribute_id', $attributeId)
                                                                ->setData('attribute_code', $productAttribute->getAttributeCode())
                                                                ->setData('product', $product)
                                                                ->toHtml(); ?>
                                                        </template>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                        <template x-if="activeStep === <?= (count($attributes) + $loopIncrement) ?>">
                                            <div class="px-3">
                                                <div class="p-4 bg-secondary-300 rounded-[0.25rem]">
                                                    <h3 class="text-lg font-bold mb-6">
                                                        <?= $escaper->escapeHtml(__('Summary')) ?>
                                                    </h3>
                                                    <ol class="grid gap-6">
                                                        <?php foreach ($attributes as $attribute): ?>
                                                            <?php $attributeId = $attribute->getAttributeId(); ?>
                                                            <?php $productAttribute = $attribute->getProductAttribute();  ?>
                                                            <li class="swatch-attribute <?= $escaper->escapeHtmlAttr($productAttribute->getAttributeCode()) ?>">
                                                                <template x-if="showSwatches">
                                                                    <div class="grid gap-2">
                                                                        <span class="uppercase text-sm opacity-30">
                                                                            <?= $escaper->escapeHtml($productAttribute->getStoreLabel()) ?>
                                                                        </span>
                                                                        <span
                                                                            x-html="getSwatchLabel(<?= (int)$attributeId ?>, selectedValues[<?= (int)$attributeId ?>]) || ''"
                                                                        ></span>
                                                                    </div>
                                                                </template>
                                                            </li>
                                                        <?php endforeach; ?>
                                                    </ol>
                                                </div>
                                            </div>
                                        </template>
                                    </div>
                                </template>
                            </div>
                        <?php else: ?>
                            <div class="flex items-center w-full py-2">
                                <label
                                    class="w-1/2 text-left text-gray-700 label"
                                    for="attribute<?= $escaper->escapeHtmlAttr($attribute->getAttributeId()) ?>"
                                >
                                    <span>
                                        <?= $escaper->escapeHtml($attribute->getProductAttribute()->getStoreLabel()) ?>
                                    </span>
                                </label>
                                <div class="w-1/2 ml-2 text-left text-gray-900">
                                    <select
                                        name="super_attribute[<?= $escaper->escapeHtmlAttr($attribute->getAttributeId()) ?>]"
                                        id="attribute<?= $escaper->escapeHtmlAttr($attribute->getAttributeId()) ?>"
                                        class="form-select super-attribute-select"
                                        x-on:change="changeOption(<?= (int) $attribute->getAttributeId() ?>, event.target.value)"
                                        required="required"
                                    >
                                        <option value="">
                                            <?= $escaper->escapeHtml(__('Choose an Option...')) ?>
                                        </option>
                                        <template
                                            x-for="(item, index) in getAllowedAttributeOptions(<?= (int) $attribute->getAttributeId() ?>)"
                                            :key="item.id"
                                        >
                                            <option
                                                :value="item.id"
                                                x-html="item.label"
                                                :selected="selectedValues[<?= $escaper->escapeHtmlAttr($attribute->getAttributeId()) ?>] === item.id">
                                            </option>
                                        </template>
                                    </select>
                                </div>
                            </div>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </div>

                <?php // footer ?>
                <footer class="grid relative z-10 bg-white shadow-[0_-4px_20px_0_rgba(0,0,0,0.2)]">
                    <div class="grid px-3 py-2 gap-2 md:gap-3 md:p-3">

                        <div class="flex gap-3 items-start">
                            <?php // price and step navigation ?>
                            <?= $block->getChildHtml("product.flyout.price") ?>

                            <?php // next step button ?>
                            <button
                                x-show="activeStep < <?= (count($attributes) + $loopIncrement) ?>"
                                @click.prevent="navigateToStep(activeStep + 1)"
                                class="btn btn-secondary inline-flex gap-2 ml-auto shrink-0"
                            >
                                <span><?= $escaper->escapeHtml(__('Next')) ?> </span>
                                <?= $svgIcons->arrowRightHtml("", 20, 20, ['aria-hidden' => 'true']) ?>
                            </button>

                            <?php // add to cart button ?>
                            <button
                                x-show="activeStep === <?= (count($attributes) + $loopIncrement) ?>"
                                @click="toggleFlyout(false)"
                                type="submit"
                                :disabled="!canAddToCart"
                                class="btn btn-primary group inline-flex gap-2 ml-auto shrink-0 font-semibold"
                            >
                                <span><?= $escaper->escapeHtml(__('Add to Cart')) ?></span>
                            </button>
                        </div>

                        <?php // step navigation ?>
                        <div class="basis-full relative isolate overflow-clip rounded-full leading-[0] bg-secondary-900/50">
                            <menu class="flex">
                                <?php $counter = 0 + $loopIncrement; ?>
                                <?php if ($groupListAtts): ?>
                                    <li class="grow shrink">
                                        <button
                                            @click.prevent="navigateToStep(<?= $counter ?>)"
                                            class="w-full h-2"
                                            :disabled="activeStep <= <?= $counter ?>"
                                        >
                                            <span class="sr-only">
                                                <?= $escaper->escapeHtml(__('Go to step')) ?> <span><?= $counter ?></span>
                                            </span>
                                        </button>
                                    </li>
                                <?php endif; ?>
                                <?php if (count($attributes) > 1): ?>
                                    <?php foreach ($attributes as $attribute): ?>
                                        <?php $counter++; ?>
                                        <li class="grow shrink">
                                            <button
                                                @click.prevent="navigateToStep(<?= $counter ?>)"
                                                class="w-full h-2"
                                                :disabled="activeStep <= <?= $counter ?>"
                                            >
                                                <span class="sr-only">
                                                    <?= $escaper->escapeHtml(__('Go to step')) ?> <span><?= $counter ?></span>
                                                </span>
                                            </button>
                                        </li>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </menu>

                            <?php // progress bar ?>
                            <div
                                class="h-full absolute left-0 top-0 -z-1 bg-primary origin-left transition-all ease-out duration-200 border-r-2 border-white"
                                :style="{ width: 'calc(' + (activeStep * <?= 100 / (count($attributes) + $loopIncrement) ?>) + '% + 2px)' }"
                            ></div>
                        </div>
                    </div>

                    <?php // expected delivery date ?>
                    <div class="px-3 py-2 text-sm text-primary text-center delivery-wrapper">
                        <?php if ($product->getIsSalable()): ?>
                            <span class="font-bold label">
                                <?= $escaper->escapeHtml(__('Expected delivery time is currently')); ?>
                            </span>
                            <span
                                class="delivery-message"
                                data-delivery-message-product-id="<?= $product->getId(); ?>"
                            ></span>
                        <?php else: ?>
                            <span><?= $escaper->escapeHtml(__('Out of stock')) ?></span>
                        <?php endif; ?>
                    </div>
                </footer>
            </div>

            <?php // overlay ?>
            <div
                @click="toggleFlyout(false)"
                class="fixed inset-0 bg-black/30 -z-10 cursor-pointer"
            ></div>
        </div>
    </div>

<?php endif; ?>
