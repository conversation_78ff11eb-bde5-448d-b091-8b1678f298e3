<?php

declare(strict_types=1);

use Hyva\Theme\ViewModel\SvgIcons;
use Life\ProductQualityMark\ViewModel\QualityMarks;

/** @var \Magento\Swatches\Block\Product\Renderer\Configurable $block */
/** @var \Magento\Framework\Escaper $escaper */
/** @var \Hyva\Theme\Model\ViewModelRegistry $viewModels */

if (
    !isset($product)
    || !isset($attributeId)
    || !isset($swatchItemBlock)
    || !isset($productAttribute)
) {
    return '';
}

/** @var QualityMarks $qualityMarksViewModel */
$qualityMarksViewModel = $viewModels->require(QualityMarks::class);
$swatchQualityMarks = $qualityMarksViewModel->getSwatchAttributesMarksJson($product, $block->getJsonSwatchConfig());

/** @var SvgIcons $svgIcons */
$svgIcons = $viewModels->require(SvgIcons::class);

$materialFabricOptions = $qualityMarksViewModel->getMaterialFabricOptions($product);
?>

<?php // tab menu ?>
<?php
    $menuBtnCls = 'btn bg-secondary-300 text-primary btn-size-xs !px-6 whitespace-nowrap';
?>
<script defer>
    'use strict';
    function fabricSwatches() {
        return {
            selectedFabric: {
                label: '',
                category: ''
            },
            activePanel: 0,
            attributeId: <?= $attributeId; ?>,
            activeFilter: [0, '<?= $escaper->escapeHtml(__('All')); ?>'],

            preselectQueryStringOptions() {
                // Pre-select option like ?612=167
                const urlQueryParams = new URLSearchParams(window.location.search.replace('?',''));
                Object.values(this.optionConfig.attributes).map(attribute => {
                    if (parseInt(attribute.id) === this.attributeId && urlQueryParams.get(attribute.id)) {
                        let optionId = parseInt(urlQueryParams.get(attribute.id)),
                            selectedOption = attribute.options.find(option => parseInt(option.id) === optionId),
                            selectedOptionElement = document.querySelector('label[data-option-id="' + optionId + '"]');

                        this.selectedFabric.label = selectedOption.label;
                        if (selectedOptionElement) {
                            this.selectedFabric.category = selectedOptionElement.dataset.optionCategory;
                        }
                    }
                });
            }
        }
    }
</script>
<div
    x-data="fabricSwatches"
    class="-mt-2"
    x-init="setTimeout(function() {preselectQueryStringOptions()}, 500)"
>

    <?php // filter menu ?>
    <div class="mb-6 overflow-hidden sticky top-0 z-10 bg-white md:mb-2">
        <menu class="flex gap-2 py-1 overflow-auto max-md:max-w-[100vw] md:max-w-md md:pb-4 md:pt-2">
            <li
                class="first:ml-3"
            >
                <button
                    class="<?= $menuBtnCls ?>"
                    :class="activeFilter[0] && 'bg-transparent hover:bg-secondary-300/50'"
                    @click.prevent="
                        activeFilter = [0,'<?= $escaper->escapeHtml(__('All')); ?>'];
                        activePanel = 0
                    "
                >
                    <?= $escaper->escapeHtml(__('All')); ?>
                </button>
            </li>
            <?php foreach ($materialFabricOptions as $optionId => $option): ?>
            <?php
                if (!$qualityMarksViewModel->getSwatchAttributesMarksByFabric(
                    $product,
                    $block->getJsonSwatchConfig(),
                    (int)$optionId
                )) {
                    continue;
                }
            ?>
            <li class="last:mr-3">
                <button
                    class="<?= $menuBtnCls ?>"
                    :class="activeFilter[0] != '<?= $escaper->escapeHtmlAttr($optionId) ?>' && 'bg-transparent hover:bg-secondary-300/50'"
                    @click.prevent="
                        activeFilter = [<?= $escaper->escapeHtmlAttr($optionId) ?>, '<?= $escaper->escapeHtml($option['label'] ?? ''); ?>'];
                        activePanel = <?= $escaper->escapeHtmlAttr($optionId) ?>
                    "
                >
                    <?= $escaper->escapeHtml($option['label'] ?? ''); ?>
                </button>
            </li>
            <?php endforeach; ?>
        </menu>
    </div>

    <?php // swatches ?>
    <div class="flex flex-wrap gap-4 mb-8 px-3 py-0.5">
        <?php foreach ($materialFabricOptions as $optionId => $option): ?>
            <?php
                if (!$qualityMarksViewModel->getSwatchAttributesMarksByFabric(
                    $product,
                    $block->getJsonSwatchConfig(),
                    (int)$optionId
                )) {
                    continue;
                }
            ?>
            <template
                x-for="(item, index) in getSwatchesByMaterial(<?= (int)$attributeId ?>, <?= (int)$optionId ?>)"
                :key="item.id"
            >
                <div
                    x-cloak
                    x-show="(activeFilter[0] == '<?= $escaper->escapeHtmlAttr($optionId) ?>' || !activeFilter[0])
                        && (isVisualSwatch(<?= (int) $attributeId ?>, item.id) || isTextSwatch(<?= (int) $attributeId ?>, item.id))"
                    class="basis-10 grow-0 shrink-0"
                >
                    <?= /* @noEscape */ $swatchItemBlock->setTemplate('Magento_Swatches::product/swatch-item-fabric.phtml')
                        ->addData([
                            'attribute_id' => $attributeId,
                            'attribute_label' => $escaper->escapeHtml($option['label'] ?? ''),
                            'attribute_code' => $productAttribute->getAttributeCode(),
                            'category_id' => $escaper->escapeHtmlAttr($optionId),
                            'product' => $product
                        ])
                        ->toHtml();
                    ?>
                </div>
            </template>
        <?php endforeach; ?>
    </div>

    <?php // selected swatch ?>
    <div class="mb-4 px-3 opacity-50" aria-live="polite">
        <span x-cloak x-show="selectedFabric.category" x-text="selectedFabric.category"></span>
        <span x-cloak x-show="selectedFabric.category && selectedFabric.label" aria-hidden="true"> | </span>
        <span x-cloak x-show="selectedFabric.label" x-text="selectedFabric.label" class="font-bold"></span>
    </div>

    <?php // info ?>
    <div class="px-3 mb-4" aria-live="polite">
        <?php foreach ($materialFabricOptions as $optionId => $option): ?>
            <?php
                if (!$qualityMarksViewModel->getSwatchAttributesMarksByFabric(
                    $product,
                    $block->getJsonSwatchConfig(),
                    (int)$optionId
                )) {
                    continue;
                }
            ?>
            <section
                x-cloak
                x-show="activePanel == '<?= $escaper->escapeHtmlAttr($optionId) ?>'"
                class="rounded-md bg-secondary-300 p-3"
            >
                <h4 class="text-base font-bold">
                    <?= $escaper->escapeHtml($option['label'] ?? ''); ?>
                </h4>
                <div class="mt-2">
                    <span class="text-xs text-primary subtitle"><?= $escaper->escapeHtml($option['subtitle'] ?? ''); ?></span>
                    <?php if ($option['content']): ?>
                        <div class="inline-block" x-data="{show: false}">
                            <button
                                type="button"
                                class="text-xs font-bold underline cursor-pointer text-primary"
                                @click="show = true; $dispatch('init-page-builder-elements', {
                                    data: getSwatchesByMaterial(<?= (int)$attributeId ?>, <?= (int)$optionId ?>),
                                    attributeId: <?= (int)$attributeId ?>
                                })"
                            >
                                <?= $escaper->escapeHtml(__('Read more')); ?>
                            </button>
                            <div
                                class="fixed top-0 bottom-0 right-0 z-50 flex justify-end w-full transition duration-500 ease-in-out bg-black/30"
                                x-show="show"
                                x-transition:enter-start="opacity-0"
                                x-transition:enter-end="opacity-100"
                                x-transition:leave-start="opacity-100"
                                x-transition:leave-end="opacity-0"
                            >
                                <div
                                    class="info-drawer bg-white w-[90%] md:w-3/4 lg:w-1/2 2xl:w-5/12 h-full relative py-6 px-3 md:px-0 md:py-12 rounded-tl-xl rounded-bl-xl"
                                >
                                    <div
                                        class="absolute cursor-pointer top-6 right-6 md:top-12 md:right-12 text-primary"
                                        @click="show = false">
                                        <?= $svgIcons->closeHtml("w-6 h-6") ?>
                                    </div>
                                    <div
                                        @click.outside="show = false"
                                        class="h-full overflow-y-auto"
                                    >
                                        <?= /* @noEscape */ $option['content'] ?? ''; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
                <div class="flex flex-shrink-0 flex-wrap gap-1 mt-6 swatches-extra-info">
                    <h5 class="basis-full text-sm font-bold"><?= $escaper->escapeHtml(__('Weather conditions')); ?></h5>
                    <?= /* @noEscape */ $option['images'] ?? ''; ?>
                </div>
            </section>
        <?php endforeach; ?>
    </div>
</div>
