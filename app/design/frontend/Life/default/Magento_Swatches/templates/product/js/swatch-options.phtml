<?php

declare(strict_types=1);

use Hyva\Theme\ViewModel\Currency;
use Life\RemoveCurrencySymbol\ViewModel\CurrencySymbolForceDisplay;

/** @var \Magento\Framework\View\Element\Template $block */
/** @var \Magento\Framework\Escaper $escaper */
/** @var \Hyva\Theme\Model\ViewModelRegistry $viewModels */

/** @var Currency $currencyViewModel */
$currencyViewModel = $viewModels->require(Currency::class);

/** @var CurrencySymbolForceDisplay $currencyForceDisplayViewModel */
$currencyForceDisplayViewModel = $viewModels->require(CurrencySymbolForceDisplay::class);
$currencySymbol = $currencyForceDisplayViewModel->getCurrencySymbol($currencyViewModel->getCurrentCurrency()->getCode());
?>
<script defer="defer">
    function initSwatchOptions(swatchConfig, swatchQualityMarks) {
        return {
            swatchConfig,
            swatchQualityMarks,
            getAttributeSwatchData(attributeId) {
                const swatchConfig = Object.assign({}, this.swatchConfig[attributeId]);
                swatchConfig['details'] = JSON.parse(swatchConfig['additional_data']);

                return swatchConfig;
            },
            getAllAttributeOptions(attributeId) {
                return (
                    this.optionConfig.attributes[attributeId] &&
                    this.optionConfig.attributes[attributeId].options
                ) || []
            },
            optionIsActive(attributeId, optionId) {
                // return true if a product with this option is in stock
                return !!this.getAllowedAttributeOptions(attributeId).find(
                    option => option.id === optionId
                )
            },
            optionIsActiveForCurrentSelection(attributeId, optionId) {
                // Make sure visible out-of-stock product are still selectable
                let partialSelection = { ...this.selectedValues };
                partialSelection[attributeId] = optionId;

                return !!this.findProductIdsForPartialSelection(partialSelection).length;
            },
            optionIsEnabled(attributeId, optionId) {
                // return true if a product with this option is enabled
                for (const productId in this.optionConfig.index) {
                    if (this.optionConfig.index[productId][attributeId] === optionId) {
                        return true;
                    }
                }
                return false;
            },
            mapSwatchTypeNumberToTypeCode(typeNumber) {
                switch ("" + typeNumber) {
                    case "1":
                        return "color"
                    case "2":
                        return "image"
                    case "3":
                        return "empty"
                    case "0":
                    default:
                        return "text"
                }
            },
            getTypeOfFirstOption(attributeId) {
                for (const optionId in this.swatchConfig[attributeId]) {
                    const option = this.swatchConfig[attributeId][optionId];
                    if (typeof option.type !== 'undefined') {
                        return this.mapSwatchTypeNumberToTypeCode(option.type);
                    }
                }
            },
            getVisualSwatchType(attributeId, targetOptionId) {
                // If a type configuration is present for the given option id, use it
                const config = this.swatchConfig[attributeId];
                if (config[targetOptionId] && typeof config[targetOptionId].type !== 'undefined') {
                    return this.mapSwatchTypeNumberToTypeCode(config[targetOptionId].type);
                }

                // Otherwise - if no config is present for the target option - use the type of the first option
                // with a type property from the attribute, thus assuming its the same type as the target option.
                // (This edge case condition can occur on single swatch products if some options are not salable)
                return this.getTypeOfFirstOption(attributeId);
            },
            getSwatchType(attributeId, optionId) {
                // Deserialize the attribute details the first time they are used
                if (this.swatchConfig[attributeId] && ! this.swatchConfig[attributeId].details) {
                    this.swatchConfig[attributeId] = this.getAttributeSwatchData(attributeId);
                }
                const type =  this.swatchConfig[attributeId] &&
                    this.swatchConfig[attributeId].details &&
                    this.swatchConfig[attributeId].details.swatch_input_type ||
                    "empty";
                return type === 'visual' ? this.getVisualSwatchType(attributeId, optionId) : type;
            },
            isTextSwatch(attributeId, optionId) {
                return this.getSwatchType(attributeId, optionId) === 'text';
            },
            isVisualSwatch(attributeId, optionId) {
                const type = this.getSwatchType(attributeId, optionId);

                return ['image', 'color'].includes(type);
            },
            getSwatchBackgroundStyle(attributeId, optionId) {
                const config = this.getSwatchConfig(attributeId, optionId);
                const type = this.getSwatchType(attributeId, optionId);

                if (type === "color") {
                        return 'background-color:' + config.value;
                } else if (type === "image") {
                        return "background: #ffffff url('" + config.value + "') no-repeat center";
                } else {
                    return '';
                }
            },
            getSwatchText(attributeId, optionId) {
                const config = this.getSwatchConfig(attributeId, optionId);
                return config.label || config.value || this.getOptionLabelFromOptionConfig(attributeId, optionId);
            },
            getOptionLabelFromOptionConfig(attributeId, optionId) {
                // Fallback if no value is present in swatchConfig data
                // Reference issue https://gitlab.hyva.io/hyva-themes/magento2-default-theme/-/issues/190
                const option = this.getAllAttributeOptions(attributeId).filter(option => option.id === optionId);
                return option && option[0] && option[0].label ||'';
            },
            getSwatchConfig(attributeId, optionId) {
                return this.swatchConfig[attributeId] && this.swatchConfig[attributeId][optionId]
                    ? this.swatchConfig[attributeId][optionId]
                    : false;
            },
            activeTooltipItem: false,
            tooltipPositionElement: false,
            isTooltipVisible() {
                return this.activeTooltipItem &&
                    this.getSwatchConfig(
                        this.activeTooltipItem.attribute,
                        this.activeTooltipItem.item
                    );
            },
            isFirstItemCol() {
                return this.activeTooltipItem.index === 0;
            },
            getTooltipImageStyle(attributeId, optionId) {
                const config = this.getSwatchConfig(attributeId, optionId);
                const type = this.getSwatchType(attributeId, optionId);

                if (type === "color") {
                    return 'background-color:' + config.value + '; width: 110px; height: 90px;';
                } else if (type === "image") {
                    return "background: #ffffff url('" + config.thumb +
                        "') center center no-repeat; width: 110px; height: 90px;";
                } else {
                    return 'display:none';
                }
            },
            getTooltipPosition() {
                return this.tooltipPositionElement ?
                    `top: ${this.tooltipPositionElement.offsetTop}px;` +
                    `left: ${
                        this.tooltipPositionElement.offsetLeft - (
                            this.tooltipPositionElement.closest('.snap') &&
                            this.tooltipPositionElement.closest('.snap').scrollLeft ||
                            0
                        )
                    }px;` : ''
            },
            getTooltipLabel() {
                return this.getSwatchConfig(this.activeTooltipItem.attribute, this.activeTooltipItem.item).label
            },
            focusedLabel: false,
            focusLabel(optionId) {
                this.focusedLabel = optionId;
            },
            blurLabel() {
                this.focusedLabel = false;
            },
            showSwatches: false,
            initShowSwatchesIntersect() {
                if ('IntersectionObserver' in window && !window.scrollY) {
                    let io = new IntersectionObserver(
                        entries => {
                            entries.map(entry => {
                                if (entry.isIntersecting) {
                                    this.showSwatches = true;
                                    io.unobserve(this.$root);
                                }
                            })
                        }
                    );
                    io.observe(this.$root);
                } else {
                    this.showSwatches = true
                }
            },
            isQualityMark(optionId) {
                return this.swatchQualityMarks[optionId];
            },
            qualityMarkImage(optionId) {
                return this.swatchQualityMarks[optionId]?.image;
            },
            getLabel(attributeId, optionId) {
                return this.getSwatchConfig(attributeId, optionId).label;
            },
            qualityMarkContent(optionId) {
                return this.swatchQualityMarks[optionId]?.content;
            },
            isQualityMarkContentVisible(optionId) {
                return this.swatchQualityMarks[optionId]?.visible;
            },
            hideQualityMarkContent(optionId) {
                if (this.swatchQualityMarks[optionId]) {
                    this.swatchQualityMarks[optionId].visible = false;
                }
            },
            showQualityMarkContent(optionId) {
                if (this.swatchQualityMarks[optionId]) {
                    this.swatchQualityMarks[optionId].visible = true;
                }
            },
            getSwatchImage(attributeId, optionId) {
                const config = this.getSwatchConfig(attributeId, optionId);
                const type = this.getSwatchType(attributeId, optionId);

                if (type === "image") {
                    return config.value;
                } else {
                    return '';
                }
            },
            getSwatchColor(attributeId, optionId) {
                const config = this.getSwatchConfig(attributeId, optionId);
                const type = this.getSwatchType(attributeId, optionId);

                if (type === "color") {
                    return 'background-color:' + config.value;
                } else {
                    return '';
                }
            },
            isImageSwatch(attributeId, optionId) {
                return this.getSwatchType(attributeId, optionId) === 'image';
            },
            isColorSwatch(attributeId, optionId) {
                return this.getSwatchType(attributeId, optionId) === 'color';
            },
            getSwatchLabel(attributeId, optionId) {
                return this.getSwatchConfig(attributeId, optionId)?.label;
            },
            extraLabel(optionId) {
                return this.swatchQualityMarks[optionId]?.material_frame_label;
            },
            getSwatchesByMaterial(attributeId, optionId) {
                return this.optionConfig?.attributes[attributeId]?.options.filter(
                    option => {
                        let materialFabricValue = this.swatchQualityMarks[option.id]?.material_fabric_value;
                        return (
                            materialFabricValue
                            && (materialFabricValue == optionId)
                        )
                    }
                );
            },
            isSelectedForCurrentMaterial(attributeId, optionId) {
                let currentMaterialOptions = this.getSwatchesByMaterial(attributeId, optionId),
                    selectedOptionId = this.selectedValues[attributeId];

                return !!currentMaterialOptions.filter(
                    option => {
                        return option.id === selectedOptionId;
                    }
                ).length;
            },
            getOptionPriceDifference(attributeId, optionId) {
                let cheapestProductId = this.findCheapestProductForPartialSelection(attributeId, optionId, true),
                    currentCheapestProductId = this.findCheapestProductForPartialSelection(attributeId, optionId, false);

                if (!cheapestProductId) {
                    return '';
                }

                // When no option is selected, use the cheapest product as current product
                if (!currentCheapestProductId) {
                    currentCheapestProductId = cheapestProductId;
                }

                let optionPrice = this.optionConfig.optionPrices[cheapestProductId],
                    currentPrice = this.optionConfig.optionPrices[currentCheapestProductId];
                if (!optionPrice || !currentPrice) {
                    return '';
                }

                let priceDifference = optionPrice.finalPrice.amount - currentPrice.finalPrice.amount,
                    sign = (priceDifference > 0) ? '+' : '-';

                if (priceDifference === 0) {
                    return '';
                }

                return sign + '<?= $escaper->escapeJs($currencySymbol) ?>' +
                    hyva.formatPrice(Math.abs(priceDifference))
                        .replace(/\.00$/g, '.-')
                        .replace(/\,00$/g, ',-')
                        .replace(/\s|&nbsp;/g, '');
            },
            findCheapestProductForPartialSelection(attributeId, optionId, useCurrent = true) {
                let partialSelection = { ...this.selectedValues };
                if (useCurrent) {
                    partialSelection[attributeId] = optionId;
                }

                let candidateProducts = this.findProductIdsForPartialSelection(partialSelection);
                if (!candidateProducts || !candidateProducts.length) {
                    candidateProducts = Object.keys(this.optionConfig.index);
                }

                return candidateProducts.reduce((cheapest, simpleIdx) => {
                    if (! this.optionConfig.optionPrices[cheapest]) return simpleIdx;
                    const knownCheapestPrice = this.optionConfig.optionPrices[cheapest].finalPrice.amount;
                    return knownCheapestPrice > this.optionConfig.optionPrices[simpleIdx].finalPrice.amount
                        ? simpleIdx
                        : cheapest;
                }, 0)
            }
        }
    }
</script>
