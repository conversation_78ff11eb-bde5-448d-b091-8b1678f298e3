<?php

declare(strict_types=1);

/** @var Template $block */
/** @var MultiSafepayIdeal $magewire */
/** @var Escaper $escaper */

$issuers = $magewire->getIssuers();

use MultiSafepay\MagewireCheckout\Payment\Method\MultiSafepayIdeal;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;

?>
<div class="col-span-6" wire:payment-method="multisafepay_ideal">
    <?php if ($issuers) { ?>
        <div class="flex flex-col gap-y-2">
            <label for="multisafepay_ideal_issuer">Please select your bank</label>
            <select name="issuer" id="multisafepay_ideal_issuer" wire:model="issuer" class="form-select sm:w-1/2">
                <?php
                foreach ($issuers as $issuer) {
                    ?>
                    <option value="<?= $issuer["code"] ?>"><?= $issuer["description"] ?></option>
                    <?php
                }
                ?>
            </select>
        </div>

    <?php } ?>
</div>
