<?php

declare(strict_types=1);

use Amasty\Label\Api\Data\LabelFrontendSettingsInterface;
use Amasty\Label\Api\Data\LabelInterface;
use Amasty\Label\Api\Data\RenderSettingsInterface;
use Amasty\Label\ViewModel\Label\TextProcessor;
use Amasty\ProductLabelsHyva\ViewModel\Label as LabelViewModel;

/** @var \Amasty\Label\Block\Label $block */
/** @var \Hyva\Theme\Model\ViewModelRegistry $viewModels */
/** @var \Magento\Framework\Escaper $escaper */

/** @var LabelInterface $label */
$label = $block->getLabel();
/** @var RenderSettingsInterface $renderSettings */
$renderSettings = $label->getExtensionAttributes()->getRenderSettings();

/** @var TextProcessor $textProcessor */
$textProcessor = $block->getData('text_processor');
/** @var LabelFrontendSettingsInterface $frontendSettings */
$frontendSettings = $label->getExtensionAttributes()->getFrontendSettings();
$labelViewModel = $viewModels->require(LabelViewModel::class);
$labelViewModel->setConfigFromBlock($block);

$productId = $renderSettings->getProduct()->getId();
$mode = $labelViewModel->getMode();
$positionClassPostfix = $labelViewModel->getPosition() . "-$productId-$mode";
$key = $label->getLabelId() . '-' . $productId . '-' . $mode;
$text = $textProcessor->renderLabelText($frontendSettings->getLabelText(), $label);
$imageSrc = $block->getImageSrc();
$altTag = $textProcessor->renderLabelAltTag($frontendSettings->getAltTag(), $label);
$uniqueId = uniqid('_', false);
?>

<?php if ($renderSettings->isLabelVisible()): ?>
    <div class="amlabel-position-wrapper absolute max-w-full h-fit-content w-fit-content flex gap-amlabel-margin z-20 m-4
         amlabel-position-<?= $escaper->escapeHtmlAttr($positionClassPostfix) ?>
         <?= $labelViewModel->getAlignment() ? 'flex-row' : 'flex-col' ?>"
         :class="{
            'top-0': config.position.includes('top'),
            'top-0 bottom-0': config.position.includes('middle'),
            'bottom-0': config.position.includes('bottom'),
            'left-0': config.position.includes('left'),
            'left-0 right-0': config.position.includes('center'),
            'right-0': config.position.includes('right')
         }"
         x-data='{
             ...initAmlabel(<?= /* @noEscape */ $block->getJsonConfig() ?>),
             parentContainerProcessor: initParentContainerProcessor(),
             labelItemProcessor: initLabelItemProcessor()
         }'
         x-init="initLabel($el)"
         x-intersect.once="render()">
        <div x-ref="amLabel"
             x-cloak
             x-show="initialized"
             x-transition:enter="transition ease-out duration-250"
             x-transition:enter-start="opacity-0 transform scale-90"
             x-transition:enter-end="opacity-100 transform scale-100"
             @resize.window.debounce="resize()"
             @click="labelItemProcessor.openLink($event, config.redirect_url)"
             class="group relative amasty-label-container min-w-[5rem] max-w-[7rem] amasty-label-container-<?= $escaper->escapeHtmlAttr($key) ?>
                amasty-label-for-<?= $escaper->escapeHtmlAttr($productId) ?>"
             style="<?= $escaper->escapeHtmlAttr($frontendSettings->getStyle()) ?>"
             data-amlabel-order="<?= $escaper->escapeHtmlAttr($block->getPriority()) ?>"
            <?php if ($labelViewModel->isShowTooltip()): ?>
                aria-describedby="label-tooltip-<?= $escaper->escapeHtmlAttr($label->getLabelId()) ?>"
                @mouseenter="labelItemProcessor.placeTooltip($event, $refs.tooltip)"
            <?php endif; ?>>
            <?php if ($labelViewModel->isShowTooltip()): ?>
                <div id="label-tooltip-<?= $escaper->escapeHtmlAttr($label->getLabelId()) ?>"
                     x-ref="tooltip"
                     class="fixed invisible opacity-0 transition-opacity -translate-x-1/2 text-center rounded z-[-1]
                        w-max max-w-[200px] mb-3 group-hover:z-30 shadow-md px-5 py-2 group-hover:visible group-hover:opacity-100 after:absolute
                        after:border-inherit after:top-[calc(100%-theme(space.1))] after:left-1/2 after:-translate-x-1/2
                        after:border-4 after:rotate-45"
                     style="background-color: <?= $escaper->escapeHtmlAttr($labelViewModel->getTooltipBackground()) ?>;
                         border-color: <?= $escaper->escapeHtmlAttr($labelViewModel->getTooltipBackground()) ?>"
                     role="tooltip">
                    <span style="color: <?= $escaper->escapeHtmlAttr($labelViewModel->getTooltipTextColor()) ?>">
                        <?= $escaper->escapeHtml($labelViewModel->getTooltipContent(), ['br']) ?>
                    </span>
                </div>
            <?php endif; ?>
            <div
                class="amlabel-text w-full flex justify-center items-center leading-none px-1.5 text-center font-bold<?= $imageSrc ? ' absolute left-1/2 top-1/2 -translate-y-1/2 -translate-x-1/2' : '' ?>">
                <span><?= /* @noEscape */ $escaper->escapeHtml($text, ['a', 'b', 'br']) ?></span>
            </div>
            <?php if ($imageSrc): ?>
                <img id="amasty-label-image-<?= $escaper->escapeHtml($key) ?>"
                     class="w-full h-full align-top opacity-100 amasty-label-image"
                     src="<?= $escaper->escapeUrl($imageSrc) ?>"
                     title="<?= $escaper->escapeHtmlAttr($altTag) ?>"
                     alt="<?= $escaper->escapeHtmlAttr($altTag) ?>">
            <?php endif; ?>
        </div>
    </div>
<?php endif; ?>
