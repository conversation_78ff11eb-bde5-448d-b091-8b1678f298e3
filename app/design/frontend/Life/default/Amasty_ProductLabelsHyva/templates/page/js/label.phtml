
<script defer="defer">
    'use strict';

    function initAmlabel(config) {
        return {
            element: null,
            parent: null,
            initialized: false,
            wrapperClass: '',
            labelContainer: null,
            config: config,
            initLabel: function ($el) {
                this.element = $el;
                this.labelContainer = this.$refs.amLabel;
                this.parent = $el.parentElement;
                this.wrapperClass = `.amlabel-position-${this.config.position}-${this.config.product}-${this.config.mode}`;
            },

            /**
             * @return {void}
             */
            render: function () {
                if (this.element.dataset.amlabelObserved) {
                    return;
                }

                this.element.dataset.amlabelObserved = true;
                this.parentContainerProcessor.process(this);
                this.labelItemProcessor.process(this);
                this.initialized = true;
            },

            /**
             * @return {void}
             */
            resize: function () {
                this.config.size && this.labelItemProcessor.setLabelSize(this);
            }
        }
    }
</script>
