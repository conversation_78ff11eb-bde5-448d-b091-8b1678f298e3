<?php

declare(strict_types=1);

/** @var \Magento\Sales\Block\Order\Item\Renderer\DefaultRenderer $block */
/** @var \Magento\Framework\Escaper $escaper */
/** @var \Hyva\Theme\Model\LocaleFormatter $localeFormatter */

/** @var \Magento\Sales\Model\Order\Item $item */
$item = $block->getItem();
$cols = $block->getData('is_context_shipment') ? 3 : ($block->getData('is_context_creditmemo') ? 7 : 4);
?>
<div class="lg:grid grid-cols-<?= $escaper->escapeHtmlAttr($cols) ?> gap-x-6">
    <div class="col-span-2">
        <div class="font-bold leading-[1.875rem] mb-2">
            <?php if ($product = $item->getProduct()): ?>
                <a
                    href="<?= $escaper->escapeUrl($product->getProductUrl()) ?>"
                    class="text-link"
                >
                    <?= $escaper->escapeHtml($item->getName()) ?>
                </a>
            <?php else: ?>
                <?= $escaper->escapeHtml($item->getName()) ?>
            <?php endif; ?>
        </div>
        <div class="item-options">
            <div class="flex text-xs text-black/60">
                <span><?= $escaper->escapeHtml(__('SKU')) ?>:</span>
                <span class="ml-1 text-xs"><?= /* @noEscape */ $block->prepareSku($block->getSku()) ?></span>
            </div>
            <?php if ($options = $block->getItemOptions()): ?>
                <?php foreach ($options as $option): ?>
                    <div class="flex mt-1 text-xs">
                        <span><?= $escaper->escapeHtml($option['label']) ?>:</span>
                        <?php if (!$block->getPrintStatus()): ?>
                            <?php $formatedOptionValue = $block->getFormatedOptionValue($option) ?>
                            <span class="ml-1">
                                <?= $escaper->escapeHtml($formatedOptionValue['value'], ['a']) ?>
                        </span>
                        <?php else: ?>
                            <span class="ml-1">
                                <?= $escaper->escapeHtml(
                                    (isset($option['print_value']) ? $option['print_value'] : $option['value'])
                                ) ?>
                            </span>
                        <?php endif; ?>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
            <?php /* downloadable */ ?>
            <?php if ($links = $block->getLinks()): ?>
                <div class="my-2 item options">
                    <p><?= $escaper->escapeHtml($block->getLinksTitle()) ?></p>
                    <?php foreach ($links->getPurchasedItems() as $link): ?>
                        <p class="ml-1"><?= $escaper->escapeHtml($link->getLinkTitle()) ?></p>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
            <?php $addtInfoBlock = $block->getProductAdditionalInformationBlock(); ?>
            <?php if ($addtInfoBlock): ?>
                <?= $addtInfoBlock->setItem($item)->toHtml() ?>
            <?php endif; ?>
            <?= $escaper->escapeHtml($item->getDescription()) ?>
        </div>
    </div>
    <div class="flex justify-between lg:block">
        <p class="font-medium lg:sr-only">
            <?php if ($block->getData('is_context_invoice')): ?>
                <?= $escaper->escapeHtml(__('Qty Invoiced')) ?>
            <?php elseif ($block->getData('is_context_shipment')): ?>
                <?= $escaper->escapeHtml(__('Qty Shipped')) ?>
            <?php else: ?>
                <?= $escaper->escapeHtml(__('Qty')) ?>
            <?php endif; ?>
        </p>
        <div class="font-bold text-right lg:text-left">
            <p>
                <span class="title"><?= $escaper->escapeHtml(__('Qty')) ?>:</span>
                <span class="content">
                    <?= /** @noEscape */ $localeFormatter->formatNumber($block->getItem()->getQtyOrdered()) ?>
                </span>
            </p>
        </div>
    </div>
    <?php if (!$block->getData('is_context_shipment')): ?>
        <div class="flex justify-between text-right lg:block">
            <p class="font-medium lg:sr-only"><?= $escaper->escapeHtml(__('Subtotal')) ?></p>
            <?= $block->getItemRowTotalHtml() ?>
        </div>
    <?php endif; ?>
    <?php if ($block->getData('is_context_creditmemo')): ?>
        <div class="flex justify-between text-right lg:block">
            <p class="font-medium lg:sr-only"><?= $escaper->escapeHtml(__('Discount Amount')) ?></p>
            <?= /* @noEscape */ $block->getOrder()->formatPrice($item->getDiscountAmount()) ?>
        </div>
        <div class="flex justify-between text-right lg:block">
            <p class="font-medium lg:sr-only"><?= $escaper->escapeHtml(__('Row Total')) ?></p>
            <?= $block->getItemRowTotalAfterDiscountHtml() ?>
        </div>
    <?php endif; ?>
</div>
