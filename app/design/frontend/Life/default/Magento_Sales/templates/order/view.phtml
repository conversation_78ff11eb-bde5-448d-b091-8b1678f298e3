<?php

use Hyva\Theme\ViewModel\SvgIcons;
use Magento\Framework\Escaper;
use Magento\GiftMessage\Helper\Message;
use Magento\Sales\Block\Order\View;

// phpcs:disable Magento2.Templates.ThisInTemplate.FoundThis
// phpcs:disable Magento2.Templates.ThisInTemplate.FoundHelper

/** @var View $block */
/** @var Escaper $escaper */
/** @var \Hyva\Theme\Model\ViewModelRegistry $viewModels */

$giftMessageHelper = $this->helper(Message::class);

/** @var SvgIcons $svgIcons */
$svgIcons = $viewModels->require(SvgIcons::class);
?>
<?php $order = $block->getOrder() ?>
<div class="p-4 bg-white rounded-xl">
    <div class="items-center pb-4 mb-4 border-b md:flex md:justify-between border-secondary-900/50">
        <div class="items-center text-center lg:flex md:text-left">
            <div class="lg:inline-block">
                <div class="flex items-center mb-2 gap-x-6">
                    <a class="flex items-center" href="<?= $escaper->escapeUrl($block->getBackUrl()) ?>">
                        <?= $svgIcons->chevronLeftHtml("w-4 h-4") ?>
                        <span class="sr-only"><?= $escaper->escapeHtml($block->getBackTitle()) ?></span>
                    </a>
                    <span class="text-xl leading-9"><?= $escaper->escapeHtml(__('Order # %1', $order->getRealOrderId())) ?></span>
                </div>
                <?= $block->getChildHtml('order.date') ?>
            </div>
        </div>
        <?php if (!$block->getData('is_context_shipment')): ?>
            <?= $block->getChildHtml('sales.order.buttons') ?>
        <?php else: ?>
            <?= $block->getChildHtml('sales.shipment.buttons') ?>
        <?php endif; ?>
    </div>
    <div class="order-details-items ordered">
        <div class="pb-4 mb-4 border-b border-secondary-900/50">
            <div class="flex max-sm:flex-wrap gap-x-6 gap-y-4">
                <?= $block->getChildHtml('order.status') ?>
                <?= $block->getChildHtml('order.delivery.date') ?>
            </div>

            <?= $block->getChildHtml('order.bumbal.url') ?>
        </div>

        <?= $block->getChildHtml('order.comments') ?>

        <?= $block->getChildHtml('order_items') ?>

        <?= $block->getChildHtml('sales.order.info') ?>

        <?php if ($giftMessageHelper->isMessagesAllowed('order', $order) && $order->getGiftMessageId()): ?>
            <div class="block block-order-details-gift-message">
                <div class="block-title"><strong><?= $escaper->escapeHtml(__('Gift Message for This Order')) ?></strong>
                </div>
                <?php
                $giftMessage =
                    $giftMessageHelper->getGiftMessageForEntity($order);
                ?>
                <div class="block-content">
                    <dl class="item-options">
                        <dt class="item-sender">
                            <strong class="label">
                                <?= $escaper->escapeHtml(__('From')) ?>
                            </strong>
                            <?= $escaper->escapeHtml($giftMessage->getSender()) ?>
                        </dt>
                        <dt class="item-recipient">
                            <strong class="label">
                                <?= $escaper->escapeHtml(__('To')) ?>
                            </strong>
                            <?= $escaper->escapeHtml($giftMessage->getRecipient()) ?>
                        </dt>
                        <dd class="item-message">
                            <?= /* @noEscape */ $giftMessageHelper->getEscapedGiftMessage($order) ?>
                        </dd>
                    </dl>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>
