<?php

declare(strict_types=1);

use Hyva\Theme\ViewModel\Sales\PaymentInfo;
use Life\ShippingMethodPickup\ViewModel\ShippingInfo;

/** @var \Magento\Sales\Block\Order\Info $block */
/** @var \Magento\Framework\Escaper $escaper */
/** @var \Hyva\Theme\Model\ViewModelRegistry $viewModels */

/** @var \Magento\Sales\Model\Order $order */
$order = $block->getOrder();

/** @var \Hyva\Theme\ViewModel\Sales\PaymentInfo $paymentInfo */
$paymentInfo = $viewModels->require(PaymentInfo::class);

/** @var ShippingInfo $shippingInfo */
$shippingInfo = $viewModels->require(ShippingInfo::class);
?>
<div class="grid grid-cols-1 gap-2 gap-y-4 md:grid-cols-2 lg:grid-cols-4">
    <div class="w-full">
        <p class="mb-2 font-semibold"><?= $escaper->escapeHtml(__('Billing Address')) ?></p>
        <div class="box-content">
            <address class="text-sm"><?= /* @noEscape */ $block->getFormattedAddress($order->getBillingAddress()) ?></address>
        </div>
    </div>
    <?php if (!$order->getIsVirtual()): ?>
        <div class="w-full">
            <p class="mb-2 font-semibold"><?= $escaper->escapeHtml(__('Shipping Address')) ?></p>
            <div class="box-content">
                <address class="text-sm"><?= /* @noEscape */ $block->getFormattedAddress($order->getShippingAddress()) ?></address>
            </div>
        </div>
        <div class="w-full">
            <p class="mb-2 font-semibold"><?= $escaper->escapeHtml(__('Shipping Method')) ?></p>
            <div class="box-content text-sm">
                <?php if ($order->getShippingDescription()): ?>
                    <?= $escaper->escapeHtml($order->getShippingDescription()) ?>
                <?php else: ?>
                    <?= $escaper->escapeHtml(__('No shipping information available')) ?>
                <?php endif; ?>
                <?php if (
                    str_starts_with($order->getShippingMethod(), 'afhalen')
                    && trim($pickupAddress = $shippingInfo->getPickupAddress())
                ): ?>
                    <div class="pt-4 prose">
                        <?= /* @noEscape */ $pickupAddress; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    <?php endif; ?>
    <div class="w-full">
        <p class="mb-2 font-semibold"><?= $escaper->escapeHtml(__('Payment Method')) ?></p>
        <div class="box-content text-sm">
            <?= $escaper->escapeHtml($paymentInfo->getPaymentTitle($order)) ?>
        </div>
    </div>
</div>
