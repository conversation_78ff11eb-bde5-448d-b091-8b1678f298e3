<?php

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\SvgIcons;
use Life\ShowroomOrderHistory\ViewModel\OrderHistoryData;
use Magento\Framework\Escaper;
use Magento\Sales\Block\Order\History;

// phpcs:disable Magento2.Templates.ThisInTemplate.FoundThis
// phpcs:disable Magento2.Templates.ThisInTemplate.FoundHelper

/** @var History $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */

/** @var SvgIcons $svgIcons */
$svgIcons = $viewModels->require(SvgIcons::class);

/** @var OrderHistoryData $orderHistory */
$orderHistory = $viewModels->require(OrderHistoryData::class);
?>
<?php $orders = $block->getOrders(); ?>
<h2 id="my-orders-title" class="mb-6 text-2xl"><?= $escaper->escapeHtml(__('My Orders')) ?></h2>
<?= $block->getChildHtml('info') ?>
<?php if ($orders && count($orders)): ?>
    <div
        role="table"
        class="mb-4 card"
        aria-labelledby="my-orders-title"
    >
        <div
            role="row"
            id="my-orders-table"
            class="grid grid-cols-4 border-b lg:grid-cols-5 border-secondary-900/50"
        >
            <div
                role="columnheader"
                class="col-span-2 px-1 pb-4 text-sm font-bold lg:col-span-1"
            >
                <?= $escaper->escapeHtml(__('Order #')) ?>
            </div>
            <div
                role="columnheader"
                class="hidden px-1 pb-4 text-sm font-bold lg:block"
            >
                <?= $escaper->escapeHtml(__('Date')) ?>
            </div>
            <div
                role="columnheader"
                class="px-1 pb-4 text-sm font-bold"
            >
                <?= $escaper->escapeHtml(__('Order Total')) ?>
            </div>
            <div
                role="columnheader"
                class="hidden px-1 pb-4 text-sm font-bold lg:block"
            >
                <?= $escaper->escapeHtml(__('Status')) ?>
            </div>
            <div
                role="columnheader"
                class="hidden px-1 pb-4 text-sm font-bold md:block"
            >
                <?= $escaper->escapeHtml(__('View')) ?>
            </div>
        </div>
        <?php $i = 0; ?>
        <?php foreach ($orders as $order): ?>
            <div
                role="row"
                class="grid grid-cols-4 lg:grid-cols-5"
            >
                <?php $i++; ?>
                <div
                    id="order<?= $escaper->escapeHtmlAttr($order->getRealOrderId()) ?>"
                    role="rowheader"
                    class="flex flex-col justify-center h-12 col-span-2 px-1 text-xs border-b lg:col-span-1 md:justify-start md:items-center md:flex-row border-secondary-900/50 gap-x-1"
                >
                    <?= $escaper->escapeHtml($order->getRealOrderId()) ?>
                    <p class="lg:hidden">
                        <?= $escaper->escapeHtml($block->formatDate($order->getCreatedAt())) ?>
                    </p>
                </div>
                <div
                    role="cell"
                    class="items-center hidden h-12 px-1 text-xs border-b lg:flex border-secondary-900/50"
                >
                    <?php
                        $date = $order->getCreatedAt();
                        $format = $block->getDateFormat() ?: \IntlDateFormatter::SHORT;
                        $formatLong = $block->getDateFormat() ?: \IntlDateFormatter::LONG;
                    ?>
                    <span aria-hidden="true">
                        <?= $escaper->escapeHtmlAttr($block->formatDate($date, $format)); ?>
                    </span>
                    <span class="sr-only">
                        <?= $escaper->escapeHtml($block->formatDate($date, $formatLong)); ?>
                    </span>
                </div>
                <div
                    role="cell"
                    class="flex items-center h-12 px-1 text-xs border-b border-secondary-900/50"
                >
                    <?= /* @noEscape */ $order->formatPrice($order->getGrandTotal()) ?>
                </div>
                <div
                    role="cell"
                    class="items-center hidden h-12 px-1 text-xs border-b lg:flex border-secondary-900/50"
                >
                    <?= $escaper->escapeHtml(
                        $order->getStatusLabel()
                    ) ?>
                </div>
                <div
                    role="cell"
                    class="flex items-center justify-end h-12 px-1 text-xs border-b border-secondary-900/50 md:justify-start"
                >
                    <a
                        href="<?= $escaper->escapeUrl($block->getViewUrl($order)) ?>"
                        class="inline-block text-link"
                        title="<?= $escaper->escapeHtmlAttr(__('View Order')) ?>"
                        aria-label="<?= $escaper->escapeHtmlAttr(__('View order') . ' ' . ltrim($order->getRealOrderId(), '0')) ?>"
                    >
                        <?= $escaper->escapeHtml(__('View')) ?>
                    </a>
                </div>
            </div>
        <?php endforeach; ?>
    </div>
    <?php if ($block->getPagerHtml()): ?>
        <div class="order-products-toolbar toolbar bottom"><?= $block->getPagerHtml() ?></div>
    <?php endif ?>
<?php else: ?>
    <div class="message info empty"><span><?= $escaper->escapeHtml($block->getEmptyOrdersMessage()) ?></span></div>
<?php endif ?>

<?php // showroom orders ?>
<section
    x-cloak
    x-data="showroomOrders()"
    x-show="orders.length > 0"
    @private-content-loaded.window="getData()"
    class="mt-6"
>
    <h2
        id="showroom-order-title"
        class="mb-6 text-2xl"
    >
        <?= $escaper->escapeHtml(__('Showroom orders')) ?>
    </h2>
    <div
        role="table"
        class="mb-4 card"
        aria-labelledby="my-showroom-orders-title"
        aria-live="polite"
    >
        <div
            role="row"
            id="my-showroom-orders-table"
            class="grid grid-cols-4 border-b lg:grid-cols-5 border-secondary-900/50"
        >
            <div
                role="columnheader"
                class="col-span-2 px-1 pb-4 text-sm font-bold lg:col-span-1"
            >
                <?= $escaper->escapeHtml(__('Order #')) ?>
            </div>
            <div
                role="columnheader"
                class="hidden px-1 pb-4 text-sm font-bold lg:block"
            >
                <?= $escaper->escapeHtml(__('Date')) ?>
            </div>
            <div
                role="columnheader"
                class="px-1 pb-4 text-sm font-bold"
            >
                <?= $escaper->escapeHtml(__('Order Total')) ?>
            </div>
            <div
                role="columnheader"
                class="hidden px-1 pb-4 text-sm font-bold lg:block"
            >
                <?= $escaper->escapeHtml(__('Status')) ?>
            </div>
            <div
                role="columnheader"
                class="hidden px-1 pb-4 text-sm font-bold md:block"
            >
                <?= $escaper->escapeHtml(__('View')) ?>
            </div>
        </div>
        <template x-for="(order, index) in orders" :key="index">
            <article
                role="row"
                class="grid grid-cols-4 lg:grid-cols-5"
            >
                <div
                    id="order"
                    role="rowheader"
                    class="flex flex-col justify-center h-12 col-span-2 px-1 text-xs border-b lg:col-span-1 md:justify-start md:items-center md:flex-row border-secondary-900/50 gap-x-1"
                >
                    <span x-text="order.order_number"></span>
                    <time
                        class="lg:hidden"
                        x-text="formatDate(order.order_created_date)"
                        :datetime="order.order_created_date"
                    ></time>
                </div>
                <time
                    role="cell"
                    class="items-center hidden h-12 px-1 text-xs border-b lg:flex border-secondary-900/50"
                    :datetime="order.order_created_date"
                >
                    <span aria-hidden="true" x-text="formatDate(order.order_created_date)"></span>
                    <span class="sr-only" x-text="formatDate(order.order_created_date, 'long')"></span>
                </time>
                <div
                    role="cell"
                    class="flex items-center h-12 px-1 text-xs border-b border-secondary-900/50"
                    x-text="hyva.formatPrice(order.order_total)"
                ></div>

                <!-- TODO: handle translations -->
                <div
                    role="cell"
                    class="items-center hidden h-12 px-1 text-xs border-b lg:flex border-secondary-900/50"
                    x-text="order.sorder_status"
                ></div>
                <div
                    role="cell"
                    class="flex items-center justify-end h-12 px-1 text-xs border-b border-secondary-900/50 md:justify-start"
                >
                    <button
                        @click="toggleDetails(event, order.order_number)"
                        class="group inline-block text-link"
                        aria-expanded="false"
                        :aria-controls="`order-details-${order.order_number}`"
                        :aria-label="`<?= $escaper->escapeHtml(__('View')) ?> <?= $escaper->escapeHtml(__('order')) ?> ${order.order_number}`"
                    >
                        <span class="group-aria-expanded:hidden pointer-events-none">
                            <?= $escaper->escapeHtml(__('View')) ?>
                        </span>
                        <span class="hidden group-aria-expanded:inline pointer-events-none">
                            <?= $escaper->escapeHtml(__('Close')) ?>
                        </span>
                    </button>
                </div>

                <?php // order items ?>
                <section
                    :id="`order-details-${order.order_number}`"
                    class="col-span-full mt-6 hidden"
                    role="cell"
                    aria-labelledby="ordered-items-title"
                >
                    <h3
                        id="ordered-items-title"
                        class="sr-only"
                    >
                        <?= $escaper->escapeHtml(__('Ordered items')) ?>
                    </h3>
                    <template x-for="(item, index) in order.order_items" :key="index">
                        <article
                            role="row"
                            class="border-b border-secondary-900/30 mb-6 pb-6 text-xs last:border-b-0 lg:last:pb-12"
                        >
                            <div class="lg:grid lg:grid-cols-5">

                                <?php // product name and sku ?>
                                <header class="col-span-3 px-1">
                                    <h4
                                        role="cell"
                                        class="text-base font-bold leading-[1.875rem] mb-2"
                                        x-text="item.name"
                                    ></h4>
                                    <p class="flex text-xs text-black/60">
                                        <span><?= $escaper->escapeHtml(__('SKU')) ?>:</span>
                                        <span
                                            class="ml-1"
                                            x-text="item.product_id"
                                        ></span>
                                    </p>
                                </header>

                                <?php // qty ?>
                                <div class="flex justify-between px-1 lg:block">
                                    <p class="lg:sr-only">
                                        <?= $escaper->escapeHtml(__('Qty')) ?>
                                    </p>
                                    <div class="text-right lg:text-left">
                                        <p>
                                            <span>
                                                <?= $escaper->escapeHtml(__('Qty')) ?>:
                                            </span>
                                            <span x-text="item.quantity"></span>
                                        </p>
                                    </div>
                                </div>

                                <?php // price ?>
                                <div class="flex justify-between px-1 lg:block">
                                    <p class="font-medium lg:sr-only">
                                        <?= $escaper->escapeHtml(__('Subtotal')) ?>
                                    </p>
                                    <span class="font-bold" x-text="hyva.formatPrice(item.total_price)"></span>
                                </div>
                            </div>
                        </article>
                    </template>
                </section>
            </article>
        </template>
    </div>
</section>
<script defer>
    'use strict';
    const orders = [];
    function showroomOrders() {
        return {
            orders: [],
            customerData: {},
            formatDate(orderDate, monthFormat = 'numeric') {
                const date = new Date(orderDate);
                return new Intl.DateTimeFormat(this.customerData.tracking.country, {
                    year: 'numeric',
                    month: monthFormat,
                    day: 'numeric',
                }).format(date);
            },
            async getData() {
                const endpointUrl = "<?= $orderHistory->getApiEndpointUrl(); ?>";
                if (endpointUrl) {
                    const token = JSON.parse(localStorage.getItem('mage-cache-storage'))?.customer?.signin_token;
                    const headers = new Headers();

                    headers.append("Content-Type", "application/json");
                    headers.append("Authorization", `Bearer ${token}`);
                    const requestOptions = {
                        method: "GET",
                        headers
                    };

                    fetch(endpointUrl, requestOptions)
                        .then((response) => response.json())
                        .then(([result]) => {
                            this.orders = result.order_history || false;
                        })
                        .catch((error) => console.error(error));
                } else {
                    this.orders = [];
                }
            },
            toggleDetails(event, orderNumber) {
                const details = document.getElementById(`order-details-${orderNumber}`);
                console.log(event.target)
                event.target.setAttribute('aria-expanded', event.target.getAttribute('aria-expanded') == 'false' ? true : false);
                console.log(event.target.getAttribute('aria-expanded'))
                details.classList.toggle('hidden');
            }
        }
    }
</script>
