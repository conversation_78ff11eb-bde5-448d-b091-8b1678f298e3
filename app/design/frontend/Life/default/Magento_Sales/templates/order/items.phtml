<?php

use Magento\ConfigurableProduct\Model\Product\Type\Configurable;
use Magento\Framework\Escaper;
use Magento\GiftMessage\Helper\Message as GiftMessageHelper;
use Magento\Sales\Block\Order\Items;

// phpcs:disable Magento2.Templates.ThisInTemplate.FoundThis
// phpcs:disable Magento2.Templates.ThisInTemplate.FoundHelper

/** @var Items $block */
/** @var Escaper $escaper */

$helper = $this->helper(GiftMessageHelper::class);
$cols = $block->getData('is_context_shipment') ? 3 : 4;
?>
<div class="mb-2 text-xl leading-9"><?= $escaper->escapeHtml(__('Ordered items')) ?></div>
<div class="order-items">
    <?php if ($block->isPagerDisplayed()): ?>
        <?= $block->getPagerHtml() ?>
    <?php endif ?>
    <?php $items = $block->getItems(); ?>
    <?php $giftMessage = '' ?>
    <?php foreach ($items as $item): ?>
        <?php
        $parentItem = $item->getParentItem();
        if ($parentItem && $parentItem->getData('product_type') === Configurable::TYPE_CODE) {
            continue;
        }
        ?>
        <div class="
            border-b border-secondary-900/30
            <?= ($parentItem) ?
                'child-item ml-2 px-4 mb-2 border-l-4 border-container text-sm' :
                'parent-item mb-6 pb-6'
            ?>
        ">
            <?= $block->getItemHtml($item) ?>
        </div>
        <?php if ($helper->isMessagesAllowed('order_item', $item) && $item->getGiftMessageId()): ?>
            <?php $giftMessage = $helper->getGiftMessageForEntity($item); ?>
        <div class="mb-2 col options">
            <a href="#"
               id="order-item-gift-message-link-<?= (int)$item->getId() ?>"
               class="action show"
               aria-controls="order-item-gift-message-<?= (int)$item->getId() ?>"
               data-item-id="<?= (int)$item->getId() ?>">
                <?= $escaper->escapeHtml(__('Gift Message')) ?>
            </a>
            <?php $giftMessage =
                $helper->getGiftMessageForEntity($item); ?>
            <div class="order-gift-message" id="order-item-gift-message-<?= (int)$item->getId() ?>" role="region"
                 aria-expanded="false" tabindex="-1">
                <a href="#"
                   title="<?= $escaper->escapeHtml(__('Close')) ?>"
                   aria-controls="order-item-gift-message-<?= (int)$item->getId() ?>"
                   data-item-id="<?= (int)$item->getId() ?>"
                   class="action close">
                    <?= $escaper->escapeHtml(__('Close')) ?>
                </a>
                <dl class="item-options">
                    <dt class="item-sender">
                        <strong class="label">
                            <?= $escaper->escapeHtml(__('From')) ?>
                        </strong>
                        <?= $escaper->escapeHtml($giftMessage->getSender()) ?>
                    </dt>
                    <dt class="item-recipient">
                        <strong class="label">
                            <?= $escaper->escapeHtml(__('To')) ?>
                        </strong>
                        <?= $escaper->escapeHtml($giftMessage->getRecipient()) ?>
                    </dt>
                    <dd class="item-message">
                        <?= /* @noEscape */ $helper->getEscapedGiftMessage($item) ?>
                    </dd>
                </dl>
            </div>
        </div>
    <?php endif ?>
    <?php endforeach; ?>
    <?php if ($block->isPagerDisplayed()): ?>
        <?= $block->getPagerHtml() ?>
    <?php endif ?>
</div>
<div class="flex justify-end pb-6 mb-6 border-b border-secondary-900/50">
    <div class="lg:w-1/3">
        <div class="grid grid-cols-2 gap-2 text-right">
            <?= $block->getChildHtml('order_totals') ?>
        </div>
    </div>
</div>
