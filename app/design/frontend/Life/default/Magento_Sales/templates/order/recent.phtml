<?php

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsOutline;
use Hyva\Theme\ViewModel\SvgIcons;
use Magento\Framework\Data\Helper\PostHelper;
use Magento\Framework\Escaper;
use Magento\Sales\Block\Order\Recent;
use Magento\Sales\Helper\Reorder;

// phpcs:disable Magento2.Templates.ThisInTemplate.FoundThis
// phpcs:disable Magento2.Templates.ThisInTemplate.FoundHelper

/** @var Escaper $escaper */
/** @var Recent $block */
/** @var ViewModelRegistry $viewModels */

/** @var HeroiconsOutline $heroicons */
$heroicons = $viewModels->require(HeroiconsOutline::class);

/** @var SvgIcons $svgIcons */
$svgIcons = $viewModels->require(SvgIcons::class);

$orders = $block->getOrders();
$count  = count($orders);
?>
<div class="block mb-6 block-dashboard-orders card"
     x-data="{ tabIndex: -1 }"
>
    <div class="flex items-center justify-between mb-6 block-title order">
        <h2 id="recentOrders"
            class="block text-xl leading-9"
            @focus="tabIndex = 0;"
            @blur="tabIndex = -1"
            :tabindex="tabIndex"
        >
          <?= $escaper->escapeHtml(__('Recent Orders')) ?>
        </h2>
    </div>
    <div
        role="table"
        class="block-content"
        aria-labelledby="recentOrders"
    >
        <?= $block->getChildHtml() ?>
        <?php if ($count > 0): ?>
            <div
                role="row"
                id="my-orders-table"
                class="grid grid-cols-4 border-b lg:grid-cols-5 border-secondary-900/50"
            >
                <div
                    role="columnheader"
                    class="col-span-2 px-1 pb-4 text-sm font-bold lg:col-span-1"
                >
                    <?= $escaper->escapeHtml(__('Order #')) ?>
                </div>
                <div
                    role="columnheader"
                    class="hidden px-1 pb-4 text-sm font-bold lg:block"
                >
                    <?= $escaper->escapeHtml(__('Date')) ?>
                </div>
                <div
                    role="columnheader"
                    class="px-1 pb-4 text-sm font-bold"
                >
                    <?= $escaper->escapeHtml(__('Order Total')) ?>
                </div>
                <div
                    role="columnheader"
                    class="hidden px-1 pb-4 text-sm font-bold lg:block"
                >
                    <?= $escaper->escapeHtml(__('Status')) ?>
                </div>
                <div
                    role="columnheader"
                    class="hidden px-1 pb-4 text-sm font-bold md:block"
                >
                    <?= $escaper->escapeHtml(__('View')) ?>
                </div>
            </div>
            <?php $i = 0; ?>
            <?php foreach ($orders as $order): ?>
                <div
                    role="row"
                    class="grid grid-cols-4 lg:grid-cols-5"
                >
                    <?php $i++; ?>
                    <div
                        id="order<?= $escaper->escapeHtmlAttr($order->getRealOrderId()) ?>"
                        role="rowheader"
                        class="flex flex-col justify-center h-12 col-span-2 px-1 text-xs border-b lg:col-span-1 md:justify-start md:items-center md:flex-row border-secondary-900/50 gap-x-1"
                    >
                        <?= $escaper->escapeHtml($order->getRealOrderId()) ?>
                        <p class="lg:hidden">
                            <?= $escaper->escapeHtml($block->formatDate($order->getCreatedAt())) ?>
                        </p>
                    </div>
                    <div
                        role="cell"
                        class="items-center hidden h-12 px-1 text-xs border-b lg:flex border-secondary-900/50"
                    >
                        <?php
                            $date = $order->getCreatedAt();
                            $format = $block->getDateFormat() ?: \IntlDateFormatter::SHORT;
                            $formatLong = $block->getDateFormat() ?: \IntlDateFormatter::LONG;
                        ?>
                        <span aria-hidden="true">
                            <?= $escaper->escapeHtmlAttr($block->formatDate($date, $format)); ?>
                        </span>
                        <span class="sr-only">
                            <?= $escaper->escapeHtml($block->formatDate($date, $formatLong)); ?>
                        </span>
                    </div>
                    <div
                        role="cell"
                        class="flex items-center h-12 px-1 text-xs border-b border-secondary-900/50"
                    >
                        <?= /* @noEscape */ $order->formatPrice($order->getGrandTotal()) ?>
                    </div>
                    <div
                        role="cell"
                        class="items-center hidden h-12 px-1 text-xs border-b lg:flex border-secondary-900/50"
                    >
                        <?= $escaper->escapeHtml(
                            $order->getStatusLabel()
                        ) ?>
                    </div>
                    <div
                        role="cell"
                        class="flex items-center justify-end h-12 px-1 text-xs border-b border-secondary-900/50 md:justify-start"
                    >
                        <a
                            href="<?= $escaper->escapeUrl($block->getViewUrl($order)) ?>"
                            class="inline-block text-link"
                            title="<?= $escaper->escapeHtmlAttr(__('View Order')) ?>"
                            aria-label="<?= $escaper->escapeHtmlAttr(__('View order') . ' ' . ltrim($order->getRealOrderId(), '0')) ?>"
                        >
                            <?= $escaper->escapeHtml(__('View')) ?>
                        </a>
                    </div>
                </div>
            <?php endforeach; ?>
        <?php else: ?>
            <div class="message info empty"><span><?= $escaper->escapeHtml(__('You have placed no orders.')) ?></span>
            </div>
        <?php endif; ?>
    </div>

    <?php if ($count > 0): ?>
        <a
            class="inline-flex items-center mt-6 text-sm gap-x-2"
            href="<?= $escaper->escapeUrl($block->getUrl('sales/order/history')) ?>"
            aria-label="<?= $escaper->escapeHtmlAttr(__('View all orders')) ?>"
        >
            <span class="text-link"><?= $escaper->escapeHtml(__('View all orders')) ?></span>
            <?= $svgIcons->arrowRightHtml('w-5 h-5', 20, 20, ['aria-hidden' => 'true']); ?>
        </a>
    <?php endif; ?>
</div>
