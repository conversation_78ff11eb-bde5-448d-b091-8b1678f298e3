<?php

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\SvgIcons;
use Magento\Framework\Escaper;
use Magento\Sales\Block\Order\Info\Buttons;

// phpcs:disable Magento2.Templates.ThisInTemplate.FoundThis
// phpcs:disable Magento2.Templates.ThisInTemplate.FoundHelper

/** @var Escaper $escaper */
/** @var Buttons $block */
/** @var ViewModelRegistry $viewModels */

/** @var SvgIcons $svgIcons */
$svgIcons = $viewModels->require(SvgIcons::class);
?>
<div class="flex items-center justify-center my-4 md:justify-end md:my-0 gap-x-6">
    <?php $order = $block->getOrder() ?>
    <?php if ($this->helper(\Magento\Sales\Helper\Reorder::class)->canReorder($order->getEntityId())): ?>
        <?php $formData = json_decode(
            $this->helper(\Magento\Framework\Data\Helper\PostHelper::class)->getPostData(
                $block->getReorderUrl($order)
            ),
            true
        ) ?>
        <form action="<?= $escaper->escapeUrl($formData['action']) ?>" method="post" class="inline-flex items-center">
            <?= $block->getBlockHtml('formkey'); ?>
            <input type="hidden" name="data" value='<?= /** @noEscape */ json_encode($formData['data']) ?>'/>
            <button type="submit" class="btn btn-secondary text-secondary-darker">
                <?= $svgIcons->reorderHtml('inline-block', 24, 24, ['aria-hidden' => 'true']); ?>
                <span class="sr-only"><?= $escaper->escapeHtml(__('Reorder')) ?></span>
            </button>
        </form>
    <?php endif ?>
    <a href="<?= $escaper->escapeUrl($block->getPrintUrl($order)) ?>"
       class="btn btn-secondary text-secondary-darker"
       target="_blank"
       rel="noopener">
        <?= $svgIcons->printHtml('inline-block', 24, 24, ['aria-hidden' => 'true']); ?>
        <span class="sr-only"><?= $escaper->escapeHtml(__('Print Order')) ?></span>
    </a>
    <?= $block->getChildHtml() ?>
</div>
