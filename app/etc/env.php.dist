<?php
return [
    'backend' => [
        'frontName' => 'beheer'
    ],
    'crypt' => [
        'key' => '%CRYPT_KEY%'
    ],
    'db' => [
        'table_prefix' => '',
        'connection' => [
            'default' => [
                'host' => 'db',
                'dbname' => 'db',
                'username' => 'db',
                'password' => 'db',
                'model' => 'mysql4',
                'engine' => 'innodb',
                'initStatements' => 'SET NAMES utf8;',
                'active' => '1'
            ]
        ]
    ],
    'queue' => [
        'consumers_wait_for_messages' => 0,
        'amqp' => [
            'host' => 'rabbitmq',
            'port' => '5672',
            'user' => 'guest',
            'password' => 'guest',
            'virtualhost' => '/'
        ]
    ],
    'resource' => [
        'default_setup' => [
            'connection' => 'default'
        ]
    ],
    'x-frame-options' => 'SAMEORIGIN',
    'MAGE_MODE' => 'developer',
    'session' => [
        'save' => 'redis',
        'redis' => [
            'host' => 'ddev-life-redis.ddev_default',
            'port' => '6379',
            'password' => '',
            'timeout' => '2.5',
            'persistent_identifier' => '',
            'database' => '2',
            'compression_threshold' => '2048',
            'compression_library' => 'gzip',
            'log_level' => '4',
            'max_concurrency' => '6',
            'break_after_frontend' => '5',
            'break_after_adminhtml' => '30',
            'first_lifetime' => '600',
            'bot_first_lifetime' => '60',
            'bot_lifetime' => '7200',
            'disable_locking' => '0',
            'min_lifetime' => '60',
            'max_lifetime' => '2592000'
        ]
    ],
    'cache' => [
        'frontend' => [
            'default' => [
                'backend' => 'Magento\\Framework\\Cache\\Backend\\Redis',
                'backend_options' => [
                    'server' => 'ddev-life-redis.ddev_default',
                    'database' => '0',
                    'port' => '6379'
                ],
            ],
            'page_cache' => [
                'backend' => 'Magento\\Framework\\Cache\\Backend\\Redis',
                'backend_options' => [
                    'server' => 'ddev-life-redis.ddev_default',
                    'port' => '6379',
                    'database' => '1',
                    'compress_data' => '0'
                ]
            ]
        ]
    ],
    'cache_types' => [
        'config' => 1,
        'layout' => 1,
        'block_html' => 1,
        'collections' => 1,
        'reflection' => 1,
        'db_ddl' => 1,
        'compiled_config' => 1,
        'eav' => 1,
        'customer_notification' => 1,
        'config_integration' => 1,
        'config_integration_api' => 1,
        'full_page' => 1,
        'config_webservice' => 1,
        'translate' => 1,
        'vertex' => 1
    ],
    'lock' => [
        'provider' => 'db'
    ],
    'install' => [
        'date' => 'Tue, 09 Apr 2019 07:17:00 +0000'
    ],
    'system' => [
        'default' => [
            'admin' => [
                'security' => [
                    'password_lifetime' => '',
                    'session_lifetime' => '31536000'
                ]
            ],
            'web' => [
                'unsecure' => [
                    'base_url' => 'https://www.lifeoutdoorliving.com.local/',
                    'base_link_url' => '{{unsecure_base_url}}'
                ],
                'secure' => [
                    'base_url' => '{{unsecure_base_url}}',
                    'base_link_url' => '{{secure_base_url}}'
                ],
            ],
            'catalog' => [
                'search' => [
                    'engine' => 'elasticsuite',
                    'elasticsearch7_server_hostname' => 'elasticsearch',
                    'elasticsearch7_server_port' => '9200',
                    'elasticsearch7_index_prefix' => 'magento2',
                    'elasticsearch7_enable_auth' => '0',
                    'elasticsearch7_server_timeout' => '15'
                ]
            ],
            'smile_elasticsuite_core_base_settings' => [
                'es_client' => [
                    'servers' => 'elasticsearch:9200',
                    'enable_https_mode' => 0,
                    'http_auth_user' => '',
                    'http_auth_pwd' => '',
                    'enable_http_auth' => false
                ]
            ],
            'postcodenl_api' => [
                'general' => [
                    'enabled' => '1',
                    'api_key' => 'NHL8dfhqDVqoOqshYvPS7Z3XWqxhmTgVTF9fMEWnDoy',
                    'api_secret' => 'TUD6WwcB7XtXaOYqwhhNJKoCaDufDSROmpzOlGaGfj5qTqnAx1'
                ]
            ]
        ],
        'websites' => [
            'life_nl' => [
                'web' => [
                    'unsecure' => [
                        'base_link_url' => '{{unsecure_base_url}}nl/'
                    ],
                    'secure' => [
                        'base_link_url' => '{{unsecure_base_url}}nl/'
                    ]
                ]
            ],
            'life_be' => [
                'web' => [
                    'unsecure' => [
                        'base_link_url' => '{{unsecure_base_url}}be/'
                    ],
                    'secure' => [
                        'base_link_url' => '{{unsecure_base_url}}be/'
                    ]
                ]
            ],
            'life_de' => [
                'web' => [
                    'unsecure' => [
                        'base_link_url' => '{{unsecure_base_url}}de/'
                    ],
                    'secure' => [
                        'base_link_url' => '{{unsecure_base_url}}de/'
                    ]
                ]
            ],
            'life_int' => [
                'web' => [
                    'unsecure' => [
                        'base_link_url' => '{{unsecure_base_url}}en/'
                    ],
                    'secure' => [
                        'base_link_url' => '{{unsecure_base_url}}en/'
                    ]
                ]
            ]
        ],
        'stores' => [
            'showroom_en' => [
                'web' => [
                    'unsecure' => [
                        'base_url' => 'https://showroom.lifeoutdoorliving.com.local/'
                    ]
                ]
            ],
            'showroom' => [
                'web' => [
                    'unsecure' => [
                        'base_url' => 'https://showroom.lifeoutdoorliving.com.local/'
                    ]
                ]
            ]
        ]
    ],
    'downloadable_domains' => [
        'www.lifeoutdoorliving.com.local',
        'showroom.lifeoutdoorliving.com.local'
    ]
];
