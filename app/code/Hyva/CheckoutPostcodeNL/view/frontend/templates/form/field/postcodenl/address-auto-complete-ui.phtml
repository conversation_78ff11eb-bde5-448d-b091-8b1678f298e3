<?php

declare(strict_types=1);

use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;

/** @var Template $block */
/** @var Escaper $escaper */

?>
<script>
    function addressAutoComplete(wire, area) {
        return {
            isLoading: false,
            area: area,
            address: {
                postcode: '',
                house_number: '',
                country: ''
            },
            restUrl: '<?= $escaper->escapeUrl(trim($block->getBaseUrl(), '/') .
                '/rest/V1/hyva-checkout/address-auto-complete/search/by-postcode-housenumber/'); ?>',
            postCodeField: null,
            houseNumberField: null,
            cityField: null,
            streetField: null,
            regionField: null,

            initialize: function() {
                this.postCodeField = document.getElementById(this.area + '-postcode');
                this.houseNumberField = document.getElementById(this.area + '-street-1');
                this.cityField = document.getElementById(this.area + '-city');
                this.streetField = document.getElementById(this.area + '-street-0');
                this.regionField = document.getElementById(this.area + '-region');
                this.countryField = document.getElementById(this.area + '-country_id');

                this.address.postcode = this.postCodeField.value;
                this.postCodeField.addEventListener("change", (event) => {
                    this.onInputChange('postcode', event.target.value, this.address.postcode)
                });

                this.address.house_number = this.houseNumberField.value;
                this.houseNumberField.addEventListener("change", (event) => {
                    this.onInputChange('house_number', event.target.value, this.address.house_number)
                });

                this.address.country = this.countryField.value;
                this.countryField.addEventListener("change", (event) => {
                    this.onInputChange('country', event.target.value, this.address.country)
                });
            },

            onInputChange: function(ref, newValue, previousValue) {
                if (newValue == previousValue) {
                    return;
                }

                this.address[ref] = newValue;
                if (this.countryField.value !== 'NL') {
                    return;
                }

                if (
                    !this.address.postcode
                    || !this.address.house_number
                ) {
                    return;
                }

                this.isLoading = true;
                let self = this;
                fetch(this.restUrl + this.address.postcode + '/' + this.address.house_number)
                    .then(response => {
                        return response.json()
                    })
                    .then(data => {
                        this.updateAddress(data);
                    })
                    .catch(error => {
                        console.log(error);
                    })
                    .finally(() => {
                        // Wait 1 second for Magewire fields sync
                        setTimeout(function(){ self.isLoading = false }, 1000);
                    })
            },

            updateAddress: function (data) {
                if (!data || !data[0]) {
                    return;
                }

                let addressData = data[0];

                if (
                    this.cityField
                    && addressData['city']
                ) {
                    wire.sync(`address.city`, addressData['city']);
                }

                if (
                    this.streetField
                    && addressData['street']
                ) {
                    wire.sync(`address.street.0`, addressData['street']);
                }

                if (
                    this.regionField
                    && addressData['province']
                ) {
                    wire.sync(`address.region`, addressData['province']);
                }
            }
        }
    }
</script>
