<?php

declare(strict_types=1);

namespace Hyva\CheckoutPostcodeNL\Api;

interface AddressAutoCompleteInterface
{
    /**
     * @param string $query
     * @return string[][]
     */
    public function searchByQuery(string $query): array;

    /**
     * @param string $postcode
     * @param string $number
     * @return string[][]
     */
    public function searchByPostcodeHouseNumber(string $postcode, string $number): array;
}
