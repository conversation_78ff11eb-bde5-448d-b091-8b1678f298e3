<?php

declare(strict_types=1);

namespace Hyva\AmastyMostviewed\Plugin;

use Amasty\Mostviewed\Api\Data\GroupInterface;
use Life\SerieProductLinks\Ui\DataProvider\Product\Form\Modifier\Serie;
use Magento\Catalog\Ui\DataProvider\Product\Form\Modifier\Related;
use Amasty\Mostviewed\Model\{
    OptionSource\BlockPosition,
    OptionSource\ReplaceType,
    ProductProvider,
    Repository\GroupRepository
};
use Hyva\Theme\ViewModel\ProductList;
use Magento\Catalog\Api\ProductRepositoryInterface;
use Magento\Catalog\Model\Product;
use Magento\Checkout\Model\Session;
use Magento\Framework\View\Element\Template;
use Magento\Quote\Model\Quote;
use Psr\Log\LoggerInterface;

class AutoRelatedProductListProvider
{
    public function __construct(
        private readonly ProductProvider $productProvider,
        private readonly ProductRepositoryInterface $productRepository,
        private readonly Template $templateBlock,
        private readonly GroupRepository $groupRepository,
        private readonly Session $checkoutSession,
        private readonly LoggerInterface $logger
    ) {}

    /**
     * @param ProductList $subject
     * @param Product\Link[] $relations
     * @param string $linkType
     * @param ...$items
     * @return Product\Link[]
     */
    public function afterGetLinkedItems(
        ProductList $subject,
        array $relations,
        string $linkType,
        ...$items
    ): array {
        try {
            if ($amastyLinkType = $this->getAmastyLinkType($linkType)) {
                $excludedProducts = [];
                if ($amastyLinkType === BlockPosition::CART_INTO_CROSSSEL) {
                    $excludedProducts = $this->getCartProductIds();
                }

                foreach ($items as $product) {
                    if (is_int($product)) {
                        /** @var Product $product */
                        $product = $this->productRepository->getById($product);
                    }

                    /** @var \Amasty\Mostviewed\Api\Data\GroupInterface $group */
                    $group = $this->groupRepository->getGroupByIdAndPosition((int)$product->getId(), $amastyLinkType);
                    if (!empty($group)) { /** @phpstan-ignore-line */
                        $relations = $this->getAutoRelatedProducts(
                            $product,
                            $amastyLinkType,
                            $relations,
                            $excludedProducts,
                            $group
                        );
                    }
                }
            }
        } catch (\Exception $e) {
            $this->logger->critical($e->getMessage());
        }

        return $relations;
    }

    protected function getAmastyLinkType(string $linkType): ?string
    {
        return match($linkType) {
            Related::DATA_SCOPE_RELATED => BlockPosition::PRODUCT_INTO_RELATED,
            Related::DATA_SCOPE_UPSELL =>  BlockPosition::PRODUCT_INTO_UPSELL,
            Related::DATA_SCOPE_CROSSSELL => BlockPosition::CART_INTO_CROSSSEL,
            Serie::DATA_SCOPE_SERIE => AutoRelatedProductDataProvider::PRODUCT_INTO_SERIE,
            default => null
        };
    }

    /**
     * @param string[][] $relations,
     * @param string[] $excludedProducts
     * @return string[][]
     */
    protected function getAutoRelatedProducts(
        Product $product,
        string $amastyLinkType,
        array $relations,
        array $excludedProducts,
        GroupInterface $group
    ): array {
        /** @var \Magento\Catalog\Model\ResourceModel\Product\Collection<Product>|array<Product> $autoRelatedProducts */
        $autoRelatedProducts = $this->productProvider->modifyCollection(
            $amastyLinkType,
            $product,
            [],
            $excludedProducts,
            $this->templateBlock
        );

        if (is_object($autoRelatedProducts)) {
            $autoRelatedIds = $autoRelatedProducts->getAllIds();
        } else {
            $autoRelatedIds = array_map(function ($product) {
                return $product->getId();
            }, $autoRelatedProducts);
        }

        // If rule is marked as "replace" (should not add), reset current relations
        if ((string)$group->getReplaceType() !== ReplaceType::ADD) {
            $relations = [];
        }
        foreach ($autoRelatedIds as $autoRelatedId) {
            $relations[] = $this->productRepository->getById($autoRelatedId);
        }

        return $relations;
    }

    /**
     * @return string[]
     */
    protected function getCartProductIds(): array
    {
        $ids = [];
        $itemsCollection = $this->getQuote()->getItemsCollection();
        foreach ($itemsCollection as $item) {
            $product = $item->getProduct();
            if ($product) {
                $ids[] = $product->getId();
            }
        }

        return $ids;
    }

    protected function getQuote(): Quote
    {
        return $this->checkoutSession->getQuote();
    }
}
