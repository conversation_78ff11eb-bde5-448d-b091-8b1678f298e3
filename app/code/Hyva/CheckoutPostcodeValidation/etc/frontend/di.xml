<?xml version="1.0"?>
<config
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd"
>
    <type name="Hyva\Checkout\Model\Form\EntityForm\EavAttributeShippingAddressForm">
        <arguments>
            <!-- Default base modifiers have positions between 900 and 1000 -->
            <argument name="entityFormModifiers" xsi:type="array">
                <item
                    name="postcode_validation"
                    sortOrder="1200"
                    xsi:type="object"
                >Hyva\CheckoutPostcodeValidation\Model\Form\EntityFormModifier\PostcodeModifier</item>
            </argument>
        </arguments>
    </type>
    <type name="Hyva\Checkout\Model\Form\EntityForm\EavAttributeBillingAddressForm">
        <arguments>
            <!-- Default base modifiers have positions between 900 and 1000 -->
            <argument name="entityFormModifiers" xsi:type="array">
                <item
                    name="postcode_validation"
                    sortOrder="1200"
                    xsi:type="object"
                >Hyva\CheckoutPostcodeValidation\Model\Form\EntityFormModifier\PostcodeModifier</item>
            </argument>
        </arguments>
    </type>
</config>
