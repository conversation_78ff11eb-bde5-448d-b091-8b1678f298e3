<?php

declare(strict_types=1);

/** @var \Magento\Framework\View\Element\Template $block */
/** @var \Hyva\Theme\Model\ViewModelRegistry $viewModels */
/** @var \Magento\Framework\Escaper $escaper */
/** @var \Magewirephp\Magewire\Component\Form $magewire */

/** @Tailwind md:w-1/4 md:w-2/4 md:w-3/4 md:w-4/4 mb-2 */

/** @var \Hyva\Checkout\Model\Form\EntityField\EavEntityAddress\TelephoneAttributeField $element */
$element = $block->getData('element');
$element->setValidationRule('valid-telephone-number');

?>

<div class="w-full text-xs text-primary <?= /* @noEscape */ $element->isRequired() ? 'required' : 'not-required' ?>">
    <?= /* @noEscape */ $element->getRenderer()->renderLabel($element) ?>
    <?= /* @noEscape */ $element->getRenderer()->renderBefore($element) ?>

    <?php if ($element->hasRelatives()) : ?>
        <div class="space-y-2">
        <?php endif ?>

        <div class="flex items-center gap-4">
            <input
                class="<?= $escaper->escapeHtmlAttr($element->renderClass(['block w-full form-input grow renderer-text'])) ?>"
                <?php if ($element->hasAttributes()) : ?>
                <?= /* @noEscape */ $element->renderAttributes($escaper) ?>
                <?php endif ?>
            />

            <?php if ($element->hasTooltip()) : ?>
                <?= /* @noEscape */ $element->getRenderer()->renderTooltip($element) ?>
            <?php endif ?>
        </div>

        <?php if ($element->hasRelatives()) : ?>
            <?php foreach ($element->getRelatives() as $relative) : ?>
                <?= /* @noEscape */ $relative->render() ?>
            <?php endforeach ?>
        <?php endif ?>

        <?php if ($element->hasRelatives()) : ?>
        </div>
    <?php endif ?>

    <?= /* @noEscape */ $element->getRenderer()->renderAfter($element) ?>
</div>

<?= $block->assign('element', $element)->fetchView((string)$block->getTemplateFile('Hyva_CheckoutTelephoneValidation::form/field/telephone/include.phtml')) ?>
