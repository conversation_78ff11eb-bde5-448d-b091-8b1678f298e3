<?php

declare(strict_types=1);

namespace Redkiwi\ForceStoreRedirect\Observer;

use Magento\Customer\Model\Session as CustomerSession;
use Magento\Framework\App\Action\Action;
use Magento\Framework\App\ActionFlag;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Framework\App\Http\Context;
use Magento\Framework\App\RequestInterface;
use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;
use Magento\Framework\UrlInterface;
use Magento\Store\Model\ScopeInterface;
use Magento\Store\Model\StoreManagerInterface;

class RedirectToDefaultStore implements ObserverInterface
{
    const EXEMPT_FROM_REDIRECT = ['stores'];
    const CONFIG_PATH_ENABLED = 'force_store_redirect/general/enabled';

    public function __construct(
        private readonly ActionFlag $actionFlag,
        private readonly StoreManagerInterface $storeManager,
        private readonly UrlInterface $url,
        private readonly RequestInterface $request,
        private readonly CustomerSession $customerSession,
        private readonly Context $context,
        private readonly ScopeConfigInterface $scopeConfig
    ) {}

    /**
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     * @return void
     */
    public function execute(Observer $observer)
    {
        // Do not redirect POST requests or if the SERVER store code mapping is missing
        if (
            !$this->isEnabled()
            || $this->request->isPost() /* @phpstan-ignore-line */
            || !$this->request->getServer('STORE_CODES_MAPPING')
        ) {
            return;
        }

        try {
            $storesMapping = json_decode($this->request->getServer('STORE_CODES_MAPPING'), true);
        } catch (\Exception $e) {
            return;
        }

        $path = $this->request->getServer('ORIGINAL_REQUEST_URI') ?? $this->request->getServer('REQUEST_URI');
        if (!$path) {
            return;
        }
        $pathParts = preg_split('/[?\/]/', $path, -1);
        $urlCode = $pathParts[1] ?? '';

        // Without context `X-Magento-Vary` header will be deleted
        $this->initContext();

        // Find the default mapped store code
        $defaultStoreCode = $this->storeManager->getDefaultStoreView()->getCode();
        foreach ($storesMapping as $mappedCode => $code) {
            if ($defaultStoreCode === $code) {
                $defaultStoreCode = $mappedCode;
                break;
            }
        }

        // Find the current mapped store code
        $storeCode = $this->storeManager->getStore()->getCode();
        foreach ($storesMapping as $mappedCode => $code) {
            if ($storeCode === $code) {
                $storeCode = $mappedCode;
                break;
            }
        }

        if (!$defaultStoreCode || !$storeCode || $defaultStoreCode !== $storeCode) {
            return;
        }

        $baseUrlWithoutStoreCode = str_replace(
            '/' . $defaultStoreCode,
            '',
            $this->storeManager->getStore()->getBaseUrl()
        );

        if (trim($baseUrlWithoutStoreCode, '/') === trim($this->url->getCurrentUrl(), '/')) {
            // If URL is without store code, do not redirect
            return;
        }

        /**
         * If not on homepage, and first path is not in array of storecodes, redirect to URL with storecode
         * f.e. domain.com/product will redirect to domain.com/storecode/product
         */
        if (
            !in_array(
                $urlCode,
                array_merge(array_keys($storesMapping), self::EXEMPT_FROM_REDIRECT)
            )
        ) {
            $path = ltrim($path, '/');
            $this->actionFlag->set('', Action::FLAG_NO_DISPATCH, true);
            $observer
                ->getData('controller_action')
                ->getResponse()
                ->setRedirect($this->storeManager->getStore()->getBaseUrl() . $path, 301);
        }
    }

    /**
     * Set customer group and customer session id to HTTP context.
     *
     * Usually this happens inside a plugin on `Action::execute()` but is
     * omitted by `Action::FLAG_NO_DISPATCH`. Without these values the
     * `X-Magento-Vary` header will be deleted.
     *
     * @see Magento\Customer\Model\App\Action\ContextPlugin::beforeExecute()
     * @see Magento\Framework\App\Response\Http::sendVary()
     */
    private function initContext(): self
    {
        $this->context->setValue(
            \Magento\Customer\Model\Context::CONTEXT_GROUP,
            $this->customerSession->getCustomerGroupId(),
            \Magento\Customer\Model\Group::NOT_LOGGED_IN_ID
        );

        $this->context->setValue(
            \Magento\Customer\Model\Context::CONTEXT_AUTH,
            $this->customerSession->isLoggedIn(),
            false
        );

        return $this;
    }

    public function isEnabled(): bool
    {
        return (bool)$this->scopeConfig->getValue(
            self::CONFIG_PATH_ENABLED,
            ScopeInterface::SCOPE_STORE
        );
    }
}
