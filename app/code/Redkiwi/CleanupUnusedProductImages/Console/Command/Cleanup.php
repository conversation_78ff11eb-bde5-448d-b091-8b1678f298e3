<?php

declare(strict_types=1);

namespace Redkiwi\CleanupUnusedProductImages\Console\Command;

use FilesystemIterator;
use Magento\Framework\App\{Area, Filesystem\DirectoryList, ResourceConnection, State};
use Magento\Framework\Filesystem;
use RecursiveDirectoryIterator;
use RecursiveIteratorIterator;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Helper\ProgressBar;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class Cleanup extends Command
{
    const PRODUCT_MEDIA_PATH = 'catalog/product';

    public function __construct(
        private readonly ResourceConnection $resourceConnection,
        private readonly State $appState,
        private readonly Filesystem $filesystem
    ) {
        parent::__construct();
    }

    protected function configure()
    {
        $this->setName('redkiwi:product-images:cleanup')
            ->setDescription('Removes unused product images from disk')
            ->setDefinition([]);
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $output->setDecorated(true);
        $this->appState->setAreaCode(Area::AREA_ADMINHTML);

        $allImageName = $this->getAllProductImageNames();

        $mediaRootDir = $this->filesystem->getDirectoryRead(DirectoryList::MEDIA)->getAbsolutePath();
        $customOptionsDir = $mediaRootDir . self::PRODUCT_MEDIA_PATH;
        $directories = new RecursiveDirectoryIterator($customOptionsDir, FilesystemIterator::SKIP_DOTS);
        $files = new RecursiveIteratorIterator($directories, RecursiveIteratorIterator::CHILD_FIRST);

        $filesArray = array_reverse(iterator_to_array($files));
        $progress = new ProgressBar($output, count($filesArray));
        $progress->setFormat('<comment>%message%</comment> %current%/%max% [%bar%] %percent:3s%% %elapsed%');

        foreach ($files as $file) {
            if ($file->isDir()) {
                $progress->setMessage("This is a directory. Skipping!");
                $progress->advance();
                continue;
            }

            $basename = basename(strtolower($file->getPathname()));
            $fileInfo = pathinfo($basename);

            if (empty($fileInfo['filename'])) {
                $progress->setMessage("This is not a proper image name. Skipping!");
                $progress->advance();
                continue;
            }

            $progress->setMessage("File {$file->getPathname()} ");

            if (in_array($fileInfo['filename'], $allImageName)) {
                $progress->advance();
                continue;
            }

            unlink($file->getPathname());

            $progress->advance();
        }

        $output->writeln('');

        return 0;
    }

    /**
     * @return string[]
     */
    protected function getAllProductImageNames(): array
    {
        $connection = $this->resourceConnection->getConnection();

        return array_map(function ($fileName) {
                $fileName = strtolower(basename($fileName));
                $fileInfo = pathinfo($fileName);

                return $fileInfo['filename'] ?? '';
            },
            $connection->fetchCol(
                $connection->select()->from(
                    $connection->getTableName('catalog_product_entity_media_gallery'),
                    ['value']
                )
            )
        );
    }
}
