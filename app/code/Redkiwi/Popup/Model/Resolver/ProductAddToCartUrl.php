<?php

declare(strict_types=1);

namespace Redkiwi\Popup\Model\Resolver;

use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;
use Magento\Framework\GraphQl\Query\ResolverInterface;
use Magento\Catalog\Model\Product;
use Magento\Checkout\Helper\Cart;

class ProductAddToCartUrl implements ResolverInterface
{
    public function __construct(
        private readonly Cart $cartHelper
    ) {}

    /**
     * {@inheritDoc}
     * @param array{'model': Product}|null $value
     * @param string[]|null $args
     */
    public function resolve(
        Field $field,
        $context,
        ResolveInfo $info,
        array $value = null,
        array $args = null
    ) {
        if (!isset($value['model'])) {
            throw new LocalizedException(__('"model" value should be specified'));
        }

        /** @var Product $product */
        $product = $value['model'];

        return $this->cartHelper->getAddUrl($product);
    }
}
