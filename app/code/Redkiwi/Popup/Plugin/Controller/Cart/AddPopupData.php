<?php

declare(strict_types=1);

namespace Redkiwi\Popup\Plugin\Controller\Cart;

use Life\ProductsConnector\Setup\Patch\Data\UpdateProductGroupByAttribute;
use Magento\Catalog\Api\ProductAttributeRepositoryInterface;
use Magento\Catalog\Api\ProductRepositoryInterface;
use Magento\Catalog\Api\Data\ProductInterface;
use Magento\Catalog\Helper\Image;
use Magento\Catalog\Model\Product;
use Magento\Checkout\Controller\Cart\Add;
use Magento\ConfigurableProduct\Model\Product\Type\Configurable;
use Magento\Eav\Api\Data\AttributeInterface;
use Magento\Framework\App\{RequestInterface, ResponseInterface};
use Magento\Framework\Controller\ResultInterface;
use Magento\Framework\Message\ManagerInterface;
use Magento\Framework\Pricing\Helper\Data;
use Magento\Store\Model\StoreManagerInterface;
use Psr\Log\LoggerInterface;

class AddPopupData
{
    public function __construct(
        private readonly RequestInterface $request,
        private readonly ResponseInterface $response,
        private readonly ProductRepositoryInterface $productRepository,
        private readonly StoreManagerInterface $storeManager,
        private readonly Image $imageHelper,
        private readonly Data $priceHelper,
        private readonly ProductAttributeRepositoryInterface $attributeRepository,
        private readonly LoggerInterface $logger,
        private readonly ManagerInterface $messageManager,
        private readonly Configurable $configurableProduct
    ) {}

    /**
     * @see Add::execute()
     *
     * @param ResponseInterface|ResultInterface $result
     * @return ResponseInterface|ResultInterface
     */
    public function afterExecute(Add $subject, $result)
    {
        if (
            $this->request->isAjax()
            && ($response = $this->response->getBody())
            && ($product = $this->getProduct())
        ) {
            $responseData = json_decode($response, true) ? : [];

            /** @var ProductInterface&Product $product */
            $productImage = $this->imageHelper
                ->init($product, 'product_page_image_large')
                ->setImageFile($product->getImage())
                ->getUrl();

            $responseData['redkiwi_popup'] = [
                'product_image' => $productImage,
                'product_name' => $product->getName(),
                'product_price' => $this->priceHelper->currency($product->getFinalPrice(), true, false)
            ];

            $attributes = [];
            $productGroupByAttributes = explode(',', $product->getData(UpdateProductGroupByAttribute::ATTRIBUTE_CODE) ?? '');
            foreach ($productGroupByAttributes as $attributeCode) {
                if (
                    $attributeCode
                    && ($optionLabel = $product->getAttributeText($attributeCode))
                ) {
                    $attributes[] = $optionLabel;
                }
            }

            if ($superAttributes = $this->request->getParam('super_attribute')) {
                foreach ($superAttributes as $attributeId => $optionId) {
                    if ($optionLabel = $this->getOptionLabel((int)$attributeId, (int)$optionId)) {
                        $attributes[] = $optionLabel;
                    }
                }

                $simpleProduct = $this->configurableProduct->getProductByAttributes($superAttributes, $product);
                if (
                    $simpleProduct
                    && $simpleProduct->getImage()
                ) {
                    $imageUrl = $this->imageHelper
                        ->init($product, 'product_page_image_large')
                        ->setImageFile($simpleProduct->getImage())
                        ->getUrl();

                    if (!str_contains($imageUrl, 'placeholder')) {
                        $responseData['redkiwi_popup']['product_image'] = $imageUrl;
                    }
                }
            }

            $responseData['redkiwi_popup']['product_options'] = implode(' | ', $attributes);

            if (count($this->messageManager->getMessages()->getItemsByType('success'))) {
                // Get messages again with 'clear' flag => do not show success message as the pop-up in itself is a confirmation
                $this->messageManager->getMessages(true);
            }

            $this->response->representJson(json_encode($responseData));
        }

        return $result;
    }

    protected function getOptionLabel($attributeId, $optionId): ?string
    {
        if (
            ($attribute = $this->getProductAttribute($attributeId))
            && $attribute->usesSource()
        ) {
            return  $attribute->getSource()->getOptionText($optionId);
        }

        return null;
    }

    protected function getProductAttribute(int $attributeId): ?AttributeInterface
    {
        static $attributes = [];

        if (empty($attributes[$attributeId])) {
            try {
                $attributes[$attributeId] = $this->attributeRepository->get($attributeId);
            } catch (\Exception $e) {}
        }

        return $attributes[$attributeId] ?? null;
    }

    protected function getProduct(): ?ProductInterface
    {
        if ($productId = (int)$this->request->getParam('product')) {
            try {
                $storeId = $this->storeManager->getStore()->getId();

                return $this->productRepository->getById($productId, false, $storeId);
            } catch (\Exception $e) {
                $this->logger->critical('Redkiwi_Popup: ' . $e->getMessage());
            }
        }

        return null;
    }
}
