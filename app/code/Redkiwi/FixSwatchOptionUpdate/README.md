# Redkiwi_FixSwatchOptionUpdate

Fix for: https://github.com/magento/magento2/issues/37794

Disable `save_swatches_option_params` plugin on `\Magento\Catalog\Model\ResourceModel\Eav\Attribute`
from `webapi_rest` and `webapi_soap` areas.

Reason:
Inside the plugin `\Magento\Swatches\Model\Plugin\EavAttribute` 
it's expected that the swatch values are provided in request data, which happens for
admin, but there is no support of this from web API.
Disabling this plugin will fix the issue until Magento adds support for option swatch 
data in API.
