<?php

declare(strict_types=1);

namespace Life\AmastyFaqExtension\Plugin;

use Amasty\Faq\Model\Config\Layouts;

class AddOnceColumnLayout
{
    public const LAYOUT_1COLUMN = '1column';

    /**
     * @param array{value:string, label:string} $options
     * @return array{value:string, label:string}
     */
    public function afterToOptionArray(Layouts $layouts, array $options): array
    {
        return [
            [
                'value' => self::LAYOUT_1COLUMN, 'label' => __('1 column')
            ],
            ...$options
        ];
    }
}
