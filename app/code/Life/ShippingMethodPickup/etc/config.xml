<?xml version="1.0"?>
<config
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Store:etc/config.xsd"
>
    <default>
        <carriers>
            <afhalen>
                <active>1</active>
                <title>Pickup</title>
                <name>Pickup</name>
                <maximum_order_total>999999</maximum_order_total>
                <old_price>0</old_price>
                <sallowspecific>0</sallowspecific>
                <sort_order>40</sort_order>
                <model>Life\ShippingMethodPickup\Model\Carrier\Pickup</model>
            </afhalen>
        </carriers>
    </default>
</config>
