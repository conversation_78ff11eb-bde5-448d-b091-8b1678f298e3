<config
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd"
>
    <virtualType name="AppearanceSourceRow">
        <arguments>
            <argument name="optionsData" xsi:type="array">
                <item name="3" xsi:type="array">
                    <item name="value" xsi:type="string">doorways</item>
                    <item name="title" xsi:type="string" translate="true">Doorways</item>
                    <item
                        name="icon"
                        xsi:type="string"
                    >Life_PageBuilderDoorwaysLayout/css/images/content-type/row/appearance/doorways.svg</item>
                </item>
                <item name="4" xsi:type="array">
                    <item name="value" xsi:type="string">blog-doorways</item>
                    <item name="title" xsi:type="string" translate="true">Blog Doorways</item>
                    <item
                        name="icon"
                        xsi:type="string"
                    >Life_PageBuilderDoorwaysLayout/css/images/content-type/row/appearance/blog-doorways.svg</item>
                </item>
            </argument>
        </arguments>
    </virtualType>
</config>
