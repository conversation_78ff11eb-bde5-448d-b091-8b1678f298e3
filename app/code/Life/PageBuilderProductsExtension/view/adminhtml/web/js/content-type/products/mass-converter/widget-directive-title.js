/** @see vendor/magento/module-page-builder/view/adminhtml/web/js/content-type/products/mass-converter/carousel-widget-directive.js */
function _inheritsLoose(subClass, superClass) {
    subClass.prototype = Object.create(superClass.prototype);
    subClass.prototype.constructor = subClass;
    _setPrototypeOf(subClass, superClass);
}

function _setPrototypeOf(o, p) {
    _setPrototypeOf = Object.setPrototypeOf || function _setPrototypeOf(o, p) {
        o.__proto__ = p;

        return o;
    };

    return _setPrototypeOf(o, p);
}

define(
[
    "Magento_PageBuilder/js/content-type/products/mass-converter/carousel-widget-directive",
    "Magento_PageBuilder/js/mass-converter/widget-directive-abstract",
    "Magento_PageBuilder/js/utils/object"
], function (_widgetDirectiveCarousel, _widgetDirectiveAbstract, _object)
{
    let WidgetDirectiveTitle = function (_widgetDirectiveCarousel, _widgetDirectiveAbstract) {
        "use strict";

        _inheritsLoose(WidgetDirectiveTitle, _widgetDirectiveCarousel);

        function WidgetDirectiveTitle() {
            return _widgetDirectiveCarousel.apply(this, arguments) || this;
        }

        let _proto = WidgetDirectiveTitle.prototype;

        /**
         * Convert value to internal format
         *
         * @param {object} data
         * @param {object} config
         * @returns {object}
         */
        _proto.fromDom = function fromDom(data, config) {

            let carouselData = _widgetDirectiveCarousel.prototype.fromDom.call(this, data, config),
                attributes = _widgetDirectiveAbstract.prototype.fromDom.call(this, data, config);

            carouselData.title = attributes.title;

            return carouselData;
        };

        /**
         * Convert value to knockout format
         *
         * @param {object} data
         * @param {object} config
         * @returns {object}
         */
        _proto.toDom = function toDom(data, config) {
            let attributes = _widgetDirectiveAbstract.prototype.fromDom.call(this, data, config);

            attributes.title = data.title;

            (0, _object.set)(data, config.html_variable, this.buildDirective(attributes));

            return data;
        };

        return WidgetDirectiveTitle;

    }(_widgetDirectiveCarousel, _widgetDirectiveAbstract);

    return WidgetDirectiveTitle;
});
