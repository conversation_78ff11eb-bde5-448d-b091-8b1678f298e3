<?php

declare(strict_types=1);

namespace Life\ShippingMethodPostDelivery\Setup\Patch\Data;

use Magento\Catalog\Model\Category;
use Magento\Eav\Model\Entity\Attribute\ScopedAttributeInterface;
use Magento\Eav\Setup\{EavSetup, EavSetupFactory};
use Magento\Framework\Setup\ModuleDataSetupInterface;
use Magento\Framework\Setup\Patch\DataPatchInterface;

class AddPostDeliveryAllowedCategoryAttribute implements DataPatchInterface
{
    const ATTRIBUTE_CODE = 'post_delivery_allowed';

    public function __construct(
        private readonly ModuleDataSetupInterface $moduleDataSetup,
        private readonly EavSetupFactory $eavSetupFactory
    ) {}

    public function apply()
    {
        /** @var EavSetup $eavSetup */
        $eavSetup = $this->eavSetupFactory->create(['setup' => $this->moduleDataSetup]);

        $eavSetup->addAttribute(
            Category::ENTITY,
            self::ATTRIBUTE_CODE,
            [
                'type' => 'varchar',
                'label' => 'Post Delivery Allowed',
                'input' => 'text',
                'sort_order' => 220,
                'global' => ScopedAttributeInterface::SCOPE_STORE,
                'required' => false,
                'group' => 'General'
            ]
        );

        return $this;
    }

    public static function getDependencies(): array
    {
        return [];
    }

    public function getAliases(): array
    {
        return [];
    }
}
