<?xml version="1.0" encoding="UTF-8"?>
<form
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd"
>
    <fieldset name="general">
        <field name="post_delivery_allowed" sortOrder="220" formElement="input">
            <settings>
                <dataType>string</dataType>
                <label translate="true">Post Delivery Allowed</label>
                <notice translate="true">Use 0 or leave empty in case Post Delivery is not allowed for this category.</notice>
                <validation>
                    <rule name="validate-digits" xsi:type="boolean">true</rule>
                </validation>
            </settings>
        </field>
    </fieldset>
</form>
