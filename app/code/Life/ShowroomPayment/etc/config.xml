<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Store:etc/config.xsd">
    <default>
        <showroom_payment>
            <general>
                <dealer_payment_methods>dealer_payment</dealer_payment_methods>
            </general>
        </showroom_payment>
        <payment>
            <dealer_payment>
                <active>0</active>
                <model>Life\ShowroomPayment\Model\Method\DealerPayment</model>
                <order_status>pending</order_status>
                <payment_action>authorize_capture</payment_action>
                <title>Dealer Payment Method</title>
                <allowspecific>0</allowspecific>
                <sort_order>1</sort_order>
                <group>offline</group>
                <allowed_customer_groups>5</allowed_customer_groups>
            </dealer_payment>
        </payment>
    </default>
    <websites>
        <showroom>
            <payment>
                <dealer_payment>
                    <active>1</active>
                </dealer_payment>
            </payment>
        </showroom>
    </websites>
</config>
