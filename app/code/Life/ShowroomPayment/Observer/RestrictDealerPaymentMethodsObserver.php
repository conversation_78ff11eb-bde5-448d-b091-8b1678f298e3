<?php

declare(strict_types=1);

namespace Life\ShowroomPayment\Observer;

use Life\ShowroomBase\Api\ActorDealerResolverInterface;
use Life\ShowroomBase\Api\ScopeResolverInterface;
use Magento\Framework\Event\ObserverInterface;
use Magento\Framework\Event\Observer;
use Life\ShowroomPayment\Model\ConfigProvider as PaymentConfigProvider;

/**
 * Class for restrict payment methods for dealer
 */
class RestrictDealerPaymentMethodsObserver implements ObserverInterface
{
    /**
     * Constructor
     *
     * @param PaymentConfigProvider $paymentConfigProvider
     * @param ActorDealerResolverInterface $actorDealerResolver
     * @param ScopeResolverInterface $scopeResolver
     */
    public function __construct(
        private readonly PaymentConfigProvider $paymentConfigProvider,
        private readonly ActorDealerResolverInterface $actorDealerResolver,
        private readonly ScopeResolverInterface $scopeResolver
    ) {}

    /**
     * Restrict payment methods
     *
     * @param Observer $observer
     */
    public function execute(Observer $observer): void
    {
        $allowedMethods = $this->paymentConfigProvider->getShowroomDealerPaymentMethodList();
        $isDealer = $this->actorDealerResolver->isDealer();
        $isShowroom = $this->scopeResolver->isShowroomWebsite();
        if ($isShowroom && $isDealer && $allowedMethods) {
            $methodInstance = $observer->getEvent()->getMethodInstance();
            $result = $observer->getEvent()->getResult();

            if (!in_array($methodInstance->getCode(), $allowedMethods)) {
                $result->setData('is_available', false);
            }
        }
    }
}
