<?php

declare(strict_types=1);

namespace Life\Sales\Setup\Patch\Data;

use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Framework\App\Config\Storage\WriterInterface;
use Magento\Framework\Setup\ModuleDataSetupInterface;
use Magento\Framework\Setup\Patch\DataPatchInterface;
use Magento\Store\Model\ScopeInterface;

/**
 * Patch set config for carriers code to be same at all environments (second version)
 */
class SetCodesForCustomCarriersV1 implements DataPatchInterface
{
    private const CONFIG_DATA = [
        [
            'path' => 'carriers/package_delivery/shipping_cost',
            //adding standard_delivery_ to code
            // phpcs:ignore
            'value' => '{"_1702401628946_946":{"code":"standard_service_4950","order_total":"0","cost":"49.5"},"_1702401683413_413":{"code":"standard_service_free","order_total":"250","cost":"0"}}',
            'scope' => ScopeConfigInterface::SCOPE_TYPE_DEFAULT,
            'scopeId' => 0
        ],
        [
            'path' => 'carriers/post/shipping_cost',
            //adding post_ to code
            'value' => '{"_1702456625062_62":{"code":"post_nl","order_total":"0","cost":"4.5"}}',
            'scope' => ScopeInterface::SCOPE_WEBSITES,
            'scopeId' => 1
        ],
        [
            'path' => 'carriers/post/shipping_cost',
            //adding post_ to code
            'value' => '{"_1702456711169_169":{"code":"post_nl","order_total":"0","cost":"4.5"}}',
            'scope' => ScopeInterface::SCOPE_WEBSITES,
            'scopeId' => 2
        ],
        [
            'path' => 'carriers/post/shipping_cost',
            //adding post_ to code
            'value' => '{"_1702456736732_732":{"code":"post_de","order_total":"0","cost":"7.5"}}',
            'scope' => ScopeInterface::SCOPE_WEBSITES,
            'scopeId' => 3
        ],
    ];

    public function __construct(
        private readonly ModuleDataSetupInterface $moduleDataSetup,
        private readonly WriterInterface $configWriter
    ) {
    }

    /**
     * {@inheritdoc}
     */
    public function apply(): void
    {
        $this->moduleDataSetup->startSetup();
        foreach (self::CONFIG_DATA as $config) {
            $this->configWriter->save($config['path'], $config['value'], $config['scope'], $config['scopeId']);
        }
        $this->moduleDataSetup->endSetup();
    }

    /**
     * {@inheritdoc}
     */
    public function getAliases(): array
    {
        return [];
    }

    /**
     * {@inheritdoc}
     */
    public static function getDependencies(): array
    {
        return [SetCodesForCustomCarriers::class];
    }
}
