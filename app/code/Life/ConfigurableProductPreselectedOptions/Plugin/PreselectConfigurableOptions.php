<?php

declare(strict_types=1);

namespace Life\ConfigurableProductPreselectedOptions\Plugin;

use Magento\Catalog\Api\ProductRepositoryInterface;
use Magento\Catalog\Api\Data\ProductInterface;
use Magento\Catalog\Helper\Product as ProductHelper;
use Magento\Catalog\Model\Product;
use Magento\ConfigurableProduct\Model\Product\Type\Configurable;
use Magento\Framework\DataObject;
use Magento\Framework\Exception\NoSuchEntityException;

class PreselectConfigurableOptions
{
    public function __construct(
        private readonly ProductRepositoryInterface $productRepository
    ) {}

    /**
     * @see ProductHelper::initProduct()
     *
     * @param int|string $productId
     * @param \Magento\Framework\App\Action\Action $controller
     * @param DataObject|array|null $params
     * @return mixed[]
     */
    public function beforeInitProduct(
        ProductHelper $subject,
        $productId,
        $controller,
        $params = null
    ): array {
        $params = $this->prepareConfigurableOptionParams((int)$productId, $params);

        return [$productId, $controller, $params];
    }

    /**
     * @param int $productId
     * @param DataObject|array|null $params
     * @return DataObject
     */
    protected function prepareConfigurableOptionParams(int $productId, $params): DataObject
    {
        // Prepare product params
        if (!$params) {
            $params = new DataObject();
        }
        if (is_array($params)) {
            $params = new DataObject($params);
        }

        // Probably buy request params are set from cart edit, so these should be kept
        if ($params->getData('buy_request')) {
            return $params;
        }

        // Prepare configurable options based on set default product
        $superAttributeOptions = [];
        if (
            ($product = $this->getProductById($productId))
            && ($product->getTypeId() === Configurable::TYPE_CODE)
            && ($defaultSku = $product->getData('default_sku'))
            && ($simpleProduct = $this->getProductBySku($defaultSku))
            && ($superAttributes = $this->getSuperAttributes($product))
        ) {
            foreach ($superAttributes as $attributeId => $attributeCode) {
                $superAttributeOptions[$attributeId] = $simpleProduct->getData($attributeCode);
            }
        }

        if ($superAttributeOptions) {
            $buyRequest = new DataObject();
            $buyRequest->setData('super_attribute', $superAttributeOptions);
            $params->setData('buy_request', $buyRequest);
        }

        return $params;
    }

    /**
     * @return array<int, string>
     */
    protected function getSuperAttributes(ProductInterface $product): array
    {
        $superAttributes = [];

        /** @var ProductInterface&Product $product */
        /** @var \Magento\ConfigurableProduct\Model\Product\Type\Configurable $productTypeInstance */
        $productTypeInstance = $product->getTypeInstance();

        foreach ($productTypeInstance->getConfigurableAttributes($product) as $superAttribute) {
            $superAttributes[$superAttribute->getAttributeId()] = $superAttribute->getProductAttribute()->getAttributeCode();
        }

        return $superAttributes;
    }

    protected function getProductById(int $productId): ?ProductInterface
    {
        try {
            return $this->productRepository->getById($productId);
        } catch (NoSuchEntityException $exception) {}

        return null;
    }

    protected function getProductBySku(string $productSku): ?ProductInterface
    {
        try {
            return $this->productRepository->get($productSku);
        } catch (NoSuchEntityException $exception) {}

        return null;
    }
}
