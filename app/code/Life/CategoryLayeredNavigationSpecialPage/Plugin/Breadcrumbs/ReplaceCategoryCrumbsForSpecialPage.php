<?php

declare(strict_types=1);

namespace Life\CategoryLayeredNavigationSpecialPage\Plugin\Breadcrumbs;

use Life\CategoryLayeredNavigationSpecialPage\Model\SpecialCategoryPage;
use Magento\Catalog\Api\Data\CategoryInterface;
use Magento\Catalog\Helper\Data;
use Psr\Log\LoggerInterface;

class ReplaceCategoryCrumbsForSpecialPage
{
    public function __construct(
        private readonly SpecialCategoryPage $specialCategoryPage,
        private readonly LoggerInterface $logger
    ) {}

    /**
     * @see Data::getBreadcrumbPath()
     *
     * @param Data $subject
     * @param array{label:string, link:string} $crumbs
     * @return array{label:string, link:string}
     */
    public function afterGetBreadcrumbPath(
        Data $subject,
        array $crumbs
    ): array {
        try {
            if (
                $this->specialCategoryPage->isSpecialCategoryPage()
                && ($mappedCategory = $this->specialCategoryPage->getMappedCategory())
                && ($currentCategory = $this->specialCategoryPage->getCurrentCategory())
            ) {
                return $this->rebuiltCategoryCrumbs($mappedCategory, $currentCategory);
            }
        } catch (\Exception $exception) {
            $this->logger->critical('Life_CategoryLayeredNavigationSpecialPage: ' . $exception->getMessage());
        }

        return $crumbs;
    }

    /**
     * @return array{label:string, link:string}
     */
    protected function rebuiltCategoryCrumbs(
        CategoryInterface $mappedCategory,
        CategoryInterface $currentCategory
    ): array {
        $paths = [];
        // Use mapped category to get the crumbs
        $pathInStore = $mappedCategory->getPathInStore();
        $pathIds = array_reverse(explode(',', $pathInStore));

        $categories = $mappedCategory->getParentCategories();

        foreach ($pathIds as $categoryId) {
            if (isset($categories[$categoryId]) && $categories[$categoryId]->getName()) {
                $paths['category' . $categoryId] = [
                    'label' => $categories[$categoryId]->getName(),
                    'link' => $categories[$categoryId]->getUrl()
                ];
            }
        }

        $paths['category' . $currentCategory->getId()] = [
            'label' => $currentCategory->getName(),
            'link' => ''
        ];

        return $paths;
    }
}
