<?php

declare(strict_types=1);

namespace Life\CategoryLayeredNavigationSpecialPage\Plugin\Layer;

use Life\CategoryLayeredNavigationSpecialPage\Model\SpecialCategoryPage;
use Magento\Catalog\Api\Data\CategoryInterface;
use Magento\Catalog\Model\Layer;

class ReplaceCategoryForSpecialPage
{
    public function __construct(
        private readonly SpecialCategoryPage $specialCategoryPage
    ) {}

    /**
     * @see Layer::getCurrentCategory()
     */
    public function afterGetCurrentCategory(
        Layer $subject,
        ?CategoryInterface $category
    ): ?CategoryInterface {
        if (
            $this->specialCategoryPage->isSpecialCategoryPage()
            && ($mappedCategory = $this->specialCategoryPage->getMappedCategory())
        ) {
            return $mappedCategory;
        }

        return $category;
    }
}
