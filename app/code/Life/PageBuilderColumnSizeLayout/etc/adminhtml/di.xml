<?xml version="1.0"?>
<config
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd"
>
    <virtualType name="AppearanceSourceColumn">
        <arguments>
            <argument name="optionsData" xsi:type="array">
                <item name="4" xsi:type="array">
                    <item name="value" xsi:type="string">big-width</item>
                    <item name="title" xsi:type="string" translate="true">Mobile Big</item>
                    <item
                        name="icon"
                        xsi:type="string"
                    >Life_PageBuilderColumnSizeLayout::css/images/content-type/column/appearance/big.svg</item>
                </item>
                <item name="5" xsi:type="array">
                    <item name="value" xsi:type="string">small-width</item>
                    <item name="title" xsi:type="string" translate="true">Mobile Small</item>
                    <item
                        name="icon"
                        xsi:type="string"
                    >Life_PageBuilderColumnSizeLayout/css/images/content-type/column/appearance/small.svg</item>
                </item>
            </argument>
        </arguments>
    </virtualType>
</config>
