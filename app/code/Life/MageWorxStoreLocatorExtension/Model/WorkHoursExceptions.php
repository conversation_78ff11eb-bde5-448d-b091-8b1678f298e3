<?php

declare(strict_types=1);

namespace Life\MageWorxStoreLocatorExtension\Model;

use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Framework\Serialize\SerializerInterface;
use Magento\Framework\View\Element\Block\ArgumentInterface;
use Magento\Store\Model\ScopeInterface;

class WorkHoursExceptions implements ArgumentInterface
{
    const PATH_EXCEPTIONS = 'mageworx_locations/opening_hours/exceptions';

    public function __construct(
        private readonly ScopeConfigInterface $scopeConfig,
        private readonly SerializerInterface $serializer
    ) {}

    public function getExceptions(string $scopeCode = null): ?array
    {
        static $exceptions = null;

        if (is_null($exceptions)) {
            $value = $this->getValue(self::PATH_EXCEPTIONS, $scopeCode);

            try {
                $exceptions = (array)$this->serializer->unserialize($value);
            } catch (\Throwable $e) {
                $exceptions = [];
            }
        }

        return $exceptions;
    }

    /**
     * @return mixed
     */
    protected function getValue(
        string $path,
        string $scopeCode = null,
        string $scopeType = ScopeInterface::SCOPE_STORES
    ): string
    {
        return (string)$this->scopeConfig->getValue($path, $scopeType, $scopeCode);
    }
}
