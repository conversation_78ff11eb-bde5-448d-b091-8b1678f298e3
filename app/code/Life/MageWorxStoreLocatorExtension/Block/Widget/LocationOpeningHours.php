<?php

declare(strict_types=1);

namespace Life\MageWorxStoreLocatorExtension\Block\Widget;

use Magento\Framework\View\Element\Template;
use Magento\Widget\Block\BlockInterface;
use MageWorx\Locations\Api\Data\LocationInterface;
use MageWorx\Locations\Api\LocationRepositoryInterface;

class LocationOpeningHours extends Template implements BlockInterface
{
    /**
     * @param mixed[] $data
     */
    public function __construct(
        private readonly LocationRepositoryInterface $locationRepository,
        Template\Context $context,
        array $data = []
    ) {
        parent::__construct($context, $data);
    }

    public function getLocation(): ?LocationInterface
    {
        try {
            $location = $this->locationRepository->getById($this->getLocationId());

            if ($location->getIsActive()) {
                return $location;
            }
        } catch (\Exception $e) {}

        return null;
    }

    protected function getLocationId(): ?int
    {
        if ($this->getData('location_id')) {
            return (int)$this->getData('location_id');
        }

        return null;
    }

    /**
     * @param \MageWorx\Locations\Api\Data\LocationInterface $location
     * @return string
     */
    public function getRouteUrl($location)
    {
        $region = $location->getRegion() == \MageWorx\Locations\Model\Source\Region::NO_REGIONS ?
            '' : $location->getRegion();

        return "//maps.google.com/maps/dir/?api=1&destination=" . $location->getAddress() .
            ", " . $location->getCity() . ", " . $region . ", " . $location->getCountry();
    }
}
