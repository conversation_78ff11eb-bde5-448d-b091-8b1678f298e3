<?php

declare(strict_types=1);

namespace Life\MageWorxStoreLocatorExtension\Block\Adminhtml\Form\Field;

use Hyva\Checkout\Block\Adminhtml\Element\FieldArray\TypeRenderer;
use Magento\Backend\Block\Template;
use Magento\Backend\Block\Template\Context;
use Magento\Config\Block\System\Config\Form\Field\FieldArray\AbstractFieldArray;
use Magento\Framework\Data\Form\Element\AbstractElement;
use Magento\Framework\DataObject;
use Magento\Framework\View\Element\BlockFactory;

class Exceptions extends AbstractFieldArray
{
    /**
     * @param mixed[] $data
     */
    public function __construct(
        private readonly BlockFactory $blockFactory,
        Context $context,
        array $data = []
    ) {
        parent::__construct($context, $data);
    }

    protected function _prepareToRender(): void
    {
        $this->addColumn(
            'stores',
            [
                'label' => __('Stores'),
                'renderer' => $this->getStoresRenderer(),
                'extra_params' => 'multiple="multiple"'
            ]
        );
        $this->addColumn(
            'date',
            [
                'label' => __('Date'),
                'class' => 'js-excluded-weekdays-datepicker required'
            ]
        );
        $this->addColumn(
            'open_time',
            [
                'label' => __('Open Time'),
                'style' => 'width: 75px'
            ]
        );
        $this->addColumn(
            'closed_time',
            [
                'label' => __('Closed Time'),
                'style' => 'width: 75px'
            ]
        );

        $this->_addAfter = false;
        $this->_addButtonLabel = (string) __('Add Exception');

        parent::_prepareToRender();
    }

    protected function _prepareArrayRow(DataObject $row): void
    {
        $key = 'date';
        $rowId = $row['_id'];
        try {
            /** @var \DateTime $sourceDate */
            $sourceDate = \DateTime::createFromFormat('Y-m-d', $row[$key]);
            $renderedDate = $sourceDate->format('d-m-Y');
            $row[$key] = $renderedDate;
            $columnValues = $row['column_values'];
            $columnValues[$this->_getCellInputElementId($rowId, $key)] = $renderedDate;
            $row['column_values'] = $columnValues;
        } catch (\Throwable $e) {
            // Just skipping error values
        }

        try {
            $options = [];
            $stores = $row['stores'] ?? [];
            if (count($stores) > 0) {
                foreach ($stores as $store) {
                    $options['option_' . $this->getStoresRenderer()->calcOptionHash($store)] = 'selected="selected"';
                }
            }
            $row['option_extra_attrs'] = $options;
        } catch (\Throwable $e) {
            // Just skipping error values
        }
    }

    protected function _getElementHtml(AbstractElement $element): string
    {
        $html = parent::_getElementHtml($element);
        /** @phpstan-ignore-next-line  */
        $html .= $this->getLayout()
            ->createBlock(Template::class)
            ->setTemplate('Life_MageWorxStoreLocatorExtension::config/datepicker-js.phtml')
            ->toHtml()
        ;

        return $html;
    }

    private function getStoresRenderer()
    {
        static $storesRenderer = null;

        if (is_null($storesRenderer)) {
            $storesRenderer = $this->getLayout()->createBlock(
                StoresColumn::class,
                '',
                ['data' => ['is_render_to_js_template' => true]]
            );
        }

        return $storesRenderer;
    }
}
