<?php

declare(strict_types=1);

namespace Life\MageWorxStoreLocatorExtension\Block\Adminhtml\Form\Field;

use Magento\Framework\View\Element\Context;
use Magento\Framework\View\Element\Html\Select;
use MageWorx\Locations\Model\ResourceModel\Location\CollectionFactory;

class StoresColumn extends Select
{
    public function __construct(
        private readonly CollectionFactory $collectionFactory,
        Context $context,
        array $data = []
    ) {
        parent::__construct($context, $data);
    }

    public function setInputName($value)
    {
        return $this->setName($value . '[]');
    }

    public function _toHtml(): string
    {
        if (!$this->getOptions()) {
            $stores = $this->collectionFactory->create()
                ->addAttributeToSelect('name');
            $options = [];
            /** @var \MageWorx\Locations\Model\Location $store */
            foreach ($stores as $store) {
                $options[$store->getCode()] = $store->getName();
            }
            $this->setOptions($options);
        }
        $this->setExtraParams('multiple="multiple"');

        return parent::_toHtml();
    }
}
