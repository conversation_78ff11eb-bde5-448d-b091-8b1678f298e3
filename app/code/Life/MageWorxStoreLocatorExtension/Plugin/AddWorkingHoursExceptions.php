<?php

declare(strict_types=1);

namespace Life\MageWorxStoreLocatorExtension\Plugin;

use Life\MageWorxStoreLocatorExtension\Model\WorkHoursExceptions;
use Magento\Framework\Stdlib\DateTime;
use Magento\Framework\Stdlib\DateTime\TimezoneInterface;
use MageWorx\Locations\Model\Location;

class AddWorkingHoursExceptions
{
    const DAYS_BUFFER = 7; // One week

    public function __construct(
        private readonly WorkHoursExceptions $workHoursExceptions,
        private readonly TimezoneInterface $timezone,
        private readonly DateTime $dateTime
    ) {}

    /**
     * @see Location::getWorkingHours()
     *
     * @param array{from: string, to: string, off: bool|string, lunch_from: string, lunch_to: string, has_lunch_time: bool|string} $workingHours
     * @return array{from: string, to: string, off: bool|string, lunch_from: string, lunch_to: string, has_lunch_time: bool|string}
     */
    public function afterGetWorkingHours(
        Location $location,
        array $workingHours
    ): array
    {
        try {
            if ($exceptions = $this->getCurrentStoreExceptions($location)) {
                return array_merge($workingHours, $exceptions);
            }
        } catch (\Exception $e) {
            // Do nothing
        }

        return $workingHours;
    }

    protected function getCurrentStoreExceptions(Location $location): array
    {
        $storeExceptions = [];
        $startTimestamp = strtotime($this->timezone->date()->format('Y-m-d') . ' 00:00:00');
        $bufferTimeStamp = strtotime('+' . self::DAYS_BUFFER . ' days', $startTimestamp);
        $exceptions = $this->workHoursExceptions->getExceptions();

        // Exception should contain current store location and be within now and in a future buffer date
        foreach ($exceptions as $exception) {
            if (
                isset($exception['stores'], $exception['date'])
                && in_array($location->getCode(), $exception['stores'] ?: [])
                && ($exceptionDate = strtotime($exception['date']))
                && ($exceptionDate >= $startTimestamp)
                && ($exceptionDate <= $bufferTimeStamp)
            ) {
                // Prepare exception data, as needed by store locator
                $exception['has_lunch_time'] = 0;
                $exception['off'] = 0;
                if (empty(trim($exception['open_time'])) || empty(trim($exception['closed_time']))) {
                    $exception['off'] = 1;
                } else {
                    $exception['from'] = $exception['open_time'];
                    $exception['to'] = $exception['closed_time'];
                }
                $storeExceptions[$this->getLocaleDate($exceptionDate)] = $exception;
            }
        }

        return $storeExceptions;
    }

    /**
     * @see Location::isOpenNow()
     */
    public function afterIsOpenNow(Location $location, bool $isOpen): bool
    {
        $todayDate = $this->getLocaleDate();

        foreach ($location->getWorkingHours() as $day => $data) {
            if ($day == $todayDate) {
                if (!empty($data['off'])) {
                    return false;
                } else {
                    return $this->isOpen($data['from'], $data['to']);
                }
            }
        }

        return $isOpen;
    }

    protected function isOpen($fromStr, $toStr)
    {
        $from = strtotime($this->timezone->date()->format('Y-m-d') . ' ' . $fromStr);
        $to = strtotime($this->timezone->date()->format('Y-m-d') . ' ' . $toStr);
        $current = strtotime($this->timezone->date()->format('Y-m-d H:i:s'));

        return ($from < $to && $current > $from && $current < $to);
    }

    /**
     * @see Location::getWorkingHoursInfo()
     */
    public function afterGetWorkingHoursInfo(Location $location, string $info): string
    {
        $todayDate = $this->getLocaleDate();
        $workingHours = $location->getWorkingHours();

        $closedEntireDay = false;
        $todayDayname = strtolower(date('l'));

        // Check against exception open/close time
        foreach ($workingHours as $day => $data) {
            if ($day == $todayDate) {
                if ($location->isOpenNow()) {
                    return __('Open') . ' | ' . __('closes') . ' ' . $data['to'];
                } else {
                    return (string)__('Closed');
                }
            }

            // Check if store is closed for current week day, and if so, set a flag to check the day(s) after
            if (
                ($day === $todayDayname)
                && (!empty($data['off']))
            ) {
                $closedEntireDay = true;
                continue;
            }

            // If the store was closed in the day(s) before, use current day name open hours
            if (
                $closedEntireDay
                && (empty($data['off']))
            ) {
                return __('Closed') . ' | ' . __('opens') . ' ' . __(ucfirst($day)) . ' ' . $this->getFormattedTime($data['from']);
            }
        }

        return $info;
    }

    public function getLocaleDate(?int $timestamp = null): string
    {
        return $this->timezone->formatDate(
            date('Y-m-d H:i:s', $timestamp),
            \IntlDateFormatter::MEDIUM,
            false
        );
    }

    protected function getFormattedTime(string $time): string
    {
        if ($this->dateTime->isEmptyDate($time)) {
            return $time;
        }

        return $this->timezone->formatDateTime(
            $time,
            \IntlDateFormatter::NONE,
            \IntlDateFormatter::SHORT,
            null,
            $this->timezone->getDefaultTimezone()
        );
    }
}
