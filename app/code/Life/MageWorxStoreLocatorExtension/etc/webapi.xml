<?xml version="1.0"?>
<routes
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Webapi:etc/webapi.xsd"
>
    <route url="/V1/life/get-store-schedule/:locationId" method="GET">
        <service class="Life\MageWorxStoreLocatorExtension\Api\StoreScheduleInterface" method="getSchedule" />
        <resources>
            <resource ref="anonymous"/>
        </resources>
    </route>
</routes>
