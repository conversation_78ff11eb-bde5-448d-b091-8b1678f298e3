<?php

namespace Life\Squeezely\Model;

use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Store\Model\ScopeInterface;

class Config
{
    const XML_PATH_API_BASE_URI = 'life__squeezely/api/base_uri';
    const XML_PATH_API_ACCOUNT = 'life__squeezely/api/account';
    const XML_PATH_API_KEY = 'life__squeezely/api/key';

    public function __construct(
        private readonly ScopeConfigInterface $scopeConfig,
    ) {
    }

    public function getApiBaseUri(): string
    {
        return (string)$this->scopeConfig->getValue(self::XML_PATH_API_BASE_URI, ScopeInterface::SCOPE_STORE);
    }

    public function getApiAccount(): string
    {
        return (string)$this->scopeConfig->getValue(self::XML_PATH_API_ACCOUNT, ScopeInterface::SCOPE_STORE);
    }

    public function getApiKey(): string
    {
        return (string)$this->scopeConfig->getValue(self::XML_PATH_API_KEY, ScopeInterface::SCOPE_STORE);
    }
}
