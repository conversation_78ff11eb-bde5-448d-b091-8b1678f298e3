<?php

namespace Life\Squeezely\Model;

use GuzzleHttp\Client;

class Api
{
    public function __construct(
        private readonly Config $config,
        private readonly Client $client,
    ) {
    }

    public function __invoke(string $method, string $path, array $options = [])
    {
        $options['headers']['X-AUTH-ACCOUNT'] = $this->config->getApiAccount();
        $options['headers']['X-AUTH-APIKEY'] = $this->config->getApiKey();

        $response = $this->client->request(
            $method,
            "{$this->config->getApiBaseUri()}/{$path}",
            $options
        );
        $response = json_decode($response->getBody(), true);

        return $response;
    }
}
