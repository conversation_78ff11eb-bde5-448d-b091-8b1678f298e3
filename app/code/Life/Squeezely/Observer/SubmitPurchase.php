<?php

namespace Life\Squeezely\Observer;

use Life\Squeezely\Model\Api;
use Magento\Framework\Event\{Observer, ObserverInterface};
use Psr\Log\LoggerInterface;

class SubmitPurchase implements ObserverInterface
{
    public function __construct(
        private readonly Api $api,
        private readonly LoggerInterface $logger,
    ) {
    }

    public function execute(Observer $observer)
    {
        /** @var \Magento\Sales\Model\Order $order */
        $order = $observer->getData('order');

        $email = $order->getCustomerEmail();
        $email = hash('sha256', $email);

        $products = array_map(
            fn (\Magento\Sales\Model\Order\Item $item) => [
                'id' => $item->getSku(),
                'name' => $item->getProduct()->getName(),
                'category_ids' => $item->getProduct()->getCategoryIds(),
                'price' => $item->getPriceInclTax(),
                'quantity' => $item->getQtyOrdered(),
            ],
            $order->getAllVisibleItems()
        );

        $data = [
            'events' => [
                [
                    'event' => 'Purchase',
                    'email' => $email,
                    'firstname' => $order->getCustomerFirstname(),
                    'lastname' => $order->getCustomerLastname(),
                    'orderid' => $order->getIncrementId(),
                    'userid' => $order->getCustomer()?->getId(),
                    'products' => $products,
                ]
            ]
        ];

        try {
            $response = ($this->api)('POST', 'track', [
                'json' => $data
            ]);

            $success = $response['success'] ?? true;

            if (!$success) {
                throw new \Exception($response['errors'][0] ?? 'Unknown error');
            }
        } catch (\Exception $e) {
            $this->logger->error("Error while submitting purchase to Squeezely: {$e->getMessage()}");
        }
    }
}
