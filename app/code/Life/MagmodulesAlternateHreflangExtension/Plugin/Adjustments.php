<?php

declare(strict_types=1);

namespace Life\MagmodulesAlternateHreflangExtension\Plugin;

use Magento\Framework\App\RequestInterface;
use Magento\Store\Model\StoreManagerInterface;
use Magmodules\AlternateHreflang\ViewModel\Page;

class Adjustments
{
    public function __construct(
        private readonly RequestInterface $request,
        private readonly StoreManagerInterface $storeManager
    ) {}

    /**
     * @param array<string, string> $alternateUrls
     * @return array<string, string>
     */
    public function afterGetAlternateUrls(Page $subject, array $alternateUrls): array
    {
        $this->trimEndSlashAndAddStoreCode($alternateUrls);
        $this->addPaginationParam($alternateUrls);

        return $alternateUrls;
    }

    /**
     * @param array<string, string> $alternateUrls
     */
    private function trimEndSlashAndAddStoreCode(array &$alternateUrls): void
    {
        $baseUrl = rtrim($this->storeManager->getStore()->getBaseUrl(), '/');
        $baseUrlParts = explode('/', $baseUrl);
        array_pop($baseUrlParts);
        $baseUrlWithoutStoreCode = implode('/', $baseUrlParts);

        foreach ($alternateUrls as &$url) {
            $url = rtrim($url, '/');

            if ($url === $baseUrlWithoutStoreCode) {
                $url = $baseUrl;
            }
        }
    }

    /**
     * @param array<string, string> $alternateUrls
     */
    private function addPaginationParam(array &$alternateUrls): void
    {
        if ($page = $this->request->getParam('p')) {
            foreach ($alternateUrls as &$url) {
                $url .= '?p=' . $page;
            }
        }
    }
}
