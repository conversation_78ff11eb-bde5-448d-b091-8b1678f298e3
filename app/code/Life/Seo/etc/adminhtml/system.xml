<?xml version="1.0"?>
<config
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd"
>
    <system>
        <section
            id="life_seo"
            type="text"
            translate="label"
            sortOrder="30"
            showInDefault="1"
            showInWebsite="1"
            showInStore="1"
        >
            <label>Seo</label>
            <tab>life</tab>
            <resource>Life_Seo::system_config</resource>
            <group
                id="general"
                translate="label"
                type="text"
                sortOrder="10"
                showInDefault="1"
                showInWebsite="1"
                showInStore="1"
            >
                <label>General</label>
                <field
                    id="enable_on_homepage"
                    translate="label"
                    type="select"
                    sortOrder="10"
                    showInDefault="1"
                    showInWebsite="1"
                    showInStore="1"
                >
                    <label>Enable on homepage</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field
                    id="enable_on_cms_page"
                    translate="label"
                    type="select"
                    sortOrder="20"
                    showInDefault="1"
                    showInWebsite="1"
                    showInStore="1"
                >
                    <label>Enable on CMS page</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
            </group>
        </section>
    </system>
</config>
