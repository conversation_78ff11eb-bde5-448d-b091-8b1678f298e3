<?php

declare(strict_types=1);

namespace Life\Seo\Plugin;

use Magento\Cms\Api\Data\PageInterface;
use Magento\Cms\Controller\Page\View;
use Magento\Cms\Helper\Page as PageHelper;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Framework\Controller\ResultInterface;
use Magento\Framework\View\Result\Layout;

class AddCanonicalToCmsPage
{
    public function __construct(
        private readonly ScopeConfigInterface $scopeConfig,
        private readonly PageInterface $page,
        private readonly PageHelper $pageHelper
    ) {}

    /**
     * @see View::execute()
     */
    public function afterExecute(
        View $subject,
        ResultInterface $result,
        $coreRoute = null
    ): ResultInterface {
        if (!$this->scopeConfig->isSetFlag('life_seo/general/enable_on_cms_page', 'store')) {
            return $result;
        }

        // Only apply to pages
        if (!$result instanceof Layout) {
            return $result;
        }

        $result->getConfig()
            ->addRemotePageAsset(
                $this->getCurrentPageUrl(),
                'canonical',
                [
                    'attributes' => [
                        'rel' => 'canonical'
                    ]
                ]
            )
        ;

        return $result;
    }

    private function getCurrentPageUrl(): string
    {
        $currentId = $this->page->getIdentifier();
        $url = $this->pageHelper->getPageUrl($currentId);

        return $url;
    }
}
