<?php

declare(strict_types=1);

namespace Life\DisableCatalog\Helper;

use Life\DisableCatalog\Model\Config as ConfigModel;
use Magento\Framework\App\Helper\{AbstractHelper, Context};

class Config extends AbstractHelper
{
    public function __construct(
        Context $context,
        private readonly ConfigModel $config
    ) {
        parent::__construct($context);
    }

    public function getConfig(): ConfigModel
    {
        return $this->config;
    }

    /**
     * @param int|string|null $scopeCode
     */
    public function getDisable($scopeCode = null): bool
    {
        return $this->config->getDisable($scopeCode);
    }
}
