<?php

declare(strict_types=1);

namespace Life\DisableCatalog\Plugin;

use Life\DisableCatalog\Model\Config;
use Smile\ElasticsuiteCatalog\Model\Product\Indexer\Fulltext\Datasource\AttributeData;

class ElasticsuiteCatalogDisableProducts
{
    public function __construct(
        private readonly Config $config
    ) {}

    /**
     * @param mixed[] $result
     * @param int $storeId
     * @param mixed[] $indexData
     * @return mixed[]
     */
    public function afterAddData(AttributeData $subject, array $result, $storeId, array $indexData): array
    {
        if ($this->config->getDisable($storeId)) {
            return [];
        }

        return $result;
    }
}
