<?xml version="1.0" ?>
<config
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd"
>
    <preference for="AppearanceSourceSlider" type="AppearanceSourceSliderReset" />
    <virtualType name="AppearanceSourceSliderReset" type="Magento\PageBuilder\Model\Source\VisualSelect">
        <arguments>
            <argument name="optionsSize" xsi:type="string">large</argument>
            <argument name="optionsData" xsi:type="array">
                <item name="1" xsi:type="array">
                    <item name="value" xsi:type="string">slider</item>
                    <item name="title" xsi:type="string" translate="true">Slider</item>
                    <item
                        name="icon"
                        xsi:type="string"
                    >Life_PageBuilderSliderAppearances::css/images/content-type/slider/appearance/slider.svg</item>
                </item>
                <item name="2" xsi:type="array">
                    <item name="value" xsi:type="string">carousel</item>
                    <item name="title" xsi:type="string" translate="true">Carousel</item>
                    <item
                        name="icon"
                        xsi:type="string"
                    >Life_PageBuilderSliderAppearances::css/images/content-type/slider/appearance/carousel.svg</item>
                </item>
                <item name="3" xsi:type="array">
                    <item name="value" xsi:type="string">image_slider</item>
                    <item name="title" xsi:type="string" translate="true">Image Slider</item>
                    <item
                        name="icon"
                        xsi:type="string"
                    >Life_PageBuilderSliderAppearances::css/images/content-type/slider/appearance/carousel.svg</item>
                </item>
            </argument>
        </arguments>
    </virtualType>
</config>
