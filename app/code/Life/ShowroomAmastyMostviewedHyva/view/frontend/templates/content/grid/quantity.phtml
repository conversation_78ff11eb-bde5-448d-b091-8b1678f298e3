<?php

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\ProductStockItem;
use Hyva\Theme\ViewModel\HeroiconsOutline;
use Magento\Catalog\Block\Product\View;
use Magento\Catalog\Model\Product;
use Magento\Framework\Escaper;

// phpcs:disable Generic.WhiteSpace.ScopeIndent.Incorrect

/** @var View $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */

/** @var Product $product */
$product = $block->getProduct();

/** @var HeroiconsOutline $heroicons */
$heroicons = $viewModels->require(HeroiconsOutline::class);

/** @var ProductStockItem $stockItemViewModel */
$stockItemViewModel = $viewModels->require(ProductStockItem::class);
$minSalesQty = $stockItemViewModel->getMinSaleQty($product);
$maxSalesQty = $stockItemViewModel->getMaxSaleQty($product);
$defaultQty = $block->getProductDefaultQty() * 1;

$step = $stockItemViewModel->getQtyIncrements($product)
    ? $stockItemViewModel->getQtyIncrements($product)
    : null;

/**
 * sets minimum and maximum values taking into account the values set in the admin,
 * but taking into account the value of Qty Increments
 */
if ($step) {
    $minSalesQty = ceil($minSalesQty / $step) * $step;
    $maxSalesQty = floor($maxSalesQty / $step) * $step;
    $defaultQty = ceil($defaultQty / $step) * $step;
}

$maxSalesQtyLength = ($maxSalesQty ? strlen((string)$maxSalesQty) : 4)
    + (/* add one if decimal for separator */
    (int)$stockItemViewModel->isQtyDecimal($product));


?>
<?php

if (!$block->shouldRenderQuantity()) {
    return;
}

?>
<script>
    function initQtyField() {

        function findPathParam(key) {
            // get all path pairs after BASE_URL/front_name/action_path/action
            const baseUrl = (BASE_URL.substring(0, 2) === '//' ? 'http:' : '') + BASE_URL;
            const baseUrlParts = (new URL(baseUrl)).pathname.replace(/\/$/, '').split('/');
            const pathParts = window.location.pathname.split('/').slice(baseUrlParts.length + 3);
            for (let i = 0; i < pathParts.length; i += 2) {
                if (pathParts[i] === key && pathParts.length > i) {
                    return pathParts[i + 1];
                }
            }
        }

        return {
            qty: <?= /** @noEscape */ $defaultQty ?>,
            itemId: (new URLSearchParams(window.location.search)).get('id') || findPathParam('id'),
            productId: '<?= (int)$product->getId() ?>',
            <?php /* populate the qty when editing a product from the cart */ ?>
            onGetCartData: function onGetCartData(data, $dispatch) {
                const cart = data && data.data && data.data.cart;
                if (this.itemId && cart && cart.items) {
                    const cartItem = cart.items.find((item) => {
                        return item.item_id === this.itemId && item.product_id === this.productId;
                    });
                    if (cartItem && cartItem.qty) {
                        this.qty = cartItem.qty;
                        $dispatch('update-qty-' + this.productId, this.qty);
                    }
                }
            }
        };
    }
</script>
<div x-data="initQtyField()"
     x-init="$dispatch('update-qty-<?= (int)$product->getId() ?>', qty)"
>
    <?php if ($product->isSaleable()): ?>
        <div class="flex">
            <?php $btn_base_class = 'inline-flex items-center justify-center w-12 aspect-square border-y border-secondary'; ?>
            <button
                @click="qty--"
                class="<?= $btn_base_class ?> border-l rounded-l-lg"
            >
                <?= $heroicons->minusHtml('', 18, 18, ['aria-hidden' => 'true']); ?>
                <span class="sr-only"><?= $escaper->escapeHtml(__('Substract 1')) ?></span>
            </button>
            <label for="qty[<?= (int)$product->getId() ?>]"
                   class="sr-only"
            >
                <?= $escaper->escapeHtml(__('Quantity')) ?>
            </label>
            <input
                aria-live="polite"
                name="qty"
                @private-content-loaded.window="onGetCartData($event.detail, $dispatch)"
                id="qty[<?= (int)$product->getId() ?>]"
                form="product_addtocart_form_<?= (int)$product->getId() ?>"
                <?php if ($stockItemViewModel->isQtyDecimal($product)): ?>
                    type="text"
                    pattern="[0-9]+(\.[0-9]{1,<?= /** @noEscape */
                    $maxSalesQtyLength ?>})?"
                    inputmode="decimal"
                <?php else: ?>
                    type="number"
                    pattern="[0-9]{0,<?= /** @noEscape */
                    $maxSalesQtyLength ?>}"
                    inputmode="numeric"
                    <?php if ($minSalesQty): ?>min="<?= /** @noEscape */
                    $minSalesQty ?>"<?php endif; ?>
                    <?php if ($maxSalesQty): ?>max="<?= /** @noEscape */
                    $maxSalesQty ?>"<?php endif; ?>
                    <?php if ($step): ?>step="<?= /** @noEscape */
                    $step ?>"<?php endif; ?>
                <?php endif; ?>
                :value="qty"
                value="<?= $block->getProductDefaultQty() * 1 ?>"
                class="no-spin-buttons input-unstyled w-12 aspect-square border-secondary text-center font-bold invalid:ring-2 invalid:ring-red-500"
                x-model.number="qty"
                @input="$dispatch('update-qty-<?= (int)$product->getId() ?>', qty)"
            />
            <button
                @click="qty++"
                class="<?= $btn_base_class ?> border-r rounded-r-lg"
            >
                <?= $heroicons->plusHtml('', 18, 18, ['aria-hidden' => 'true']); ?>
                <span class="sr-only"><?= $escaper->escapeHtml(__('Add 1')) ?></span>
            </button>
        </div>
    <?php endif; ?>
</div>
