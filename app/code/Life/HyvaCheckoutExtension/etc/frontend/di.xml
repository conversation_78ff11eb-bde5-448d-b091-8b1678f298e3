<?xml version="1.0"?>
<config
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd"
>
    <!-- Icon renderer plugins -->
    <type name="Hyva\Checkout\Model\MethodMetaData">
        <plugin
            name="life_hyva_checkout_set_can_render_icon"
            type="Life\HyvaCheckoutExtension\Plugin\RenderPaymentMethodIcons"
        />
    </type>
    <type name="Hyva\Checkout\Model\MethodMetaDataInterface">
        <plugin
            name="life_hyva_checkout_popuplate_icon_data"
            type="Life\HyvaCheckoutExtension\Plugin\PopulateIconData"
        />
    </type>
    <type name="Magento\Checkout\Model\ConfigProviderInterface">
        <plugin
            name="life_hyva_checkout_update_shipping_total"
            type="Life\HyvaCheckoutExtension\Plugin\UpdateShippingTotal"
        />
    </type>
    <type name="Hyva\Checkout\ViewModel\Checkout\PriceSummary\TotalSegments">
        <plugin
            name="life_hyva_checkout_fix_shipping_total_amount"
            type="Life\HyvaCheckoutExtension\Plugin\ViewModel\Checkout\TotalsSegments"
        />
    </type>
    <type name="Hyva\Checkout\Magewire\Checkout\PriceSummary\CartItems">
        <plugin
            name="life_hyva_checkout_use_simple_product_image"
            type="Life\HyvaCheckoutExtension\Plugin\CartItems\UseSimpleProductImage"
        />
    </type>

    <type name="Hyva\Checkout\Model\Form\EntityForm\EavAttributeShippingAddressForm">
        <arguments>
            <!-- Default base modifiers have positions between 900 and 1000 -->
            <argument name="entityFormModifiers" xsi:type="array">
                <item
                    name="disable_autocomplete"
                    sortOrder="1100"
                    xsi:type="object"
                >Life\HyvaCheckoutExtension\Model\Form\EntityFormModifier\DisableAutocomplete</item>
                <item
                    name="add_on_change_validation"
                    sortOrder="1110"
                    xsi:type="object"
                >Life\HyvaCheckoutExtension\Model\Form\EntityFormModifier\ValidateOnChange</item>
            </argument>
        </arguments>
    </type>
    <type name="Hyva\Checkout\Model\Form\EntityForm\EavAttributeBillingAddressForm">
        <arguments>
            <!-- Default base modifiers have positions between 900 and 1000 -->
            <argument name="entityFormModifiers" xsi:type="array">
                <item
                    name="disable_autocomplete"
                    sortOrder="1100"
                    xsi:type="object"
                >Life\HyvaCheckoutExtension\Model\Form\EntityFormModifier\DisableAutocomplete</item>
                <item
                    name="add_on_change_validation"
                    sortOrder="1110"
                    xsi:type="object"
                >Life\HyvaCheckoutExtension\Model\Form\EntityFormModifier\ValidateOnChange</item>
            </argument>
        </arguments>
    </type>
</config>
