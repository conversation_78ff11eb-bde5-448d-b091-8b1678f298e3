<?php

declare(strict_types=1);

namespace Life\ProductAttributeMeasurementUnit\Plugin\Attributes;

use Life\ProductAttributeMeasurementUnit\ViewModel\AttributeMeasurement;
use Magento\Catalog\Block\Product\View\Attributes;

class AddMeasurementUnitToValue
{
    public function __construct(
        private readonly AttributeMeasurement $attributeMeasurement
    ) {}

    /**
     * @see Attributes::getAdditionalData()
     *
     * @param mixed[] $result
     * @return mixed[]
     */
    public function afterGetAdditionalData(
        Attributes $subject,
        array $result
    ): array {
        foreach ($result as $attributeCode => &$data) {
            if (
                isset($data['value'])
                && ($unit = $this->attributeMeasurement->getMeasurementUnit($attributeCode))
            ) {
                $data['value'] .= ' ' . $unit;
            }
        }

        return $result;
    }
}
