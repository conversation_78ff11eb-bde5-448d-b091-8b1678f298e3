<?xml version="1.0" ?>
<config
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd"
>
    <preference for="AppearanceSourceSlide" type="AppearanceSourceSlideReset" />
    <virtualType name="AppearanceSourceSlideReset" type="Magento\PageBuilder\Model\Source\VisualSelect">
        <arguments>
            <argument name="optionsSize" xsi:type="string">large</argument>
            <argument name="optionsData" xsi:type="array">
                <item name="0" xsi:type="array">
                    <item name="value" xsi:type="string">collage-left</item>
                    <item name="title" xsi:type="string" translate="true">Collage Left</item>
                    <item
                        name="icon"
                        xsi:type="string"
                    >Life_PageBuilderContentTypeSlide::css/images/content-type/slide/appearance/collage-left.svg</item>
                </item>
                <item name="1" xsi:type="array">
                    <item name="value" xsi:type="string">category-doorway</item>
                    <item name="title" xsi:type="string" translate="true">Category Doorway</item>
                    <item
                        name="icon"
                        xsi:type="string"
                    >Life_PageBuilderContentTypeSlide::css/images/content-type/slide/appearance/category-doorway.svg</item>
                </item>
            </argument>
        </arguments>
    </virtualType>
</config>
