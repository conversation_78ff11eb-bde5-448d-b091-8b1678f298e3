<?xml version="1.0" encoding="UTF-8" ?>
<form
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd"
>
    <fieldset name="appearance_fieldset">
        <field name="slide_width" sortOrder="40" formElement="select">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="default" xsi:type="string">wide</item>
                </item>
            </argument>
            <settings>
                <dataType>text</dataType>
                <label translate="true">Width</label>
            </settings>
            <formElements>
                <select>
                    <settings>
                        <options>
                            <option name="wide" xsi:type="array">
                                <item name="value" xsi:type="string">wide</item>
                                <item name="label" xsi:type="string" translate="true">Wide</item>
                            </option>
                            <option name="narrow" xsi:type="array">
                                <item name="value" xsi:type="string">narrow</item>
                                <item name="label" xsi:type="string" translate="true">Narrow</item>
                            </option>
                        </options>
                    </settings>
                </select>
            </formElements>
        </field>
    </fieldset>
    <fieldset name="general">
        <field name="hide_image_overlay" sortOrder="50" formElement="checkbox">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="default" xsi:type="string">false</item>
                </item>
            </argument>
            <settings>
                <dataType>text</dataType>
                <label translate="true">Add Gradient Overlay</label>
            </settings>
            <formElements>
                <checkbox>
                    <settings>
                        <valueMap>
                            <map name="false" xsi:type="string">false</map>
                            <map name="true" xsi:type="string">true</map>
                        </valueMap>
                        <prefer>toggle</prefer>
                    </settings>
                </checkbox>
            </formElements>
        </field>
    </fieldset>
</form>
