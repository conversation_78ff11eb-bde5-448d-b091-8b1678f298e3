<?php

declare(strict_types=1);

namespace Life\MultiSafepayPartialPayment\Model\Config\Backend\Serialized;

use Magento\Config\Model\Config\Backend\Serialized\ArraySerialized;

class Options extends ArraySerialized
{
    public function beforeSave(): ArraySerialized
    {
        $data = $this->getValue();

        if (is_array($data)) {
            if (isset($data['__empty'])) {
                unset($data['__empty']);
            }

            foreach ($data as $key => $row) {
                if (empty($row['percentage'])){
                    unset($data[$key]);
                }
            }
        }
        $this->setValue($data);

        return parent::beforeSave();
    }
}
