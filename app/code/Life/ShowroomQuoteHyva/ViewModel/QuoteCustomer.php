<?php

declare(strict_types=1);

namespace Life\ShowroomQuoteHyva\ViewModel;

use Magento\Checkout\Model\Session;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Customer\Api\Data\CustomerInterface;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\View\Element\Block\ArgumentInterface;
use Magento\Quote\Api\Data\CartInterface;
use Psr\Log\LoggerInterface;

/**
 * Class for quote's customer
 */
class QuoteCustomer implements ArgumentInterface
{

    /**
     * Constructor
     *
     * @param Session $checkoutSession
     * @param CustomerRepositoryInterface $customerRepository
     * @param LoggerInterface $logger
     */
    public function __construct(
        private readonly Session $checkoutSession,
        private readonly CustomerRepositoryInterface $customerRepository,
        private readonly LoggerInterface $logger
    ) {}

    /**
     * Get Quote
     *
     * @return CartInterface
     * @throws LocalizedException
     * @throws NoSuchEntityException
     */
    public function getQuote(): CartInterface
    {
        return $this->checkoutSession->getQuote();
    }

    /**
     * Get quote real customer
     */
    public function getQuoteRealCustomer(): ?CustomerInterface
    {
        $realCustomer = null;
        try {
            $quote = $this->getQuote();
            if ($quote->getRealCustomerId()) {
                $realCustomer = $this->customerRepository->getById($quote->getRealCustomerId());
            }
        } catch (LocalizedException $exception) {
            $this->logger->error($exception->getMessage());
        }
        return $realCustomer;
    }
}
