<?php

declare(strict_types=1);

namespace Life\Catalog\ViewModel;

use Life\ProductAttributeMeasurementUnit\ViewModel\AttributeMeasurement;
use Life\ProductsConnector\Setup\Patch\Data\AddSetItemsProductAttribute;
use Magento\Catalog\Api\Data\ProductInterface;
use Magento\Catalog\Api\ProductRepositoryInterface;
use Magento\Catalog\Model\Product;
use Magento\ConfigurableProduct\Model\Product\Type\Configurable;
use Magento\Eav\Model\Entity\Attribute\AbstractAttribute;
use Magento\Framework\Phrase;
use Magento\Framework\Pricing\PriceCurrencyInterface;
use Magento\Framework\View\Element\Block\ArgumentInterface;
use Magento\Store\Model\StoreManagerInterface;

/**
 * Class is created based on logic inside \Magento\Catalog\Block\Product\View\Attributes.
 * Class prepare attributes for the child products of configurable.
 */
class ProductAttributes implements ArgumentInterface
{
    public function __construct(
        private readonly PriceCurrencyInterface $priceCurrency,
        private readonly ProductRepositoryInterface $productRepository,
        private readonly StoreManagerInterface $storeManager,
        private readonly AttributeMeasurement $attributeMeasurement
    ) {
    }

    /**
     * Method looks for child products and build Attribute data for them
     * if it is not configurable - we left default logic.
     * @param ProductInterface $parentProduct
     * @return array
     */
    public function getChildProductAttributes(ProductInterface $parentProduct): array
    {
        $result = [];
        $setSkus = $this->getSetItemsSkus($parentProduct);

        if ($parentProduct->getTypeId() === Configurable::TYPE_CODE) {
            $children = $parentProduct->getTypeInstance()->getUsedProducts($parentProduct);
            foreach ($children as $child) {
                $result[$child->getId()] = [
                    'sku' =>
                        [
                            'code' => 'sku',
                            'label' => __('Sku'),
                            'value' => $child->getSku()
                        ],
                    ...$this->getAdditionalData($child)
                ];
                $setSkus = [...$setSkus, ...$this->getSetItemsSkus($child)];
            }
        }

        foreach ($setSkus as $sku) {
            try {
                $setItem = $this->productRepository->get($sku);
            } catch (\Exception $e) {
                continue;
            }

            $result[$sku] = [
                'sku' =>
                    [
                        'code' => 'sku',
                        'label' => __('Sku'),
                        'value' => $sku
                    ],
                ...$this->getAdditionalData($setItem)
            ];
        }

        return $result;
    }

    /**
     * @param ProductInterface $product
     * @return string[][]
     */
    public function getSetItemsByProduct(ProductInterface $product): array
    {
        $setItems = [
            'default' => $this->prepareSetItemsData($product, $this->getSetItemsSkus($product))
        ];

        /** @var ProductInterface&Product $product */
        if ($product->getTypeId() !== Configurable::TYPE_CODE) {
            return $setItems;
        }

        $children = $product->getTypeInstance()->getUsedProducts($product);
        foreach ($children as $child) {
            if ($skus = $this->getSetItemsSkus($child)) {
                $setItems[$child->getId()] = $this->prepareSetItemsData($product, $skus);
            }
        }

        return $setItems;
    }

    /**
     * @param ProductInterface $product
     * @param string[] $skus
     * @return string[]
     */
    protected function prepareSetItemsData(ProductInterface $product, array $skus): array
    {
        $skusData = [];

        foreach ($skus as $sku) {
            $skusData[$sku] = $this->getProductTypeBySku($product, $sku);
        }

        return $skusData;
    }

    protected function getProductTypeBySku(ProductInterface $product, string $sku): string
    {
        /** @var ProductInterface&Product $product */
        $productId = $product->getIdBySku($sku);

        if (!$productId) {
            return $sku;
        }

        $typeId = $product->getResource()->getAttributeRawValue( /** @phpstan-ignore-line */
            $productId,
            'item_type',
            $this->storeManager->getStore()->getId()
        );

        if (is_array($typeId)) {
            return $sku;
        }

        $attribute = $product->getResource()->getAttribute('item_type'); /** @phpstan-ignore-line */
        if ($attribute->usesSource()) {
            return $attribute->getSource()->getOptionText((string)$typeId);
        }

        return (string)$typeId;
    }

    /**
     * @return string[]
     */
    public function getSetItemsSkus(ProductInterface $product): array
    {
        $setItems = $product->getResource()->getAttributeRawValue(
            $product->getId(),
            AddSetItemsProductAttribute::ATTRIBUTE_CODE,
            $this->storeManager->getStore()->getId()
        );

        if (is_array($setItems)) {
            return $setItems;
        }

        return array_filter(explode(',', trim((string)$setItems)), 'strlen');
    }

    /**
     * method copies logic from core class \Magento\Catalog\Block\Product\View\Attributes::getAdditionalData
     * but adds parameter ProductInterface
     * @param ProductInterface $product
     * @param string[] $excludeAttr
     * @return array
     */
    private function getAdditionalData(ProductInterface $product, array $excludeAttr = []): array
    {
        $data = [];
        $attributes = $product->getAttributes();
        foreach ($attributes as $attribute) {
            if ($this->isVisibleOnFrontend($attribute, $excludeAttr)) {
                $value = $attribute->getFrontend()->getValue($product);

                if ($value instanceof Phrase) {
                    $value = (string)$value;
                } elseif ($attribute->getFrontendInput() == 'price' && is_string($value)) {
                    $value = $this->priceCurrency->convertAndFormat($value);
                }

                if (is_string($value) && strlen(trim($value))) {
                    $value .= ' ' . $this->attributeMeasurement->getMeasurementUnit($attribute->getAttributeCode());

                    $data[$attribute->getAttributeCode()] = [
                        'label' => $attribute->getStoreLabel(),
                        'value' => $value,
                        'code' => $attribute->getAttributeCode(),
                    ];
                }
            }
        }

        return $data;
    }

    /**
     * @param AbstractAttribute $attribute
     * @param string[] $excludeAttr
     * @return bool
     */
    private function isVisibleOnFrontend(
        AbstractAttribute $attribute,
        array $excludeAttr
    ): bool {

        return ($attribute->getIsVisibleOnFront() && !in_array($attribute->getAttributeCode(), $excludeAttr));
    }
}
