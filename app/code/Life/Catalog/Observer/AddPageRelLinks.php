<?php

declare(strict_types=1);

namespace Life\Catalog\Observer;

use Magento\Framework\Event\{Observer, ObserverInterface};
use Magento\Framework\View\Page\Config;
use Magento\Framework\UrlInterface;

class AddPageRelLinks implements ObserverInterface
{
    public function __construct(
        private readonly Config $pageConfig,
        private readonly UrlInterface $urlBuilder
    ) {}

    public function execute(Observer $observer)
    {
        if ($observer->getEvent()->getFullActionName() !== 'catalog_category_view') {
            return;
        }

        /** @var \Magento\Catalog\Block\Product\ListProduct $productListBlock */
        $productListBlock = $observer->getEvent()->getLayout()->getBlock('category.products.list');
        if (!$productListBlock) {
            return;
        }

        /** @var \Magento\Catalog\Block\Product\ProductList\Toolbar $toolbarBlock */
        $toolbarBlock = $productListBlock->getToolbarBlock();
        if (!$toolbarBlock) {
            return;
        }

        /** @var \Magento\Theme\Block\Html\Pager $pagerBlock */
        $pagerBlock = $toolbarBlock->getChildBlock('product_list_toolbar_pager');
        if (!$pagerBlock) {
            return;
        }

        $pagerBlock->setAvailableLimit($toolbarBlock->getAvailableLimit())
            ->setLimit($toolbarBlock->getLimit())
            ->setCollection($productListBlock->getLayer()->getProductCollection());

        if ($pagerBlock->getCurrentPage() > 1) {
            $this->pageConfig->addRemotePageAsset(
                $this->getPageUrl([
                    $pagerBlock->getPageVarName() => $pagerBlock->getCollection()->getCurPage(-1)
                ]),
                'link_rel',
                ['attributes' => ['rel' => 'prev']]
            );
        }

        if ($pagerBlock->getCurrentPage() < $pagerBlock->getLastPageNum()) {
            $this->pageConfig->addRemotePageAsset(
                $this->getPageUrl([
                    $pagerBlock->getPageVarName() => $pagerBlock->getCollection()->getCurPage(+1)
                ]),
                'link_rel',
                ['attributes' => ['rel' => 'next']]
            );
        }
    }

    protected function getPageUrl(array $params = []): string
    {
        $urlParams = [
            '_current' => false,
            '_escape' => true,
            '_use_rewrite' => true,
            '_query' => $params
        ];

        return $this->urlBuilder->getUrl('*/*/*', $urlParams);
    }
}
