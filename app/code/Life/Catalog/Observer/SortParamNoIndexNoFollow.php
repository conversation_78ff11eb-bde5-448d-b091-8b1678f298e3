<?php

declare(strict_types=1);

namespace Life\Catalog\Observer;

use Magento\Framework\App\RequestInterface;
use Magento\Framework\Event\{Observer, ObserverInterface};
use Magento\Framework\View\Page\Config;

class SortParamNoIndexNoFollow implements ObserverInterface
{
    public function __construct(
        private readonly Config $pageConfig,
        private readonly RequestInterface $request
    ) {}

    public function execute(Observer $observer)
    {
        // Don't index pages with sorting parameters in URL
        if (
            $this->request->getParam('product_list_order')
            || $this->request->getParam('product_list_dir')
        ) {
            $this->pageConfig->setRobots('NOINDEX,NOFOLLOW');
        }
    }
}
