<?xml version="1.0" ?>
<config
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:noNamespaceSchemaLocation="urn:magento:framework:Event/etc/events.xsd"
>
    <event name="layout_generate_blocks_after">
        <observer name="add_page_rel_links" instance="Life\Catalog\Observer\AddPageRelLinks" />
        <observer
            name="add_no_index_no_follow_for_layered_pages"
            instance="Life\Catalog\Observer\LayerNavigationNoIndexNoFollow"
        />
        <observer
            name="add_no_index_no_follow_for_pages_with_sort_param"
            instance="Life\Catalog\Observer\SortParamNoIndexNoFollow"
        />
    </event>
</config>
