<?xml version="1.0" ?>
<config
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd"
>
    <system>
        <section
            id="life_catalog"
            translate="label"
            type="text"
            sortOrder="300"
            showInDefault="1"
            showInWebsite="1"
            showInStore="1"
        >
            <label>Catalog</label>
            <tab>life</tab>
            <resource>Life_Catalog::settings</resource>
            <group
                id="lists"
                translate="label"
                type="text"
                sortOrder="10"
                showInDefault="1"
                showInWebsite="1"
                showInStore="1"
            >
                <label>Related, Upsells, Cross-sells and Serie Lists</label>
                <field
                    id="upsells_title"
                    type="text"
                    translate="label comment"
                    sortOrder="10"
                    showInDefault="1"
                    showInWebsite="1"
                    showInStore="1"
                >
                    <label>Upsells Title</label>
                </field>
                <field
                    id="cross_sells_title"
                    type="text"
                    translate="label comment"
                    sortOrder="20"
                    showInDefault="1"
                    showInWebsite="1"
                    showInStore="1"
                >
                    <label>Cross-sells Title</label>
                </field>
                <field
                    id="serie_title"
                    type="text"
                    translate="label comment"
                    sortOrder="30"
                    showInDefault="1"
                    showInWebsite="1"
                    showInStore="1"
                >
                    <label>Serie Title</label>
                </field>
                <field
                    id="serie_description"
                    type="editor"
                    translate="label"
                    sortOrder="40"
                    showInDefault="1"
                    showInWebsite="1"
                    showInStore="1"
                >
                    <label>Serie Description</label>
                    <frontend_model>Life\ThemeConfigurations\Block\Adminhtml\System\Config\Editor</frontend_model>
                </field>
                <field
                    id="serie_bg_color"
                    translate="label"
                    type="select"
                    sortOrder="50"
                    showInDefault="1"
                    showInWebsite="1"
                    showInStore="1"
                >
                    <label>Serie Background Color</label>
                    <source_model>Life\Catalog\Model\Config\Source\BackgroundColor</source_model>
                </field>
            </group>
            <group
                id="price"
                translate="label"
                type="text"
                sortOrder="20"
                showInDefault="1"
                showInWebsite="1"
                showInStore="1"
            >
                <label>Price</label>
                <field
                    id="discount_percentage_threshold"
                    translate="label comment"
                    type="text"
                    sortOrder="10"
                    showInDefault="1"
                    showInWebsite="1"
                    showInStore="1"
                >
                    <label>Display discount % for price below</label>
                    <comment><![CDATA[For product prices above set amount, the discount is shown as an exact amount.]]></comment>
                    <validate>validate-digits</validate>
                </field>
            </group>
        </section>
    </system>
</config>
