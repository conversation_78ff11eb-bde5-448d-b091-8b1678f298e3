<?php

declare(strict_types=1);

namespace Life\Catalog\Plugin;

use Magento\Catalog\Model\Product;

class FixImagesError
{
    /**
     * @see Product::getMediaGalleryImages()
     *
     * Sometimes the cache data seems to be an array instead of an object. In this case, unset data, which will force
     * the gallery images data to be initialized again
     * https://github.com/magento/magento2/issues/33965
     */
    public function beforeGetMediaGalleryImages(Product $subject)
    {
        if ($subject->hasData('media_gallery_images')) {
            $images = $subject->getData('media_gallery_images');
            if (!is_object($images)) {
                $subject->unsetData('media_gallery_images');
            }
        }
    }
}
