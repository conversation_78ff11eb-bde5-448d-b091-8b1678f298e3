<?php

declare(strict_types=1);

namespace Life\Catalog\Plugin;

use Magento\Catalog\Api\Data\CategoryInterface;
use Magento\Catalog\Api\Data\ProductInterface;
use Magento\Catalog\Helper\Data;
use Magento\Catalog\Model\Category;
use Magento\CatalogUrlRewrite\Model\CategoryUrlRewriteGenerator;
use Magento\Framework\App\RequestInterface;
use Magento\Framework\View\Page\Config;
use Magento\UrlRewrite\Model\UrlFinderInterface;
use Magento\UrlRewrite\Service\V1\Data\UrlRewrite;

class ChangeCategoryCanonicalUrl
{
    public function __construct(
        private readonly Data $catalogData,
        private readonly RequestInterface $request,
        private readonly UrlFinderInterface $urlFinder
    ) {}

    /**
     * @param string $url
     * @param string $contentType
     * @param mixed ...$args
     * @return mixed[]
     */
    public function beforeAddRemotePageAsset(
        Config $subject,
        $url,
        $contentType,
        ...$args
    ): array
    {
        if (
            ($contentType === 'canonical')
            && ($category = $this->getCategory())
            && (!$this->getProduct())
        ) {
            $url = $this->getNonRedirectCategoryUrl($category);

            // Strip parameters from category URL
            $url = explode('?', $url)[0];

            // Add page to canonical
            if ($page = $this->request->getParam('p')) {
                $url .= '?p=' . $page;
            }

            // Remove p=1 from canonical link - since we do not need it for the first page.
            if ($page == 1) {
                $url = str_replace('?p=1', '', $url);
            }
        }

        return [$url, $contentType, ...$args];
    }

    protected function getProduct(): ?ProductInterface
    {
        return $this->catalogData->getProduct();
    }

    protected function getCategory(): ?CategoryInterface
    {
        return $this->catalogData->getCategory();
    }

    protected function getNonRedirectCategoryUrl(CategoryInterface $category): string
    {
        /**
         * Inside Category::getUrl() the "UrlRewrite::REDIRECT_TYPE => 0" is missing, which will take
         * the category redirect from default store URL to current store URL, which is not good for canonical
         */
        /** @var CategoryInterface&Category $category */
        $rewrite = $this->urlFinder->findOneByData(
            [
                UrlRewrite::ENTITY_ID => $category->getId(),
                UrlRewrite::ENTITY_TYPE => CategoryUrlRewriteGenerator::ENTITY_TYPE,
                UrlRewrite::STORE_ID => $category->getStoreId(),
                UrlRewrite::REDIRECT_TYPE => 0
            ]
        );

        if ($rewrite) {
            return $category->getUrlInstance()->getDirectUrl($rewrite->getRequestPath());
        }

        return $category->getUrl();
    }
}
