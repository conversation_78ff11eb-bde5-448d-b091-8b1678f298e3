<?php

declare(strict_types=1);

namespace Life\WidgetButton\Model\Config\Source;

use Magento\Framework\Data\OptionSourceInterface;
use Magento\Framework\Phrase;

class Type implements OptionSourceInterface
{
    const PRIMARY_BUTTON = 'primary';
    const SECONDARY_BUTTON = 'secondary';
    const SIMPLE_LINK = 'link';

    /**
     * Get options in "key-value" format
     * @return Phrase[]
     */
    public function toArray(): array
    {
        return [
            self::PRIMARY_BUTTON => __('Primary Button'),
            self::SECONDARY_BUTTON => __('Secondary Button'),
            self::SIMPLE_LINK => __('Link'),
        ];
    }

    /**
     * Get options in ["value", "label"] format
     * @return array<mixed>
     */
    public function toOptionArray(): array
    {
        $options = $this->toArray();
        $formattedOptions = [];
        foreach ($options as $key => $option) {
            $formattedOptions[] = [
                'value' => $key,
                'label' => $option
            ];
        }

        return $formattedOptions;
    }
}
