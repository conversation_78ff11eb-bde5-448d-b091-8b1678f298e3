<?php

declare(strict_types=1);

use Life\WidgetButton\Block\Widget\Button;
use Life\WidgetButton\Model\Config\Source\Type;
use Magento\Framework\Escaper;

/** @var Escaper $escaper */
/** @var Button $block */

$ctaTitle = $block->getData('button_title');
$ctaLink = $block->getData('button_link');
$ctaTarget = $block->getData('button_target') ?: '_self';
$ctaType = $block->getData('button_type') ?: 'link';

?>
<?php if (!empty($ctaTitle) && !empty($ctaLink)): ?>
    <?php if ($ctaType === Type::SIMPLE_LINK): ?>
        <a
            href="<?= $escaper->escapeUrl($ctaLink) ?>"
            target="<?= $escaper->escapeHtmlAttr($ctaTarget) ?>"
        >
            <?= $escaper->escapeHtml($ctaTitle); ?>
        </a>
    <?php else: ?>
        <a
            href="<?= $escaper->escapeUrl($ctaLink) ?>"
            class="btn btn-<?= $escaper->escapeHtmlAttr($ctaType); ?>"
            target="<?= $escaper->escapeHtmlAttr($ctaTarget) ?>"
        >
            <span><?= $escaper->escapeHtml($ctaTitle); ?></span>
        </a>
    <?php endif; ?>
<?php endif; ?>
