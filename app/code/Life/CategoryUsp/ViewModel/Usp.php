<?php

declare(strict_types=1);

namespace Life\CategoryUsp\ViewModel;

use Life\CategoryUsp\Setup\Patch\Data\InstallCategoryUspAttribute;
use Magento\Catalog\Helper\Data;
use Magento\Catalog\Model\Category;
use Magento\Cms\Model\Template\FilterProvider;
use Magento\Framework\View\Element\Block\ArgumentInterface;

class Usp implements ArgumentInterface
{
    public function __construct(
        private readonly Data $catalogData,
        private readonly FilterProvider $filterProvider
    ) {}

    public function getDoorways(): array
    {
        static $doorways = null;

        if (
            is_null($doorways)
            && ($category = $this->getCategory())
        ) {
            try {
                $doorwaysText = $category->getData(InstallCategoryUspAttribute::ATTRIBUTE_CODE);
                if (is_string($doorwaysText) && trim($doorwaysText)) {
                    /**
                     * Trigger content parsing to add doorways to current category
                     * @see \Life\CategoryUsp\Block\Widget\CategoryUsp::getDoorways()
                     */
                    $this->filterProvider->getPageFilter()->filter($doorwaysText);

                    $doorways = $category->getData('usps') ?? [];
                    if (is_string($doorways)) {
                        $doorways = [];
                    }
                }
            } catch (\Exception $e) {
                // Do nothing
            }
        }

        return $doorways ?? [];
    }

    public function getCategory(): ?Category
    {
        return $this->catalogData->getCategory();
    }
}
