<?php

declare(strict_types=1);

use Hyva\Theme\ViewModel\SvgIcons;
use Life\CategoryUsp\ViewModel\Usp;

/** @var \Magento\Catalog\Block\Product\ListProduct $block */
/** @var \Magento\Framework\Escaper $escaper */
/** @var \Hyva\Theme\Model\ViewModelRegistry $viewModels */

/** @var SvgIcons $svgIcons */
$svgIcons = $viewModels->require(SvgIcons::class);

/** @var Usp $uspViewModel */
$uspViewModel = $viewModels->require(Usp::class);

$usps = $uspViewModel->getDoorways();

if (empty($usps)) {
    return '';
}
?>

<?php foreach (['desktop', 'mobile'] as $mode): ?>
    <?php foreach ($usps[$mode] as $line => $lineUsps): ?>
        <?php
            $backgroundColor = '';
            $textColor = 'text-black';
            $firstUsp = $lineUsps[0] ?? null;
            if ($firstUsp && !empty($firstUsp['link']['color'])) {
                $color = $firstUsp['link']['color'];
                $backgroundColor = 'bg-' . $color;
                if ($color === 'primary') {
                    $textColor = 'text-white';
                }
            }
        ?>
        <div
            x-data="categoryUsps($el, <?= /* @noEscape */ $line ?>)"
            @resize.window.debounce.10="checkIsMobileResolution(); repositionUsps()"
            class="usp-line w-full flex items-center justify-center rounded-md p-3 gap-x-10
                <?= $escaper->escapeHtmlAttr($backgroundColor); ?>
                <?= $escaper->escapeHtmlAttr($textColor); ?>"
            :style="{ order: `${position}`, gridColumn: `${gridSpace}` }"
            x-show="<?= ($mode === 'desktop') ? '!isMobile' : 'isMobile'; ?> && isVisible"
            x-cloak
        >
            <?php foreach ($lineUsps as $usp): ?>
                <?php
                    $title = trim($usp['title'] ?? '');
                    $link = trim($usp['link']['url'] ?? '');
                ?>
                <li class="items-center justify-center hidden first:flex lg:flex gap-x-2">
                    <?php if ($link): ?>
                        <a
                            class="flex items-center gap-x-2 hover:underline"
                            href="<?= $escaper->escapeHtmlAttr($link) ?>"
                            target="<?= $escaper->escapeHtmlAttr($usp['link']['target'] ?? '') ?>"
                        >
                    <?php endif ?>

                        <?php if ($image = trim($usp['image_url'] ?? '')) : ?>
                            <img
                                src="<?= $escaper->escapeHtmlAttr($image) ?>"
                                alt="<?= $escaper->escapeHtmlAttr($title) ?>"
                                class="max-w-[1rem]"
                            />
                        <?php else : ?>
                            <?= $svgIcons->checkHtml("w-4 h-4 text-primary-300 flex-shrink-0") ?>
                        <?php endif ?>

                        <div class="text-sm truncate lg:whitespace-normal">
                            <span class="usp-text--text"><?= /* @noEscape */ $title ?></span>
                        </div>

                    <?php if ($link): ?>
                        </a>
                    <?php endif ?>
                </li>
            <?php endforeach; ?>
        </div>
    <?php endforeach; ?>
<?php endforeach; ?>
