<?xml version="1.0"?>
<config
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd"
>
    <type name="Redkiwi\LibWidgetParameters\Model\Serializer\IncludeParameters">
        <arguments>
            <argument name="byType" xsi:type="array">
                <item name="Life\CategoryUsp\Block\Widget\CategoryUsp" xsi:type="array">
                    <item name="doorways" xsi:type="string">doorways</item>
                </item>
            </argument>
        </arguments>
    </type>

    <type name="Life\CategoryUsp\Model\Source\Background">
        <arguments>
            <argument name="colors" xsi:type="array">
                <item name="primary" xsi:type="string" translate="true">Dark</item>
                <item name="secondary" xsi:type="string" translate="true">Light</item>
                <item name="" xsi:type="string" translate="true">Transparent</item>
            </argument>
        </arguments>
    </type>

    <type name="Life\CategoryUsp\Block\Adminhtml\Form\Field\Renderer\Background">
        <arguments>
            <argument name="sourceModel" xsi:type="object">Life\CategoryUsp\Model\Source\Background</argument>
        </arguments>
    </type>
</config>
