<?xml version="1.0" ?>
<config
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_PageBuilder:etc/content_type.xsd"
>
    <type name="banner">
        <appearances>
            <appearance
                name="collage-left"
                master_template="Life_PageBuilderContentTypeBanner/content-type/banner/collage-left/master"
            >
                <elements>
                    <element name="main">
                        <style
                            name="display"
                            source="display"
                            converter="Magento_PageBuilder/js/converter/style/display"
                            preview_converter="Magento_PageBuilder/js/converter/style/preview/display"
                        />
                        <style
                            name="margins"
                            storage_key="margins_and_padding"
                            reader="Magento_PageBuilder/js/property/margins"
                            converter="Magento_PageBuilder/js/converter/style/margins"
                        />
                        <attribute name="name" source="data-content-type" />
                        <attribute name="appearance" source="data-appearance" />
                        <attribute name="background_color_type" source="data-background-color-type" />
                        <attribute name="background_style" source="data-background-style" />
                        <attribute name="border_radius_type" source="data-border-radius-type" />
                        <attribute name="show_button" source="data-show-button" />
                        <attribute name="show_overlay" source="data-show-overlay" />
                        <css name="css_classes" />
                    </element>
                    <element name="empty_link" />
                    <element name="wrapper">
                        <style name="background_color" source="background_color" />
                        <style
                            name="background_image"
                            source="background_image"
                            converter="Magento_PageBuilder/js/converter/style/background-image"
                            preview_converter="Magento_PageBuilder/js/converter/style/preview/background-image"
                            persistence_mode="write"
                        />
                        <style name="background_position" source="background_position" />
                        <style name="background_size" source="background_size" />
                        <style name="background_repeat" source="background_repeat" />
                        <style name="background_attachment" source="background_attachment" />
                        <style
                            name="border"
                            source="border_style"
                            converter="Magento_PageBuilder/js/converter/style/border-style"
                        />
                        <style name="border_color" source="border_color" />
                        <style
                            name="border_width"
                            source="border_width"
                            converter="Magento_PageBuilder/js/converter/style/border-width"
                        />
                        <style
                            name="border_radius"
                            source="border_radius"
                            converter="Magento_PageBuilder/js/converter/style/remove-px"
                        />
                        <style
                            name="padding"
                            storage_key="margins_and_padding"
                            reader="Magento_PageBuilder/js/property/paddings"
                            converter="Magento_PageBuilder/js/converter/style/paddings"
                        />
                        <style
                            name="min_height"
                            source="min_height"
                            converter="Magento_PageBuilder/js/converter/style/min-height"
                        />
                        <style name="text_align" source="text_align" />
                        <attribute name="background_images" source="data-background-images" />
                        <attribute name="background_type" source="data-background-type" />
                        <attribute
                            name="video_source"
                            source="data-video-src"
                            converter="Magento_PageBuilder/js/content-type/video/converter/attribute/src"
                        />
                        <attribute name="video_loop" source="data-video-loop" />
                        <attribute name="video_play_only_visible" source="data-video-play-only-visible" />
                        <attribute name="video_lazy_load" source="data-video-lazy-load" />
                        <attribute
                            name="video_fallback_image"
                            source="data-video-fallback-src"
                            converter="Magento_PageBuilder/js/converter/attribute/src"
                            preview_converter="Magento_PageBuilder/js/converter/attribute/preview/src"
                        />
                    </element>
                    <element name="overlay">
                        <style
                            name="overlay_color"
                            source="background_color"
                            converter="Magento_PageBuilder/js/content-type/banner/converter/style/overlay-background-color"
                            persistence_mode="write"
                        />
                        <attribute
                            name="overlay_color_attribute"
                            storage_key="overlay_color"
                            source="data-overlay-color"
                            converter="Magento_PageBuilder/js/content-type/banner/converter/attribute/overlay-color"
                        />
                        <attribute name="alt" source="aria-label" />
                        <attribute name="title_attribute" source="title" />
                    </element>
                    <element name="content">
                        <html name="message" preview_converter="Magento_PageBuilder/js/converter/html/directive" />
                    </element>
                    <element name="video_overlay">
                        <style
                            name="video_overlay_color"
                            source="background_color"
                            converter="Magento_PageBuilder/js/converter/style/video-overlay-color"
                            persistence_mode="write"
                        />
                        <attribute
                            name="video_overlay_color_attribute"
                            storage_key="video_overlay_color"
                            source="data-video-overlay-color"
                            converter="Magento_PageBuilder/js/converter/attribute/video-overlay-color"
                        />
                    </element>
                    <element name="button">
                        <style
                            name="opacity"
                            source="opacity"
                            converter="Magento_PageBuilder/js/content-type/banner/converter/style/button-opacity"
                            persistence_mode="write"
                        />
                        <style
                            name="visibility"
                            source="visibility"
                            converter="Magento_PageBuilder/js/content-type/banner/converter/style/button-visibility"
                            persistence_mode="write"
                        />
                        <css name="button_type">
                            <filter>
                                <class source="pagebuilder-banner-button" />
                            </filter>
                        </css>
                        <attribute
                            name="link_url"
                            reader="Magento_PageBuilder/js/property/link"
                            persistence_mode="read"
                        />
                        <attribute
                            name="virtual_link_href"
                            storage_key="link_url"
                            source="href"
                            converter="Magento_PageBuilder/js/converter/attribute/link-href"
                            persistence_mode="write"
                        />
                        <attribute
                            name="virtual_link_target"
                            storage_key="link_url"
                            source="target"
                            converter="Magento_PageBuilder/js/converter/attribute/link-target"
                            persistence_mode="write"
                        />
                        <attribute
                            name="virtual_link_type"
                            storage_key="link_url"
                            source="data-link-type"
                            converter="Magento_PageBuilder/js/converter/attribute/link-type"
                            persistence_mode="write"
                        />
                        <attribute name="title_attribute" source="title" />
                        <html
                            name="button_text"
                            converter="Life_PageBuilderContentTypeBanner/js/converter/html/tag-remove"
                        />
                    </element>
                </elements>
                <converters>
                    <converter name="background_type" component="Magento_PageBuilder/js/mass-converter/background-type">
                        <config>
                            <item name="attribute_name" value="background_type" />
                            <item name="desktop_image_variable" value="background_image" />
                            <item name="mobile_image_variable" value="mobile_image" />
                            <item name="video_source_variable" value="video_source" />
                            <item name="video_fallback_image_variable" value="video_fallback_image" />
                            <item name="video_overlay_color_variable" value="video_overlay_color" />
                        </config>
                    </converter>
                    <converter
                        name="background_images"
                        component="Magento_PageBuilder/js/mass-converter/background-images"
                    >
                        <config>
                            <item name="attribute_name" value="background_images" />
                            <item name="desktop_image_variable" value="background_image" />
                            <item name="mobile_image_variable" value="mobile_image" />
                        </config>
                    </converter>
                </converters>
            </appearance>
            <appearance
                name="collage-left-round"
                preview_template="Magento_PageBuilder/content-type/banner/collage-left/preview"
                master_template="Life_PageBuilderContentTypeBanner/content-type/banner/collage-left-round/master"
                reader="Magento_PageBuilder/js/master-format/read/configurable"
            >
                <elements>
                    <element name="main">
                        <style
                            name="display"
                            source="display"
                            converter="Magento_PageBuilder/js/converter/style/display"
                            preview_converter="Magento_PageBuilder/js/converter/style/preview/display"
                        />
                        <style
                            name="margins"
                            storage_key="margins_and_padding"
                            reader="Magento_PageBuilder/js/property/margins"
                            converter="Magento_PageBuilder/js/converter/style/margins"
                        />
                        <attribute name="name" source="data-content-type" />
                        <attribute name="appearance" source="data-appearance" />
                        <attribute name="background_color_type" source="data-background-color-type" />
                        <attribute name="background_style" source="data-background-style" />
                        <attribute name="border_radius_type" source="data-border-radius-type" />
                        <attribute name="show_button" source="data-show-button" />
                        <attribute name="show_overlay" source="data-show-overlay" />
                        <css name="css_classes" />
                    </element>
                    <element name="empty_link" />
                    <element name="wrapper">
                        <style name="background_color" source="background_color" />
                        <style
                            name="background_image"
                            source="background_image"
                            converter="Magento_PageBuilder/js/converter/style/background-image"
                            preview_converter="Magento_PageBuilder/js/converter/style/preview/background-image"
                            persistence_mode="write"
                        />
                        <style name="background_position" source="background_position" />
                        <style name="background_size" source="background_size" />
                        <style name="background_repeat" source="background_repeat" />
                        <style name="background_attachment" source="background_attachment" />
                        <style
                            name="border"
                            source="border_style"
                            converter="Magento_PageBuilder/js/converter/style/border-style"
                        />
                        <style name="border_color" source="border_color" />
                        <style
                            name="border_width"
                            source="border_width"
                            converter="Magento_PageBuilder/js/converter/style/border-width"
                        />
                        <style
                            name="border_radius"
                            source="border_radius"
                            converter="Magento_PageBuilder/js/converter/style/remove-px"
                        />
                        <style
                            name="padding"
                            storage_key="margins_and_padding"
                            reader="Magento_PageBuilder/js/property/paddings"
                            converter="Magento_PageBuilder/js/converter/style/paddings"
                        />
                        <style
                            name="min_height"
                            source="min_height"
                            converter="Magento_PageBuilder/js/converter/style/min-height"
                        />
                        <style name="text_align" source="text_align" />
                        <attribute name="background_images" source="data-background-images" />
                        <attribute name="background_type" source="data-background-type" />
                        <attribute
                            name="video_source"
                            source="data-video-src"
                            converter="Magento_PageBuilder/js/content-type/video/converter/attribute/src"
                        />
                        <attribute name="video_loop" source="data-video-loop" />
                        <attribute name="video_play_only_visible" source="data-video-play-only-visible" />
                        <attribute name="video_lazy_load" source="data-video-lazy-load" />
                        <attribute
                            name="video_fallback_image"
                            source="data-video-fallback-src"
                            converter="Magento_PageBuilder/js/converter/attribute/src"
                            preview_converter="Magento_PageBuilder/js/converter/attribute/preview/src"
                        />
                    </element>
                    <element name="overlay">
                        <style
                            name="overlay_color"
                            source="background_color"
                            converter="Magento_PageBuilder/js/content-type/banner/converter/style/overlay-background-color"
                            persistence_mode="write"
                        />
                        <attribute
                            name="overlay_color_attribute"
                            storage_key="overlay_color"
                            source="data-overlay-color"
                            converter="Magento_PageBuilder/js/content-type/banner/converter/attribute/overlay-color"
                        />
                        <attribute name="alt" source="aria-label" />
                        <attribute name="title_attribute" source="title" />
                    </element>
                    <element name="content">
                        <html name="message" preview_converter="Magento_PageBuilder/js/converter/html/directive" />
                    </element>
                    <element name="video_overlay">
                        <style
                            name="video_overlay_color"
                            source="background_color"
                            converter="Magento_PageBuilder/js/converter/style/video-overlay-color"
                            persistence_mode="write"
                        />
                        <attribute
                            name="video_overlay_color_attribute"
                            storage_key="video_overlay_color"
                            source="data-video-overlay-color"
                            converter="Magento_PageBuilder/js/converter/attribute/video-overlay-color"
                        />
                    </element>
                    <element name="button">
                        <style
                            name="opacity"
                            source="opacity"
                            converter="Magento_PageBuilder/js/content-type/banner/converter/style/button-opacity"
                            persistence_mode="write"
                        />
                        <style
                            name="visibility"
                            source="visibility"
                            converter="Magento_PageBuilder/js/content-type/banner/converter/style/button-visibility"
                            persistence_mode="write"
                        />
                        <css name="button_type">
                            <filter>
                                <class source="pagebuilder-banner-button" />
                            </filter>
                        </css>
                        <attribute
                            name="link_url"
                            reader="Magento_PageBuilder/js/property/link"
                            persistence_mode="read"
                        />
                        <attribute
                            name="virtual_link_href"
                            storage_key="link_url"
                            source="href"
                            converter="Magento_PageBuilder/js/converter/attribute/link-href"
                            persistence_mode="write"
                        />
                        <attribute
                            name="virtual_link_target"
                            storage_key="link_url"
                            source="target"
                            converter="Magento_PageBuilder/js/converter/attribute/link-target"
                            persistence_mode="write"
                        />
                        <attribute
                            name="virtual_link_type"
                            storage_key="link_url"
                            source="data-link-type"
                            converter="Magento_PageBuilder/js/converter/attribute/link-type"
                            persistence_mode="write"
                        />
                        <attribute name="title_attribute" source="title" />
                        <html
                            name="button_text"
                            converter="Life_PageBuilderContentTypeBanner/js/converter/html/tag-remove"
                        />
                    </element>
                </elements>
                <converters>
                    <converter name="background_type" component="Magento_PageBuilder/js/mass-converter/background-type">
                        <config>
                            <item name="attribute_name" value="background_type" />
                            <item name="desktop_image_variable" value="background_image" />
                            <item name="mobile_image_variable" value="mobile_image" />
                            <item name="video_source_variable" value="video_source" />
                            <item name="video_fallback_image_variable" value="video_fallback_image" />
                            <item name="video_overlay_color_variable" value="video_overlay_color" />
                        </config>
                    </converter>
                    <converter
                        name="background_images"
                        component="Magento_PageBuilder/js/mass-converter/background-images"
                    >
                        <config>
                            <item name="attribute_name" value="background_images" />
                            <item name="desktop_image_variable" value="background_image" />
                            <item name="mobile_image_variable" value="mobile_image" />
                        </config>
                    </converter>
                </converters>
            </appearance>
            <appearance
                name="collage-right"
                master_template="Life_PageBuilderContentTypeBanner/content-type/banner/collage-right/master"
            >
                <elements>
                    <element name="main">
                        <style
                            name="display"
                            source="display"
                            converter="Magento_PageBuilder/js/converter/style/display"
                            preview_converter="Magento_PageBuilder/js/converter/style/preview/display"
                        />
                        <style
                            name="margins"
                            storage_key="margins_and_padding"
                            reader="Magento_PageBuilder/js/property/margins"
                            converter="Magento_PageBuilder/js/converter/style/margins"
                        />
                        <attribute name="name" source="data-content-type" />
                        <attribute name="appearance" source="data-appearance" />
                        <attribute name="background_color_type" source="data-background-color-type" />
                        <attribute name="background_style" source="data-background-style" />
                        <attribute name="border_radius_type" source="data-border-radius-type" />
                        <attribute name="show_button" source="data-show-button" />
                        <attribute name="show_overlay" source="data-show-overlay" />
                        <css name="css_classes" />
                    </element>
                    <element name="empty_link" />
                    <element name="wrapper">
                        <style name="background_color" source="background_color" />
                        <style
                            name="background_image"
                            source="background_image"
                            converter="Magento_PageBuilder/js/converter/style/background-image"
                            preview_converter="Magento_PageBuilder/js/converter/style/preview/background-image"
                            persistence_mode="write"
                        />
                        <style name="background_position" source="background_position" />
                        <style name="background_size" source="background_size" />
                        <style name="background_repeat" source="background_repeat" />
                        <style name="background_attachment" source="background_attachment" />
                        <style
                            name="border"
                            source="border_style"
                            converter="Magento_PageBuilder/js/converter/style/border-style"
                        />
                        <style name="border_color" source="border_color" />
                        <style
                            name="border_width"
                            source="border_width"
                            converter="Magento_PageBuilder/js/converter/style/border-width"
                        />
                        <style
                            name="border_radius"
                            source="border_radius"
                            converter="Magento_PageBuilder/js/converter/style/remove-px"
                        />
                        <style
                            name="padding"
                            storage_key="margins_and_padding"
                            reader="Magento_PageBuilder/js/property/paddings"
                            converter="Magento_PageBuilder/js/converter/style/paddings"
                        />
                        <style
                            name="min_height"
                            source="min_height"
                            converter="Magento_PageBuilder/js/converter/style/min-height"
                        />
                        <style name="text_align" source="text_align" />
                        <attribute name="background_images" source="data-background-images" />
                        <attribute name="background_type" source="data-background-type" />
                        <attribute
                            name="video_source"
                            source="data-video-src"
                            converter="Magento_PageBuilder/js/content-type/video/converter/attribute/src"
                        />
                        <attribute name="video_loop" source="data-video-loop" />
                        <attribute name="video_play_only_visible" source="data-video-play-only-visible" />
                        <attribute name="video_lazy_load" source="data-video-lazy-load" />
                        <attribute
                            name="video_fallback_image"
                            source="data-video-fallback-src"
                            converter="Magento_PageBuilder/js/converter/attribute/src"
                            preview_converter="Magento_PageBuilder/js/converter/attribute/preview/src"
                        />
                    </element>
                    <element name="overlay">
                        <style
                            name="overlay_color"
                            source="background_color"
                            converter="Magento_PageBuilder/js/content-type/banner/converter/style/overlay-background-color"
                            persistence_mode="write"
                        />
                        <attribute
                            name="overlay_color_attribute"
                            storage_key="overlay_color"
                            source="data-overlay-color"
                            converter="Magento_PageBuilder/js/content-type/banner/converter/attribute/overlay-color"
                        />
                        <attribute name="alt" source="aria-label" />
                        <attribute name="title_attribute" source="title" />
                    </element>
                    <element name="content">
                        <html name="message" preview_converter="Magento_PageBuilder/js/converter/html/directive" />
                    </element>
                    <element name="video_overlay">
                        <style
                            name="video_overlay_color"
                            source="background_color"
                            converter="Magento_PageBuilder/js/converter/style/video-overlay-color"
                            persistence_mode="write"
                        />
                        <attribute
                            name="video_overlay_color_attribute"
                            storage_key="video_overlay_color"
                            source="data-video-overlay-color"
                            converter="Magento_PageBuilder/js/converter/attribute/video-overlay-color"
                        />
                    </element>
                    <element name="button">
                        <style
                            name="opacity"
                            source="opacity"
                            converter="Magento_PageBuilder/js/content-type/banner/converter/style/button-opacity"
                            persistence_mode="write"
                        />
                        <style
                            name="visibility"
                            source="visibility"
                            converter="Magento_PageBuilder/js/content-type/banner/converter/style/button-visibility"
                            persistence_mode="write"
                        />
                        <css name="button_type">
                            <filter>
                                <class source="pagebuilder-banner-button" />
                            </filter>
                        </css>
                        <attribute
                            name="link_url"
                            reader="Magento_PageBuilder/js/property/link"
                            persistence_mode="read"
                        />
                        <attribute
                            name="virtual_link_href"
                            storage_key="link_url"
                            source="href"
                            converter="Magento_PageBuilder/js/converter/attribute/link-href"
                            persistence_mode="write"
                        />
                        <attribute
                            name="virtual_link_target"
                            storage_key="link_url"
                            source="target"
                            converter="Magento_PageBuilder/js/converter/attribute/link-target"
                            persistence_mode="write"
                        />
                        <attribute
                            name="virtual_link_type"
                            storage_key="link_url"
                            source="data-link-type"
                            converter="Magento_PageBuilder/js/converter/attribute/link-type"
                            persistence_mode="write"
                        />
                        <attribute name="title_attribute" source="title" />
                        <html
                            name="button_text"
                            converter="Life_PageBuilderContentTypeBanner/js/converter/html/tag-remove"
                        />
                    </element>
                </elements>
                <converters>
                    <converter name="background_type" component="Magento_PageBuilder/js/mass-converter/background-type">
                        <config>
                            <item name="attribute_name" value="background_type" />
                            <item name="desktop_image_variable" value="background_image" />
                            <item name="mobile_image_variable" value="mobile_image" />
                            <item name="video_source_variable" value="video_source" />
                            <item name="video_fallback_image_variable" value="video_fallback_image" />
                            <item name="video_overlay_color_variable" value="video_overlay_color" />
                        </config>
                    </converter>
                    <converter
                        name="background_images"
                        component="Magento_PageBuilder/js/mass-converter/background-images"
                    >
                        <config>
                            <item name="attribute_name" value="background_images" />
                            <item name="desktop_image_variable" value="background_image" />
                            <item name="mobile_image_variable" value="mobile_image" />
                        </config>
                    </converter>
                </converters>
            </appearance>
            <appearance
                name="collage-right-round"
                preview_template="Magento_PageBuilder/content-type/banner/collage-right/preview"
                master_template="Life_PageBuilderContentTypeBanner/content-type/banner/collage-right-round/master"
                reader="Magento_PageBuilder/js/master-format/read/configurable"
            >
                <elements>
                    <element name="main">
                        <style
                            name="display"
                            source="display"
                            converter="Magento_PageBuilder/js/converter/style/display"
                            preview_converter="Magento_PageBuilder/js/converter/style/preview/display"
                        />
                        <style
                            name="margins"
                            storage_key="margins_and_padding"
                            reader="Magento_PageBuilder/js/property/margins"
                            converter="Magento_PageBuilder/js/converter/style/margins"
                        />
                        <attribute name="name" source="data-content-type" />
                        <attribute name="appearance" source="data-appearance" />
                        <attribute name="background_color_type" source="data-background-color-type" />
                        <attribute name="background_style" source="data-background-style" />
                        <attribute name="border_radius_type" source="data-border-radius-type" />
                        <attribute name="show_button" source="data-show-button" />
                        <attribute name="show_overlay" source="data-show-overlay" />
                        <css name="css_classes" />
                    </element>
                    <element name="empty_link" />
                    <element name="wrapper">
                        <style name="background_color" source="background_color" />
                        <style
                            name="background_image"
                            source="background_image"
                            converter="Magento_PageBuilder/js/converter/style/background-image"
                            preview_converter="Magento_PageBuilder/js/converter/style/preview/background-image"
                            persistence_mode="write"
                        />
                        <style name="background_position" source="background_position" />
                        <style name="background_size" source="background_size" />
                        <style name="background_repeat" source="background_repeat" />
                        <style name="background_attachment" source="background_attachment" />
                        <style
                            name="border"
                            source="border_style"
                            converter="Magento_PageBuilder/js/converter/style/border-style"
                        />
                        <style name="border_color" source="border_color" />
                        <style
                            name="border_width"
                            source="border_width"
                            converter="Magento_PageBuilder/js/converter/style/border-width"
                        />
                        <style
                            name="border_radius"
                            source="border_radius"
                            converter="Magento_PageBuilder/js/converter/style/remove-px"
                        />
                        <style
                            name="padding"
                            storage_key="margins_and_padding"
                            reader="Magento_PageBuilder/js/property/paddings"
                            converter="Magento_PageBuilder/js/converter/style/paddings"
                        />
                        <style
                            name="min_height"
                            source="min_height"
                            converter="Magento_PageBuilder/js/converter/style/min-height"
                        />
                        <style name="text_align" source="text_align" />
                        <attribute name="background_images" source="data-background-images" />
                        <attribute name="background_type" source="data-background-type" />
                        <attribute
                            name="video_source"
                            source="data-video-src"
                            converter="Magento_PageBuilder/js/content-type/video/converter/attribute/src"
                        />
                        <attribute name="video_loop" source="data-video-loop" />
                        <attribute name="video_play_only_visible" source="data-video-play-only-visible" />
                        <attribute name="video_lazy_load" source="data-video-lazy-load" />
                        <attribute
                            name="video_fallback_image"
                            source="data-video-fallback-src"
                            converter="Magento_PageBuilder/js/converter/attribute/src"
                            preview_converter="Magento_PageBuilder/js/converter/attribute/preview/src"
                        />
                    </element>
                    <element name="overlay">
                        <style
                            name="overlay_color"
                            source="background_color"
                            converter="Magento_PageBuilder/js/content-type/banner/converter/style/overlay-background-color"
                            persistence_mode="write"
                        />
                        <attribute
                            name="overlay_color_attribute"
                            storage_key="overlay_color"
                            source="data-overlay-color"
                            converter="Magento_PageBuilder/js/content-type/banner/converter/attribute/overlay-color"
                        />
                        <attribute name="alt" source="aria-label" />
                        <attribute name="title_attribute" source="title" />
                    </element>
                    <element name="content">
                        <html name="message" preview_converter="Magento_PageBuilder/js/converter/html/directive" />
                    </element>
                    <element name="video_overlay">
                        <style
                            name="video_overlay_color"
                            source="background_color"
                            converter="Magento_PageBuilder/js/converter/style/video-overlay-color"
                            persistence_mode="write"
                        />
                        <attribute
                            name="video_overlay_color_attribute"
                            storage_key="video_overlay_color"
                            source="data-video-overlay-color"
                            converter="Magento_PageBuilder/js/converter/attribute/video-overlay-color"
                        />
                    </element>
                    <element name="button">
                        <style
                            name="opacity"
                            source="opacity"
                            converter="Magento_PageBuilder/js/content-type/banner/converter/style/button-opacity"
                            persistence_mode="write"
                        />
                        <style
                            name="visibility"
                            source="visibility"
                            converter="Magento_PageBuilder/js/content-type/banner/converter/style/button-visibility"
                            persistence_mode="write"
                        />
                        <css name="button_type">
                            <filter>
                                <class source="pagebuilder-banner-button" />
                            </filter>
                        </css>
                        <attribute
                            name="link_url"
                            reader="Magento_PageBuilder/js/property/link"
                            persistence_mode="read"
                        />
                        <attribute
                            name="virtual_link_href"
                            storage_key="link_url"
                            source="href"
                            converter="Magento_PageBuilder/js/converter/attribute/link-href"
                            persistence_mode="write"
                        />
                        <attribute
                            name="virtual_link_target"
                            storage_key="link_url"
                            source="target"
                            converter="Magento_PageBuilder/js/converter/attribute/link-target"
                            persistence_mode="write"
                        />
                        <attribute
                            name="virtual_link_type"
                            storage_key="link_url"
                            source="data-link-type"
                            converter="Magento_PageBuilder/js/converter/attribute/link-type"
                            persistence_mode="write"
                        />
                        <attribute name="title_attribute" source="title" />
                        <html
                            name="button_text"
                            converter="Life_PageBuilderContentTypeBanner/js/converter/html/tag-remove"
                        />
                    </element>
                </elements>
                <converters>
                    <converter name="background_type" component="Magento_PageBuilder/js/mass-converter/background-type">
                        <config>
                            <item name="attribute_name" value="background_type" />
                            <item name="desktop_image_variable" value="background_image" />
                            <item name="mobile_image_variable" value="mobile_image" />
                            <item name="video_source_variable" value="video_source" />
                            <item name="video_fallback_image_variable" value="video_fallback_image" />
                            <item name="video_overlay_color_variable" value="video_overlay_color" />
                        </config>
                    </converter>
                    <converter
                        name="background_images"
                        component="Magento_PageBuilder/js/mass-converter/background-images"
                    >
                        <config>
                            <item name="attribute_name" value="background_images" />
                            <item name="desktop_image_variable" value="background_image" />
                            <item name="mobile_image_variable" value="mobile_image" />
                        </config>
                    </converter>
                </converters>
            </appearance>
            <appearance
                name="usp"
                preview_template="Magento_PageBuilder/content-type/banner/collage-left/preview"
                master_template="Life_PageBuilderContentTypeBanner/content-type/banner/usp/master"
                reader="Magento_PageBuilder/js/master-format/read/configurable"
            >
                <elements>
                    <element name="main">
                        <style
                            name="display"
                            source="display"
                            converter="Magento_PageBuilder/js/converter/style/display"
                            preview_converter="Magento_PageBuilder/js/converter/style/preview/display"
                        />
                        <style
                            name="margins"
                            storage_key="margins_and_padding"
                            reader="Magento_PageBuilder/js/property/margins"
                            converter="Magento_PageBuilder/js/converter/style/margins"
                        />
                        <attribute name="name" source="data-content-type" />
                        <attribute name="appearance" source="data-appearance" />
                        <attribute name="background_color_type" source="data-background-color-type" />
                        <attribute name="background_style" source="data-background-style" />
                        <attribute name="border_radius_type" source="data-border-radius-type" />
                        <attribute name="show_button" source="data-show-button" />
                        <attribute name="show_overlay" source="data-show-overlay" />
                        <css name="css_classes" />
                    </element>
                    <element name="empty_link" />
                    <element name="wrapper">
                        <style name="background_color" source="background_color" />
                        <style
                            name="background_image"
                            source="background_image"
                            converter="Magento_PageBuilder/js/converter/style/background-image"
                            preview_converter="Magento_PageBuilder/js/converter/style/preview/background-image"
                            persistence_mode="write"
                        />
                        <style name="background_position" source="background_position" />
                        <style name="background_size" source="background_size" />
                        <style name="background_repeat" source="background_repeat" />
                        <style name="background_attachment" source="background_attachment" />
                        <style
                            name="border"
                            source="border_style"
                            converter="Magento_PageBuilder/js/converter/style/border-style"
                        />
                        <style name="border_color" source="border_color" />
                        <style
                            name="border_width"
                            source="border_width"
                            converter="Magento_PageBuilder/js/converter/style/border-width"
                        />
                        <style
                            name="border_radius"
                            source="border_radius"
                            converter="Magento_PageBuilder/js/converter/style/remove-px"
                        />
                        <style
                            name="padding"
                            storage_key="margins_and_padding"
                            reader="Magento_PageBuilder/js/property/paddings"
                            converter="Magento_PageBuilder/js/converter/style/paddings"
                        />
                        <style
                            name="min_height"
                            source="min_height"
                            converter="Magento_PageBuilder/js/converter/style/min-height"
                        />
                        <style name="text_align" source="text_align" />
                        <attribute name="background_images" source="data-background-images" />
                        <attribute name="background_type" source="data-background-type" />
                        <attribute
                            name="video_source"
                            source="data-video-src"
                            converter="Magento_PageBuilder/js/content-type/video/converter/attribute/src"
                        />
                        <attribute name="video_loop" source="data-video-loop" />
                        <attribute name="video_play_only_visible" source="data-video-play-only-visible" />
                        <attribute name="video_lazy_load" source="data-video-lazy-load" />
                        <attribute
                            name="video_fallback_image"
                            source="data-video-fallback-src"
                            converter="Magento_PageBuilder/js/converter/attribute/src"
                            preview_converter="Magento_PageBuilder/js/converter/attribute/preview/src"
                        />
                    </element>
                    <element name="overlay">
                        <style
                            name="overlay_color"
                            source="background_color"
                            converter="Magento_PageBuilder/js/content-type/banner/converter/style/overlay-background-color"
                            persistence_mode="write"
                        />
                        <attribute
                            name="overlay_color_attribute"
                            storage_key="overlay_color"
                            source="data-overlay-color"
                            converter="Magento_PageBuilder/js/content-type/banner/converter/attribute/overlay-color"
                        />
                        <attribute name="alt" source="aria-label" />
                        <attribute name="title_attribute" source="title" />
                    </element>
                    <element name="content">
                        <html name="message" preview_converter="Magento_PageBuilder/js/converter/html/directive" />
                    </element>
                    <element name="video_overlay">
                        <style
                            name="video_overlay_color"
                            source="background_color"
                            converter="Magento_PageBuilder/js/converter/style/video-overlay-color"
                            persistence_mode="write"
                        />
                        <attribute
                            name="video_overlay_color_attribute"
                            storage_key="video_overlay_color"
                            source="data-video-overlay-color"
                            converter="Magento_PageBuilder/js/converter/attribute/video-overlay-color"
                        />
                    </element>
                    <element name="button">
                        <style
                            name="opacity"
                            source="opacity"
                            converter="Magento_PageBuilder/js/content-type/banner/converter/style/button-opacity"
                            persistence_mode="write"
                        />
                        <style
                            name="visibility"
                            source="visibility"
                            converter="Magento_PageBuilder/js/content-type/banner/converter/style/button-visibility"
                            persistence_mode="write"
                        />
                        <css name="button_type">
                            <filter>
                                <class source="pagebuilder-banner-button" />
                            </filter>
                        </css>
                        <attribute
                            name="link_url"
                            reader="Magento_PageBuilder/js/property/link"
                            persistence_mode="read"
                        />
                        <attribute
                            name="virtual_link_href"
                            storage_key="link_url"
                            source="href"
                            converter="Magento_PageBuilder/js/converter/attribute/link-href"
                            persistence_mode="write"
                        />
                        <attribute
                            name="virtual_link_target"
                            storage_key="link_url"
                            source="target"
                            converter="Magento_PageBuilder/js/converter/attribute/link-target"
                            persistence_mode="write"
                        />
                        <attribute
                            name="virtual_link_type"
                            storage_key="link_url"
                            source="data-link-type"
                            converter="Magento_PageBuilder/js/converter/attribute/link-type"
                            persistence_mode="write"
                        />
                        <attribute name="title_attribute" source="title" />
                        <html
                            name="button_text"
                            converter="Life_PageBuilderContentTypeBanner/js/converter/html/tag-remove"
                        />
                    </element>
                </elements>
                <converters>
                    <converter name="background_type" component="Magento_PageBuilder/js/mass-converter/background-type">
                        <config>
                            <item name="attribute_name" value="background_type" />
                            <item name="desktop_image_variable" value="background_image" />
                            <item name="mobile_image_variable" value="mobile_image" />
                            <item name="video_source_variable" value="video_source" />
                            <item name="video_fallback_image_variable" value="video_fallback_image" />
                            <item name="video_overlay_color_variable" value="video_overlay_color" />
                        </config>
                    </converter>
                    <converter
                        name="background_images"
                        component="Magento_PageBuilder/js/mass-converter/background-images"
                    >
                        <config>
                            <item name="attribute_name" value="background_images" />
                            <item name="desktop_image_variable" value="background_image" />
                            <item name="mobile_image_variable" value="mobile_image" />
                        </config>
                    </converter>
                </converters>
            </appearance>
            <appearance
                name="blog"
                preview_template="Magento_PageBuilder/content-type/banner/collage-left/preview"
                master_template="Life_PageBuilderContentTypeBanner/content-type/banner/blog/master"
                reader="Magento_PageBuilder/js/master-format/read/configurable"
            >
                <elements>
                    <element name="main">
                        <style
                            name="display"
                            source="display"
                            converter="Magento_PageBuilder/js/converter/style/display"
                            preview_converter="Magento_PageBuilder/js/converter/style/preview/display"
                        />
                        <style
                            name="margins"
                            storage_key="margins_and_padding"
                            reader="Magento_PageBuilder/js/property/margins"
                            converter="Magento_PageBuilder/js/converter/style/margins"
                        />
                        <attribute name="name" source="data-content-type" />
                        <attribute name="appearance" source="data-appearance" />
                        <attribute name="background_color_type" source="data-background-color-type" />
                        <attribute name="background_style" source="data-background-style" />
                        <attribute name="border_radius_type" source="data-border-radius-type" />
                        <attribute name="show_button" source="data-show-button" />
                        <attribute name="show_overlay" source="data-show-overlay" />
                        <css name="css_classes" />
                    </element>
                    <element name="empty_link" />
                    <element name="wrapper">
                        <style name="background_color" source="background_color" />
                        <style
                            name="background_image"
                            source="background_image"
                            converter="Magento_PageBuilder/js/converter/style/background-image"
                            preview_converter="Magento_PageBuilder/js/converter/style/preview/background-image"
                            persistence_mode="write"
                        />
                        <style name="background_position" source="background_position" />
                        <style name="background_size" source="background_size" />
                        <style name="background_repeat" source="background_repeat" />
                        <style name="background_attachment" source="background_attachment" />
                        <style
                            name="border"
                            source="border_style"
                            converter="Magento_PageBuilder/js/converter/style/border-style"
                        />
                        <style name="border_color" source="border_color" />
                        <style
                            name="border_width"
                            source="border_width"
                            converter="Magento_PageBuilder/js/converter/style/border-width"
                        />
                        <style
                            name="border_radius"
                            source="border_radius"
                            converter="Magento_PageBuilder/js/converter/style/remove-px"
                        />
                        <style
                            name="padding"
                            storage_key="margins_and_padding"
                            reader="Magento_PageBuilder/js/property/paddings"
                            converter="Magento_PageBuilder/js/converter/style/paddings"
                        />
                        <style
                            name="min_height"
                            source="min_height"
                            converter="Magento_PageBuilder/js/converter/style/min-height"
                        />
                        <style name="text_align" source="text_align" />
                        <attribute name="background_images" source="data-background-images" />
                        <attribute name="background_type" source="data-background-type" />
                        <attribute
                            name="video_source"
                            source="data-video-src"
                            converter="Magento_PageBuilder/js/content-type/video/converter/attribute/src"
                        />
                        <attribute name="video_loop" source="data-video-loop" />
                        <attribute name="video_play_only_visible" source="data-video-play-only-visible" />
                        <attribute name="video_lazy_load" source="data-video-lazy-load" />
                        <attribute
                            name="video_fallback_image"
                            source="data-video-fallback-src"
                            converter="Magento_PageBuilder/js/converter/attribute/src"
                            preview_converter="Magento_PageBuilder/js/converter/attribute/preview/src"
                        />
                    </element>
                    <element name="overlay">
                        <style
                            name="overlay_color"
                            source="background_color"
                            converter="Magento_PageBuilder/js/content-type/banner/converter/style/overlay-background-color"
                            persistence_mode="write"
                        />
                        <attribute
                            name="overlay_color_attribute"
                            storage_key="overlay_color"
                            source="data-overlay-color"
                            converter="Magento_PageBuilder/js/content-type/banner/converter/attribute/overlay-color"
                        />
                        <attribute name="alt" source="aria-label" />
                        <attribute name="title_attribute" source="title" />
                    </element>
                    <element name="content">
                        <html name="message" preview_converter="Magento_PageBuilder/js/converter/html/directive" />
                    </element>
                    <element name="video_overlay">
                        <style
                            name="video_overlay_color"
                            source="background_color"
                            converter="Magento_PageBuilder/js/converter/style/video-overlay-color"
                            persistence_mode="write"
                        />
                        <attribute
                            name="video_overlay_color_attribute"
                            storage_key="video_overlay_color"
                            source="data-video-overlay-color"
                            converter="Magento_PageBuilder/js/converter/attribute/video-overlay-color"
                        />
                    </element>
                    <element name="button">
                        <style
                            name="opacity"
                            source="opacity"
                            converter="Magento_PageBuilder/js/content-type/banner/converter/style/button-opacity"
                            persistence_mode="write"
                        />
                        <style
                            name="visibility"
                            source="visibility"
                            converter="Magento_PageBuilder/js/content-type/banner/converter/style/button-visibility"
                            persistence_mode="write"
                        />
                        <css name="button_type">
                            <filter>
                                <class source="pagebuilder-banner-button" />
                            </filter>
                        </css>
                        <attribute
                            name="link_url"
                            reader="Magento_PageBuilder/js/property/link"
                            persistence_mode="read"
                        />
                        <attribute
                            name="virtual_link_href"
                            storage_key="link_url"
                            source="href"
                            converter="Magento_PageBuilder/js/converter/attribute/link-href"
                            persistence_mode="write"
                        />
                        <attribute
                            name="virtual_link_target"
                            storage_key="link_url"
                            source="target"
                            converter="Magento_PageBuilder/js/converter/attribute/link-target"
                            persistence_mode="write"
                        />
                        <attribute
                            name="virtual_link_type"
                            storage_key="link_url"
                            source="data-link-type"
                            converter="Magento_PageBuilder/js/converter/attribute/link-type"
                            persistence_mode="write"
                        />
                        <attribute name="title_attribute" source="title" />
                        <html
                            name="button_text"
                            converter="Life_PageBuilderContentTypeBanner/js/converter/html/tag-remove"
                        />
                    </element>
                </elements>
                <converters>
                    <converter name="background_type" component="Magento_PageBuilder/js/mass-converter/background-type">
                        <config>
                            <item name="attribute_name" value="background_type" />
                            <item name="desktop_image_variable" value="background_image" />
                            <item name="mobile_image_variable" value="mobile_image" />
                            <item name="video_source_variable" value="video_source" />
                            <item name="video_fallback_image_variable" value="video_fallback_image" />
                            <item name="video_overlay_color_variable" value="video_overlay_color" />
                        </config>
                    </converter>
                    <converter
                        name="background_images"
                        component="Magento_PageBuilder/js/mass-converter/background-images"
                    >
                        <config>
                            <item name="attribute_name" value="background_images" />
                            <item name="desktop_image_variable" value="background_image" />
                            <item name="mobile_image_variable" value="mobile_image" />
                        </config>
                    </converter>
                </converters>
            </appearance>
            <appearance
                name="garden-style"
                preview_template="Magento_PageBuilder/content-type/banner/collage-left/preview"
                master_template="Life_PageBuilderContentTypeBanner/content-type/banner/garden-style/master"
                reader="Magento_PageBuilder/js/master-format/read/configurable"
            >
                <elements>
                    <element name="main">
                        <style
                            name="display"
                            source="display"
                            converter="Magento_PageBuilder/js/converter/style/display"
                            preview_converter="Magento_PageBuilder/js/converter/style/preview/display"
                        />
                        <style
                            name="margins"
                            storage_key="margins_and_padding"
                            reader="Magento_PageBuilder/js/property/margins"
                            converter="Magento_PageBuilder/js/converter/style/margins"
                        />
                        <attribute name="name" source="data-content-type" />
                        <attribute name="appearance" source="data-appearance" />
                        <attribute name="background_color_type" source="data-background-color-type" />
                        <attribute name="background_style" source="data-background-style" />
                        <attribute name="border_radius_type" source="data-border-radius-type" />
                        <attribute name="show_button" source="data-show-button" />
                        <attribute name="show_overlay" source="data-show-overlay" />
                        <css name="css_classes" />
                    </element>
                    <element name="empty_link" />
                    <element name="wrapper">
                        <style name="background_color" source="background_color" />
                        <style
                            name="background_image"
                            source="background_image"
                            converter="Magento_PageBuilder/js/converter/style/background-image"
                            preview_converter="Magento_PageBuilder/js/converter/style/preview/background-image"
                            persistence_mode="write"
                        />
                        <style name="background_position" source="background_position" />
                        <style name="background_size" source="background_size" />
                        <style name="background_repeat" source="background_repeat" />
                        <style name="background_attachment" source="background_attachment" />
                        <style
                            name="border"
                            source="border_style"
                            converter="Magento_PageBuilder/js/converter/style/border-style"
                        />
                        <style name="border_color" source="border_color" />
                        <style
                            name="border_width"
                            source="border_width"
                            converter="Magento_PageBuilder/js/converter/style/border-width"
                        />
                        <style
                            name="border_radius"
                            source="border_radius"
                            converter="Magento_PageBuilder/js/converter/style/remove-px"
                        />
                        <style
                            name="padding"
                            storage_key="margins_and_padding"
                            reader="Magento_PageBuilder/js/property/paddings"
                            converter="Magento_PageBuilder/js/converter/style/paddings"
                        />
                        <style
                            name="min_height"
                            source="min_height"
                            converter="Magento_PageBuilder/js/converter/style/min-height"
                        />
                        <style name="text_align" source="text_align" />
                        <attribute name="background_images" source="data-background-images" />
                        <attribute name="background_type" source="data-background-type" />
                        <attribute
                            name="video_source"
                            source="data-video-src"
                            converter="Magento_PageBuilder/js/content-type/video/converter/attribute/src"
                        />
                        <attribute name="video_loop" source="data-video-loop" />
                        <attribute name="video_play_only_visible" source="data-video-play-only-visible" />
                        <attribute name="video_lazy_load" source="data-video-lazy-load" />
                        <attribute
                            name="video_fallback_image"
                            source="data-video-fallback-src"
                            converter="Magento_PageBuilder/js/converter/attribute/src"
                            preview_converter="Magento_PageBuilder/js/converter/attribute/preview/src"
                        />
                    </element>
                    <element name="overlay">
                        <style
                            name="overlay_color"
                            source="background_color"
                            converter="Magento_PageBuilder/js/content-type/banner/converter/style/overlay-background-color"
                            persistence_mode="write"
                        />
                        <attribute
                            name="overlay_color_attribute"
                            storage_key="overlay_color"
                            source="data-overlay-color"
                            converter="Magento_PageBuilder/js/content-type/banner/converter/attribute/overlay-color"
                        />
                        <attribute name="alt" source="aria-label" />
                        <attribute name="title_attribute" source="title" />
                    </element>
                    <element name="content">
                        <html name="message" preview_converter="Magento_PageBuilder/js/converter/html/directive" />
                    </element>
                    <element name="video_overlay">
                        <style
                            name="video_overlay_color"
                            source="background_color"
                            converter="Magento_PageBuilder/js/converter/style/video-overlay-color"
                            persistence_mode="write"
                        />
                        <attribute
                            name="video_overlay_color_attribute"
                            storage_key="video_overlay_color"
                            source="data-video-overlay-color"
                            converter="Magento_PageBuilder/js/converter/attribute/video-overlay-color"
                        />
                    </element>
                    <element name="button">
                        <style
                            name="opacity"
                            source="opacity"
                            converter="Magento_PageBuilder/js/content-type/banner/converter/style/button-opacity"
                            persistence_mode="write"
                        />
                        <style
                            name="visibility"
                            source="visibility"
                            converter="Magento_PageBuilder/js/content-type/banner/converter/style/button-visibility"
                            persistence_mode="write"
                        />
                        <css name="button_type">
                            <filter>
                                <class source="pagebuilder-banner-button" />
                            </filter>
                        </css>
                        <attribute
                            name="link_url"
                            reader="Magento_PageBuilder/js/property/link"
                            persistence_mode="read"
                        />
                        <attribute
                            name="virtual_link_href"
                            storage_key="link_url"
                            source="href"
                            converter="Magento_PageBuilder/js/converter/attribute/link-href"
                            persistence_mode="write"
                        />
                        <attribute
                            name="virtual_link_target"
                            storage_key="link_url"
                            source="target"
                            converter="Magento_PageBuilder/js/converter/attribute/link-target"
                            persistence_mode="write"
                        />
                        <attribute
                            name="virtual_link_type"
                            storage_key="link_url"
                            source="data-link-type"
                            converter="Magento_PageBuilder/js/converter/attribute/link-type"
                            persistence_mode="write"
                        />
                        <attribute name="title_attribute" source="title" />
                        <html
                            name="button_text"
                            converter="Life_PageBuilderContentTypeBanner/js/converter/html/tag-remove"
                        />
                    </element>
                </elements>
                <converters>
                    <converter name="background_type" component="Magento_PageBuilder/js/mass-converter/background-type">
                        <config>
                            <item name="attribute_name" value="background_type" />
                            <item name="desktop_image_variable" value="background_image" />
                            <item name="mobile_image_variable" value="mobile_image" />
                            <item name="video_source_variable" value="video_source" />
                            <item name="video_fallback_image_variable" value="video_fallback_image" />
                            <item name="video_overlay_color_variable" value="video_overlay_color" />
                        </config>
                    </converter>
                    <converter
                        name="background_images"
                        component="Magento_PageBuilder/js/mass-converter/background-images"
                    >
                        <config>
                            <item name="attribute_name" value="background_images" />
                            <item name="desktop_image_variable" value="background_image" />
                            <item name="mobile_image_variable" value="mobile_image" />
                        </config>
                    </converter>
                </converters>
            </appearance>
            <appearance
                name="doorway"
                preview_template="Magento_PageBuilder/content-type/banner/collage-left/preview"
                master_template="Life_PageBuilderContentTypeBanner/content-type/banner/doorway/master"
                reader="Magento_PageBuilder/js/master-format/read/configurable"
            >
                <elements>
                    <element name="main">
                        <style
                            name="display"
                            source="display"
                            converter="Magento_PageBuilder/js/converter/style/display"
                            preview_converter="Magento_PageBuilder/js/converter/style/preview/display"
                        />
                        <style
                            name="margins"
                            storage_key="margins_and_padding"
                            reader="Magento_PageBuilder/js/property/margins"
                            converter="Magento_PageBuilder/js/converter/style/margins"
                        />
                        <attribute name="name" source="data-content-type" />
                        <attribute name="appearance" source="data-appearance" />
                        <attribute name="background_color_type" source="data-background-color-type" />
                        <attribute name="background_style" source="data-background-style" />
                        <attribute name="border_radius_type" source="data-border-radius-type" />
                        <attribute name="show_button" source="data-show-button" />
                        <attribute name="show_overlay" source="data-show-overlay" />
                        <css name="css_classes" />
                    </element>
                    <element name="empty_link" />
                    <element name="wrapper">
                        <style name="background_color" source="background_color" />
                        <style
                            name="background_image"
                            source="background_image"
                            converter="Magento_PageBuilder/js/converter/style/background-image"
                            preview_converter="Magento_PageBuilder/js/converter/style/preview/background-image"
                            persistence_mode="write"
                        />
                        <style name="background_position" source="background_position" />
                        <style name="background_size" source="background_size" />
                        <style name="background_repeat" source="background_repeat" />
                        <style name="background_attachment" source="background_attachment" />
                        <style
                            name="border"
                            source="border_style"
                            converter="Magento_PageBuilder/js/converter/style/border-style"
                        />
                        <style name="border_color" source="border_color" />
                        <style
                            name="border_width"
                            source="border_width"
                            converter="Magento_PageBuilder/js/converter/style/border-width"
                        />
                        <style
                            name="border_radius"
                            source="border_radius"
                            converter="Magento_PageBuilder/js/converter/style/remove-px"
                        />
                        <style
                            name="padding"
                            storage_key="margins_and_padding"
                            reader="Magento_PageBuilder/js/property/paddings"
                            converter="Magento_PageBuilder/js/converter/style/paddings"
                        />
                        <style
                            name="min_height"
                            source="min_height"
                            converter="Magento_PageBuilder/js/converter/style/min-height"
                        />
                        <style name="text_align" source="text_align" />
                        <attribute name="background_images" source="data-background-images" />
                        <attribute name="background_type" source="data-background-type" />
                        <attribute
                            name="video_source"
                            source="data-video-src"
                            converter="Magento_PageBuilder/js/content-type/video/converter/attribute/src"
                        />
                        <attribute name="video_loop" source="data-video-loop" />
                        <attribute name="video_play_only_visible" source="data-video-play-only-visible" />
                        <attribute name="video_lazy_load" source="data-video-lazy-load" />
                        <attribute
                            name="video_fallback_image"
                            source="data-video-fallback-src"
                            converter="Magento_PageBuilder/js/converter/attribute/src"
                            preview_converter="Magento_PageBuilder/js/converter/attribute/preview/src"
                        />
                    </element>
                    <element name="overlay">
                        <style
                            name="overlay_color"
                            source="background_color"
                            converter="Magento_PageBuilder/js/content-type/banner/converter/style/overlay-background-color"
                            persistence_mode="write"
                        />
                        <attribute
                            name="overlay_color_attribute"
                            storage_key="overlay_color"
                            source="data-overlay-color"
                            converter="Magento_PageBuilder/js/content-type/banner/converter/attribute/overlay-color"
                        />
                        <attribute name="alt" source="aria-label" />
                        <attribute name="title_attribute" source="title" />
                    </element>
                    <element name="content">
                        <html name="message" preview_converter="Magento_PageBuilder/js/converter/html/directive" />
                    </element>
                    <element name="video_overlay">
                        <style
                            name="video_overlay_color"
                            source="background_color"
                            converter="Magento_PageBuilder/js/converter/style/video-overlay-color"
                            persistence_mode="write"
                        />
                        <attribute
                            name="video_overlay_color_attribute"
                            storage_key="video_overlay_color"
                            source="data-video-overlay-color"
                            converter="Magento_PageBuilder/js/converter/attribute/video-overlay-color"
                        />
                    </element>
                    <element name="button">
                        <style
                            name="opacity"
                            source="opacity"
                            converter="Magento_PageBuilder/js/content-type/banner/converter/style/button-opacity"
                            persistence_mode="write"
                        />
                        <style
                            name="visibility"
                            source="visibility"
                            converter="Magento_PageBuilder/js/content-type/banner/converter/style/button-visibility"
                            persistence_mode="write"
                        />
                        <css name="button_type">
                            <filter>
                                <class source="pagebuilder-banner-button" />
                            </filter>
                        </css>
                        <attribute
                            name="link_url"
                            reader="Magento_PageBuilder/js/property/link"
                            persistence_mode="read"
                        />
                        <attribute
                            name="virtual_link_href"
                            storage_key="link_url"
                            source="href"
                            converter="Magento_PageBuilder/js/converter/attribute/link-href"
                            persistence_mode="write"
                        />
                        <attribute
                            name="virtual_link_target"
                            storage_key="link_url"
                            source="target"
                            converter="Magento_PageBuilder/js/converter/attribute/link-target"
                            persistence_mode="write"
                        />
                        <attribute
                            name="virtual_link_type"
                            storage_key="link_url"
                            source="data-link-type"
                            converter="Magento_PageBuilder/js/converter/attribute/link-type"
                            persistence_mode="write"
                        />
                        <attribute name="title_attribute" source="title" />
                        <html
                            name="button_text"
                            converter="Life_PageBuilderContentTypeBanner/js/converter/html/tag-remove"
                        />
                    </element>
                </elements>
                <converters>
                    <converter name="background_type" component="Magento_PageBuilder/js/mass-converter/background-type">
                        <config>
                            <item name="attribute_name" value="background_type" />
                            <item name="desktop_image_variable" value="background_image" />
                            <item name="mobile_image_variable" value="mobile_image" />
                            <item name="video_source_variable" value="video_source" />
                            <item name="video_fallback_image_variable" value="video_fallback_image" />
                            <item name="video_overlay_color_variable" value="video_overlay_color" />
                        </config>
                    </converter>
                    <converter
                        name="background_images"
                        component="Magento_PageBuilder/js/mass-converter/background-images"
                    >
                        <config>
                            <item name="attribute_name" value="background_images" />
                            <item name="desktop_image_variable" value="background_image" />
                            <item name="mobile_image_variable" value="mobile_image" />
                        </config>
                    </converter>
                </converters>
            </appearance>
        </appearances>
    </type>
</config>
