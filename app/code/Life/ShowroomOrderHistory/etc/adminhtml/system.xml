<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <tab id="showroom" translate="label" sortOrder="350">
            <label>Showroom</label>
        </tab>
        <section id="showroom_order_history" translate="label" type="text" sortOrder="80" showInDefault="1" showInWebsite="1" showInStore="0">
            <class>separator-top</class>
            <label>Order History</label>
            <tab>showroom</tab>
            <resource>Life_ShowroomOrderHistory::config_order_history</resource>
            <group id="general" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>General</label>
                <field
                    id="enable_live"
                    translate="label comment"
                    type="select"
                    sortOrder="10"
                    showInDefault="1"
                    showInWebsite="1"
                    showInStore="1"
                >
                    <label>Enable Live Order History Update</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <comment><![CDATA[If Live Updates Disabled, no requests to External API happens and Magento API Endpoint always returns error response.]]></comment>
                </field>
            </group>
            <group id="api" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>Alumio Api Connection</label>
                <field
                    id="base_url"
                    translate="label"
                    type="text"
                    sortOrder="10"
                    showInDefault="1"
                    showInWebsite="1"
                    showInStore="1"
                    canRestore="1"
                >
                    <label>Base URL</label>
                </field>
                <field
                    id="key"
                    translate="label"
                    type="password"
                    sortOrder="20"
                    showInDefault="1"
                    showInWebsite="1"
                    showInStore="1"
                    canRestore="1"
                >
                    <label>Key</label>
                </field>
                <field
                    id="endpoint"
                    translate="label comment"
                    type="text"
                    sortOrder="30"
                    showInDefault="1"
                    showInWebsite="1"
                    showInStore="1"
                    canRestore="1"
                >
                    <label>Endpoint</label>
                </field>
                <field
                    id="timeout"
                    type="text"
                    sortOrder="50"
                    showInDefault="1"
                    showInWebsite="1"
                    showInStore="1"
                    canRestore="1"
                >
                    <label>Connection Timeout</label>
                </field>
            </group>
            <group id="developer" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="0" showInStore="0">
                <label>Developer Tools</label>
                <field
                    id="enable_extended_logging"
                    translate="label comment"
                    type="select"
                    sortOrder="10"
                    showInDefault="1"
                    showInWebsite="0"
                    showInStore="0"
                >
                    <label>Enable Extended Logging</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <comment><![CDATA[In Extended Mode Request and Response will be logged. In Normal mode only Errors will be logged.]]></comment>
                </field>
                <field
                    id="enable_mocking_mode"
                    translate="label comment"
                    type="select"
                    sortOrder="10"
                    showInDefault="1"
                    showInWebsite="0"
                    showInStore="0"
                >
                    <label>Enable Mocking Mode</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <comment><![CDATA[When enabled any calls to Magento API Endpoint will return Mocking Mode Response Content]]></comment>
                </field>
                <field
                    id="mocking_mode_response"
                    translate="label comment"
                    type="textarea"
                    sortOrder="10"
                    showInDefault="1"
                    showInWebsite="0"
                    showInStore="0"
                >
                    <label>Mocking Mode Response</label>
                    <depends>
                        <field id="enable_mocking_mode">1</field>
                    </depends>
                    <comment><![CDATA[ Content will be returned to any request to the Magento API Endpoint. Should be used for development. Content expected to be json string.]]></comment>
                </field>
            </group>
        </section>
    </system>
</config>
