<?php

declare(strict_types=1);

namespace Life\Cylindo\ViewModel;

use Life\Cylindo\Model\Config;
use Life\Cylindo\Setup\Patch\Data\{AddProductCylindoAttributes, AddProductCylindoSfeerImagesAttribute};
use Magento\Catalog\Api\Data\ProductInterface;
use Magento\ConfigurableProduct\Model\Product\Type\Configurable;
use Magento\ConfigurableProduct\Model\ResourceModel\Product\Type\Configurable\Attribute\Collection;
use Magento\Eav\Model\Config as EavConfig;
use Magento\Framework\Serialize\Serializer\Json;
use Magento\Framework\View\Element\Block\ArgumentInterface;
use Magento\Store\Model\Store;

class Product implements ArgumentInterface
{
    public function __construct(
        private readonly Json $jsonEncoder,
        private readonly EavConfig  $eavConfig,
        private readonly Config $config
    ) {}

    public function isEnabled(?ProductInterface $product = null): bool
    {
        $enabled = $this->config->isEnabled()
            && $this->config->getAccountId();

        if ($product) {
            return $enabled && $product->getData(AddProductCylindoAttributes::CYLINDO_GALLERY_ENABLED_ATTRIBUTE_CODE);
        }

        return $enabled;
    }

    public function useSfeerImages(?ProductInterface $product = null): bool
    {
        $useSfeerImages = $this->config->useSfeerImages();

        return $product ?
            $useSfeerImages && $product->getData(AddProductCylindoSfeerImagesAttribute::CYLINDO_USE_SFEER_IMAGES_ATTRIBUTE) :
            $useSfeerImages;
    }

    public function getProductCode(ProductInterface $product): string
    {
        return $product->getData(AddProductCylindoAttributes::CYLINDO_SKU_ATTRIBUTE_CODE) ?:
            $product->getSku();
    }

    public function getViewerCode(ProductInterface $product): string
    {
        return $product->getData(AddProductCylindoAttributes::CYLINDO_VIEWER_ATTRIBUTE_CODE) ?:
            $this->config->getViewerCode();
    }

    public function getAccountId(): string
    {
        return $this->config->getAccountId();
    }

    public function getData(ProductInterface $product): string
    {
        $data = [
            'accountId' => $this->config->getAccountId(),
            'productId' => $product->getId(),
            'productCode' => $this->getProductCode($product),
            'viewerCode' => $this->getViewerCode($product),
            'attributes' => [],
            'productType' => $product->getTypeId()
        ];

        if ($attributes = $this->getAttributes($product)) {
            foreach ($attributes as $attribute) {
                $data['attributes'][] = [
                    'id' => $attribute['product_attribute']['attribute_id'],
                    'code' => $attribute['product_attribute']['frontend_label'],
                    'optionsMap' => $attribute['options_map'],
                    'options' => $this->getAdminOptions($attribute['product_attribute']['attribute_code']),
                ];
            }
        }

        return $this->jsonEncoder->serialize($data);
    }

    public function getAttributes(ProductInterface $product): ?Collection
    {
        if ($product->getTypeId() !== Configurable::TYPE_CODE) {
            return null;
        }

        return $product->getTypeInstance()->getConfigurableAttributes($product);
    }

    /**
     * @return mixed[]
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function getAdminOptions(string $attributeCode): array
    {
        $options = [];

        $attribute = $this->eavConfig->getAttribute('catalog_product', $attributeCode);

        // Use admin labels as mapping codes for Cylindo option codes
        $attributeOptions = $attribute->setStoreId(Store::DEFAULT_STORE_ID)->getSource()->getAllOptions();
        foreach ($attributeOptions as $option) {
            if ($option['value'] > 0) {
                $options[] = $option;
            }
        }

        return $options;
    }
}
