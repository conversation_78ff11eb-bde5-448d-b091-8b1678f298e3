<?php

declare(strict_types=1);

namespace Life\Cylindo\Model;

use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Store\Model\ScopeInterface;

class Config
{
    const XML_BASE_PATH = 'cylindo/general/';
    const XML_PATH_ACCOUNT_ID = self::XML_BASE_PATH . 'account_id';
    const XML_PATH_VIEWER_CODE = self::XML_BASE_PATH . 'viewer_code';
    const XML_PATH_ENABLED = self::XML_BASE_PATH . 'enabled';
    const XML_PATH_USE_SFEER_IMAGES = self::XML_BASE_PATH . 'use_sfeer_images';

    public function __construct(
        private readonly ScopeConfigInterface $scopeConfig
    ) {}

    public function getAccountId(string $storeCode = null): string
    {
        return $this->getConfigValue(self::XML_PATH_ACCOUNT_ID, $storeCode);
    }

    public function getViewerCode(string $storeCode = null): string
    {
        return $this->getConfigValue(self::XML_PATH_VIEWER_CODE, $storeCode);
    }

    public function isEnabled(string $storeCode = null): bool
    {
        return (bool)$this->getConfigValue(self::XML_PATH_ENABLED, $storeCode);
    }

    public function useSfeerImages(string $storeCode = null): bool
    {
        return (bool)$this->getConfigValue(self::XML_PATH_USE_SFEER_IMAGES, $storeCode);
    }

    protected function getConfigValue(
        string $path,
        string $scopeCode = null,
        string $scopeType = ScopeInterface::SCOPE_STORES
    ): string
    {
        return (string)$this->scopeConfig->getValue($path, $scopeType, $scopeCode);
    }
}
