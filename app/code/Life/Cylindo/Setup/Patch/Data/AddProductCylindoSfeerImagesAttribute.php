<?php

declare(strict_types=1);

namespace Life\Cylindo\Setup\Patch\Data;

use Magento\Catalog\Model\Product;
use Magento\Eav\Model\Entity\Attribute\Source\Boolean;
use Magento\Eav\Model\Entity\Attribute\ScopedAttributeInterface;
use Magento\Eav\Setup\EavSetup;
use Magento\Eav\Setup\EavSetupFactory;
use Magento\Framework\Setup\ModuleDataSetupInterface;
use Magento\Framework\Setup\Patch\DataPatchInterface;

class AddProductCylindoSfeerImagesAttribute implements DataPatchInterface
{
    const CYLINDO_USE_SFEER_IMAGES_ATTRIBUTE = 'cylindo_use_sfeer_images_in_gallery';

    public function __construct(
        private readonly ModuleDataSetupInterface $moduleDataSetup,
        private readonly EavSetupFactory $eavSetupFactory
    ) {}

    public function apply()
    {
        /** @var EavSetup $eavSetup */
        $eavSetup = $this->eavSetupFactory->create(['setup' => $this->moduleDataSetup]);

        $eavSetup->addAttribute(
            Product::ENTITY,
            self::CYLINDO_USE_SFEER_IMAGES_ATTRIBUTE,
            [
                'apply_to' => '',
                'backend' => '',
                'comparable' => false,
                'default' => '1',
                'filterable' => false,
                'frontend' => '',
                'global' => ScopedAttributeInterface::SCOPE_STORE,
                'group' => AddProductCylindoAttributes::CYLINDO_ATTRIBUTE_GROUP,
                'input' => 'boolean',
                'is_used_in_grid' => false,
                'is_visible_in_grid' => false,
                'is_filterable_in_grid' => false,
                'label' => 'Use Sfeer Images In Cylindo Gallery',
                'required' => false,
                'searchable' => false,
                'source' => Boolean::class,
                'type' => 'int',
                'used_in_product_listing' => true,
                'user_defined' => false,
                'visible' => true,
                'visible_on_front' => false
            ]
        );

        return $this;
    }

    public static function getDependencies(): array
    {
        return [];
    }

    public function getAliases(): array
    {
        return [];
    }
}
