<?xml version="1.0"?>
<config
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd"
>
    <system>
        <section
            id="cylindo"
            translate="label"
            sortOrder="200"
            showInDefault="1"
            showInWebsite="1"
            showInStore="1"
        >
            <label>Cylindo</label>
            <tab>life</tab>
            <resource>Life_Cylindo::admin_settings</resource>
            <group
                id="general"
                type="text"
                translate="label"
                sortOrder="10"
                showInDefault="1"
                showInWebsite="1"
                showInStore="1"
            >
                <label>General</label>
                <field
                    id="account_id"
                    translate="label"
                    type="text"
                    sortOrder="10"
                    showInDefault="1"
                    showInWebsite="1"
                    showInStore="0"
                    canRestore="1"
                >
                    <label>Account ID</label>
                </field>
                <field
                    id="viewer_code"
                    translate="label"
                    type="text"
                    sortOrder="20"
                    showInDefault="1"
                    showInWebsite="1"
                    showInStore="0"
                    canRestore="1"
                >
                    <label>Viewer/Curator Code</label>
                </field>
                <field
                    id="enabled"
                    translate="label"
                    type="select"
                    sortOrder="30"
                    showInDefault="1"
                    showInWebsite="1"
                    showInStore="1"
                >
                    <label>Enabled</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field
                    id="use_sfeer_images"
                    translate="label"
                    type="select"
                    sortOrder="40"
                    showInDefault="1"
                    showInWebsite="1"
                    showInStore="1"
                >
                    <label>Use Sfeer Images</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
            </group>
        </section>
    </system>
</config>
