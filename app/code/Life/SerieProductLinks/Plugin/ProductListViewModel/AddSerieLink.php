<?php

declare(strict_types=1);

namespace Life\SerieProductLinks\Plugin\ProductListViewModel;

use Hyva\Theme\ViewModel\ProductList;
use Life\SerieProductLinks\Model\Product\LinkFactory;
use Life\SerieProductLinks\Ui\DataProvider\Product\Form\Modifier\Serie;
use Magento\Catalog\Model\Product\Link;

class AddSerieLink
{
    public function __construct(
        private readonly LinkFactory $linkFactory
    ) {}

    /**
     * @see ProductList::getLinkTypeModel()
     */
    public function afterGetLinkTypeModel(ProductList $subject, Link $linkModel, string $linkType): Link
    {
        if ($linkType === Serie::DATA_SCOPE_SERIE) {
            $linkModel = $this->linkFactory->create();
            $linkModel->useSerieLinks();
        }

        return $linkModel;
    }
}
