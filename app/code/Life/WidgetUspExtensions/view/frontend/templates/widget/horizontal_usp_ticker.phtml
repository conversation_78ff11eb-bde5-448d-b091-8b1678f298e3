<?php

/** @var Redkiwi\WidgetDoorways\Block\Widget\Doorways $block */
/** @var Magento\Framework\Escaper $escaper */

?>

<?php if ($doorways = $block->getDoorways()) : ?>
    <script defer="defer">
        function initTicker($el, options) {
            return {
                selector: 'li',
                timeout: 3000,
                ...options,
                start: function () {
                    const doorways = $el.querySelectorAll(this.selector);

                    const next = function (i = 0) {
                        this.removeClass(doorways, '!opacity-100');
                        this.removeClass(doorways, '!pointer-events-auto');

                        doorways[i % doorways.length].classList.add('!opacity-100','!pointer-events-auto');

                        setTimeout(
                            function () {
                                next(i + 1);
                            },
                            this.timeout
                        );
                    }.bind(this)

                    next();
                },
                removeClass: function (elements, htmlClass) {
                    elements.forEach((element) => element.classList.remove(htmlClass));
                }
            };
        }
    </script>
    <section x-data="initTicker($el, {})" x-init="start()" class="relative z-30 flex items-center text-white bg-primary widget widget--redkiwi--widget-usp has-ticker h-9">
        <div class="container">
            <ul class="relative flex items-center justify-center py-2 text-xs h-9 sm:py-1 lg:gap-x-5 xl:gap-x-10 lg:h-auto lg:opacity-100">
                <?php /** @var array{image: string, text: array{title: string, subtitle: string}, link: array{url: string, target: string}} $doorway */ ?>
                <?php foreach ($doorways as $doorway) : ?>
                    <?php $block->setCurrentDoorway($doorway) ?>
                    <li class="absolute left-0 right-0 flex items-center justify-center transition-all duration-1000 opacity-0 max-lg:pointer-events-none gap-x-2 lg:whitespace-normal lg:static lg:opacity-100">
                        <?php if ($link = trim($doorway['link']['url'] ?? '')) : ?>
                            <a class="flex items-center gap-x-2 hover:underline" href="<?= $escaper->escapeHtmlAttr($link) ?>" target="<?= $escaper->escapeHtmlAttr($doorway['link']['target']) ?>">
                                <?= $block->fetchView((string)$block->getTemplateFile('Life_WidgetUspExtensions::widget/usp/usp-ticker.phtml')) ?>
                            </a>
                        <?php else : ?>
                            <?= $block->fetchView((string)$block->getTemplateFile('Life_WidgetUspExtensions::widget/usp/usp-ticker.phtml')) ?>
                        <?php endif ?>
                    </li>
                <?php endforeach ?>
            </ul>
        </div>
    </section>
<?php endif ?>
