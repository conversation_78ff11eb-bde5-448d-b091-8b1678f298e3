<?php

declare(strict_types=1);

namespace Life\SalesDeliveryDate\ViewModel;

use Life\BumbalAppointment\Model\SapStatus;
use Life\SalesDeliveryDate\Plugin\OrderRepositoryPlugin;
use Magento\Framework\Stdlib\DateTime\TimezoneInterface;
use Magento\Framework\View\Element\Block\ArgumentInterface;
use Magento\Sales\Model\Order;

class DeliveryDate implements ArgumentInterface
{
    public function __construct(
        private readonly TimezoneInterface $timezone
    ) {}

    public function getOrderDeliveryDate(Order $order): string
    {
        return (string)$order->getData(OrderRepositoryPlugin::FIELD_NAME);
    }

    public function getOrderSapStatus(Order $order): int
    {
        return (int)$order->getData(SapStatus::BUMBAL_SAP_STATUS_ATTRIBUTE);
    }

    public function getDeliveryMessage(Order $order): string
    {
        $orderDeliveryDate = $this->getOrderDeliveryDate($order);
        $sapStatus = $this->getOrderSapStatus($order);

        if (empty($sapStatus) || empty($orderDeliveryDate)) {
            return '-';
        }

        return match ($sapStatus) {
            SapStatus::SAP_STATUS_BUMBAL_NOT_PLANNABLE, SapStatus::SAP_STATUS_PLANNED_IN_SAP => '-',
            SapStatus::SAP_STATUS_INVITED => (string)__('Possibility to schedule an appointment'),
            SapStatus::SAP_STATUS_UNPLANNED => $this->getWeeksNumber($orderDeliveryDate),
            SapStatus::SAP_STATUS_DATE_PLANNED => $this->getLocaleDate($orderDeliveryDate),
            SapStatus::SAP_STATUS_PLANNED_ON_ROUTE => $this->getLocaleDateWithTimeInterval($orderDeliveryDate),
            default => '-'
        };
    }

    public function getLocaleDateWithTimeInterval(string $orderDeliveryDate): string
    {
        $date = $this->getLocaleDate($orderDeliveryDate);

        // Add time interval: -1 hour / +1 hour
        try {
            $orderDeliveryDate = new \DateTime($orderDeliveryDate);
            $orderDeliveryDate->modify('-1 hour');
            $date .= ' ' . $orderDeliveryDate->format('H:i');
            $orderDeliveryDate->modify('+2 hours');
            $date .= '-' . $orderDeliveryDate->format('H:i');
        } catch (\Exception $e) {}

        return $date;
    }

    public function getLocaleDate(string $orderDeliveryDate): string
    {
        return $this->timezone->formatDate(
            date('Y-m-d H:i:s', strtotime($orderDeliveryDate)),
            \IntlDateFormatter::SHORT,
            false
        );
    }

    public function getWeeksNumber(string $orderDeliveryDate): string
    {
        try {
            $orderDeliveryDate = new \DateTime($orderDeliveryDate);

            if ($orderDeliveryDate > new \DateTime()) {
                return (string)__('week %1', $orderDeliveryDate->format("W"));
            }
        } catch (\Exception $e) {}

        return '-';
    }
}
