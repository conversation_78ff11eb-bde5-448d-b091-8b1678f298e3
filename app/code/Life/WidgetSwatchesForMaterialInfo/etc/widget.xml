<?xml version="1.0" ?>
<widgets
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Widget:etc/widget.xsd"
>
    <widget class="Life\WidgetSwatchesForMaterialInfo\Block\Widget\Swatches" id="life_swatches_for_info_modal">
        <label translate="true">Life - Swatches for Material info pop-up</label>
        <description translate="true">Adds available swatches for current material info pop-up.</description>
        <parameters>
            <parameter name="template" sort_order="40" required="true" visible="false" xsi:type="select">
                <label translate="true">Template</label>
                <options>
                    <option
                        name="default"
                        value="Life_WidgetSwatchesForMaterialInfo::widget/swatches.phtml"
                        selected="true"
                    >
                        <label translate="true">Default</label>
                    </option>
                </options>
            </parameter>
        </parameters>
    </widget>
</widgets>
