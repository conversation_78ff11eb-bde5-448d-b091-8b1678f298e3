<?php

declare(strict_types=1);

namespace Life\ShowroomCustomer\Block\Adminhtml\System\Config\Form\Field\FieldType;

use Life\ShowroomBase\Model\Config\Source\CustomerGroup as CustomerGroupOptions;
use Magento\Framework\View\Element\Context;
use Magento\Framework\View\Element\Html\Select;

/**
 * Class for customer group
 */
class CustomerGroup extends Select
{
    /**
     * Constructor
     *
     * @param CustomerGroupOptions $customerGroupOptions
     * @param Context $context
     * @param array $data
     */
    public function __construct(
        private readonly CustomerGroupOptions $customerGroupOptions,
        Context $context,
        array $data = []
    ) {
        parent::__construct($context, $data);
    }

    /**
     * @inheritdoc
     */
    protected function _toHtml()
    {
        if (!$this->getOptions()) {
            $this->setOptions($this->customerGroupOptions->toOptionArray());
        }
        $this->setClass('customer-group-options-select');
        return parent::_toHtml();
    }

    /**
     * Sets name for input element
     *
     * @param string $value
     * @return $this
     */
    public function setInputName(string $value): self
    {
        return $this->setName($value);
    }
}
