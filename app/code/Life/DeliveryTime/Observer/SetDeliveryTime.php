<?php

declare(strict_types=1);

namespace Life\DeliveryTime\Observer;

use Life\CatalogProductDeliveryTime\Api\DeliveryTimeInterface as CatalogProductDeliveryTime;
use Life\CatalogProductDeliveryTime\Model\DeliveryTimeData;
use Life\CatalogProductDeliveryTime\Setup\Patch\Data\ShippingDate;
use Life\DeliveryTime\Api\DeliveryTimeInterface;
use Life\DeliveryTime\Model\Config;
use Magento\Catalog\Api\Data\ProductInterface;
use Magento\Catalog\Model\Product;
use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;
use Magento\Framework\Stdlib\DateTime\TimezoneInterface;
use Magento\InventoryConfigurationApi\Api\Data\StockItemConfigurationInterface;
use Magento\InventoryConfigurationApi\Api\GetStockItemConfigurationInterface;
use Magento\InventorySales\Model\GetBackorderQty;
use Magento\InventorySalesApi\Api\Data\SalesChannelInterface;
use Magento\InventorySalesApi\Api\StockResolverInterface;
use Magento\Quote\Api\Data\CartItemInterface;
use Magento\Quote\Model\Quote\Item;
use Magento\Store\Model\StoreManagerInterface;
use Psr\Log\LoggerInterface;

class SetDeliveryTime implements ObserverInterface
{
    const ATTRIBUTE_CODE = 'delivery_time';

    public function __construct(
        private readonly GetStockItemConfigurationInterface $getStockItemConfiguration,
        private readonly GetBackorderQty $getBackorderQty,
        private readonly StoreManagerInterface $storeManager,
        private readonly StockResolverInterface $stockResolver,
        private readonly DeliveryTimeInterface $deliveryTime,
        private readonly Config $config,
        private readonly TimezoneInterface $timezone,
        private readonly LoggerInterface $logger,
        private readonly DeliveryTimeData $deliveryTimeData,
        private readonly CatalogProductDeliveryTime $catalogProductDeliveryTime
    ) {}

    public function execute(Observer $observer)
    {
        if (!$this->config->isRealTimeStockCheck()) {
            return;
        }
        try {
            /** @var CartItemInterface&Item $quoteItem */
            $quoteItem = $observer->getEvent()->getItem();
            /** @var ProductInterface&Product $product */
            $product = $quoteItem->getProduct();

            $quoteStockData = $this->catalogProductDeliveryTime->getStockDataForQuote($quoteItem->getQuote());
            $stockData = $quoteStockData[$quoteItem->getSku()] ??
                $this->deliveryTime->getStockData($quoteItem->getSku(), (int)$quoteItem->getQty());

            $deliveryTime = $this->timezone->date()->format('Y-m-d');
            if (
                $this->isBackorder($quoteItem->getSku(), $quoteItem->getQty())
                && !empty($stockData)
                && ($apiDeliveryTime = $this->deliveryTime->getDeliveryDate($stockData))
            ) {
                $deliveryTime = $apiDeliveryTime;
            } elseif ($productDeliveryTime = $product->getData(ShippingDate::ATTRIBUTE_CODE)) {
                $deliveryTime = $productDeliveryTime;
            }

            $quoteItem->setData(
                self::ATTRIBUTE_CODE,
                $deliveryTime
            );

            if (
                ($deliveryMessage = $this->config->getDeliveryMessage())
                && ($deliveryTimeString = $this->deliveryTimeData->calculateDeliveryTime($deliveryTime))
            ) {
                $quoteItem->setData(
                    self::ATTRIBUTE_CODE . '_message',
                    str_replace('%d', (string)$deliveryTimeString, $deliveryMessage)
                );
            }
        } catch (\Exception $e) {
            $this->logger->critical('Life_DeliveryTime: ' . $e->getMessage());
        }
    }

    protected function isBackorder(string $sku, float $requestedQty): bool
    {
        $stockId = $this->getStockId();
        $stockItemConfiguration = $this->getStockItemConfiguration->execute($sku, $stockId);

        if (
            $stockItemConfiguration->isManageStock()
            && (
                $stockItemConfiguration->getBackorders() === StockItemConfigurationInterface::BACKORDERS_YES_NONOTIFY
                || $stockItemConfiguration->getBackorders() === StockItemConfigurationInterface::BACKORDERS_YES_NOTIFY
            )
        ) {
            $backorderQty = $this->getBackorderQty->execute($sku, $stockId, $requestedQty);

            if ($backorderQty > 0) {
                return true;
            }
        }

        return false;
    }

    protected function getStockId(): ?int
    {
        static $stockId = null;

        if (null === $stockId) {
            $websiteCode = $this->storeManager->getWebsite()->getCode();
            $stock = $this->stockResolver->execute(SalesChannelInterface::TYPE_WEBSITE, $websiteCode);
            $stockId = (int)$stock->getStockId();
        }

        return $stockId;
    }
}
