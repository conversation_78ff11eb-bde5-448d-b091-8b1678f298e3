<?php

declare(strict_types=1);

namespace Life\DeliveryTime\Model;

use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Store\Model\ScopeInterface;

class Config
{
    const XML_BASE_PATH = 'delivery_time/api/';
    const XML_PATH_API_BASE_URL = self::XML_BASE_PATH . 'api_base_url';
    const XML_PATH_API_CONNECTION_TIMEOUT = self::XML_BASE_PATH . 'timeout';
    const XML_PATH_API_KEY = self::XML_BASE_PATH . 'api_key';
    const XML_PATH_API_DELIVERY_TIME_ENDPOINT = self::XML_BASE_PATH . 'api_delivery_time_endpoint';
    const XML_PATH_DELIVERY_MESSAGE = self::XML_BASE_PATH . 'delivery_message';
    const XML_PATH_REAL_TIME_STOCK_CHECK = self::XML_BASE_PATH . 'real_time_stock_check';

    public function __construct(
        private readonly ScopeConfigInterface $scopeConfig
    ) {}

    public function getApiConnectionTimeout(string $storeCode = null): int
    {
        return (int)$this->getConfigValue(self::XML_PATH_API_CONNECTION_TIMEOUT, $storeCode);
    }

    public function getApiKey(string $storeCode = null): string
    {
        return $this->getConfigValue(self::XML_PATH_API_KEY, $storeCode);
    }

    public function getApiBaseUrl(string $storeCode = null): string
    {
        return trim($this->getConfigValue(self::XML_PATH_API_BASE_URL, $storeCode), '/') . '/';
    }

    public function getDeliveryTimeEndpoint(string $storeCode = null): string
    {
        return trim($this->getConfigValue(self::XML_PATH_API_DELIVERY_TIME_ENDPOINT, $storeCode), '/');
    }

    public function getDeliveryMessage(string $storeCode = null): string
    {
        return $this->getConfigValue(self::XML_PATH_DELIVERY_MESSAGE, $storeCode);
    }

    public function isRealTimeStockCheck(string $storeCode = null): bool
    {
        return (bool)$this->getConfigValue(self::XML_PATH_REAL_TIME_STOCK_CHECK, $storeCode);
    }

    /**
     * @return mixed
     */
    protected function getConfigValue(
        string $path,
        string $scopeCode = null,
        string $scopeType = ScopeInterface::SCOPE_STORES
    ): string
    {
        return (string)$this->scopeConfig->getValue($path, $scopeType, $scopeCode);
    }
}
