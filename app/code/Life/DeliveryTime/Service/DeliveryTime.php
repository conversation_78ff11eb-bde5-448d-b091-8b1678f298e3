<?php

declare(strict_types=1);

namespace Life\DeliveryTime\Service;

use GuzzleHttp\{
    Client as HttpClient,
    ClientFactory as HttpClientFactory,
    Exception\BadResponseException,
    RequestOptions
};
use Life\DeliveryTime\Api\DeliveryTimeInterface;
use Life\DeliveryTime\Model\Config;
use Magento\Framework\Serialize\Serializer\Json;
use Magento\Framework\Stdlib\DateTime\TimezoneInterface;
use Psr\Http\Message\ResponseInterface as HttpResponseInterface;
use Psr\Log\LoggerInterface;

class DeliveryTime implements DeliveryTimeInterface
{
    const SKU_PARAM = 'sku';
    const QTY_PARAM = 'quantity';
    const SHIP_DATE_PARAM = 'ship_date';
    const ON_HAND_QTY_PARAM = 'on_hand_quantity';

    public function __construct(
        private readonly Config $config,
        private readonly HttpClientFactory $httpClientFactory,
        private readonly Json $json,
        private readonly TimezoneInterface $timezone,
        private readonly LoggerInterface $logger
    ) {}

    public function isInStock(array $stockData): bool
    {
        // Check if there is "on hand" qty or there is a shipping date available
        return (!empty($stockData[self::ON_HAND_QTY_PARAM]) || !empty($stockData[self::SHIP_DATE_PARAM]));
    }

    public function getDeliveryDate(array $stockData): ?string
    {
        if (!empty($stockData[self::SHIP_DATE_PARAM])) {
            try {
                return $this->timezone->date(new \DateTime($stockData[self::SHIP_DATE_PARAM]))->format('Y-m-d');
            } catch (\Exception $exception) {
            }
        }

        return null;
    }

    public function getStockData(string $sku, int $qty): ?array
    {
        try {
            $httpResponse = $this->makeGetRequest($sku, $qty);

            return $this->json->unserialize($httpResponse->getBody()->getContents());
        } catch (BadResponseException $e) {
            $this->logger->critical('Life_DeliveryTime: ' . $e->getResponse()->getBody()->getContents());
        } catch (\Exception $e) {
            $this->logger->critical('Life_DeliveryTime: ' . $e->getMessage());
        }

        return null;
    }

    private function makeGetRequest(string $sku, int $qty): HttpResponseInterface
    {
        $httpClient = $this->getHttpClient();

        return $httpClient->post(
            $this->config->getDeliveryTimeEndpoint(),
            [
                RequestOptions::HEADERS => [
                    'Authorization' => 'Bearer ' . $this->config->getApiKey()
                ],
                RequestOptions::DEBUG => false,
                RequestOptions::FORM_PARAMS => [
                    self::SKU_PARAM => $sku,
                    self::QTY_PARAM => $qty
                ],
                RequestOptions::TIMEOUT => $this->getConnectionTimeout()
            ]
        );
    }

    private function getHttpClient(): HttpClient
    {
        static $httpClient = null;

        if ($httpClient === null) {
            $httpClient = $this->httpClientFactory->create(
                [
                    'config' => [
                        'base_uri' => $this->config->getApiBaseUrl(),
                        'connect_timeout' => $this->getConnectionTimeout()
                    ]
                ]
            );
        }

        return $httpClient;
    }

    protected function getConnectionTimeout(): int
    {
        return $this->config->getApiConnectionTimeout();
    }
}
