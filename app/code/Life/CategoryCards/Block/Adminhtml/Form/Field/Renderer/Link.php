<?php

declare(strict_types=1);

namespace Life\CategoryCards\Block\Adminhtml\Form\Field\Renderer;

use Magento\Framework\View\Element\Template\Context;
use Redkiwi\LibWidgetParametersTable\Block\Adminhtml\Widget\Form\Field\Renderer\{
    AbstractRenderer,
    Input,
    InputFactory
};
use Redkiwi\WidgetDoorways\Block\Adminhtml\Form\Field\Renderer\{Target, TargetFactory};

class Link extends AbstractRenderer
{
    /**
     * @param mixed[] $data
     */
    public function __construct(
        Context $context,
        private readonly InputFactory $inputFactory,
        private readonly TargetFactory $targetFactory,
        private readonly ButtonColorFactory $buttonColorFactory,
        private readonly OverlayFactory $overlayFactory,
        array $data = []
    ) {
        parent::__construct($context, $data);
    }

    public function _toHtml()
    {
        $data = function ($name) {
            $data = $this->getData();

            $data['input_name'] .= "[{$name}]";
            $data['input_id'] .= "_{$name}";
            $data['column_name'] .= ".{$name}";

            return $data;
        };

        /** @var Input $input */
        $input = $this->inputFactory->create(['data' => $data('url')]);

        /** @var Input $label */
        $label = $this->inputFactory->create(['data' => $data('label')]);

        /** @var Target $target */
        $target = $this->targetFactory->create(['data' => $data('target')]);

        /** @var ButtonColor $target */
        $buttonColor = $this->buttonColorFactory->create(['data' => $data('color')]);

        /** @var Overlay $overlay */
        $overlay = $this->overlayFactory->create(['data' => $data('overlay')]);

        return '<div class="admin__field">' . __('Url:')->render() . $input->_toHtml() . '</div>' .
            '<div class="admin__field">' . __('Label:')->render() . $label->_toHtml() . '</div>' .
            '<div class="admin__field">' . __('Target:')->render() . $target->_toHtml() . '</div>' .
            '<div class="admin__field">' . __('Color:')->render() . $buttonColor->_toHtml() . '</div>' .
            '<div class="admin__field">' . __('Overlay:')->render() . $overlay->_toHtml() . '</div>';
    }
}
