<?php

declare(strict_types=1);

namespace Life\CategoryCards\Block\Widget;

use Magento\Catalog\Helper\Data;
use Magento\Catalog\Model\Category;
use Magento\Framework\View\Element\Template;
use Parsedown;

class CategoryCards extends \Redkiwi\WidgetDoorways\Block\Widget\Doorways
{
    /**
     * @param mixed[] $data
     */
    public function __construct(
        Template\Context $context,
        Parsedown $parsedown,
        private readonly Data $catalogData,
        array $data = []
    ) {
        parent::__construct($context, $parsedown, $data);
    }

    /**
     * @return mixed[]
     */
    public function getDoorways(): array
    {
        $doorways = parent::getDoorways();

        $categoryDoorways = [];
        foreach ($doorways as &$doorway) {
            $image = trim($doorway['image']  ?? '');
            $title = trim($doorway['text']['title'] ?? '');
            $subtitle = trim($doorway['text']['subtitle'] ?? '');
            $position = trim($doorway['position'] ?? 0);

            $doorway['image_url'] = $image ? $this->getImageUrl($image) : '';
            $doorway['title'] = $title ? $this->parseMarkdownLine($title) : '';
            $doorway['subtitle'] = $subtitle ? $this->parseMarkdownLine($subtitle) : '';

            $categoryDoorways[$position] = $doorway;
        }

        if (
            ($category = $this->getCategory())
            && ($this->getData('template') === 'Redkiwi_WidgetDoorways::widget/doorways.phtml')
        ) {
            $category->setData('signposting_cards', $categoryDoorways);
        }

        return $doorways;
    }

    public function getCategory(): ?Category
    {
        return $this->catalogData->getCategory();
    }
}
