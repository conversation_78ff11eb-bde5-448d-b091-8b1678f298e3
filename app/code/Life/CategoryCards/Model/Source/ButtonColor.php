<?php

declare(strict_types=1);

namespace Life\CategoryCards\Model\Source;

class ButtonColor implements \Magento\Framework\Data\OptionSourceInterface
{
    /** @var string[] */
    private $colors;

    public function __construct(
        array $colors = []
    ) {
        $this->colors = $colors;
    }

    public function toOptionArray() {
        return array_map(function ($key) {
            return [
                'value' => $key,
                'label' => $this->colors[$key],
            ];
        }, array_keys($this->colors));
    }
}
