<?php

declare(strict_types=1);

namespace Life\ProductsConnector\ViewModel;

use Magento\Catalog\Block\Product\Image;
use Magento\Catalog\Block\Product\ImageFactory;
use Magento\Catalog\Model\Product;
use Magento\Framework\View\Element\Block\ArgumentInterface;

class HoverImage implements ArgumentInterface
{
    public function __construct(
        private readonly ImageFactory $imageFactory
    ) {}

    public function getImageUrl(Product $product, string $imageId): string
    {
        try {
            if ($product->getHoverImage() && $product->getHoverImage() !== 'no_selection') {
                return $this->createHoverImage($product, $imageId)->getImageUrl();
            }
        } catch (\Exception $e) {}

        return '';
    }

    public function createHoverImage(Product $product, string $imageId): Image
    {
        /** @phpstan-ignore-next-line phpstan cant handle existing factory classes */
        return $this->imageFactory->create($product, $imageId);
    }
}
