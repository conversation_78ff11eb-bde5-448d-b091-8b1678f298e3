<?xml version="1.0"?>
<page
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd"
>
    <body>
        <referenceBlock name="product.info">
            <block
                class="Magento\Catalog\Block\Product\View"
                name="life.product.group.list"
                template="Life_ProductsConnector::product/product-group/list.phtml"
                before="-"
            >
                <arguments>
                    <argument name="view_model" xsi:type="object">
                        Life\ProductsConnector\ViewModel\ProductGroupList
                    </argument>
                </arguments>
            </block>
        </referenceBlock>
    </body>
</page>
