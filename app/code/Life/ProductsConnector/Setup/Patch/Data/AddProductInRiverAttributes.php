<?php

declare(strict_types=1);

namespace Life\ProductsConnector\Setup\Patch\Data;

use Magento\Catalog\Api\ProductAttributeRepositoryInterface;
use Magento\Catalog\Model\Product;
use Magento\Eav\Model\Entity\Attribute\ScopedAttributeInterface;
use Magento\Eav\Model\Entity\Attribute\Source\{Boolean, Table};
use Magento\Eav\Setup\{EavSetup, EavSetupFactory};
use Magento\Framework\Setup\ModuleDataSetupInterface;
use Magento\Framework\Setup\Patch\DataPatchInterface;

class AddProductInRiverAttributes implements DataPatchInterface
{
    public function __construct(
        private readonly ModuleDataSetupInterface $moduleDataSetup,
        private readonly ProductAttributeRepositoryInterface $productAttributeRepository,
        private readonly EavSetupFactory $eavSetupFactory
    ) {}

    public function apply()
    {
        /** @var EavSetup $eavSetup */
        $eavSetup = $this->eavSetupFactory->create(['setup' => $this->moduleDataSetup]);

        /** Add InRiver group to all sets */
        foreach ($eavSetup->getAllAttributeSetIds(Product::ENTITY) as $attributeSetId) {
            $eavSetup->addAttributeGroup(
                Product::ENTITY,
                $attributeSetId,
                'InRiver Data',
                1000
            );
        }

        $textTypeData = [
            'input' => 'text',
            'type' => 'varchar',
            'source' => ''
        ];
        $selectTypeData = [
            'input' => 'select',
            'type' => 'int',
            'source' => Table::class
        ];
        $swatchTypeData = [
            'input' => 'select',
            'type' => 'int',
            'source' => Table::class,
            'additional_data' => '{"swatch_input_type":"visual","update_product_preview_image":"0","use_product_image_for_swatch":"0"}'
        ];
        $boolTypeData = [
            'input' => 'select',
            'type' => 'int',
            'source' => Boolean::class
        ];

        $attributes = [
            'width' => [
                ...$textTypeData,
                'label' => 'Width'
            ],
            'length' => [
                ...$textTypeData,
                'label' => 'Length'
            ],
            'height' => [
                ...$textTypeData,
                'label' => 'Height'
            ],
            'material_frame' => [
                ...$selectTypeData,
                'label' => 'Material Frame'
            ],
            'material_armrest' => [
                ...$selectTypeData,
                'label' => 'Material Armrest'
            ],
            'material_top' => [
                ...$selectTypeData,
                'label' => 'Material Top'
            ],
            'material_fabric' => [
                ...$selectTypeData,
                'label' => 'Material Fabric'
            ],
            'color_frame' => [
                ...$swatchTypeData,
                'label' => 'Color Frame'
            ],
            'color_armrest' => [
                ...$swatchTypeData,
                'label' => 'Color Armrest'
            ],
            'color_top' => [
                ...$swatchTypeData,
                'label' => 'Color Top'
            ],
            'color_fabric' => [
                ...$swatchTypeData,
                'label' => 'Color Fabric'
            ],
            'shape' => [
                ...$selectTypeData,
                'label' => 'Shape'
            ],
            'number_of_persons' => [
                ...$selectTypeData,
                'label' => 'Number of Persons'
            ],
            'post_delivery' => [
                ...$boolTypeData,
                'label' => 'Post Delivery'
            ],
            'color_family' => [
                ...$selectTypeData,
                'label' => 'Color Family'
            ],
            'frame' => [
                ...$swatchTypeData,
                'label' => 'Frame'
            ],
            'arrangement' => [
                ...$swatchTypeData,
                'label' => 'Arrangement'
            ]
        ];

        $position = 10;
        foreach ($attributes as $attributeCode => $attributeData) {
            $eavSetup->addAttribute(
                Product::ENTITY,
                $attributeCode,
                [
                    ...$attributeData,
                    'backend' => '',
                    'comparable' => false,
                    'default' => '',
                    'filterable' => false,
                    'frontend' => '',
                    'global' => ScopedAttributeInterface::SCOPE_STORE,
                    'required' => false,
                    'searchable' => false,
                    'sort_order' => $position,
                    'unique' => false,
                    'used_in_product_listing' => false,
                    'user_defined' => true,
                    'visible' => true,
                    'visible_on_front' => false,
                    'group' => 'InRiver Data'
                ]
            );
            $position += 10;

            // Update attribute data for swatches after attribute is created
            if (isset($attributeData['additional_data'])) {
                $attribute = $this->productAttributeRepository->get($attributeCode);
                $attribute->addData([
                    'additional_data' => $attributeData['additional_data']
                ]);
                $this->productAttributeRepository->save($attribute);
            }
        }

        return $this;
    }

    public static function getDependencies(): array
    {
        return [];
    }

    public function getAliases(): array
    {
        return [];
    }
}
