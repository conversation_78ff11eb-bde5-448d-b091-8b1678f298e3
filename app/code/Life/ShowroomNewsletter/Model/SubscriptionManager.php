<?php

declare(strict_types=1);

namespace Life\ShowroomNewsletter\Model;

use Life\ShowroomNewsletter\Api\SubscriptionManagerInterface;
use Magento\Newsletter\Model\SubscriberFactory;

/**
 * Class for subscribtion manager
 */
class SubscriptionManager implements SubscriptionManagerInterface
{
    /**
     * Constructor
     *
     * @param SubscriberFactory $subscriberFactory
     */
    public function __construct(
        private readonly SubscriberFactory $subscriberFactory,
    ) {}

    /**
     * {@inheritdoc}
     */
    public function isCustomerSubscribed(string $customerEmail, int $websiteId): bool
    {
        $subscriber = $this->subscriberFactory->create()->loadBySubscriberEmail($customerEmail, $websiteId);
        return $subscriber->isSubscribed();
    }
}
