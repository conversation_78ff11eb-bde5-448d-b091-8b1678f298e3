<?php

declare(strict_types=1);

namespace Life\ShowroomShipping\Model;

use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Store\Model\ScopeInterface;

/**
 * Class for config provider
 */
class ConfigProvider
{
    private const XML_PATH_SHOWROOM_DEALER_SHIPPING_METHODS = 'showroom_shipping/general/dealer_shipping_methods';

    /**
     * Constructor
     *
     * @param ScopeConfigInterface $scopeConfig
     */
    public function __construct(
        private readonly ScopeConfigInterface $scopeConfig,
    ) {}

    /**
     *  Get showroom dealer shipping method list
     *
     * @return array
     */
    public function getShowroomDealerShippingMethodList(): array
    {
        $groupIds = (string)$this->scopeConfig->getValue(
            self::XML_PATH_SHOWROOM_DEALER_SHIPPING_METHODS,
            ScopeInterface::SCOPE_WEBSITE
        );
        return $groupIds ? explode(',', $groupIds) : [];
    }
}
