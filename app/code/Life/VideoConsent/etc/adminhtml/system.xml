<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <tab id="life" translate="label" sortOrder="300">
            <label>Life Outdoor Living</label>
        </tab>
        <section id="video_consent" translate="label" sortOrder="10" showInDefault="1" showInWebsite="0"
                 showInStore="0">
            <label>Youtube Video Consent Src</label>
            <tab>life</tab>
            <resource>Life_VideoConsent::admin_settings</resource>
            <group id="general"  sortOrder="90" showInDefault="1" showInWebsite="1"
                   showInStore="0">
                <field id="enabled" type="select" translate="label" showInDefault="1" showInWebsite="0" showInStore="0"
                       sortOrder="10">
                    <label>Enable Src Modification for Youtube Videos in CMS Page</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <comment>If enabled changes iframe properties for all Youtube Videos in CMS Pages according to
                        https://support.cookiebot.com/hc/en-us/articles/360003790854-Iframe-cookie-consent-with-YouTube-example
                    </comment>
                </field>
            </group>
        </section>
    </system>
</config>
