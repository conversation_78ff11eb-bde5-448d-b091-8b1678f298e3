<?php

declare(strict_types=1);

namespace Life\ShippingMethodFullServiceDelivery\Model\Carrier;

use Life\ShippingMethodFullServiceDelivery\Setup\Patch\Data\AddShippingSurchargeProductAttribute;
use Life\ShippingMethodPackageDelivery\Model\Carrier\PackageDelivery;
use Magento\Quote\Model\Quote\Address\RateRequest;
use Magento\Quote\Model\Quote\Item;
use Magento\Shipping\Model\Carrier\CarrierInterface;

class FullServiceDelivery extends PackageDelivery implements CarrierInterface
{
    public const CARRIER_CODE = 'full_service';
    /** @var string */
    protected $_code = self::CARRIER_CODE;
    /** @var bool */
    protected $_isFixed = true;

    public function collectRates(RateRequest $request)
    {
        $result = parent::collectRates($request);

        if ($result) {
            foreach ($result->getAllRates() as $rate) {
                // Check and apply surcharge
                if (
                    ($rate->getData('carrier') === $this->_code)
                    && ($surcharge = $this->getSurchargeAmount($request))
                ) {
                    $rate->setData('price', $surcharge);
                    $rate->getData('cost', $surcharge);
                }
            }
        }

        return $result;
    }

    protected function getSurchargeAmount(RateRequest $request): float
    {
        $surcharge = 0;

        /** @var Item $item */
        foreach ($request->getAllItems() as $item) {
            $surcharge += (
                $item->getQty() *
                (float)$item->getProduct()->getData(AddShippingSurchargeProductAttribute::PRICE_ATTRIBUTE)
            );
        }

        return $surcharge;
    }
}
