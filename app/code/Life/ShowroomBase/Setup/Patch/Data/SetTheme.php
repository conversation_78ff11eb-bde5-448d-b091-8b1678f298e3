<?php

declare(strict_types=1);

namespace Life\ShowroomBase\Setup\Patch\Data;

use Life\ShowroomBase\Api\ScopeResolverInterface;
use Magento\Store\Api\WebsiteRepositoryInterface;
use Magento\Store\Model\ScopeInterface;
use Magento\Framework\Setup\ModuleDataSetupInterface;
use Magento\Framework\Setup\Patch\DataPatchInterface;
use Magento\Theme\Model\Config;
use Magento\Theme\Model\ThemeFactory;

/**
 * Patch set config for showroom theme
 */
class SetTheme implements DataPatchInterface
{
    private const SHOWROOM_CODE = 'showroom';

    public function __construct(
        private readonly ThemeFactory $themeFactory,
        private readonly Config $config,
        private readonly ModuleDataSetupInterface $moduleDataSetup,
        private readonly WebsiteRepositoryInterface $websiteRepository
    ) {
    }

    /**
     * {@inheritdoc}
     */
    public function apply(): void
    {
        $this->moduleDataSetup->startSetup();

        /** @phpstan-ignore-next-line */
        $theme = $this->themeFactory->create()->load('Life/' . self::SHOWROOM_CODE, 'code');
        if ($theme->getId()) {
            $showroomWebsite = $this->websiteRepository->get(self::SHOWROOM_CODE);
            $this->config->assignToStore($theme, [$showroomWebsite->getId()],
                ScopeInterface::SCOPE_WEBSITES);
        }
        $this->moduleDataSetup->endSetup();
    }

    /**
     * {@inheritdoc}
     */
    public function getAliases(): array
    {
        return [];
    }

    /**
     * {@inheritdoc}
     */
    public static function getDependencies(): array
    {
        return [];
    }
}
