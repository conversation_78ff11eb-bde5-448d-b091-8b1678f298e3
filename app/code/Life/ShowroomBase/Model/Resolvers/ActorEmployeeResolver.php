<?php

declare(strict_types=1);

namespace Life\ShowroomBase\Model\Resolvers;

use Life\ShowroomBase\Api\ActorEmployeeResolverInterface;

/**
 * @ingeritdoc
 */
class ActorEmployeeResolver extends ActorResolver implements ActorEmployeeResolverInterface
{
    private const XML_PATH_EMPLOYEE_GROUP_ID = 'showroom_general/general/employee';
    private ?bool $isCurrentCustomerEmployee = null;

    /**
     * @ingeritdoc
     */
    public function isEmployee(): bool {
        if ($this->isCurrentCustomerEmployee !== null) {
            return $this->isCurrentCustomerEmployee;
        }
        $this->isCurrentCustomerEmployee = false;
        if ($this->isActor(self::XML_PATH_EMPLOYEE_GROUP_ID)) {
            $this->isCurrentCustomerEmployee = true;
        }

        return $this->isCurrentCustomerEmployee;
    }

    /**
     * @return string
     */
    public function getEmployeeGroupId(): string
    {
        return $this->getActorGroupId(self::XML_PATH_EMPLOYEE_GROUP_ID);
    }
}
