<?php

declare(strict_types=1);

namespace Life\ShowroomBase\Model\Resolvers;

use Exception;
use Magento\Customer\Model\Session;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Psr\Log\LoggerInterface;

/**
 * Basis class to avoid code duplication for all Actor Resolvers.
 */
class ActorResolver
{
    public function __construct(
        private readonly Session $customerSession,
        private readonly ScopeConfigInterface $scopeConfig,
        private readonly LoggerInterface $logger
    ) {}

    /**
     * Check if Current Customer an actor we need. We use constants for now and compare it with current customer group
     * code.
     * @param string $pathForActorConfig
     * @return bool
     */
    public function isActor(string $pathForActorConfig): bool
    {
        try {
            if ($this->customerSession->getCustomer()->getGroupId() == $this->getActorGroupId($pathForActorConfig)) {

                return true;
            }
        } catch (Exception $exception) {
            $this->logger->error($exception->getMessage());
        }

        return false;
    }

    /**
     * @param string $pathForActorConfig
     *
     * @return string
     */
    public function getActorGroupId(string $pathForActorConfig): string
    {
        return $this->scopeConfig->getValue($pathForActorConfig);
    }
}
