<?php

declare(strict_types=1);

namespace Life\ShowroomBase\Model\Resolvers;

use Exception;
use Life\ShowroomBase\Api\ScopeResolverInterface;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Store\Model\StoreManagerInterface;
use Psr\Log\LoggerInterface;

/**
 * @ingeritdoc
 */
class ScopeResolver implements ScopeResolverInterface
{
    private const XML_PATH_SHOWROOM_WEBSITE = 'showroom_general/general/website';

    public function __construct(
        private readonly StoreManagerInterface $storeManager,
        private readonly ScopeConfigInterface $scopeConfig,
        private readonly LoggerInterface $logger
    ) {
    }

    /**
     * @inheritdoc
     */
    public function isShowroomWebsite(): bool {
        try {
            $currentWebsite = $this->storeManager->getWebsite();
            if ($currentWebsite->getId() == $this->getShowroomWebsiteId()) {
                return true;
            }
        } catch (Exception $exception) {
            $this->logger->error($exception->getMessage());
        }

        return false;
    }

    /**
     * @inheritdoc
     */
    public function getShowroomWebsiteId(): ?int
    {
        $websiteId = $this->scopeConfig->getValue(self::XML_PATH_SHOWROOM_WEBSITE);
        return $websiteId !== null ? (int)$websiteId : null;
    }
}
