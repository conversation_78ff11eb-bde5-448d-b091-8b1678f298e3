<?php

declare(strict_types=1);

namespace Life\ShowroomBase\Model\Config\Source;

use Magento\Store\Model\StoreManagerInterface;
use Magento\Framework\Data\OptionSourceInterface;

class Websites implements OptionSourceInterface
{
    public function __construct(
        private readonly StoreManagerInterface $storeManager
    ) {
    }

    public function toOptionArray(): array {
        $websites = $this->storeManager->getWebsites();
        $options = [];

        foreach ($websites as $website) {
            $options[] = [
                'value' => $website->getId(),
                'label' => $website->getName(),
            ];
        }

        return $options;
    }
}
