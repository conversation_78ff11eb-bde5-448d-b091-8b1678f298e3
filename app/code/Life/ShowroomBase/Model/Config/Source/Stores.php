<?php

declare(strict_types=1);

namespace Life\ShowroomBase\Model\Config\Source;

use Magento\Store\Model\StoreManagerInterface;
use Magento\Framework\Data\OptionSourceInterface;

/**
 * Class for store list
 */
class Stores implements OptionSourceInterface
{
    /**
     * Constructor
     *
     * @param StoreManagerInterface $storeManager
     */
    public function __construct(
        private readonly StoreManagerInterface $storeManager
    ) {}

    /**
     * To option array
     */
    public function toOptionArray(): array
    {
        $websites = $this->storeManager->getStores();
        $options = [];

        foreach ($websites as $website) {
            $options[] = [
                'value' => $website->getId(),
                'label' => $website->getName(),
            ];
        }
        return $options;
    }
}
