<?php

declare(strict_types=1);

namespace Life\ShowroomBase\Model\Config\Source;

use Magento\Customer\Api\GroupRepositoryInterface;
use Magento\Framework\Api\SearchCriteriaBuilder;
use Magento\Framework\Data\OptionSourceInterface;

class CustomerGroup implements OptionSourceInterface
{
    public function __construct(
        private readonly GroupRepositoryInterface $groupRepository,
        private readonly SearchCriteriaBuilder $searchCriteriaBuilder
    ) {
    }

    public function toOptionArray(): array {
        $customerGroups = $this->groupRepository->getList($this->searchCriteriaBuilder->create())->getItems();
        $options = [];

        foreach ($customerGroups as $customerGroup) {
            $options[] = [
                'value' => $customerGroup->getId(),
                'label' => $customerGroup->getCode(),
            ];
        }

        return $options;
    }
}
