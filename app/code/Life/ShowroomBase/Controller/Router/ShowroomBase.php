<?php

declare(strict_types=1);

namespace Life\ShowroomBase\Controller\Router;

use Life\ShowroomBase\Api\ScopeResolverInterface;
use Magento\Framework\App\Action\Forward;
use Magento\Framework\App\ActionFactory;
use Magento\Framework\App\ActionInterface;
use Magento\Framework\App\RequestInterface;
use Magento\Framework\App\RouterInterface;
use Magento\Framework\Exception\LocalizedException;
use Magento\Store\Model\StoreManagerInterface;

class ShowroomBase implements RouterInterface
{
    public function __construct(
        private readonly ActionFactory $actionFactory,
        private readonly ScopeResolverInterface $scopeResolver,
        private readonly StoreManagerInterface $storeManager
    ) {}

    /**
     * Showroom base router for homepage and 404.
     * ajax calls ignored.
     *
     * @param RequestInterface $request
     * @return ActionInterface|null
     * @throws LocalizedException
     */
    public function match(RequestInterface $request): ?ActionInterface
    {
        if (!$this->scopeResolver->isShowroomWebsite()) {
            return null;
        }
        /** @phpstan-ignore-next-line */
        if ($request->isAjax()) {
            return null;
        }

        //no forward if we are already on the required category.

        if ($request->getControllerName() == 'category') {
            return null;
        }

        $pathInfo = $request->getPathInfo();

        // If it's the homepage
        if (in_array($pathInfo, ['/', ''])) {
            $request->setModuleName('catalog')
                ->setControllerName('category')
                ->setActionName('view')
                ->setParam('id', (int)$this->storeManager->getStore()->getRootCategoryId());

            return $this->actionFactory->create(Forward::class);
        }

        // Handle 404 (No Route) - forwarding to category page
        if ($request->getControllerName() == 'noroute') {
            $request->setModuleName('catalog')
                ->setControllerName('category')
                ->setActionName('view')
                ->setParam('id', (int)$this->storeManager->getStore()->getRootCategoryId());

            return $this->actionFactory->create(Forward::class);
        }

        return null;
    }
}
