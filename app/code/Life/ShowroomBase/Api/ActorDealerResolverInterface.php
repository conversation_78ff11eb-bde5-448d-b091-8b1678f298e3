<?php

declare(strict_types=1);

namespace Life\ShowroomBase\Api;

/**
 * Interface is an abstraction for the logic required over all showroom application.
 * It checks if current user belongs to the specific Actor group - Dealer.
 */
interface ActorDealerResolverInterface
{
    /**
     * method checks if current user is Dealer
     * @return bool
     */
    public function isDealer():bool;

    /**
     * @return string
     */
    public function getDealerGroupId():string;
}
