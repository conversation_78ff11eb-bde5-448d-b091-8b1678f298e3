<?xml version="1.0" ?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Magento\Quote\Api\CartRepositoryInterface">
        <plugin name="Life_ShowroomQuote::QuoteRepositoryPlugin" type="Life\ShowroomQuote\Plugin\QuoteRepositoryPlugin" />
    </type>
    <type name="Magento\Quote\Model\ChangeQuoteControl">
        <plugin name="Life_ShowroomQuote::ModelChangeQuoteControl" type="Life\ShowroomQuote\Plugin\ModelChangeQuoteControl" />
    </type>
    <type name="Magento\Quote\Api\CartManagementInterface">
        <plugin name="Life_ShowroomQuote::modify_quote_before_order" type="Life\ShowroomQuote\Plugin\Quote\Api\CartManagementInterface\ModifyQuoteBeforeOrderPlugin" />
    </type>
    <preference for="Life\ShowroomQuote\Api\QuoteSelectionListInterface" type="Life\ShowroomQuote\Model\QuoteSelectionList"/>
    <preference for="Life\ShowroomQuote\Api\QuoteJugglingInterface" type="Life\ShowroomQuote\Model\QuoteJuggling"/>
    <preference for="Life\ShowroomQuote\Api\QuoteRepositoryInterface" type="Life\ShowroomQuote\Model\QuoteRepository"/>
    <preference for="Life\ShowroomQuote\Api\QuoteItemRepositoryInterface" type="Life\ShowroomQuote\Model\QuoteItemRepository"/>
</config>
