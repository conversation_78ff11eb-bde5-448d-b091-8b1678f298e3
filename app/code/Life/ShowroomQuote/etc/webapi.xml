<?xml version="1.0"?>
<routes xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Webapi:etc/webapi.xsd">
    <route url="/V1/showroom_quote/selection-list/location/:locationId/employee/:employeeId/date/:date/search/:query" method="GET">
        <service class="Life\ShowroomQuote\Api\QuoteSelectionListInterface" method="getList"/>
        <resources>
            <resource ref="self" />
        </resources>
    </route>

    <route url="/V1/showroom_carts/:cartId/items/:itemId" method="PUT">
        <service class="Life\ShowroomQuote\Api\QuoteItemRepositoryInterface" method="saveByEmployee"/>
        <resources>
            <resource ref="self"/>
        </resources>
        <data>
            <parameter name="employeeCustomerId" force="true">%customer_id%</parameter>
        </data>
    </route>

    <route url="/V1/showroom_carts/mine" method="PUT">
        <service class="Life\ShowroomQuote\Api\QuoteRepositoryInterface" method="saveByEmployee"/>
        <resources>
            <resource ref="self" />
        </resources>
        <data>
            <parameter name="employeeCustomerId" force="true">%customer_id%</parameter>
            <parameter name="quote.id" force="true">%cart_id%</parameter>
        </data>
    </route>
</routes>
