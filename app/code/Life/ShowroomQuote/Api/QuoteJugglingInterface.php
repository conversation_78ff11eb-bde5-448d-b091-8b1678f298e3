<?php

declare(strict_types=1);

namespace Life\ShowroomQuote\Api;

use Magento\Customer\Api\Data\CustomerInterface;
use Magento\Framework\Exception\LocalizedException;
use Magento\Quote\Api\Data\CartInterface;

interface QuoteJugglingInterface
{
    public function unshelve(CartInterface $quote, CustomerInterface $customer): CartInterface;

    /**
     * @throws LocalizedException
     */
    public function park(CartInterface $quote): bool;

    public function delete(CartInterface $quote): bool;
}
