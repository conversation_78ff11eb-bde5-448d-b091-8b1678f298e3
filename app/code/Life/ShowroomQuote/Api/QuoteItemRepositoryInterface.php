<?php

declare(strict_types=1);

namespace Life\ShowroomQuote\Api;

use Magento\Framework\Exception\CouldNotSaveException;
use Magento\Framework\Exception\InputException;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Quote\Api\Data\CartItemInterface;

/**
 * Class for cart item repository
 */
interface QuoteItemRepositoryInterface
{
    /**
     * Save by employee
     *
     * @param CartItemInterface $cartItem
     * @param int $employeeCustomerId
     * @return CartItemInterface
     * @throws LocalizedException
     * @throws CouldNotSaveException
     * @throws InputException
     * @throws NoSuchEntityException
     */
    public function saveByEmployee(CartItemInterface $cartItem, int $employeeCustomerId): CartItemInterface;
}
