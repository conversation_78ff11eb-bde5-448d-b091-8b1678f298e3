<?php

declare(strict_types=1);

namespace Life\ShowroomQuote\Api;

interface QuoteSelectionListInterface
{
    public const KEY_LOCATION_ID_PARAM = 'location';
    public const KEY_EMPLOYEE_ID_PARAM = 'employee';
    public const KEY_DATE_PARAM = 'date';
    public const KEY_SEARCH_QUERY_PARAM = 'search';

    /**
     * Get quote list in JSON format
     *
     * @param string $query     search query separated by whitespaces
     * @param string $date      date filter will be applied to quote by updated_at column; expected format: "Y-m-d"
     * @param int $locationId   locationId filter will be applied to quote by sales_point_id field
     * @param int $employeeId   employeeId filter will be applied to quote by employee_id field
     *
     * @return string[][]
     */
    public function getList(string $query, string $date, int $locationId, int $employeeId): array;
}
