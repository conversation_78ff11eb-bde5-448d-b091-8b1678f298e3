<?php

declare(strict_types=1);

namespace Life\ShowroomQuote\Controller\Selection;

use Life\ShowroomBase\Api\ActorEmployeeResolverInterface;
use Life\ShowroomBase\Api\ScopeResolverInterface;
use Magento\Framework\App\RequestInterface;
use Life\ShowroomQuote\Controller\AbstractController;
use Magento\Framework\App\ResponseInterface;
use Magento\Framework\Controller\Result\Redirect;
use Magento\Framework\Controller\ResultFactory;
use Magento\Framework\Controller\ResultInterface;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Store\Model\StoreManagerInterface;
use Psr\Log\LoggerInterface;

class Index extends AbstractController
{
    /**
     * @param RequestInterface $request
     * @param ResponseInterface $response
     * @param ScopeResolverInterface $showroomResolver
     * @param ActorEmployeeResolverInterface $actorEmployeeResolver
     * @param StoreManagerInterface $storeManager
     * @param ResultFactory $resultFactory
     * @param LoggerInterface $logger
     */
    public function __construct(
        ScopeResolverInterface $showroomResolver,
        ActorEmployeeResolverInterface $actorEmployeeResolver,
        RequestInterface $request,
        ResponseInterface $response,
        private readonly StoreManagerInterface $storeManager,
        private readonly ResultFactory $resultFactory,
        private readonly LoggerInterface $logger
    ) {
        parent::__construct($showroomResolver, $actorEmployeeResolver, $request, $response);
    }

    /**
     * @return ResultInterface|ResponseInterface|Redirect
     */
    public function execute()
    {
        if (!$this->isValidRequest()) {
            try {
                return $this->resultFactory->create(ResultFactory::TYPE_REDIRECT)
                    ->setUrl($this->storeManager->getStore()->getBaseUrl());
            } catch (NoSuchEntityException $exception) {
                $this->logger->critical($exception);
            }
        }

        return $this->resultFactory->create(ResultFactory::TYPE_PAGE);
    }
}
