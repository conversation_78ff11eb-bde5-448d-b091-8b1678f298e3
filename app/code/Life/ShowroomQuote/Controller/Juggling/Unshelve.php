<?php

declare(strict_types=1);

namespace Life\ShowroomQuote\Controller\Juggling;

use Life\ShowroomBase\Api\ActorEmployeeResolverInterface;
use Life\ShowroomBase\Api\ScopeResolverInterface;
use Life\ShowroomQuote\Api\Data\CartInterface as ShowroomCartInterface;
use Life\ShowroomQuote\Api\QuoteJugglingInterface;
use Life\ShowroomQuote\Controller\AbstractController;
use Magento\Framework\App\Response\RedirectInterface;
use MageWorx\Locations\Api\LocationRepositoryInterface;
use Magento\Checkout\Model\Session as CheckoutSession;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Customer\Model\Session as CustomerSession;
use Magento\Framework\App\RequestInterface;
use Magento\Framework\App\ResponseInterface;
use Magento\Framework\Controller\ResultFactory;
use Magento\Framework\Controller\ResultInterface;
use Magento\Framework\Controller\Result\Redirect;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\Message\ManagerInterface as MessageManagerInterface;
use Magento\Quote\Api\CartRepositoryInterface;
use Magento\Quote\Api\Data\CartInterface;
use Magento\Store\Model\StoreManagerInterface;
use Psr\Log\LoggerInterface;

class Unshelve extends AbstractController
{
    private const QUOTE_ID_PARAM = 'quote';

    public function __construct(
        ScopeResolverInterface $showroomResolver,
        ActorEmployeeResolverInterface $actorEmployeeResolver,
        RequestInterface $request,
        ResponseInterface $response,
        private readonly StoreManagerInterface $storeManager,
        private readonly CheckoutSession $checkoutSession,
        private readonly CustomerSession $customerSession,
        private readonly LocationRepositoryInterface $locationRepository,
        private readonly CartRepositoryInterface $quoteRepository,
        private readonly CustomerRepositoryInterface $customerRepository,
        private readonly QuoteJugglingInterface $quoteJuggling,
        private readonly ResultFactory $resultFactory,
        private readonly MessageManagerInterface $messageManager,
        private readonly LoggerInterface $logger,
        private readonly RedirectInterface $redirect
    )
    {
        parent::__construct($showroomResolver, $actorEmployeeResolver, $request, $response);
    }

    /**
     * @return ResultInterface|ResponseInterface|Redirect
     *
     * @throws NoSuchEntityException
     */
    public function execute()
    {
        $result = $this->resultFactory->create(ResultFactory::TYPE_REDIRECT)
            ->setUrl($this->redirect->getRefererUrl());

        if (!$this->isValidRequest()) {
            return $result;
        }

        $quoteId = (int)$this->getRequest()->getParam(self::QUOTE_ID_PARAM, 0);
        if (!$quoteId) {
            $this->messageManager->addErrorMessage(__('Quote ID is required.'));

            return $result;
        }

        try {
            $quoteToUnshelve = $this->quoteRepository->get($quoteId);
            if (!$this->isAvailable($quoteToUnshelve)) {
                return $result;
            }

            try {
                $this->freeCurrentQuote();
                $customer = $this->customerRepository->getById($this->customerSession->getCustomerId());
                $this->quoteJuggling->unshelve($quoteToUnshelve, $customer);
                $this->messageManager->addSuccessMessage(__('Quote is successfully activated.'));
            } catch (LocalizedException|NoSuchEntityException $exception) {
                $this->logger->critical($exception);
                $this->messageManager->addErrorMessage($exception->getMessage());
            }
        } catch (NoSuchEntityException $exception) {
            $this->logger->critical($exception);
            $this->messageManager->addErrorMessage(__('Quote with ID %s does not exist.', $quoteId));
        }

        $result = $this->resultFactory->create(ResultFactory::TYPE_REDIRECT)
            ->setUrl($this->storeManager->getStore()->getUrl('checkout/cart'));

        return $result;
    }

    /**
     * @param CartInterface $quote
     *
     * @return bool
     */
    private function isAvailable(CartInterface $quote): bool
    {
        $isAvailable = !$quote->getCustomerId();
        if (!$isAvailable) {
            try {
                $customer = $this->customerRepository->getById($quote->getCustomerId());
                $location = $this->locationRepository->getById(
                    $quote->getData(ShowroomCartInterface::KEY_SALES_POINT_ID)
                );
                $message = __(
                    'This quote is already in use by %1 %2 from the %3 filial.',
                    $customer->getFirstname(),
                    $customer->getLastname(),
                    $location->getName()
                );
            } catch (NoSuchEntityException|LocalizedException $exception) {
                $this->logger->critical($exception);
                $message = __('This quote is already in use by another employee.');
            }

            $this->messageManager->addErrorMessage($message);
        }

        return $isAvailable;
    }

    /**
     * @return void
     *
     * @throws NoSuchEntityException
     * @throws LocalizedException
     */
    private function freeCurrentQuote(): void
    {
        $quote = $this->checkoutSession->getQuote();
        $quote->hasItems() ?
            $this->quoteJuggling->park($quote) :
            $this->quoteJuggling->delete($quote);
    }
}
