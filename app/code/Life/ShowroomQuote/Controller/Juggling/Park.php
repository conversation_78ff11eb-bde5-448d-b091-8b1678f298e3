<?php

declare(strict_types=1);

namespace Life\ShowroomQuote\Controller\Juggling;

use Life\ShowroomBase\Api\ActorEmployeeResolverInterface;
use Life\ShowroomBase\Api\ScopeResolverInterface;
use Life\ShowroomQuote\Api\QuoteJugglingInterface;
use Life\ShowroomQuote\Controller\AbstractController;
use Magento\Checkout\Model\Session as CheckoutSession;
use Magento\Framework\App\RequestInterface;
use Magento\Framework\App\ResponseInterface;
use Magento\Framework\Controller\ResultFactory;
use Magento\Framework\Controller\ResultInterface;
use Magento\Framework\Controller\Result\Redirect;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\Message\ManagerInterface as MessageManagerInterface;
use Magento\Store\Model\StoreManagerInterface;
use Psr\Log\LoggerInterface;

class Park extends AbstractController
{
    public function __construct(
        ScopeResolverInterface                   $showroomResolver,
        ActorEmployeeResolverInterface           $actorEmployeeResolver,
        RequestInterface                         $request,
        ResponseInterface                        $response,
        private readonly StoreManagerInterface   $storeManager,
        private readonly QuoteJugglingInterface  $quoteJuggling,
        private readonly CheckoutSession         $checkoutSession,
        private readonly ResultFactory           $resultFactory,
        private readonly MessageManagerInterface $messageManager,
        private readonly LoggerInterface         $logger
    ) {
        parent::__construct($showroomResolver, $actorEmployeeResolver, $request, $response);
    }

    /**
     * @return ResultInterface|ResponseInterface|Redirect
     *
     * @throws NoSuchEntityException|LocalizedException
     */
    public function execute()
    {
        $hasErrors = false;
        $result = $this->resultFactory->create(ResultFactory::TYPE_REDIRECT)
            ->setUrl($this->storeManager->getStore()->getBaseUrl());

        if (!$this->isValidRequest()) {
            return $result;
        }

        $message = __('No quote items, cannot park the quote.');
        try {
            $quote = $this->checkoutSession->getQuote();
            if ($quote->hasItems()) {
                try {
                    $this->quoteJuggling->park($quote);
                    $message = __('The quote #%1 is successfully parked.', $quote->getId());
                } catch (LocalizedException $exception) {
                    $hasErrors = true;
                    $message = $exception->getMessage();
                }
            }
        } catch (NoSuchEntityException|LocalizedException $exception) {
            $hasErrors = true;
            $this->logger->critical($exception);
            $message = __('An error occurred during parking the quote: %1', $exception->getMessage());
        }

        $hasErrors ? $this->messageManager->addErrorMessage($message) :
            $this->messageManager->addSuccessMessage($message);

        return $result;
    }
}
