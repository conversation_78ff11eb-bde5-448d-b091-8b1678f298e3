<?php

declare(strict_types=1);

namespace Life\ShowroomQuote\ViewModel;

use Life\ShowroomCustomer\Api\CustomerQuoteInterface;
use Life\ShowroomQuote\Api\Data\CartInterface as ShowroomCartInterface;
use Magento\Checkout\Model\Session;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Customer\Api\Data\CustomerInterface;
use Magento\Framework\Api\SortOrderBuilder;
use Magento\Framework\Api\SortOrder;
use Magento\Framework\Api\SearchCriteriaBuilder;
use Magento\Framework\App\RequestInterface;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\Pricing\PriceCurrencyInterface;
use Magento\Framework\UrlInterface;
use Magento\Framework\View\Element\Block\ArgumentInterface;
use Magento\Quote\Api\CartRepositoryInterface;
use Magento\Quote\Api\CartTotalRepositoryInterface;
use Magento\Quote\Api\Data\CartInterface;
use Magento\Quote\Api\Data\TotalsInterface;
use MageWorx\Locations\Api\Data\LocationInterface;
use MageWorx\Locations\Api\LocationRepositoryInterface;
use Psr\Log\LoggerInterface;

class QuoteDataProvider implements ArgumentInterface
{
    public const QUOTE_ACTIVATE_ROUTE = 'showroom_quote/juggling/unshelve';
    public const QUOTE_PARK_ROUTE = 'showroom_quote/juggling/park';

    private ?int $quoteId = null;

    private ?CartInterface $quote = null;

    public function __construct(
        private readonly Session $checkoutSession,
        private readonly CartRepositoryInterface $quoteRepository,
        private readonly CartTotalRepositoryInterface $cartTotalRepository,
        private readonly CustomerRepositoryInterface $customerRepository,
        private readonly LocationRepositoryInterface $locationRepository,
        private readonly SearchCriteriaBuilder $searchCriteriaBuilder,
        private readonly SortOrderBuilder $sortOrderBuilder,
        private readonly PriceCurrencyInterface $priceCurrency,
        private readonly RequestInterface $request,
        private readonly UrlInterface $urlBuilder,
        private readonly LoggerInterface $logger
    ) {
    }

    /**
     * @return int|null
     */
    public function getQuoteId(): ?int
    {
        if (!isset($this->quoteId)) {
            $this->quoteId = (int)$this->request->getParam('quote', 0);
        }

        return $this->quoteId;
    }

    /**
     * @param int $quoteId
     *
     * @return QuoteDataProvider
     */
    public function setQuoteId(int $quoteId): QuoteDataProvider
    {
        $this->quoteId = $quoteId;

        return $this;
    }

    /**
     * @return CartInterface|null
     */
    public function getQuote(): ?CartInterface
    {
        if (!$this->quote && $this->getQuoteId()) {
            try {
                $this->quote = $this->quoteRepository->get($this->getQuoteId());
            } catch (NoSuchEntityException $exception) {
                $this->logger->critical($exception->getMessage());
            }
        }

        return $this->quote;
    }

    /**
     * @return int|null
     */
    public function getCurrentQuoteId(): ?int
    {
        return (int)$this->checkoutSession->getQuoteId() ?: null;
    }

    /**
     * @param CartInterface $quote
     *
     * @return LocationInterface|null
     */
    public function getQuoteSalesPoint(CartInterface $quote): ?LocationInterface
    {
        $location = null;
        if ($quote->getData(ShowroomCartInterface::KEY_SALES_POINT_ID)) {
            try {
                $location = $this->locationRepository->getById(
                    $quote->getData(ShowroomCartInterface::KEY_SALES_POINT_ID)
                );
            } catch (NoSuchEntityException|LocalizedException $exception) {
                $this->logger->critical($exception->getMessage());
            }
        }

        return $location;
    }

    /**
     * @param CartInterface $quote
     *
     * @return CustomerInterface|null
     */
    public function getQuoteEmployee(CartInterface $quote): ?CustomerInterface
    {
        $employee = null;
        if ($quote->getData(ShowroomCartInterface::KEY_EMPLOYEE_ID)) {
            try {
                $employee = $this->customerRepository->getById(
                    $quote->getData(ShowroomCartInterface::KEY_EMPLOYEE_ID)
                );
            } catch (NoSuchEntityException|LocalizedException $exception) {
                $this->logger->critical($exception->getMessage());
            }
        }

        return $employee;
    }

    /**
     * Get customer active quotes.
     *
     * @param int $customerId
     *
     * @return CartInterface[]
     */
    public function getCustomerActiveQuotes(int $customerId): array
    {
        $sortOrder = $this->sortOrderBuilder
            ->setField(CartInterface::KEY_UPDATED_AT)
            ->setDirection(SortOrder::SORT_DESC)
            ->create();
        $searchCriteria = $this->searchCriteriaBuilder
            ->addFilter(CustomerQuoteInterface::REAL_CUSTOMER_ID_ATTRIBUTE, $customerId)
            ->addFilter(CartInterface::KEY_IS_ACTIVE, 1)
            ->addSortOrder($sortOrder)
            ->create();

        $quoteList = $this->quoteRepository->getList($searchCriteria);

        return $quoteList->getItems();
    }

    /**
     * @param CartInterface $quote
     *
     * @return TotalsInterface|null
     */
    public function getTotals(CartInterface $quote): ?TotalsInterface
    {
        $result = null;
        try {
            $result = $this->cartTotalRepository->get($quote->getId());
        } catch (NoSuchEntityException $exception) {
            $this->logger->critical($exception->getMessage());
        }

        return $result;
    }

    /**
     * @param float $price
     *
     * @return string
     */
    public function getFormattedPrice(float $price): string
    {
        return $this->priceCurrency->format($price);
    }

    /**
     * @param int|string $customerId
     * @param int|string $quoteId
     *
     * @return string
     */
    public function getCustomerToQuoteApiUrl($customerId, $quoteId): string
    {
        return rtrim($this->urlBuilder->getBaseUrl(), '/') . '/' .
            "rest/V1/showroom_customer/customer/$customerId/assign-to-quote/$quoteId";
    }
}
