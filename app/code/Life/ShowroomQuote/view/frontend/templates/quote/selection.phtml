<?php

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\SvgIcons;
use Life\ShowroomQuote\Block\Quote\Selection;
use Magento\Framework\Escaper;

/** @var Selection $block */
/** @var Escaper $escaper */
/** @var SvgIcons $svgIcons */
/** @var Escaper $escaper */
/** @var SvgIcons $svgIcons */
/** @var ViewModelRegistry $viewModels */

$employeeList = $block->getEmployeeFilterList();
$locale = 'nl-NL'; // TODO: Get from config.
$salesPointFilterList = $block->getSalesPointFilterList();
$svgIcons = $viewModels->require(SvgIcons::class);

?>
<script>
    'use strict';
    function initQuoteSearchForm() {
        return {
            results: [],
            location: '',
            searchValue: 0,
            searching: false,
            activeFilterMenus: [],
            appliedFilters: {
                location: 0,
                employee: 0,
                date: 0
            },
            selectedFilters: {
                location: 0,
                employee: 0,
                date: null
            },
            async search() {
                this.searching = true;
                const encodedSearchValue = encodeURIComponent(this.searchValue.toString().trim());
                const url = `<?= $block->getBaseUrl(); ?>rest/V1/showroom_quote/selection-list/location/${this.selectedFilters.location}/employee/${this.selectedFilters.employee}/date/${this.selectedFilters.date || 0}/search/${encodedSearchValue}`;
                const token = JSON.parse(localStorage.getItem('mage-cache-storage'))?.customer?.signin_token;
                fetch(url, {
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    method: 'GET',
                })
                .then(response => response.json())
                .then(data => {
                    this.appliedFilters.location = this.selectedFilters.location;
                    this.appliedFilters.employee = this.selectedFilters.employee;
                    this.appliedFilters.date = this.selectedFilters.date;
                    this.results = data;
                })
                .catch(error => {
                    console.error(error);
                    this.results = [];
                }).finally(() => {
                    this.searching = false;
                });
            },
            toggleFilterMenu(menu, bool = true) {
                if (!menu) return;
                if (menu === 'all') {
                    this.activeFilterMenus = [];
                    return;
                }
                if (bool) {
                    this.activeFilterMenus.push('base');
                    this.activeFilterMenus.push(menu);
                } else {
                    this.activeFilterMenus.pop(menu);
                }
            },
            applyFilters() {
                this.activeFilterMenus = [];
                this.search();
            },
            resetFilters() {
                this.activeFilterMenus = [];
                if (this.selectedFilters.location !== 0) {
                    document.querySelector(`#sales-point-input-${this.selectedFilters.location}`).checked = false;
                }
                this.selectedFilters.location = 0;
                this.selectedFilters.employee = 0;
                this.selectedFilters.date = 0;
                this.search();
            },
        }
    }

    function formatDate(dateString) {
        const date = new Date(dateString);
        return new Intl.DateTimeFormat('<?= $locale ?>', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit'
        }).format(date);
    }

    function formatPrice(price) {
        const number = parseFloat(price).toLocaleString('<?= $locale ?>', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        });
        return number;
    }
</script>
<div
    class="container grid gap-2"
    x-data="initQuoteSearchForm()"
    x-init="search()"
>
    <header>
        <div class="grid bg-primary rounded-xl px-4 text-white">
            <div class="flex items-center gap-10 text-xs pt-4 pb-2 border-b border-white/20">
                <!-- TODO: check link -->
                <button
                    class="inline-flex items-center gap-x-2"
                    onclick="history.back(); return false;"
                >
                    <?= $svgIcons->arrowLeftHtml("", 20, 20, ['aria-hidden' => 'true']) ?>
                    <?= $escaper->escapeHtml(__('Back')) ?>
                </button>
                <span class="text-primary-100">
                    <?= $escaper->escapeHtml(__('Customer')) ?>
                </span>
            </div>

            <div class="inline-grid w-full max-w-lg mx-auto gap-6 py-14">
                <h1 class="heading-5">
                    <?= $escaper->escapeHtml(__('Search for a quote')) ?>
                </h1>
                <p>
                    <small>
                        <span><?= $escaper->escapeHtml(__('Date search should be separated with dashes')) ?>: </span>
                        <span x-html="formatDate(new Date())"></span>
                    </small>
                </p>
                <search>
                    <form
                        @submit.prevent="search(0, 0, 0, searchValue)"
                        @input="searchValue = $event.target.value"
                        class="relative"
                    >
                        <label>
                            <span class="sr-only">
                                <?= $escaper->escapeHtml(__('Date, phone number or employee')) ?>
                            </span>
                            <input
                                id="quote-search"
                                type="search"
                                inputmode="search"
                                required
                                minlength="3"
                                placeholder="<?= $escaper->escapeHtml(__('Date, phone number or employee')) ?>"
                                class="w-full !min-h-[48px] !rounded-[18.75rem] !pr-16"
                                :disabled="searching"
                            >
                        </label>
                        <button
                            role="button"
                            class="inline-flex pr-6 pl-4 items-center justify-center absolute right-0 top-1/2 -translate-y-1/2 h-12 text-black"
                            :disabled="searching"
                        >
                            <?= $svgIcons->searchHtml("", 24, 24, ['aria-hidden' => 'true']) ?>
                            <span class="sr-only">
                                <?= $escaper->escapeHtml(__('Search')) ?>
                            </span>
                        </button>
                    </form>
                </search>
            </div>
        </div>
    </header>

    <details open>
        <summary>
            <span><?= $escaper->escapeHtml(__('All open quotes')) ?></span>
        </summary>
        <div>
            <?php // filter buttons ?>
            <menu
                class="flex flex-wrap gap-3 mb-6"
                x-cloak
                x-show="results.length > 0 || selectedFilters.location !== 0 || selectedFilters.employee !== 0 || selectedFilters.date !== 0"
            >
                <li>
                    <button
                        class="btn btn-secondary gap-2 relative z-20 peer aria-expanded:bg-white"
                        aria-controls="date-filter"
                        :aria-expanded="activeFilterMenus.includes('date')"
                        @click="toggleFilterMenu('date')"
                    >
                        <?= $escaper->escapeHtml(__('Date')) ?>
                        <span
                            x-cloak
                            x-show="appliedFilters.date"
                            class="w-6 aspect-square rounded-full border border-secondary-900/50 bg-secondary-300"
                        >
                            <?= $svgIcons->checkHtml("", 16, 16, ['aria-hidden' => 'true']) ?>
                        </span>
                    </button>
                </li>
                <li class="relative">
                    <button
                        class="btn btn-secondary gap-2 relative z-20 peer aria-expanded:bg-white"
                        aria-controls="sales-point-filter"
                        :aria-expanded="activeFilterMenus.includes('sales-point')"
                        @click="toggleFilterMenu('sales-point')"
                    >
                        <?= $escaper->escapeHtml(__('Sales Point')) ?>
                        <span
                            x-cloak
                            x-show="appliedFilters.location"
                            class="w-6 aspect-square rounded-full border border-secondary-900/50 bg-secondary-300"
                        >
                            <?= $svgIcons->checkHtml("", 16, 16, ['aria-hidden' => 'true']) ?>
                        </span>
                    </button>
                </li>
                <li class="relative">
                    <button
                        class="btn btn-secondary gap-2 relative z-20 peer aria-expanded:bg-white"
                        aria-controls="employee-filter"
                        :aria-expanded="activeFilterMenus.includes('employee')"
                        @click="toggleFilterMenu('employee')"
                    >
                        <?= $escaper->escapeHtml(__('Employee')) ?>
                        <span
                            x-cloak
                            x-show="appliedFilters.employee"
                            class="w-6 aspect-square rounded-full border border-secondary-900/50 bg-secondary-300"
                        >
                            <?= $svgIcons->checkHtml("", 16, 16, ['aria-hidden' => 'true']) ?>
                        </span>
                    </button>
                </li>
            </menu>

            <?php // searching ?>
            <div
                class="flex flex-col items-center text-xl text-primary gap-y-2"
                x-cloak
                x-show="searching"
            >
                <img src="<?= $block->getViewFileUrl('images/life-loading.gif') ?>" alt="Life Outdoor Living loader" width="50" height="50" />
                <?= $escaper->escapeHtml(__('Loading...')) ?>
            </div>

            <?php // no results ?>
            <h3 x-cloak x-show="searching === false && results.length === 0" class="text-base font-normal leading-relaxed"><?= $escaper->escapeHtml(__('No results found')) ?>.</h3>

            <?php // results ?>
            <ul class="grid gap-2" x-cloak x-show="results.length > 0 && searching === false">
                <template x-for="(result, index) in results" :key="`${result.quote_id}-${index}`">
                    <li class="relative bg-white rounded-xl p-4 border border-secondary-900/50">
                        <a
                            :href="`<?= $block->getBaseUrl(); ?>showroom_quote/selection/details/quote/${result.quote_id}`"
                            class="flex gap-4 justify-between after:asbolute after:inset-0 md:items-center"
                        >
                            <div class="grid gap-1.5 grow">
                                <h3
                                    class="text-base font-normal leading-relaxed"
                                    x-html="`<?= $escaper->escapeHtml(__('Quote number')); ?> ${result.quote_id}`"
                                ></h3>
                                <ul class="flex flex-wrap gap-x-4 gap-y-2 basis-full text-black/60 text-sm leading-normal">
                                    <li class="grid">
                                        <span class="text-xxs uppercase"><?= $escaper->escapeHtml(__('Date')) ?></span>
                                        <time :datetime="result.quote_date" x-html="formatDate(result.quote_date)"></time></li>
                                    <li class="grid">
                                        <span class="text-xxs uppercase"><?= $escaper->escapeHtml(__('Sales Point')) ?></span>
                                        <span x-html="result.location_code"></span>
                                    </li>
                                    <li class="grid">
                                        <span class="text-xxs uppercase"><?= $escaper->escapeHtml(__('Employee')) ?></span>
                                        <span>
                                            <span x-html="result.employee_firstname"></span>&nbsp;<span x-html="result.employee_lastname"></span>
                                        </span>
                                    </li>
                                    <li class="grid">
                                        <span class="text-xxs uppercase"><?= $escaper->escapeHtml(__('Customer')) ?></span>
                                        <span>
                                            <span
                                                x-html="result.customer_firstname"
                                                x-cloak
                                                x-show="result.customer_firstname"
                                            ></span>
                                            <span
                                                x-cloak
                                                x-show="result.customer_lastname"
                                            >
                                                <span x-html="result.customer_lastname"></span>
                                            </span>
                                            <span
                                                x-cloak
                                                x-show="!result.customer_firstname && !result.customer_lastname"
                                            >
                                                <?= $escaper->escapeHtml(__('No customer selected yet')) ?>
                                            </span>
                                        </span>
                                    </li>
                                </ul>
                            </div>
                            <span
                                x-html="formatPrice(result.grand_total)"
                                class="text-base font-semibold leading-relaxed text-primary">
                            </span>
                        </a>
                    </li>
                </template>
            </ul>
        </div>
    </details>

    <?php // filter menu ?>
    <div
        class="fixed top-0 bottom-0 left-0 w-full z-product-filter block motion-safe:transition motion-safe:duration-500 motion-safe:ease-in-out"
        @toggle-all-filters.window="visible = !visible"
        x-show="activeFilterMenus.length > 0"
        x-cloak
        x-transition:enter-start="opacity-0"
        x-transition:enter-end="opacity-100"
        x-transition:leave-start="opacity-100"
        x-transition:leave-end="opacity-0"
    >
        <div
            class="absolute inset-0 bg-black/30"
            @click.prevent="toggleFilterMenu('all', false)"
        >
        </div>

        <?php
            $filterClass = 'fixed right-0 z-10 h-full px-10 bg-white w-[30rem] max-w-[90%] overflow-y-auto transition-opacity';
            $activeFilterClass = 'opacity-100 pointer-events-auto';
            $inactiveFilterClass = 'opacity-0 pointer-events-none';
        ?>

        <div
            class="<?= $filterClass ?>"
            :class="
                activeFilterMenus.length > 0 ?
                '<?= $activeFilterClass ?>' :
                '<?= $inactiveFilterClass ?>'
            "
        >
            <header class="grid grid-cols-[1fr_auto_1fr] items-center py-6 sticky top-0 z-10 bg-white">
                <h2 class="font-normal text-base col-start-2">
                    <?= $escaper->escapeHtml(__('All filters')) ?>
                </h2>
                <button
                    @click.prevent="toggleFilterMenu('all', false)"
                    class="inline-flex min-w-[44px] min-h-[44px] ml-auto justify-center items-center translate-x-4"
                >
                    <?= $svgIcons->closeHtml("", 24, 24, ['aria-hidden' => 'true']) ?>
                    <span class="sr-only"><?= $escaper->escapeHtml(__('Close filter')) ?></span>
                </button>
            </header>
            <menu class="font-semibold text-base">
                <li>
                    <button
                        class="flex justify-between items-center py-3 w-full"
                        @click="toggleFilterMenu('date')"
                        aria-controls="date-filter"
                        :aria-expanded="activeFilterMenus.includes('date')"
                    >
                        <span class="title">
                            <?= $escaper->escapeHtml(__('Date')) ?>
                            <span class="sr-only"><?= $escaper->escapeHtml(__(' filter')) ?></span>
                        </span>
                        <svg
                            width="24"
                            height="24"
                            viewBox="0 0 24 24"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                            class="transition-transform translate-x-2 transform duration-300 ease-in-out opacity-50 -rotate-90"
                            aria-hidden="true"
                        >
                            <path
                                d="M19 9L12 16L5 9"
                                stroke="#4A5568"
                                stroke-width="2"
                                stroke-linecap="round"
                                stroke-linejoin="round"
                            />
                        </svg>
                    </button>
                </li>
                <li>
                    <button
                        class="flex justify-between items-center py-3 w-full"
                        @click="toggleFilterMenu('sales-point')"
                        aria-controls="sales-point-filter"
                        :aria-expanded="activeFilterMenus.includes('sales-point')"
                    >
                        <span class="title">
                            <?= $escaper->escapeHtml(__('Sales Point')) ?>
                            <span class="sr-only"><?= $escaper->escapeHtml(__(' filter')) ?></span>
                        </span>
                        <svg
                            width="24"
                            height="24"
                            viewBox="0 0 24 24"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                            class="transition-transform translate-x-2 transform duration-300 ease-in-out opacity-50 -rotate-90"
                            aria-hidden="true"
                        >
                            <path
                                d="M19 9L12 16L5 9"
                                stroke="#4A5568"
                                stroke-width="2"
                                stroke-linecap="round"
                                stroke-linejoin="round"
                            />
                        </svg>
                    </button>
                </li>
                <li>
                    <button
                        class="flex justify-between items-center py-3 w-full"
                        @click="toggleFilterMenu('employee')"
                        aria-controls="employee-filter"
                        :aria-expanded="activeFilterMenus.includes('employee')"
                    >
                        <span class="title">
                            <?= $escaper->escapeHtml(__('Employee')) ?>
                            <span class="sr-only"><?= $escaper->escapeHtml(__(' filter')) ?></span>
                        </span>
                        <svg
                            width="24"
                            height="24"
                            viewBox="0 0 24 24"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                            class="transition-transform translate-x-2 transform duration-300 ease-in-out opacity-50 -rotate-90"
                            aria-hidden="true"
                        >
                            <path
                                d="M19 9L12 16L5 9"
                                stroke="#4A5568"
                                stroke-width="2"
                                stroke-linecap="round"
                                stroke-linejoin="round"
                            />
                        </svg>
                    </button>
                </li>
            </menu>

        </div>

        <?php // date filter ?>
        <div
            id="date-filter"
            class="<?= $filterClass ?>"
            :class="
                activeFilterMenus.includes('date') ?
                '<?= $activeFilterClass ?>' :
                '<?= $inactiveFilterClass ?>'
            "
        >
            <header class="grid grid-cols-[1fr_auto_1fr] items-center py-6 sticky top-0 z-10 bg-white">
                <button
                    class="inline-flex min-w-[44px] min-h-[44px] mr-auto -ml-4 justify-center items-center"
                    :class="activeFilterMenus.includes('date') && 'animate-slide-in-right'"
                    @click.prevent="toggleFilterMenu('date', false)"
                >
                    <?= $svgIcons->arrowLeftHtml("", 24, 24, ['aria-hidden' => 'true']) ?>
                    <span class="sr-only"><?= $escaper->escapeHtml(__('All filters')) ?></span>
                </button>
                <h3 class="font-normal text-base">
                    <?= $escaper->escapeHtml(__('Date')) ?>
                    <span class="sr-only"><?= $escaper->escapeHtml(__(' filter')) ?></span>
                </h3>
                <button
                    @click.prevent="toggleFilterMenu('all', false)"
                    class="inline-flex min-w-[44px] min-h-[44px] ml-auto justify-center items-center translate-x-4"
                >
                    <?= $svgIcons->closeHtml("", 24, 24, ['aria-hidden' => 'true']) ?>
                    <span class="sr-only"><?= $escaper->escapeHtml(__('Close filter')) ?></span>
                </button>
            </header>
            <fieldset>
                <legend class="sr-only"><?= $escaper->escapeHtml(__('Date')) ?></legend>
                <div class="grid gap-4 pb-32">
                    <label>
                        <input
                            id="date-input"
                            type="date"
                            name="date"
                            max="<?= date('Y-m-d'); ?>"
                            x-model="selectedFilters.date"
                            class="w-full"
                        >
                    </label>
                </div>
            </fieldset>
        </div>

        <?php // sales point filters ?>
        <div
            id="sales-point-filter"
            class="<?= $filterClass ?>"
            :class="
                activeFilterMenus.includes('sales-point') ?
                '<?= $activeFilterClass ?>' :
                '<?= $inactiveFilterClass ?>'
            "
        >
            <header class="grid grid-cols-[1fr_auto_1fr] items-center py-6 sticky top-0 z-10 bg-white">
                <button
                    class="inline-flex min-w-[44px] min-h-[44px] mr-auto -ml-4 justify-center items-center"
                    :class="activeFilterMenus.includes('sales-point') && 'animate-slide-in-right'"
                    @click.prevent="toggleFilterMenu('sales-point', false)"
                >
                    <?= $svgIcons->arrowLeftHtml("", 24, 24, ['aria-hidden' => 'true']) ?>
                    <span class="sr-only"><?= $escaper->escapeHtml(__('All filters')) ?></span>
                </button>
                <h3 class="font-normal text-base">
                    <?= $escaper->escapeHtml(__('Sales Point')) ?>
                    <span class="sr-only"><?= $escaper->escapeHtml(__(' filter')) ?></span>
                </h3>
                <button
                    @click.prevent="toggleFilterMenu('all', false)"
                    class="inline-flex min-w-[44px] min-h-[44px] ml-auto justify-center items-center translate-x-4"
                >
                    <?= $svgIcons->closeHtml("", 24, 24, ['aria-hidden' => 'true']) ?>
                    <span class="sr-only"><?= $escaper->escapeHtml(__('Close filter')) ?></span>
                </button>
            </header>
            <fieldset>
                <legend class="sr-only"><?= $escaper->escapeHtml(__('Sales Point')) ?></legend>
                <div class="grid gap-4 pb-32">
                    <?php foreach ($salesPointFilterList as $key => $value): ?>
                        <label class="flex gap-2 relative pt-1 pl-12 min-h-[2rem]">
                            <input
                                id="sales-point-input-<?= $key ?>"
                                type="radio"
                                name="sales_point"
                                class="peer absolute opacity-0"
                                value="<?= $key ?>"
                                x-model="selectedFilters.location"
                            >
                            <span class="before:absolute before:top-0 before:left-0 before:w-8 before:aspect-square before:rounded-[0.25rem] before:border before:border-primary after:absolute after:top-1.5 after:left-1.5 after:w-5 after:aspect-square after:rounded-[0.25rem] after:bg-primary after:opacity-0 after:peer-checked:opacity-100 after:transition-opacity"><?= $value ?></span>
                        </label>
                    <?php endforeach; ?>
                </div>
            </fieldset>
        </div>

        <?php // employee filters ?>
        <div
            id="employee-filter"
            class="<?= $filterClass ?>"
            :class="
                activeFilterMenus.includes('employee') ?
                '<?= $activeFilterClass ?>' :
                '<?= $inactiveFilterClass ?>'
            "
        >
            <header class="grid grid-cols-[1fr_auto_1fr] items-center py-6 sticky top-0 z-10 bg-white">
                <button
                    class="inline-flex min-w-[44px] min-h-[44px] mr-auto -ml-4 justify-center items-center"
                    :class="activeFilterMenus.includes('employee') && 'animate-slide-in-right'"
                    @click.prevent="toggleFilterMenu('employee', false)"
                >
                    <?= $svgIcons->arrowLeftHtml("", 24, 24, ['aria-hidden' => 'true']) ?>
                    <span class="sr-only"><?= $escaper->escapeHtml(__('All filters')) ?></span>
                </button>
                <h3 class="font-normal text-base">
                    <?= $escaper->escapeHtml(__('Employee')) ?>
                    <span class="sr-only"><?= $escaper->escapeHtml(__(' filter')) ?></span>
                </h3>
                <button
                    @click.prevent="toggleFilterMenu('all', false)"
                    class="inline-flex min-w-[44px] min-h-[44px] ml-auto justify-center items-center translate-x-4"
                >
                    <?= $svgIcons->closeHtml("", 24, 24, ['aria-hidden' => 'true']) ?>
                    <span class="sr-only"><?= $escaper->escapeHtml(__('Close filter')) ?></span>
                </button>
            </header>
            <fieldset>
                <legend class="sr-only"><?= $escaper->escapeHtml(__('Employee')) ?></legend>
                <div class="grid gap-4 pb-32">
                    <?php foreach ($employeeList as $key => $value): ?>
                        <label class="flex gap-2 relative pt-1 pl-12 min-h-[2rem]">
                            <input
                                id="employee-input-<?= $key ?>"
                                type="radio"
                                name="employee"
                                class="peer absolute opacity-0"
                                value="<?= $key ?>"
                                x-model="selectedFilters.employee"
                            >
                            <span class="before:absolute before:top-0 before:left-0 before:w-8 before:aspect-square before:rounded-[0.25rem] before:border before:border-primary after:absolute after:top-1.5 after:left-1.5 after:w-5 after:aspect-square after:rounded-[0.25rem] after:bg-primary after:opacity-0 after:peer-checked:opacity-100 after:transition-opacity"><?= $value ?></span>
                        </label>
                    <?php endforeach; ?>
                </div>
            </fieldset>
        </div>

        <?php // clear/apply filters buttons ?>
        <menu
            class="flex gap-2 fixed right-0 bottom-0 z-20 px-10 py-6 bg-white w-[30rem] max-w-[90%]"
            :class="
                activeFilterMenus.length > 0 ?
                '<?= $activeFilterClass ?>' :
                '<?= $inactiveFilterClass ?>'
            "
        >
            <li>
                <button
                    @click.prevent="resetFilters"
                    class="btn btn-secondary"
                    :disabled="selectedFilters.location === 0 && selectedFilters.employee === 0 && selectedFilters.date === 0"
                >
                    <?= $escaper->escapeHtml(__('Clear all')) ?>
                </button>
            </li>
            <li class="grow">
                <button
                    @click.prevent="applyFilters"
                    class="btn w-full"
                >
                    <?= $escaper->escapeHtml(__('Apply filters')) ?>
                </button>
            </li>
        </menu>
    </div>
</div>
