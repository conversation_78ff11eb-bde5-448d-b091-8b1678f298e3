<?php

declare(strict_types=1);

namespace Life\ShowroomQuote\Plugin;

use Life\ShowroomBase\Api\ActorEmployeeResolverInterface;
use Life\ShowroomBase\Api\ScopeResolverInterface;
use Life\ShowroomSales\Setup\Patch\Data\AddSalesPointId;
use Magento\Customer\Model\SessionFactory as CustomerSessionFactory;
use Life\ShowroomQuote\Api\Data\CartInterface as ShowroomCartInterface;
use Magento\Quote\Api\CartRepositoryInterface;
use Magento\Quote\Api\Data\CartInterface;

/**
 * Class for quote repository
 */
class QuoteRepositoryPlugin
{
    /**
     * Constructor
     *
     * @param CustomerSessionFactory $customerSessionFactory
     * @param ActorEmployeeResolverInterface $actorEmployeeResolver
     * @param ScopeResolverInterface $showroomResolver
     */
    public function __construct(
        private readonly CustomerSessionFactory $customerSessionFactory,
        private readonly ActorEmployeeResolverInterface $actorEmployeeResolver,
        private readonly ScopeResolverInterface $showroomResolver
    ) {}

    /**
     * @param CartRepositoryInterface $subject
     * @param CartInterface $quote
     *
     * @SuppressWarnings(PHPMD.UnusedFormalParameter)
     */
    public function beforeSave(CartRepositoryInterface $subject, CartInterface $quote): void
    {
        $this->addQuoteComments($quote);
        $this->addEmployeeData($quote);
    }

    /**
     * Add quote comments
     *
     * @param CartInterface $quote
     */
    private function addQuoteComments(CartInterface $quote): void
    {
        if ($quote->getExtensionAttributes()?->getOrderComment()) {
            $quote->setData(
                ShowroomCartInterface::KEY_ORDER_COMMENT,
                $quote->getExtensionAttributes()->getOrderComment()
            );
        }
        if ($quote->getExtensionAttributes()?->getInternalComment()) {
            $quote->setData(
                ShowroomCartInterface::KEY_INTERNAL_COMMENT,
                $quote->getExtensionAttributes()->getInternalComment()
            );
        }
        if ($quote->getExtensionAttributes()?->getCustomerReference()) {
            $quote->setData(
                ShowroomCartInterface::KEY_CUSTOMER_REFERENCE,
                $quote->getExtensionAttributes()->getCustomerReference()
            );
        }
    }

    /**
     * Add employee data
     *
     * @param CartInterface $quote
     */
    private function addEmployeeData(CartInterface $quote): void
    {
        if (
            $this->showroomResolver->isShowroomWebsite()
            && !$quote->getData(ShowroomCartInterface::KEY_EMPLOYEE_ID)
            && !$quote->getData(ShowroomCartInterface::KEY_SALES_POINT_ID)
        ) {
            $customerSession = $this->customerSessionFactory->create();
            if ($this->actorEmployeeResolver->isEmployee()) {
                $quote->setData(
                    ShowroomCartInterface::KEY_EMPLOYEE_ID,
                    $customerSession->getCustomerId()
                );
                $quote->setData(
                    ShowroomCartInterface::KEY_SALES_POINT_ID,
                    $customerSession->getCustomer()->getData(AddSalesPointId::ATTRIBUTE_CODE)
                );
            }
        }
    }
}
