<?php

declare(strict_types=1);

namespace Life\ShowroomQuote\Model\ResourceModel;

use Life\ShowroomBase\Api\ScopeResolverInterface;
use Life\ShowroomCustomer\Api\CustomerQuoteInterface;
use Life\ShowroomQuote\Api\Data\CartInterface as ShowroomCartInterface;
use Magento\Framework\App\ResourceConnection;
use Magento\Framework\DB\Select;
use Magento\Framework\Exception\LocalizedException;
use Magento\Quote\Api\Data\CartInterface;
use Magento\Store\Model\StoreManagerInterface;
use Psr\Log\LoggerInterface;
use Zend_Db_Expr;

class QuoteSearch
{
    private const DEFAULT_PAGE = 1;
    private const DEFAULT_LIMIT = 100;
    private const MAX_SEARCH_TERM_LENGTH = 30;
    private const QUOTE_TABLE = 'quote';
    private const CUSTOMER_ENTITY_TABLE = 'customer_entity';
    private const CUSTOMER_ADDRESS_TABLE = 'customer_address_entity';
    private const LOCATION_TABLE = 'mageworx_location_entity';
    private const QUOTE_TABLE_KEY = 'q';
    private const CUSTOMER_ENTITY_TABLE_KEY = 'ce';
    private const EMPLOYEE_ENTITY_TABLE_KEY = 'empl';
    private const CUSTOMER_ADDRESS_TABLE_KEY = 'cae';
    private const LOCATION_TABLE_KEY = 'loc';
    private const SELECT_FIELDS = [
        self::QUOTE_TABLE_KEY => [
            'quote_id' => 'entity_id',
            'quote_date' => 'updated_at',
            'grand_total' => 'grand_total'
        ],
        self::CUSTOMER_ENTITY_TABLE_KEY => [
            'customer_id' => 'entity_id',
            'customer_firstname' => 'firstname',
            'customer_lastname' => 'lastname',
            'customer_email' => 'email'
        ],
        self::EMPLOYEE_ENTITY_TABLE_KEY => [
            'employee_id' => 'entity_id',
            'employee_firstname' => 'firstname',
            'employee_lastname' => 'lastname',
            'employee_email' => 'email'
        ],
        self::LOCATION_TABLE_KEY => [
            'location_id' => 'entity_id',
            'location_code' => 'code',
        ],
        self::CUSTOMER_ADDRESS_TABLE_KEY => []
    ];
    private const SEARCH_FIELDS = [
        self::QUOTE_TABLE_KEY => [
            'updated_at'
        ],
        self::EMPLOYEE_ENTITY_TABLE_KEY => [
            'firstname',
            'lastname'
        ],
        self::CUSTOMER_ENTITY_TABLE_KEY => [
            'email',
            'firstname',
            'lastname'
        ],
        self::CUSTOMER_ADDRESS_TABLE_KEY => [
            'telephone',
            'postcode',
            'street',
            'city'
        ]
    ];
    private const QUICK_SEARCH_FIELDS = [
        self::CUSTOMER_ADDRESS_TABLE_KEY => [
            'postcode',
            'street'
        ]
    ];

    /**
     * @param ResourceConnection $resourceConnection
     * @param ScopeResolverInterface $scopeResolver
     * @param StoreManagerInterface $storeManager
     * @param LoggerInterface $logger
     */
    public function __construct(
        private readonly ResourceConnection $resourceConnection,
        private readonly ScopeResolverInterface $scopeResolver,
        private readonly StoreManagerInterface $storeManager,
        private readonly LoggerInterface $logger
    ) {}

    /**
     * @param string $query
     * @param string $date
     * @param int $locationId
     * @param int $employeeId
     * @param int $page
     * @param int $limit
     *
     * @return array
     */
    public function search(
        string $query,
        string $date,
        int $locationId,
        int $employeeId,
        int $page = self::DEFAULT_PAGE,
        int $limit = self::DEFAULT_LIMIT
    ): array {
        $connection = $this->resourceConnection->getConnection();
        $select = $connection->select()->distinct();
        $select->from(
            [self::QUOTE_TABLE_KEY => $connection->getTableName(self::QUOTE_TABLE)],
            self::SELECT_FIELDS[self::QUOTE_TABLE_KEY]
        )->joinLeft(
            [self::EMPLOYEE_ENTITY_TABLE_KEY => $connection->getTableName(self::CUSTOMER_ENTITY_TABLE)],
            self::EMPLOYEE_ENTITY_TABLE_KEY . '.entity_id = ' . self::QUOTE_TABLE_KEY . '.' . ShowroomCartInterface::KEY_EMPLOYEE_ID,
            self::SELECT_FIELDS[self::EMPLOYEE_ENTITY_TABLE_KEY]
        )->joinLeft(
            [self::CUSTOMER_ENTITY_TABLE_KEY => $connection->getTableName(self::CUSTOMER_ENTITY_TABLE)],
            self::CUSTOMER_ENTITY_TABLE_KEY . '.entity_id = ' . self::QUOTE_TABLE_KEY . '.' . CustomerQuoteInterface::REAL_CUSTOMER_ID_ATTRIBUTE,
            self::SELECT_FIELDS[self::CUSTOMER_ENTITY_TABLE_KEY]
        )->joinLeft(
            [self::CUSTOMER_ADDRESS_TABLE_KEY => $connection->getTableName(self::CUSTOMER_ADDRESS_TABLE)],
            self::CUSTOMER_ADDRESS_TABLE_KEY . '.parent_id = ' . self::CUSTOMER_ENTITY_TABLE_KEY . '.entity_id',
            self::SELECT_FIELDS[self::CUSTOMER_ADDRESS_TABLE_KEY]
        )->joinLeft(
            [self::LOCATION_TABLE_KEY => $connection->getTableName(self::LOCATION_TABLE)],
            self::LOCATION_TABLE_KEY . '.entity_id = ' . self::QUOTE_TABLE_KEY . '.' . ShowroomCartInterface::KEY_SALES_POINT_ID,
            self::SELECT_FIELDS[self::LOCATION_TABLE_KEY]
        )->where(
            self::QUOTE_TABLE_KEY . '.' . CartInterface::KEY_IS_ACTIVE . ' != 0'
        )->where(
            self::QUOTE_TABLE_KEY . '.' . CartInterface::KEY_ITEMS_COUNT . ' > 0'
        )->where(
            self::QUOTE_TABLE_KEY . '.' . CartInterface::KEY_STORE_ID . ' IN (?) ', $this->getInStoreIds()
        )->where(
            self::QUOTE_TABLE_KEY . '.customer_id IS NULL'
        )->limitPage(
            $page,
            $limit
        );

        $this->applyFilters($select, $date, $locationId, $employeeId);
        $this->applyWhereCondition($select, $query);

        return $connection->fetchAll($select);
    }

    /**
     * Search by the most popular combination "postcode + house number"
     *
     * @param string $query
     * @param string $date
     * @param int $locationId
     * @param int $employeeId
     * @param int $page
     * @param int $limit
     *
     * @return array
     */
    public function quickSearch(
        string $query,
        string $date,
        int $locationId,
        int $employeeId,
        int $page = self::DEFAULT_PAGE,
        int $limit = self::DEFAULT_LIMIT
    ): array {
        $queryParts = explode(' ', $query);
        if (count($queryParts) !== 2) {
            return [];
        }

        $whereParts = [];
        $queryParts = array_combine(self::QUICK_SEARCH_FIELDS[self::CUSTOMER_ADDRESS_TABLE_KEY], $queryParts);
        foreach ($queryParts as $field => $queryPart) {
            $queryPart = preg_replace('/[^A-Za-z0-9]/', '', $queryPart);
            $queryPart = substr($queryPart, 0, self::MAX_SEARCH_TERM_LENGTH);
            $whereParts[] = "`" . self::CUSTOMER_ADDRESS_TABLE_KEY . "`.`$field` LIKE '%{$queryPart}%'";
        }
        $whereCondition = implode(' AND ', $whereParts);

        $connection = $this->resourceConnection->getConnection();
        $select = $connection->select()->distinct();
        $select->from(
            [self::QUOTE_TABLE_KEY => $connection->getTableName(self::QUOTE_TABLE)],
            self::SELECT_FIELDS[self::QUOTE_TABLE_KEY]
        )->joinLeft(
            [self::EMPLOYEE_ENTITY_TABLE_KEY => $connection->getTableName(self::CUSTOMER_ENTITY_TABLE)],
            self::EMPLOYEE_ENTITY_TABLE_KEY . '.entity_id = ' . self::QUOTE_TABLE_KEY . '.' . ShowroomCartInterface::KEY_EMPLOYEE_ID,
            self::SELECT_FIELDS[self::EMPLOYEE_ENTITY_TABLE_KEY]
        )->joinLeft(
            [self::CUSTOMER_ENTITY_TABLE_KEY => $connection->getTableName(self::CUSTOMER_ENTITY_TABLE)],
            self::CUSTOMER_ENTITY_TABLE_KEY . '.entity_id = ' . self::QUOTE_TABLE_KEY . '.' . CustomerQuoteInterface::REAL_CUSTOMER_ID_ATTRIBUTE,
            self::SELECT_FIELDS[self::CUSTOMER_ENTITY_TABLE_KEY]
        )->joinLeft(
            [self::CUSTOMER_ADDRESS_TABLE_KEY => $connection->getTableName(self::CUSTOMER_ADDRESS_TABLE)],
            self::CUSTOMER_ADDRESS_TABLE_KEY . '.parent_id = ' . self::CUSTOMER_ENTITY_TABLE_KEY . '.entity_id',
            self::SELECT_FIELDS[self::CUSTOMER_ADDRESS_TABLE_KEY]
        )->joinLeft(
            [self::LOCATION_TABLE_KEY => $connection->getTableName(self::LOCATION_TABLE)],
            self::LOCATION_TABLE_KEY . '.entity_id = ' . self::QUOTE_TABLE_KEY . '.' . ShowroomCartInterface::KEY_SALES_POINT_ID,
            self::SELECT_FIELDS[self::LOCATION_TABLE_KEY]
        )->where(
            new Zend_Db_Expr($whereCondition)
        )->where(
            self::QUOTE_TABLE_KEY . '.' . CartInterface::KEY_IS_ACTIVE . ' != 0'
        )->where(
            self::QUOTE_TABLE_KEY . '.' . CartInterface::KEY_ITEMS_COUNT . ' > 0'
        )->where(
            self::QUOTE_TABLE_KEY . '.' . CartInterface::KEY_STORE_ID . ' IN (?) ', $this->getInStoreIds()
        )->limitPage(
            $page,
            $limit
        );

        $this->applyFilters($select, $date, $locationId, $employeeId);

        return $connection->fetchAll($select);
    }

    /**
     * @param Select $select
     * @param string $date
     * @param int $locationId
     * @param int $employeeId
     *
     * @return void
     */
    private function applyFilters(Select $select, string $date, int $locationId, int $employeeId): void
    {
        if ($date !== '0') {
            $select->where(
                self::QUOTE_TABLE_KEY . '.' . CartInterface::KEY_UPDATED_AT . ' LIKE ?',
                $date . '%'
            );
        }

        if ($locationId !== 0) {
            $select->where(
                self::QUOTE_TABLE_KEY . '.' . ShowroomCartInterface::KEY_SALES_POINT_ID . ' = ?',
                $locationId
            );
        }

        if ($employeeId !== 0) {
            $select->where(
                self::QUOTE_TABLE_KEY . '.' . ShowroomCartInterface::KEY_EMPLOYEE_ID . ' = ?',
                $employeeId
            );
        }
    }

    /**
     * @param Select $select
     * @param string $query
     *
     * @return void
     */
    private function applyWhereCondition(Select $select, string $query): void
    {
        if ($query === '0') {
            return;
        }

        $whereParts = [];
        $queryParts = explode(' ', $query);

        foreach ($queryParts as $key => $part) {
            $part = preg_replace('/[^A-Za-z0-9@.-]/', '', $part);
            $part = substr($part, 0, self::MAX_SEARCH_TERM_LENGTH);
            foreach (self::SEARCH_FIELDS as $table => $fields) {
                foreach ($fields as $field) {
                    if (str_contains($part, '-') || str_contains($part, '.')) {
                        if ($field !== CartInterface::KEY_UPDATED_AT) {
                            continue;
                        }
                        $part = date("Y-m-d", strtotime($part));
                    }
                    if (str_contains($part, '@') && !str_contains($field, 'email')) {
                        continue;
                    }
                    $whereParts[$key][] = "`$table`.`$field` LIKE '%$part%'";
                }
            }
            if ($whereParts[$key]) {
                $whereParts[$key] = '(' . implode(' OR ', $whereParts[$key]) . ')';
            }
        }

        $whereCondition = implode(' AND ', $whereParts);
        $select->where(new Zend_Db_Expr($whereCondition));
    }

    /**
     * @return array
     */
    private function getInStoreIds(): array
    {
        try {
            $result = $this->storeManager->getWebsite($this->scopeResolver->getShowroomWebsiteId())->getStoreIds();
        } catch (LocalizedException $exception) {
            $this->logger->critical($exception->getMessage());
            $result = [];
        }

        return $result;
    }
}
