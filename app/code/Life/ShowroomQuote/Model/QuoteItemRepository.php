<?php

declare(strict_types=1);

namespace Life\ShowroomQuote\Model;

use Life\ShowroomBase\Api\ActorEmployeeResolverInterface;
use Life\ShowroomBase\Api\ScopeResolverInterface;
use Life\ShowroomQuote\Api\QuoteItemRepositoryInterface;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Customer\Model\Session;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Framework\Exception\CouldNotSaveException;
use Magento\Framework\Exception\InputException;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Quote\Api\CartItemRepositoryInterface;
use Magento\Quote\Api\Data\CartItemInterface;

/**
 * Class for quote item repository
 */
class QuoteItemRepository implements QuoteItemRepositoryInterface
{
    /**
     * Constructor
     *
     * @param CartItemRepositoryInterface $cartItemRepository
     * @param CustomerRepositoryInterface $customerRepository
     * @param ScopeResolverInterface $scopeResolver
     * @param ActorEmployeeResolverInterface $actorEmployeeResolver
     * @param Session $customerSession
     */
    public function __construct(
        private readonly CartItemRepositoryInterface $cartItemRepository,
        private readonly CustomerRepositoryInterface $customerRepository,
        private readonly ScopeResolverInterface $scopeResolver,
        private readonly ActorEmployeeResolverInterface $actorEmployeeResolver,
        private readonly Session $customerSession
    ) {}

    /**
     * Save by employee
     *
     * @param CartItemInterface $cartItem
     * @param int $employeeCustomerId
     * @return CartItemInterface
     * @throws LocalizedException
     * @throws CouldNotSaveException
     * @throws InputException
     * @throws NoSuchEntityException
     */
    public function saveByEmployee(CartItemInterface $cartItem, int $employeeCustomerId): CartItemInterface
    {
        $employeeCustomer = $this->customerRepository->getById($employeeCustomerId);
        if (!$this->scopeResolver->isShowroomWebsite()
            || (int)$employeeCustomer->getGroupId() !== (int)$this->actorEmployeeResolver->getEmployeeGroupId()
        ) {
            throw new LocalizedException(__('Only an employee can save the quote item.'));
        }
        if (!$this->customerSession->getCustomerId()) {
            $this->customerSession->setCustomerId($employeeCustomerId);
        }
        return $this->cartItemRepository->save($cartItem);
    }
}
