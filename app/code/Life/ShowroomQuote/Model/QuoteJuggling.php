<?php

declare(strict_types=1);

namespace Life\ShowroomQuote\Model;

use Life\ShowroomQuote\Api\QuoteJugglingInterface;
use Magento\Checkout\Model\Session as CheckoutSession;
use Magento\Customer\Api\Data\CustomerInterface;
use Magento\Framework\Exception\LocalizedException;
use Magento\Quote\Api\CartRepositoryInterface;
use Magento\Quote\Api\Data\CartInterface;
use Psr\Log\LoggerInterface;

class QuoteJuggling implements QuoteJugglingInterface
{
    /**
     * @param CartRepositoryInterface $cartRepository
     * @param CheckoutSession $checkoutSession
     * @param LoggerInterface $logger
     */
    public function __construct(
        private readonly CartRepositoryInterface $cartRepository,
        private readonly CheckoutSession $checkoutSession,
        private readonly LoggerInterface $logger
    ) {}

    /**
     * @param CartInterface $quote
     * @param CustomerInterface $customer
     *
     * @return CartInterface
     */
    public function unshelve(CartInterface $quote, CustomerInterface $customer): CartInterface
    {
        $quote->setCustomer($customer);
        $this->cartRepository->save($quote);
        $this->checkoutSession->setQuoteId($quote->getId());

        return $quote;
    }

    /**
     * @param CartInterface $quote
     *
     * @return bool
     *
     * @throws LocalizedException
     */
    public function park(CartInterface $quote): bool
    {
        try {
            $quoteCustomer = $quote->getCustomer();
            $quoteCustomer->setId(null);
            $quote->setCustomer($quoteCustomer);
            $quote->setData('customer_tax_class_id');
            $quote->setData('customer_email');
            $quote->setData('customer_firstname');
            $quote->setData('customer_middlename');
            $quote->setData('customer_lastname');
            $quote->setData('customer_gender');
            $quote->setData('remote_ip');
            $this->cartRepository->save($quote);
            $this->checkoutSession->clearStorage();
            $this->logger->debug(
                __('The quote #%1 is successfully parked.', $quote->getId())
            );
        } catch (\Exception $exception) {
            $this->logger->critical($exception);
            throw new LocalizedException(
                __('An error occurred during parking the quote: %1', $exception->getMessage())
            );
        }

        return true;
    }

    /**
     * @param CartInterface $quote
     *
     * @return bool
     */
    public function delete(CartInterface $quote): bool
    {
        $this->cartRepository->delete($quote);
        $this->checkoutSession->clearStorage();

        return true;
    }
}
