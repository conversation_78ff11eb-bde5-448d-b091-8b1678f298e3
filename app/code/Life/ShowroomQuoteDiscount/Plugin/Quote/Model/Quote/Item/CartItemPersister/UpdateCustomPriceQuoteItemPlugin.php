<?php

declare(strict_types=1);

namespace Life\ShowroomQuoteDiscount\Plugin\Quote\Model\Quote\Item\CartItemPersister;

use Magento\Quote\Api\Data\CartInterface;
use Magento\Quote\Api\Data\CartItemInterface;
use Magento\Quote\Model\Quote\Item\CartItemPersister;
use Life\ShowroomQuoteDiscount\Api\Data\CartItemInterface as ShowroomQuoteItemInterface;

/**
 * Class for update custom price quote item
 */
class UpdateCustomPriceQuoteItemPlugin
{
    /**
     * After save
     *
     * @param CartItemPersister $subject
     * @param CartItemInterface $resultItem
     * @param CartInterface $quote
     * @param CartItemInterface $item
     * @return CartItemInterface
     */
    public function afterSave(
        CartItemPersister $subject,
        CartItemInterface $resultItem,
        CartInterface $quote,
        CartItemInterface $item
    ) {
        if ($item->hasData(ShowroomQuoteItemInterface::KEY_ORIGINAL_CUSTOM_PRICE)) {
            $originalCustomPrice = $item->getData(ShowroomQuoteItemInterface::KEY_ORIGINAL_CUSTOM_PRICE);
            $resultItem->setData(ShowroomQuoteItemInterface::KEY_ORIGINAL_CUSTOM_PRICE, $originalCustomPrice);
        }
        if ($item->hasData(ShowroomQuoteItemInterface::KEY_CUSTOM_PRICE)) {
            $customPrice = $item->getData(ShowroomQuoteItemInterface::KEY_CUSTOM_PRICE);
            $resultItem->setData(ShowroomQuoteItemInterface::KEY_CUSTOM_PRICE, $customPrice);
        }
        return $resultItem;
    }
}
