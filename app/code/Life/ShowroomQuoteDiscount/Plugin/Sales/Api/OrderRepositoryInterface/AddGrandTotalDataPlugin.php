<?php

declare(strict_types=1);

namespace Life\ShowroomQuoteDiscount\Plugin\Sales\Api\OrderRepositoryInterface;

use Magento\Sales\Api\Data\OrderExtensionFactory;
use Magento\Sales\Api\Data\OrderInterface;
use Magento\Sales\Api\Data\OrderSearchResultInterface;
use Magento\Sales\Api\OrderRepositoryInterface;
use Life\ShowroomQuoteDiscount\Api\Data\OrderInterface as ShowroomOrderInterface;
use Psr\Log\LoggerInterface;

/**
 * Class for add custom extension attributes
 */
class AddGrandTotalDataPlugin
{
    /**
     * Constructor
     *
     * @param OrderExtensionFactory $extensionFactory
     * @param LoggerInterface $logger
     */
    public function __construct(
        private readonly OrderExtensionFactory $extensionFactory,
        private readonly LoggerInterface $logger
    ) {}

    /**
     * After Get
     *
     * @param OrderRepositoryInterface $subject
     * @param OrderInterface $order
     * @return OrderInterface
     */
    public function afterGet(
        OrderRepositoryInterface $subject,
        OrderInterface $order
    ): OrderInterface {
        $this->addGrandTotalData($order);
        return $order;
    }

    /**
     * After get list
     *
     * @param OrderRepositoryInterface $subject
     * @param OrderSearchResultInterface $searchResult
     * @return OrderSearchResultInterface
     */
    public function afterGetList(
        OrderRepositoryInterface $subject,
        OrderSearchResultInterface $searchResult
    ): OrderSearchResultInterface {
        $orders = $searchResult->getItems();
        foreach ($orders as $order) {
            $this->addGrandTotalData($order);
        }
        return $searchResult;
    }

    /**
     * Set extension attribute data
     *
     * @param OrderInterface $order
     */
    private function addGrandTotalData(OrderInterface $order): void
    {
        $extensionAttributes = $order->getExtensionAttributes();
        $extensionAttributes = $extensionAttributes ?: $this->extensionFactory->create();
        $extensionAttributes->setCustomGrandTotal($order->getData(ShowroomOrderInterface::CUSTOM_GRAND_TOTAL));
        $extensionAttributes->setOriginalGrandTotal($order->getData(ShowroomOrderInterface::ORIGINAL_GRAND_TOTAL));
        $order->setExtensionAttributes($extensionAttributes);
    }
}
