<?php

declare(strict_types=1);

use Magento\Framework\Escaper;
use Magento\Sales\Block\Adminhtml\Order\View\Info;
use Life\ShowroomSales\ViewModel\Adminhtml\ShowroomSalesDataProvider;
use Life\ShowroomSales\Api\Data\OrderInterface as ShowroomOrderInterface;

/** @var Escaper $escaper */
/** @var Info $block */
/** @var ShowroomSalesDataProvider $viewModel */

$order = $block->getOrder();
$orderComment = $order->getData(ShowroomOrderInterface::ORDER_COMMENT);
$internalComment = $order->getData(ShowroomOrderInterface::INTERNAL_COMMENT);
$customerReference = $order->getData(ShowroomOrderInterface::CUSTOMER_REFERENCE);
$viewModel = $block->getData('showroom_order_data');
?>
<?php if ($orderComment || $internalComment || $customerReference): ?>
    <section class="admin__page-section order-comment">
        <div class="admin__page-section-title">
            <span class="title"><?= $escaper->escapeHtml(__('Order Comments')) ?></span>
        </div>
        <div class="admin__page-section-content">
            <div class="admin__page-section-item order-payment-method">

                <?php if ($orderComment): ?>
                    <div class="admin__page-section-item-title">
                        <span class="title"><?= $escaper->escapeHtml(__('Order Comment')) ?></span>
                    </div>
                    <div class="admin__page-section-item-content">
                        <?= $escaper->escapeHtml($orderComment) ?>
                    </div>
                <?php endif; ?>
                <br/>
                <?php if ($internalComment): ?>
                    <div class="admin__page-section-item-title">
                        <span class="title"><?= $escaper->escapeHtml(__('Internal Comment')) ?></span>
                    </div>
                    <div class="admin__page-section-item-content">
                        <?= $escaper->escapeHtml($internalComment) ?>
                    </div>
                <?php endif; ?>
                <br/>
                <?php if ($customerReference): ?>
                    <div class="admin__page-section-item-title">
                        <span class="title"><?= $escaper->escapeHtml(__('Customer Reference')) ?></span>
                    </div>
                    <div class="admin__page-section-item-content">
                        <?= $escaper->escapeHtml($customerReference) ?>
                    </div>
                <?php endif; ?>

            </div>
        </div>
    </section>
<?php endif; ?>
