<?php

declare(strict_types=1);

use Magento\Framework\Escaper;
use Magento\Sales\Block\Adminhtml\Order\View\Info;
use Life\ShowroomSales\ViewModel\Adminhtml\ShowroomSalesDataProvider;
use Life\ShowroomSales\Api\Data\OrderInterface as ShowroomOrderInterface;

/** @var Escaper $escaper */
/** @var Info $block */
/** @var ShowroomSalesDataProvider $viewModel */

$order = $block->getOrder();
$employeeId = $order->getData(ShowroomOrderInterface::EMPLOYEE_ID);
$salesPointId = $order->getData(ShowroomOrderInterface::SALES_POINT_ID);
$viewModel = $block->getData('showroom_order_data');
?>

<?php if ($employeeId): ?>
    <?php
    $customerUrl = $block->getUrl('customer/index/edit', ['id' => $employeeId]);
    ?>
    <tr>
        <th><?= $escaper->escapeHtml(__('Employee')) ?></th>
        <td>
            <a href="<?= $customerUrl ?>" target="_blank">
                <span id="employee_id"><?= $viewModel->getEmployeeName((int)$employeeId) ?: $employeeId ?></span>
            </a>
        </td>
    </tr>
<?php endif; ?>
<?php if ($salesPointId): ?>
    <tr>
        <th><?= $escaper->escapeHtml(__('Sales Point')) ?></th>
        <td><span id="sales_point_id"><?= $viewModel->getLocationName((int)$salesPointId) ?: $salesPointId ?></span></td>
    </tr>
<?php endif; ?>
