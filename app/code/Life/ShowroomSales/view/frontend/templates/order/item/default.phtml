<?php

declare(strict_types=1);

use Life\ShowroomCatalog\ViewModel\ProductImage;
use Magento\Framework\Escaper;
use Magento\Framework\Locale\LocaleFormatter;
use Magento\Sales\Block\Order\Item\Renderer\DefaultRenderer;
use Magento\Sales\Model\Order\Item;

/**
 * @var Escaper $escaper
 * @var DefaultRenderer $block
 * @var LocaleFormatter $localeFormatter
 * @var ProductImage $productImageViewModel
 * @var Item $_item
 */

$_item = $block->getItem();
$productImageViewModel = $block->getData('product_image');
?>

<div class="flex flex-col relative bg-white rounded-xl border border-secondary-900/50 gap-1.5 border-l-[5px] border-l-primary md:flex-row md:items-center">
    <div class="flex flex-col grow md:flex-row">
        <div class="product-item-photo flex py-4 items-center justify-center w-full shrink-0 md:w-[10rem] md:py-0">
            <?php if ($_item->getProduct()): ?>
            <?= $productImageViewModel?->getImage($_item->getProduct(), 'cart_page_product_thumbnail') /** @phpstan-ignore-line */
                ->setTemplate('Magento_Catalog::product/image.phtml')
                ->toHtml() ?>
            <?php endif; ?>
        </div>
        <div class="grow border-t border-secondary-900/50 p-4 md:border-t-0 md:border-l">
            <span class="text-base leading-[1.875rem]">
                <?= $block->escapeHtml($_item->getName()) ?>
            </span>

            <?php // attributes ?>
            <?php if ($_options = $block->getItemOptions()): ?>
            <ul class="flex flex-wrap gap-2 mt-1.5 text-sm text-primary/50">

                <?php
                    // TODO: check if this shouldn't be ProductSubtitle
                    // see app/code/Life/ShowroomQuoteHyva/view/frontend/templates/quote/selection/details/item/default.phtml:68
                ?>
                <?php foreach ($_options as $_option): ?>
                    <li>
                        <?= $block->escapeHtml($_option['value']) ?>
                    </li>
                <?php endforeach; ?>
            </ul>
            <?php endif; ?>

            <div class="flex">
                <?php // price ?>
                <div class="flex flex-col gap-0.5 items-start mt-2 md:flex-row md:items-center md:gap-2">
                    <span class="text-sm font-bold">
                        <?= $block->getItemRowTotalHtml() ?>
                    </span>
                </div>

                <?php // qty ?>
                <div class="inline-flex justify-center items-center w-8 ml-auto aspect-square rounded-md border border-[#DCDCDC]">
                    <?= $block->escapeHtml($localeFormatter->formatNumber((float) $block->getItem()->getQtyOrdered()))?>
                </div>
            </div>
        </div>
    </div>
</div>
