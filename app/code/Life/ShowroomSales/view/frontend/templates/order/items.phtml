<?php

use Magento\ConfigurableProduct\Model\Product\Type\Configurable;
use Magento\Framework\Escaper;
use Magento\Sales\Block\Order\Items;

/** @var Items $block */
/** @var Escaper $escaper */

$order = $block->getOrder();
$items = $order?->getAllItems() ?: [];
$cardCls = 'flex flex-col relative p-4 bg-white rounded-xl p-4 border border-secondary-900/50';
?>
<div class="grid gap-2">
    <?php foreach ($items as $item): ?>
        <?php
        $parentItem = $item->getParentItem();
        if ($parentItem && $parentItem->getData('product_type') === Configurable::TYPE_CODE) {
            continue;
        }
        ?>
        <?= $block->getItemHtml($item) ?>
    <?php endforeach; ?>
</div>
<div class="<?= $cardCls?> p-4">
    <div class="flex justify-between gap-2 border-b border-secondary-900/50 py-2 leading-8 font-semibold">
        <div class="w-7/12 text-left md:w-auto">
            <?= __('Subtotal'); ?>
        </div>
        <div class="w-5/12 text-right md:w-auto">
            <?= $order->formatPrice($order->getSubtotal()); ?>
        </div>
    </div>
    <div class="flex justify-between gap-2 py-2 leading-8">
        <div class="w-7/12 text-left md:w-auto" x-html="segment.title">
            <?= __('Tax'); ?>
        </div>
        <div class="w-5/12 text-right text-primary md:w-auto" x-text="hyva.formatPrice(segment.value)">
            <?= $order->formatPrice($order->getTaxAmount()); ?>
        </div>
    </div>
    <?php if ($order->getDiscountAmount() > 0): ?>
        <div class="flex justify-between gap-2 py-2 leading-8">
            <div class="w-7/12 text-left md:w-auto" x-html="segment.title">
                <?= __('Discount'); ?>
            </div>
            <div class="w-5/12 text-right text-primary md:w-auto" x-text="hyva.formatPrice(segment.value)">
                <?= $order->formatPrice($order->getDiscountAmount()); ?>
            </div>
        </div>
    <?php endif; ?>
    <div class="flex justify-between gap-2 py-2 leading-8">
        <div class="w-7/12 text-left md:w-auto" x-html="segment.title">
            <?= __('Shipping'); ?>
        </div>
        <div class="w-5/12 text-right text-primary md:w-auto" x-text="hyva.formatPrice(segment.value)">
            <?= $order->formatPrice($order->getShippingAmount()); ?>
        </div>
    </div>
    <div class="flex justify-between gap-2 py-2 leading-8 font-semibold">
        <div class="w-7/12 text-left md:w-auto">
            <?= __('Grand Total'); ?>
        </div>
        <div class="w-5/12 text-right md:w-auto">
            <?= $order->formatPrice($order->getGrandTotal()); ?>
        </div>
    </div>
</div>
