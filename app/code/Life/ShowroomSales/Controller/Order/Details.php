<?php

declare(strict_types=1);

namespace Life\ShowroomSales\Controller\Order;

use Life\ShowroomBase\Api\ActorEmployeeResolverInterface;
use Life\ShowroomBase\Api\ScopeResolverInterface;
use Life\ShowroomCustomer\ViewModel\CustomerDataProvider;
use Magento\Framework\App\Action\HttpGetActionInterface;
use Magento\Framework\App\RequestInterface;
use Magento\Framework\App\ResponseInterface;
use Magento\Framework\Controller\Result\Redirect;
use Magento\Framework\Controller\ResultFactory;
use Magento\Framework\Controller\ResultInterface;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\UrlInterface;
use Magento\Store\Model\StoreManagerInterface;
use Psr\Log\LoggerInterface;

class Details implements HttpGetActionInterface
{
    /**
     * @param ScopeResolverInterface $showroomResolver
     * @param ActorEmployeeResolverInterface $actorEmployeeResolver
     * @param RequestInterface $request
     * @param ResponseInterface $response
     * @param UrlInterface $urlBuilder
     * @param StoreManagerInterface $storeManager
     * @param ResultFactory $resultFactory
     * @param LoggerInterface $logger
     */
    public function __construct(
        private readonly ScopeResolverInterface $showroomResolver,
        private readonly ActorEmployeeResolverInterface $actorEmployeeResolver,
        private readonly RequestInterface $request,
        private readonly ResponseInterface $response,
        private readonly UrlInterface $urlBuilder,
        private readonly StoreManagerInterface $storeManager,
        private readonly ResultFactory $resultFactory,
        private readonly LoggerInterface $logger
    ) {}

    /**
     * @return ResultInterface|ResponseInterface|Redirect
     */
    public function execute()
    {
        if (!$this->isValidRequest()) {
            try {
                return $this->resultFactory->create(ResultFactory::TYPE_REDIRECT)
                    ->setUrl($this->storeManager->getStore()->getBaseUrl());
            } catch (NoSuchEntityException $exception) {
                $this->logger->critical($exception);
            }
        }

        $orderId = (int)$this->getRequest()->getParam('order', 0);
        if (!$orderId) {
            return $this->resultFactory->create(ResultFactory::TYPE_REDIRECT)
                ->setUrl($this->urlBuilder->getUrl(CustomerDataProvider::CUSTOMER_SEARCH_ROUTE));
        }

        return $this->resultFactory->create(ResultFactory::TYPE_PAGE);
    }

    /**
     * Is valid request
     */
    public function isValidRequest(): bool
    {
        return $this->showroomResolver->isShowroomWebsite() && $this->actorEmployeeResolver->isEmployee();
    }

    /**
     * Retrieve request object
     *
     * @return RequestInterface
     */
    public function getRequest(): RequestInterface
    {
        return $this->request;
    }

    /**
     * Retrieve response object
     *
     * @return ResponseInterface
     */
    public function getResponse(): ResponseInterface
    {
        return $this->response;
    }
}
