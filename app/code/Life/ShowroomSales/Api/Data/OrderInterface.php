<?php

declare(strict_types=1);

namespace Life\ShowroomSales\Api\Data;

use Magento\Sales\Api\Data\OrderInterface as OriginOrderInterface;

/**
 * {@inheritdoc}
 */
interface OrderInterface extends OriginOrderInterface
{
    public const EMPLOYEE_ID = 'employee_id';

    public const SALES_POINT_ID = 'sales_point_id';

    public const ORDER_COMMENT = 'order_comment';

    public const INTERNAL_COMMENT = 'internal_comment';

    public const CUSTOMER_REFERENCE = 'customer_reference';
}
