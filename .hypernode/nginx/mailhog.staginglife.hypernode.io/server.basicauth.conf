# Hypernode Template Checksum: 38f34013942903780812128bc0985e93
# Restrict access to Mailhog based on IP
satisfy any;
# allow *******; # Example IP
allow *************;  # Redkiwi
allow **************; # <PERSON> (Product Owner)
allow ************;   # LOL Office
allow ************;   # LOL Office
allow ************;   # Magecom VPN
deny all;
auth_basic "login required";
auth_basic_user_file /data/web/nginx/htpasswd-development;
