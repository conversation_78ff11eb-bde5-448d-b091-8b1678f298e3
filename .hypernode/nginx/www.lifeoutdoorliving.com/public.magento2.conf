# Hypernode Template Checksum: a712a8e97dc6f32d6cd374831745598c
root /data/web/public;


## Redirecting to varnish
location / {
    set $log_handler varnish;

    proxy_pass http://127.0.0.1:6081;
    proxy_read_timeout 900s;  # equal to fastcgi_read_timeout at handlers.conf:16
    proxy_set_header X-Real-IP  $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_set_header X-Forwarded-Port $server_port;
    proxy_set_header Host $http_host;
}

# in case the user wants to route a part of the site directly to FPM in override files
include /etc/nginx/handlers.conf;
