---
title: Showroom Login
---

# Feature: Showroom Login

The Showroom website can be accessed only by registered Employees and Dealers. Login can be done with a username and not email.

```
TODO
========================
1. We need to add passwords for the test user's (<PERSON><PERSON><PERSON><PERSON>, Dealer, Customer) to the env
2. We need to confirm that users remain available on the test env (including usernames and passwords)
3. Customer - random customer from E-commerce website
```
## Background:

- Given an Employee exists
- And a Dealer exists
- And a Customer exists

## Scenario: Successful Login Process for Employee

- Given Employee goes to the Showroom website Login Page
- When Employee enters the correct username and correct password
- Then Employee is redirected to the Main Page of the Showroom website

## Scenario: Successful Logout Process for Employee

- Given Employee goes to the Showroom website Login Page
- And Employee enters the correct username and correct password
- And Employee is redirected to the Main Page of the Showroom website
- When Employee clicks the Logout button
- Then Employee is redirected to the Showroom website Login Page

## Scenario: Successful Login Process for Dealer

- Given Dealer goes to the Showroom website Login Page
- When Dealer enters the correct username and correct password
- Then Dealer is redirected to the Main Page of the Showroom website

## Scenario: Unsuccessful Login Process for Customer

- Given Customer goes to the Showroom website Login Page
- When Customer enters the correct username and correct password
- Then Customer can see an error

## Scenario: Unsuccessful Login Process - Wrong Username

- Given Employee goes to the Showroom website Login Page
- When Employee enters a wrong username and correct password
- Then Employee can see an error

## Scenario: Unsuccessful Login Process - Wrong Password

- Given Employee goes to the Showroom website Login Page
- When Employee enters the correct username and wrong password
- Then Employee can see an error

## Scenario: Unsuccessful Login Process - Correct Email

- Given Employee goes to the Showroom website Login Page
- When Employee enters their email instead of username and correct password
- Then Employee can see an error
