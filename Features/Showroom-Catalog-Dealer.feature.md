# Feature: Showroom Catalog Dealer

As a Dealer I can see Product Listing Page, Product Details Page with products but without prices, and I can search for them.

## Background:

- Given an Dealer exists
- And Product 1 exist
- And Product 2 exist
- And Dealer is authorized in Showroom


## Scenario Outline: Browsing a list of products as Dealer

- Given the Dealer visits the category "<slug>"
- When the Dealer checks for products
- Then products should be visible without prices

### Examples:

  | slug       |
  | ---------- |
  | parasols   |
  | teloungesets |

## Scenario Outline: Opening a product detail page as Dealer

- Given the Dealer visits the category "<slug>"
- When the Dealer selects a product
- Then products should be visible without prices

### Examples:

  | slug        |
  |-------------|
  | parasols    |
  | loungesets  |

## Scenario Outline: Checking prices are hidden for Dealer

- Given the Dealer visits a product detail page for "<slug>"
- When the Dealer checks the price of the product
- Then the price should not be visible for Dealer

### Examples:

  | slug                    |
  |------------------------|
  | cucumber-test-product-1 |
  | cucumber-test-product-2 |


## Scenario Outline: Dealer searching for a product that is available

- Given the Dealer visits home page
- When the Dealer searches for "<slug>"
- Then product "<slug>" should be visible without prices for Dealer

### Examples:

  | slug                    |
  |-------------------------|
  | cucumber-test-product-1 |
  | cucumber-test-product-2 |


## Scenario Outline: Dealer searching for a product that is not available

- Given the Dealer visits home page
- When the Dealer searches for "<slug>"
- Then the Dealer see empty result Search Page

### Examples:

  | slug                  |
  |-----------------------|
  | not-existed-product   |
  | not-existed-product-2 |
