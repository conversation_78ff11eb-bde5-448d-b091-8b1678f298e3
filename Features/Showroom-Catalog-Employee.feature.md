
# Feature: Showroom Catalog Employee

As a Employee I can see Product Listing Page, Product Details Page with products and I can search for them.

## Background:

- Given an Employee exists
- And Product 1 exist
- And Product 2 exist
- And Employee is authorized in Showroom


## Scenario Outline: Browsing a list of products

- Given the Employee visits the category "<slug>"
- When the Employee checks for products
- Then products should be visible

### Examples:

| slug       |
| ---------- |
| parasols   |
| loungesets |

## Scenario Outline: Opening a product detail page

- Given the Employee visits the category "<slug>"
- When the Employee selects a product
- Then the Employee should be directed to the product detail page

### Examples:

| slug       |
| ---------- |
| parasols   |
| loungesets |

## Scenario Outline: Showing the price of a product

- Given the Employee visits a product detail page for "<slug>"
- When the Employee checks the price of the product
- Then the price should be visible

### Examples:

| slug                    |
|-------------------------|
| cucumber-test-product-1 |
| cucumber-test-product-2 |


## Scenario Outline: Searching for a product that is available

- Given the Employee visits home page
- When the Employee search for "<slug>"
- Then product "<slug>" should be visible

### Examples:

| slug                    |
|-------------------------|
| cucumber-test-product-1 |
| cucumber-test-product-2 |


## Scenario Outline: Searching for a product that is not available

- Given the Employee visits home page
- When the Employee search for "<slug>"
- Then the Employee see empty result Search Page

### Examples:

| slug                  |
|-----------------------|
| not-existed-product   |
| not-existed-product-2 |

 
