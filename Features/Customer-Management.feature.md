@customer-mgmt
# Feature: Customer Management

## Rule: Customer Selection

### Scenario: Employee selects a customer

* Given the employee navigates to the Cart Page
* When the employee presses the "Change Customer" button
* Then the employee sees a pop-up screen with search options by Customer Postcode, Phone, or Email
* And the following information about the customer is shown:

    | Field                                 | Description                  |
    |---------------------------------------|------------------------------|
    | Name & Surname                        | Cucumber Customer            |
    | Email                                 | <EMAIL> |
    | Default Shipping Address Postcode     | 3024BC                       |
    | Default Shipping Address Phone Number | ************                 |
* When the employee selects a customer by pressing on it
* Then the selected customer is attached to the quote

## Rule: Customer Creation

### Scenario: Employee creates a new customer

* Given the employee navigates to the Customer Selection Page
* And the employee searches for a customer
* And the employee can see the "Create new Customer" button
* When the employee presses the "Create new Customer" button
* Then the employee is redirected to the Customer Creation Page
* And the employee can add all typical fields, with the following required:

    | Field                                   | Description                            |
    |-----------------------------------------|----------------------------------------|
    | Name                                    | Customer's first name                  |
    | Surname                                 | Customer's last name                   |
    | Email Address                           | Customer's email address               |
    | Shipping Address with Phone Number      | Full shipping address including phone  |
    | Billing Address                         | Required if different from shipping    |
* When the employee presses the "Save" button
* Then the employee is redirected to the Customer Information Page

## Rule: Customer Information Updating

### Scenario: Employee updates customer information

* Given the employee finds a customer using the Customer Selection Page
* And the employee presses on the chosen customer and is redirected to the Customer Information Page
* When the employee edits the customer fields, with the following required:

    | Field            | Description                         |
    |------------------|-------------------------------------|
    | Name             | Customer's first name               |
    | Surname          | Customer's last name                |
    | Phone Number     | Customer's contact number           |
    | Email Address    | Customer's email address            |
    | Shipping Address | Full shipping address               |
    | Billing Address  | Required if different from shipping |
* And the employee presses the "Save" button
* Then the employee is redirected to the Customer Information Page with updated information

## Rule: Switching Between Quotes

### Scenario: Employee switches between quotes

* Given the employee presses the "Choose Quote" button in the Menu
* Then the employee is redirected to the Quote Selection Page
* And only quotes without orders are shown
* And the following information about each quote is displayed:

    | Field                           | Description                         |
    |---------------------------------|-------------------------------------|
    | Employee Username (Surname)     | Username or surname of employee     |
    | Shop Name                       | Name of the shop                    |
    | Customer Email                  | Email of customer (if attached)     |
    | Date of Creation                | Date the quote was created          |
    | Total Sum                       | Total amount of the quote           |
* And the employee can filter quotes by Employee Username, Shop Name, Customer Email, or Date of Creation
* Then the employee selects a quote by pressing on it
