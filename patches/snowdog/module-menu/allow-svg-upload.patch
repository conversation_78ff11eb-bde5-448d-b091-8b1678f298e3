diff --git a/Model/Menu/Node/Image/File.php b/Model/Menu/Node/Image/File.php
index e71ab8b..e335ab4 100644
--- a/Model/Menu/Node/Image/File.php
+++ b/Model/Menu/Node/Image/File.php
@@ -18,7 +18,7 @@ use Psr\Log\LoggerInterface;
 class File
 {
     const UPLOAD_FILE_ID = 'image';
-    const ALLOWED_EXTENSIONS = ['jpg', 'jpeg', 'gif', 'png'];
+    const ALLOWED_EXTENSIONS = ['jpg', 'jpeg', 'gif', 'png', 'svg'];
     const PATH = 'snowdog/menu/node';

     /**
@@ -68,7 +68,6 @@ class File
         $uploader->setAllowedExtensions(self::ALLOWED_EXTENSIONS);
         $uploader->setAllowRenameFiles(true);
         $uploader->setFilesDispersion(true);
-        $uploader->addValidateCallback('menu_node_image', $imageAdapter, 'validateUploadFile');

         $result = $uploader->save($this->getAbsolutePath());

