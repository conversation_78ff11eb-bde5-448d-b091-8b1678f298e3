diff --git a/src/Magewire/Checkout/CouponCode.php b/src/Magewire/Checkout/CouponCode.php
index 48001c7..f50822f 100644
--- a/src/Magewire/Checkout/CouponCode.php
+++ b/src/Magewire/Checkout/CouponCode.php
@@ -60,7 +60,7 @@ class CouponCode extends Component
             $this->couponManagement->set($quoteEntity, $this->couponCode);
             $this->reset(['couponHits']);
         } catch (LocalizedException $exception) {
-            $this->couponCode = null;
+            $this->couponCode = $this->couponManagement->get($this->sessionCheckout->getQuoteId());
             $this->couponHits++;

             return $this->dispatchWarningMessage($exception->getMessage());
