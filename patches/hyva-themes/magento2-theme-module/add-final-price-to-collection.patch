diff --git a/src/ViewModel/ProductList.php b/src/ViewModel/ProductList.php
index 914c64b7..9658c170 100644
--- a/src/ViewModel/ProductList.php
+++ b/src/ViewModel/ProductList.php
@@ -297,6 +297,7 @@ class ProductList implements ArgumentInterface
                    ->setIsStrongMode()
                    ->setPositionOrder()
                    ->addStoreFilter()
+                   ->addFinalPrice()
                    ->setVisibility($this->productVisibility->getVisibleInCatalogIds())
                    ->addAttributeToSelect($this->catalogConfig->getProductAttributes());

@@ -309,6 +310,7 @@ class ProductList implements ArgumentInterface
     {
         $collection = $this->productCollectionFactory->create();
         $collection->addStoreFilter()
+                   ->addFinalPrice()
                    ->setVisibility($this->productVisibility->getVisibleInCatalogIds())
                    ->addAttributeToSelect($this->catalogConfig->getProductAttributes());

