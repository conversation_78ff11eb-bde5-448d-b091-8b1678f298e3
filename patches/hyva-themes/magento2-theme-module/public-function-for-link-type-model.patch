diff --git a/src/ViewModel/ProductList.php b/src/ViewModel/ProductList.php
index 3b4edd9..99de545 100644
--- a/src/ViewModel/ProductList.php
+++ b/src/ViewModel/ProductList.php
@@ -358,7 +358,7 @@ class ProductList implements ArgumentInterface
         return $collection->getItems();
     }

-    private function getLinkTypeModel(string $linkType): Product\Link
+    public function getLinkTypeModel(string $linkType): Product\Link
     {
         $linkModel = $this->productLinkFactory->create();
         switch ($linkType) {
