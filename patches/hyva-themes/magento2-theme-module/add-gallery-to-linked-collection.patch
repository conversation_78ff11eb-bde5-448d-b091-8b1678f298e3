diff --git a/src/ViewModel/ProductList.php b/src/ViewModel/ProductList.php
index 654eafbe..914c64b7 100644
--- a/src/ViewModel/ProductList.php
+++ b/src/ViewModel/ProductList.php
@@ -197,6 +197,7 @@ class ProductList implements ArgumentInterface
         $collection = $this->createProductCollection();
         $this->applyCriteria($this->searchCriteriaBuilder->create(), $collection);
         $collection->each('setDoNotUseCategoryId', [true]);
+        $collection->addMediaGalleryData();

         return $collection->getItems();
     }
@@ -252,6 +253,7 @@ class ProductList implements ArgumentInterface
         $this->applyCriteria($criteria, $collection);
         $collection->setGroupBy(); // group by product id field - required to avoid duplicate products in collection
         $collection->each('setDoNotUseCategoryId', [true]);
+        $collection->addMediaGalleryData();

         return $collection->getItems();
     }
@@ -368,6 +370,7 @@ class ProductList implements ArgumentInterface
         $collection->setGroupBy(); // group by product id field - required to avoid duplicate products in collection

         $collection->each('setDoNotUseCategoryId', [true]);
+        $collection->addMediaGalleryData();

         return $collection->getItems();
     }
