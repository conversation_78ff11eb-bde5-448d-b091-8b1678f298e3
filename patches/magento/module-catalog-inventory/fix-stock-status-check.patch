diff --git a/Helper/Stock.php b/Helper/Stock.php
index e79d2098..8cea31c5 100644
--- a/Helper/Stock.php
+++ b/Helper/Stock.php
@@ -87,7 +87,9 @@ class Stock
      */
     public function assignStatusToProduct(Product $product, $status = null)
     {
-        if ($status === null) {
+        if ($product->getTypeId() === \Magento\ConfigurableProduct\Model\Product\Type\Configurable::TYPE_CODE) {
+            $status = true;
+        } elseif ($status === null) {
             $scopeId = $this->getStockConfiguration()->getDefaultScopeId();
             $stockStatus = $this->stockRegistryProvider->getStockStatus($product->getId(), $scopeId);
             $status = $stockStatus->getStockStatus();
diff --git a/Model/Stock/Status.php b/Model/Stock/Status.php
index 7066d06c..902a6e86 100644
--- a/Model/Stock/Status.php
+++ b/Model/Stock/Status.php
@@ -123,6 +123,10 @@ class Status extends AbstractExtensibleModel implements StockStatusInterface
      */
     public function getStockStatus(): int
     {
+        if ($this->getTypeId() === \Magento\ConfigurableProduct\Model\Product\Type\Configurable::TYPE_CODE) {
+            return 1;
+        }
+
         return (int)$this->getData(self::KEY_STOCK_STATUS);
     }

