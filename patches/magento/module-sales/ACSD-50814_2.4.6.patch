diff --git a/vendor/magento/module-sales/Model/Order/Creditmemo/Total/Tax.php b/vendor/magento/module-sales/Model/Order/Creditmemo/Total/Tax.php
index 3ef0c99bb2b..e08ed2e2814 100644
--- a/vendor/magento/module-sales/Model/Order/Creditmemo/Total/Tax.php
+++ b/vendor/magento/module-sales/Model/Order/Creditmemo/Total/Tax.php
@@ -134,8 +134,8 @@ class Tax extends AbstractTotal
             $baseShippingDiscountTaxCompensationAmount = 0;
             $shippingDelta = $baseOrderShippingAmount - $baseOrderShippingRefundedAmount;

-            if ($shippingDelta > $creditmemo->getBaseShippingAmount() ||
-                $this->isShippingIncludeTaxWithTaxAfterDiscount($order->getStoreId())) {
+            if ($orderShippingAmount > 0 && ($shippingDelta > $creditmemo->getBaseShippingAmount() ||
+                $this->isShippingIncludeTaxWithTaxAfterDiscount($order->getStoreId()))) {
                 $part = $creditmemo->getShippingAmount() / $orderShippingAmount;
                 $basePart = $creditmemo->getBaseShippingAmount() / $baseOrderShippingAmount;
                 $shippingTaxAmount = $order->getShippingTaxAmount() * $part;
