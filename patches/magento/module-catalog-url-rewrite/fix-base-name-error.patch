diff --git a/Model/Storage/DynamicStorage.php b/Model/Storage/DynamicStorage.php
index e475ab6..8edd76d 100644
--- a/Model/Storage/DynamicStorage.php
+++ b/Model/Storage/DynamicStorage.php
@@ -257,6 +257,6 @@ class DynamicStorage extends BaseDbStorage
      */
     private function getBaseName($string): string
     {
-        return preg_replace('|.*?([^/]+)$|', '\1', $string, 1);
+        return (strrpos($string, '/') !== false) ? substr($string, strrpos($string, '/') + 1) : $string;
     }
 }
