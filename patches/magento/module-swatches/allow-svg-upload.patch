diff --git a/Controller/Adminhtml/Iframe/Show.php b/Controller/Adminhtml/Iframe/Show.php
index 459999b..7ee8a35 100644
--- a/Controller/Adminhtml/Iframe/Show.php
+++ b/Controller/Adminhtml/Iframe/Show.php
@@ -79,10 +79,14 @@ class Show extends \Magento\Backend\App\Action
     {
         try {
             $uploader = $this->uploaderFactory->create(['fileId' => 'datafile']);
-            $uploader->setAllowedExtensions(['jpg', 'jpeg', 'gif', 'png']);
-            /** @var \Magento\Framework\Image\Adapter\AdapterInterface $imageAdapter */
-            $imageAdapter = $this->adapterFactory->create();
-            $uploader->addValidateCallback('catalog_product_image', $imageAdapter, 'validateUploadFile');
+            $fileExtension = trim(strtolower($uploader->getFileExtension()));
+            $uploader->setAllowedExtensions(['jpg', 'jpeg', 'gif', 'png', 'svg']);
+
+            if ($fileExtension !== 'svg') {
+                /** @var \Magento\Framework\Image\Adapter\AdapterInterface $imageAdapter */
+                $imageAdapter = $this->adapterFactory->create();
+                $uploader->addValidateCallback('catalog_product_image', $imageAdapter, 'validateUploadFile');
+            }
             $uploader->setAllowRenameFiles(true);
             $uploader->setFilesDispersion(true);
             /** @var \Magento\Framework\Filesystem\Directory\Read $mediaDirectory */
@@ -102,7 +106,9 @@ class Show extends \Magento\Backend\App\Action
             $result['file'] = $result['file'] . '.tmp';

             $newFile = $this->swatchHelper->moveImageFromTmp($result['file']);
-            $this->swatchHelper->generateSwatchVariations($newFile);
+            if ($fileExtension !== 'svg') {
+                $this->swatchHelper->generateSwatchVariations($newFile);
+            }
             $fileData = ['swatch_path' => $this->swatchHelper->getSwatchMediaUrl(), 'file_path' => $newFile];
             $this->getResponse()->setBody(json_encode($fileData));
         } catch (\Exception $e) {
