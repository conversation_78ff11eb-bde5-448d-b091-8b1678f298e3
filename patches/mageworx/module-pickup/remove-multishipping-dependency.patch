diff --git a/Block/Multishipping/FindAStore.php b/Block/Multishipping/FindAStore.php
index 3ad0fa1..2b3e4c2 100644
--- a/Block/Multishipping/FindAStore.php
+++ b/Block/Multishipping/FindAStore.php
@@ -35,14 +35,12 @@ class FindAStore extends Template
      * FindAStore constructor.
      *
      * @param \Magento\Framework\Filter\DataObject\GridFactory $gridFilterFactory
-     * @param \Magento\Multishipping\Model\Checkout\Type\Multishipping $multishipping
      * @param \Magento\Framework\Registry $coreRegistry
      * @param Template\Context $context
      * @param array $data
      */
     public function __construct(
         \Magento\Framework\Filter\DataObject\GridFactory $gridFilterFactory,
-        \Magento\Multishipping\Model\Checkout\Type\Multishipping $multishipping,
         \Magento\Framework\Registry $coreRegistry,
         Template\Context $context,
         array $data = []
@@ -50,7 +48,6 @@ class FindAStore extends Template
         parent::__construct($context, $data);

         $this->gridFilterFactory = $gridFilterFactory;
-        $this->multishipping     = $multishipping;
         $this->coreRegistry      = $coreRegistry;
     }

@@ -87,7 +84,6 @@ class FindAStore extends Template

         foreach ($this->getAddress()->getAllItems() as $item) {
             if (!$item->getParentItemId()) {
-                $item->setQuoteItem($this->multishipping->getQuote()->getItemById($item->getQuoteItemId()));
                 $items[] = $item;
             }
         }
