diff --git a/Model/ResourceModel/Location.php b/Model/ResourceModel/Location.php
index 9afcab6c..adbb25f4 100644
--- a/Model/ResourceModel/Location.php
+++ b/Model/ResourceModel/Location.php
@@ -482,9 +482,6 @@ class Location extends \Magento\Eav\Model\Entity\AbstractEntity
         }

         $website = $object->getData(LocationInterface::WEBSITE_URL);
-        if ($website) {
-            $website =  str_replace(['http://', 'https://'], '', $website);
-        }

         $object->setData(LocationInterface::WEBSITE_URL, $website);

