diff --git a/Model/Search/Autocomplete/DataProvider.php b/Model/Search/Autocomplete/DataProvider.php
index 0df5431..edb5b4a 100644
--- a/Model/Search/Autocomplete/DataProvider.php
+++ b/Model/Search/Autocomplete/DataProvider.php
@@ -105,7 +105,8 @@ class DataProvider implements DataProviderInterface
             $result[] = $this->itemFactory->create([
                 'title' => $item['title'],
                 'category' => $item['category'],
-                'url' => $this->url->getEntityUrl([$urlKey, $item['url_key']])
+                'url' => $this->url->getEntityUrl([$urlKey, $item['url_key']]),
+                'id' => $item['question_id']
             ]);
         }

