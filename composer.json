{"name": "life/webshop", "description": "eCommerce Platform for Growth (Community Edition)", "type": "project", "license": ["OSL-3.0", "AFL-3.0"], "config": {"platform": {"php": "8.1"}, "allow-plugins": {"dealerdirect/phpcodesniffer-composer-installer": true, "laminas/laminas-dependency-plugin": true, "magento/*": true, "php-http/discovery": true, "cweagans/composer-patches": true}, "preferred-install": "dist", "sort-packages": true}, "version": "2.4.6-p5", "require": {"amasty/module-automatic-related-products-subscription-package": "^2.16", "amasty/module-faq-subscription-package": "^2.13", "amasty/module-product-labels-hyva": "^2.0", "amasty/module-product-labels-subscription-pack": "^2.3", "community-engineering/language-de_de": "^0.0", "community-engineering/language-nl_nl": "^0.0", "cweagans/composer-patches": "^1.7", "hyva-themes/magento2-amasty-faq-product-questions": "^2.0", "hyva-themes/magento2-default-theme": "^1.3", "hyva-themes/magento2-hyva-checkout": "^1.1", "hyva-themes/magento2-magepal-google-analytics4": "^1.0", "hyva-themes/magento2-mageworx-storelocator": "^1.0", "hyva-themes/magento2-smile-elasticsuite": "^1.2", "justbetter/magento2-sentry": "^3.8", "magento/composer-dependency-version-audit-plugin": "~0.1", "magento/composer-root-update-plugin": "~2.0", "magento/product-community-edition": "2.4.6-p8", "magento/quality-patches": "^1.1", "magepal/magento2-google-analytics4": "^1.7", "mageworx/module-storelocatormeta": "^1.12", "magmodules/m2-alternate-hreflang": "^1.1", "magmodules/magento2-channable": "^1.15", "mailplus/mailplus-connector": "^1.8", "markshust/magento2-module-disabletwofactorauth": "^2.0", "multisafepay/magewire-checkout": "^2.0", "olegkoval/magento2-regenerate-url-rewrites": "^1.6", "postcode-nl/api-magento2-module": "^3.1", "redkiwi/magento2-build": "^100.0", "redkiwi/magento2-deploy": "^100.1", "redkiwi/module-api-log": "^101.0", "redkiwi/module-cart-shipping": "^100.1", "redkiwi/module-catalog-disable-checkout": "^100.1", "redkiwi/module-cms-breadcrumbs": "^100.1", "redkiwi/module-cms-wysiwyg-images-svg": "^100.0", "redkiwi/module-cookiebot": "^100.1", "redkiwi/module-product-breadcrumbs": "^100.0", "redkiwi/module-rich-snippets": "^104.0", "redkiwi/module-translation-editor": "^101.0", "redkiwi/module-widget-usp": "^104.0", "smile/module-elasticsuite-cms-search": "^2.2", "snowdog/module-menu": "^2.23", "squeezely/magento2-plugin": "^2.11", "tidycode/meta-robots-tag": "^1.0", "yireo/magento2-disable-csp": "^1.0", "yireo/magento2-webp2": "^0.13.3"}, "autoload": {"exclude-from-classmap": ["**/dev/**", "**/update/**", "**/Test/**"], "files": ["app/etc/NonComposerComponentRegistration.php", "app/etc/stores.php"], "psr-0": {"": ["app/code/", "generated/code/"]}, "psr-4": {"Magento\\": "app/code/Magento/", "Magento\\Framework\\": "lib/internal/Magento/Framework/", "Magento\\Setup\\": "setup/src/Magento/Setup/"}}, "require-dev": {"allure-framework/allure-phpunit": "^2", "bitexpert/phpstan-magento": "^0.30.1", "dealerdirect/phpcodesniffer-composer-installer": "^0.7", "dg/bypass-finals": "^1.4", "friendsofphp/php-cs-fixer": "^3.8", "lusitanian/oauth": "^0.8", "magento/magento-coding-standard": "*", "magento/magento2-functional-testing-framework": "^4.3.1", "pdepend/pdepend": "^2.10", "phpmd/phpmd": "^2.12", "phpstan/phpstan": "^1.9", "phpunit/phpunit": "^9.5", "sebastian/comparator": "<=4.0.6", "sebastian/phpcpd": "^6.0", "symfony/finder": "^5.4", "symfony/process": "<=v5.4.23"}, "replace": {"astock/stock-api-libphp": "*", "magento/adobe-stock-integration": "*", "magento/google-shopping-ads": "*", "magento/language-de_de": "*", "magento/language-en_us": "*", "magento/language-es_es": "*", "magento/language-fr_fr": "*", "magento/language-nl_nl": "*", "magento/language-pt_br": "*", "magento/language-zh_hans_cn": "*", "magento/module-cardinal-commerce": "*", "magento/module-dhl": "*", "magento/module-elasticsearch-6": "*", "magento/module-fedex": "*", "magento/module-google-adwords": "*", "magento/module-google-optimizer": "*", "magento/module-marketplace": "*", "magento/module-multishipping": "*", "magento/module-paypal-captcha": "*", "magento/module-paypal-recaptcha": "*", "magento/module-sample-data": "*", "magento/module-securitytxt": "*", "magento/module-send-friend-graph-ql": "*", "magento/module-ups": "*", "magento/module-usps": "*", "magento/module-version": "*", "magento/module-webapi-security": "*", "magento/theme-frontend-luma": "*", "paypal/module-braintree": "*", "temando/module-shipping-remover": "*", "magento/module-admin-analytics": "*", "magento/module-analytics": "*", "magento/module-catalog-analytics": "*", "magento/module-catalog-page-builder-analytics": "*", "magento/module-cms-page-builder-analytics": "*", "magento/module-customer-analytics": "*", "magento/module-page-builder-analytics": "*", "magento/module-page-builder-admin-analytics": "*", "magento/module-quote-analytics": "*", "magento/module-review-analytics": "*", "magento/module-sales-analytics": "*", "magento/module-wishlist-analytics": "*", "magento/module-re-captcha-checkout": "*", "magento/module-re-captcha-checkout-sales-rule": "*", "magento/module-re-captcha-migration": "*", "magento/module-re-captcha-paypal": "*", "magento/module-re-captcha-review": "*", "magento/module-re-captcha-send-friend": "*", "magento/module-re-captcha-store-pickup": "*", "magento/module-re-captcha-user": "*", "magento/module-re-captcha-version-2-checkbox": "*", "magento/module-re-captcha-version-2-invisible": "*"}, "conflict": {"gene/bluefoot": "*"}, "autoload-dev": {"psr-4": {"Magento\\PhpStan\\": "dev/tests/static/framework/Magento/PhpStan/", "Magento\\Sniffs\\": "dev/tests/static/framework/Magento/Sniffs/", "Magento\\TestFramework\\Inspection\\": "dev/tests/static/framework/Magento/TestFramework/Inspection/", "Magento\\TestFramework\\Utility\\": "dev/tests/static/framework/Magento/TestFramework/Utility/", "Magento\\Tools\\": "dev/tools/Magento/Tools/", "Magento\\Tools\\Sanity\\": "dev/build/publication/sanity/Magento/Tools/Sanity/"}}, "minimum-stability": "stable", "prefer-stable": true, "repositories": [{"type": "composer", "url": "https://composer.amasty.com/community/"}, {"type": "composer", "url": "https://packages.mageworx.com/"}, {"type": "composer", "url": "https://composer.toolscloud.nl/", "canonical": false}, {"type": "composer", "url": "https://repo.magento.com/"}, {"type": "composer", "url": "https://hyva-themes.repo.packagist.com/lifeoutdoorliving/"}, {"type": "composer", "url": "https://composer.magepal.com/"}], "scripts": {"apply-quality-patches": ["./vendor/bin/magento-patches apply ACSD-56415 ACSD-51102 ACSD-51431 ACSD-50817"], "post-install-cmd": ["@apply-quality-patches", "([ $COMPOSER_DEV_MODE -eq 0 ] || vendor/bin/phpcs --config-set installed_paths ../../magento/magento-coding-standard/)"], "post-update-cmd": ["@apply-quality-patches", "([ $COMPOSER_DEV_MODE -eq 0 ] || vendor/bin/phpcs --config-set installed_paths ../../magento/magento-coding-standard/)"]}, "extra": {"magento-force": "override", "composer-exit-on-patch-failure": true, "magento-deploy-ignore": {"*": ["/.giti<PERSON>re"]}, "patches": {"amasty/module-faq-product-questions": {"Add ID to FAQ questions suggestions": "patches/amasty/module-faq-product-questions/add-id-to-suggestions.patch"}, "hyva-themes/magento2-hyva-checkout": {"Keep applied coupon code when an error is thrown": "patches/hyva-themes/magento2-hyva-checkout/keep-applied-coupon-code-on-error.patch"}, "hyva-themes/magento2-theme-module": {"Make getLinkTypeModel() public": "patches/hyva-themes/magento2-theme-module/public-function-for-link-type-model.patch", "Add gallery to linked products collection": "patches/hyva-themes/magento2-theme-module/add-gallery-to-linked-collection.patch", "Add final price to list collection": "patches/hyva-themes/magento2-theme-module/add-final-price-to-collection.patch"}, "magento/module-catalog-import-export": {"Fix Product export website id": "patches/magento/module-catalog-import-export/fix-export-website-id.patch"}, "magento/module-catalog-inventory": {"Fix for stock status check": "patches/magento/module-catalog-inventory/fix-stock-status-check.patch"}, "magento/module-catalog-url-rewrite": {"Fix error in getBaseName()": "patches/magento/module-catalog-url-rewrite/fix-base-name-error.patch"}, "magento/module-configurable-product": {"Display out of stock configurations": "patches/magento/module-configurable-product/show-out-of-stock-configurable-options.patch"}, "magento/module-swatches": {"Allow SVG upload": "patches/magento/module-swatches/allow-svg-upload.patch"}, "mageworx/module-locations": {"Fix for external URL": "patches/mageworx/module-locations/fix-for-external-url.patch"}, "mageworx/module-pickup": {"Remove Multishipping dependency": "patches/mageworx/module-pickup/remove-multishipping-dependency.patch"}, "magmodules/m2-alternate-hreflang": {"Remove page param from href lang validation": "patches/magmodules/m2-alternate-hreflang/explude_page_param_from_validation.patch"}, "snowdog/module-menu": {"Allow SVG upload": "patches/snowdog/module-menu/allow-svg-upload.patch", "Snowdog bug fix https://github.com/SnowdogApps/magento2-menu/issues/308": "patches/snowdog/module-menu/LOL-448-topmenu-error.patch"}, "tidycode/meta-robots-tag": {"Fix wrong type returning": "patches/tidycode/meta-robots-tag/LOL-442-fixed-wrong-type.patch"}, "magento/module-sales": {"Fix creating creditmemo with free shipping method": "patches/magento/module-sales/ACSD-50814_2.4.6.patch"}}}}