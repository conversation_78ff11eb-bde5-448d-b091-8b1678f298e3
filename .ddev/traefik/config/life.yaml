#ddev-generated
# If you remove the ddev-generated line above you
# are responsible for maintaining this file. DDEV will not then
# update it, for example if you add `additional_hostnames`, etc.

http:
  routers:
    life-web-80-http:
      entrypoints:
        - http-80
      rule: HostRegexp(`^life\.local$`)|| HostRegexp(`^showroom\.lifeoutdoorliving\.com\.local$`)|| HostRegexp(`^www\.lifeoutdoorliving\.com\.local$`)
      
      service: "life-web-80"
      ruleSyntax: v3
      tls: false
      # middlewares:
      #   - "life-redirectHttps"
    life-web-8025-http:
      entrypoints:
        - http-8025
      rule: HostRegexp(`^life\.local$`)|| HostRegexp(`^showroom\.lifeoutdoorliving\.com\.local$`)|| HostRegexp(`^www\.lifeoutdoorliving\.com\.local$`)
      
      service: "life-web-8025"
      ruleSyntax: v3
      tls: false
      # middlewares:
      #   - "life-redirectHttps"
    life-xhgui-80-http:
      entrypoints:
        - http-8143
      rule: HostRegexp(`^life\.local$`)|| HostRegexp(`^showroom\.lifeoutdoorliving\.com\.local$`)|| HostRegexp(`^www\.lifeoutdoorliving\.com\.local$`)
      
      service: "life-xhgui-80"
      ruleSyntax: v3
      tls: false
      # middlewares:
      #   - "life-redirectHttps"
    life-elasticsearch-9200-http:
      entrypoints:
        - http-9200
      rule: HostRegexp(`^life\.local$`)|| HostRegexp(`^showroom\.lifeoutdoorliving\.com\.local$`)|| HostRegexp(`^www\.lifeoutdoorliving\.com\.local$`)
      
      service: "life-elasticsearch-9200"
      ruleSyntax: v3
      tls: false
      # middlewares:
      #   - "life-redirectHttps"
    life-kibana-5601-http:
      entrypoints:
        - http-5601
      rule: HostRegexp(`^life\.local$`)|| HostRegexp(`^showroom\.lifeoutdoorliving\.com\.local$`)|| HostRegexp(`^www\.lifeoutdoorliving\.com\.local$`)
      
      service: "life-kibana-5601"
      ruleSyntax: v3
      tls: false
      # middlewares:
      #   - "life-redirectHttps"
    life-rabbitmq-5672-http:
      entrypoints:
        - http-5672
      rule: HostRegexp(`^life\.local$`)|| HostRegexp(`^showroom\.lifeoutdoorliving\.com\.local$`)|| HostRegexp(`^www\.lifeoutdoorliving\.com\.local$`)
      
      service: "life-rabbitmq-5672"
      ruleSyntax: v3
      tls: false
      # middlewares:
      #   - "life-redirectHttps"
    life-redis-6379-http:
      entrypoints:
        - http-6379
      rule: HostRegexp(`^life\.local$`)|| HostRegexp(`^showroom\.lifeoutdoorliving\.com\.local$`)|| HostRegexp(`^www\.lifeoutdoorliving\.com\.local$`)
      
      service: "life-redis-6379"
      ruleSyntax: v3
      tls: false
      # middlewares:
      #   - "life-redirectHttps"
    
    
    
    life-web-80-https:
      entrypoints:
        - http-443
      rule: HostRegexp(`^life\.local$`) || HostRegexp(`^showroom\.lifeoutdoorliving\.com\.local$`) || HostRegexp(`^www\.lifeoutdoorliving\.com\.local$`)
      
      service: "life-web-80"
      ruleSyntax: v3
      
      tls: true
      
    life-web-8025-https:
      entrypoints:
        - http-8026
      rule: HostRegexp(`^life\.local$`) || HostRegexp(`^showroom\.lifeoutdoorliving\.com\.local$`) || HostRegexp(`^www\.lifeoutdoorliving\.com\.local$`)
      
      service: "life-web-8025"
      ruleSyntax: v3
      
      tls: true
      
    
    life-xhgui-80-https:
      entrypoints:
        - http-8142
      rule: HostRegexp(`^life\.local$`) || HostRegexp(`^showroom\.lifeoutdoorliving\.com\.local$`) || HostRegexp(`^www\.lifeoutdoorliving\.com\.local$`)
      
      service: "life-xhgui-80"
      ruleSyntax: v3
      
      tls: true
      
    
    
    
    
    

  middlewares:
    life-redirectHttps:
      redirectScheme:
        scheme: https
        permanent: true

  services:
    life-web-80:
      loadbalancer:
        servers:
          - url: http://ddev-life-web:80
        
    life-web-8025:
      loadbalancer:
        servers:
          - url: http://ddev-life-web:8025
        
    
    
    life-xhgui-80:
      loadbalancer:
        servers:
          - url: http://ddev-life-xhgui:80
        
    
    life-elasticsearch-9200:
      loadbalancer:
        servers:
          - url: http://ddev-life-elasticsearch:9200
        
    life-kibana-5601:
      loadbalancer:
        servers:
          - url: http://ddev-life-kibana:5601
        
    life-rabbitmq-5672:
      loadbalancer:
        servers:
          - url: http://ddev-life-rabbitmq:5672
        
    life-redis-6379:
      loadbalancer:
        servers:
          - url: http://ddev-life-redis:6379
        
    

tls:
  certificates:
    - certFile: /mnt/ddev-global-cache/traefik/certs/life.crt
      keyFile: /mnt/ddev-global-cache/traefik/certs/life.key