#!/bin/bash

ARGS=()

while [ -n "$1" ];
do
    case "$1" in
    *) ARGS+=($1);;
    esac

    shift
done

HOST=${ARGS[0]:-staginglife.hypernode.io}
ENVIRONMENT=${ARGS[1]:-magento2}

if [ -z "$HOST" ]; then
    echo "Specify a host"
    exit 1
fi

if [ -z "$ENVIRONMENT" ]; then
    echo "Specify an environment"
    exit 1
fi

echo "Pulling live DB..."

ssh app@$HOST "n98-magerun2 db:dump --root-dir=/data/web/$ENVIRONMENT/current --stdout --no-tablespaces --strip='@stripped @trade @idx catalog_product_index_price*' | gzip" | gunzip  | ddev exec -s web mysql -uroot -proot db
