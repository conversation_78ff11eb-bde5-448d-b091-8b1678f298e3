#!/bin/bash

ARGS=()
WITH_PRODUCTS=false

while [ -n "$1" ];
do
    case "$1" in
    --with-products) WITH_PRODUCTS=true;;
    *) ARGS+=($1);;
    esac

    shift
done

HOST=${ARGS[0]:-staginglife.hypernode.io}
ENVIRONMENT=${ARGS[1]:-magento2}

if [ -z "$HOST" ]; then
    echo "Specify a host"
    exit 1
fi

if [ -z "$ENVIRONMENT" ]; then
    echo "Specify an environment"
    exit 1
fi

if [ true = $WITH_PRODUCTS ]; then
    rsync -azvP app@$HOST:/data/web/$ENVIRONMENT/current/pub/media/ pub/media
else
    rsync -azvP --exclude 'catalog/product' app@$HOST:/data/web/$ENVIRONMENT/current/pub/media/ pub/media
fi
