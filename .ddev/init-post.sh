#!/bin/bash

cd "${BASH_SOURCE%/*}/.." || exit

if [ -e .ddev/.init-pre ] && [ ! -e .ddev/.init-post ]
then
    ddev exec composer install
    ddev db-pull staginglife.hypernode.io
    ddev media-pull staginglife.hypernode.io
    ddev exec bin/magento setup:upgrade
    ddev exec bin/magento index:reindex

    npm --prefix app/design/frontend/Life/default/web/tailwind install
    npm --prefix app/design/frontend/Life/default/web/tailwind run build
    npm --prefix app/design/frontend/Life/showroom/web/tailwind install
    npm --prefix app/design/frontend/Life/showroom/web/tailwind run build

    date > .ddev/.init-post
fi
