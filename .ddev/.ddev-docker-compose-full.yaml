name: ddev-life
networks:
    ddev_default:
        external: true
        name: ddev_default
    default:
        labels:
            com.ddev.platform: ddev
        name: ddev-life_default
services:
    db:
        build:
            args:
                BASE_IMAGE: ddev/ddev-dbserver-mariadb-10.4:v1.24.4
                gid: "20"
                uid: "502"
                username: jelmer
            context: /Users/<USER>/RK-Projects/LOL/.ddev/.dbimageBuild
            dockerfile: Dockerfile
        cap_add:
            - SYS_NICE
        command: []
        container_name: ddev-life-db
        environment:
            BITNAMI_VOLUME_DIR: ""
            COLUMNS: "202"
            DDEV_DATABASE: mariadb:10.4
            DDEV_DATABASE_FAMILY: mysql
            DDEV_GOARCH: arm64
            DDEV_GOOS: darwin
            DDEV_HOSTNAME: life.local,showroom.lifeoutdoorliving.com.local,www.lifeoutdoorliving.com.local
            DDEV_PHP_VERSION: "8.1"
            DDEV_PRIMARY_URL: https://life.local
            DDEV_PROJECT: life
            DDEV_PROJECT_TYPE: magento2
            DDEV_ROUTER_HTTP_PORT: "80"
            DDEV_ROUTER_HTTPS_PORT: "443"
            DDEV_SITENAME: life
            DDEV_TLD: local
            DOCKER_IP: 127.0.0.1
            HOST_DOCKER_INTERNAL_IP: ""
            IS_DDEV_PROJECT: "true"
            LINES: "23"
            MYSQL_HISTFILE: /mnt/ddev-global-cache/mysqlhistory/life-db/mysql_history
            PGDATABASE: db
            PGHOST: 127.0.0.1
            PGPASSWORD: db
            PGUSER: db
            POSTGRES_DB: db
            POSTGRES_PASSWORD: db
            POSTGRES_USER: db
            TZ: Europe/Amsterdam
            USER: jelmer
        healthcheck:
            interval: 1s
            retries: 70
            start_interval: 1s
            start_period: 2m0s
            timeout: 1m10s
        hostname: life-db
        image: ddev/ddev-dbserver-mariadb-10.4:v1.24.4-life-built
        labels:
            com.ddev.app-type: magento2
            com.ddev.approot: /Users/<USER>/RK-Projects/LOL
            com.ddev.platform: ddev
            com.ddev.site-name: life
        networks:
            ddev_default: null
            default: null
        ports:
            - host_ip: 127.0.0.1
              mode: ingress
              protocol: tcp
              target: 3306
        restart: "no"
        stop_grace_period: 1m0s
        user: "502:20"
        volumes:
            - source: database
              target: /var/lib/mysql
              type: volume
              volume:
                nocopy: true
            - bind:
                create_host_path: true
              source: /Users/<USER>/RK-Projects/LOL/.ddev
              target: /mnt/ddev_config
              type: bind
            - bind:
                create_host_path: true
              source: /Users/<USER>/RK-Projects/LOL/.ddev/db_snapshots
              target: /mnt/snapshots
              type: bind
            - source: ddev-global-cache
              target: /mnt/ddev-global-cache
              type: volume
              volume: {}
        working_dir: /home/<USER>
    elasticsearch:
        build:
            context: /Users/<USER>/RK-Projects/LOL/.ddev/elasticsearch
            dockerfile: Dockerfile
        container_name: ddev-life-elasticsearch
        environment:
            HTTP_EXPOSE: "9200"
            VIRTUAL_HOST: life.local,showroom.lifeoutdoorliving.com.local,www.lifeoutdoorliving.com.local
            discovery.type: single-node
        labels:
            com.ddev.approot: /Users/<USER>/RK-Projects/LOL
            com.ddev.site-name: life
        networks:
            ddev_default: null
            default: null
        ports:
            - host_ip: 127.0.0.1
              mode: ingress
              protocol: tcp
              target: 9200
            - host_ip: 127.0.0.1
              mode: ingress
              protocol: tcp
              target: 9300
        volumes:
            - source: elasticsearch
              target: /usr/share/elasticsearch/data
              type: volume
              volume: {}
    kibana:
        container_name: ddev-life-kibana
        environment:
            ELASTICSEARCH_HOSTS: http://ddev-life-elasticsearch.ddev_default:9200
            HTTP_EXPOSE: "5601"
            VIRTUAL_HOST: life.local,showroom.lifeoutdoorliving.com.local,www.lifeoutdoorliving.com.local
        image: docker.redkiwi.nl/docker/elastic/kibana:7.9.3
        labels:
            com.ddev.approot: /Users/<USER>/RK-Projects/LOL
            com.ddev.site-name: life
        networks:
            ddev_default: null
            default: null
        ports:
            - host_ip: 127.0.0.1
              mode: ingress
              protocol: tcp
              target: 5601
        restart: always
    rabbitmq:
        container_name: ddev-life-rabbitmq
        environment:
            HTTP_EXPOSE: "5672"
            VIRTUAL_HOST: life.local,showroom.lifeoutdoorliving.com.local,www.lifeoutdoorliving.com.local
        image: rabbitmq
        labels:
            com.ddev.approot: /Users/<USER>/RK-Projects/LOL
            com.ddev.site-name: life
        networks:
            ddev_default: null
            default: null
        ports:
            - host_ip: 127.0.0.1
              mode: ingress
              protocol: tcp
              target: 5672
    redis:
        container_name: ddev-life-redis
        environment:
            HTTP_EXPOSE: "6379"
            VIRTUAL_HOST: life.local,showroom.lifeoutdoorliving.com.local,www.lifeoutdoorliving.com.local
        image: redis:6.0
        labels:
            com.ddev.approot: /Users/<USER>/RK-Projects/LOL
            com.ddev.site-name: life
        networks:
            ddev_default: null
            default: null
        ports:
            - host_ip: 127.0.0.1
              mode: ingress
              protocol: tcp
              target: 6379
        restart: always
    web:
        build:
            args:
                BASE_IMAGE: ddev/ddev-webserver:v1.24.4
                DDEV_DATABASE: mariadb:10.4
                DDEV_PHP_VERSION: "8.1"
                gid: "20"
                uid: "502"
                username: jelmer
            context: /Users/<USER>/RK-Projects/LOL/.ddev/.webimageBuild
            dockerfile: Dockerfile
        cap_add:
            - SYS_PTRACE
        command:
            - /pre-start.sh
        container_name: ddev-life-web
        depends_on:
            elasticsearch:
                condition: service_started
                required: true
                restart: true
            rabbitmq:
                condition: service_started
                required: true
                restart: true
            redis:
                condition: service_started
                required: true
                restart: true
        environment:
            COLUMNS: "202"
            COREPACK_ENABLE_DOWNLOAD_PROMPT: "0"
            COREPACK_HOME: /mnt/ddev-global-cache/corepack
            DDEV_COMPOSER_ROOT: /var/www/html
            DDEV_DATABASE: mariadb:10.4
            DDEV_DATABASE_FAMILY: mysql
            DDEV_DOCROOT: pub
            DDEV_FILES_DIR: /var/www/html/pub/media
            DDEV_FILES_DIRS: /var/www/html/pub/media
            DDEV_GOARCH: arm64
            DDEV_GOOS: darwin
            DDEV_HOSTNAME: life.local,showroom.lifeoutdoorliving.com.local,www.lifeoutdoorliving.com.local
            DDEV_MUTAGEN_ENABLED: "false"
            DDEV_PHP_VERSION: "8.1"
            DDEV_PRIMARY_URL: https://life.local
            DDEV_PROJECT: life
            DDEV_PROJECT_TYPE: magento2
            DDEV_ROUTER_HTTP_PORT: "80"
            DDEV_ROUTER_HTTPS_PORT: "443"
            DDEV_SITENAME: life
            DDEV_TLD: local
            DDEV_VERSION: v1.24.4
            DDEV_WEB_ENTRYPOINT: /mnt/ddev_config/web-entrypoint.d
            DDEV_WEBSERVER_TYPE: nginx-fpm
            DDEV_XDEBUG_ENABLED: "false"
            DDEV_XHPROF_MODE: prepend
            DEPLOY_NAME: local
            DOCKER_IP: 127.0.0.1
            DOCROOT: pub
            DRUSH_OPTIONS_URI: https://life.local
            HOST_DOCKER_INTERNAL_IP: ""
            HTTP_EXPOSE: 80:80,8025:8025
            HTTPS_EXPOSE: 443:80,8026:8025
            IS_DDEV_PROJECT: "true"
            LINES: "23"
            MYSQL_HISTFILE: /mnt/ddev-global-cache/mysqlhistory/life-web/mysql_history
            NODE_EXTRA_CA_CERTS: /mnt/ddev-global-cache/mkcert/rootCA.pem
            PGDATABASE: db
            PGHOST: db
            PGPASSWORD: db
            PGUSER: db
            PHP_IDE_CONFIG: serverName=life.local
            SSH_AUTH_SOCK: /home/<USER>/socket
            START_SCRIPT_TIMEOUT: "30"
            TZ: Europe/Amsterdam
            USER: jelmer
            VIRTUAL_HOST: life.local,showroom.lifeoutdoorliving.com.local,www.lifeoutdoorliving.com.local
            npm_config_cache: /mnt/ddev-global-cache/npm
        external_links:
            - ddev-router:life.local
            - ddev-router:showroom.lifeoutdoorliving.com.local
            - ddev-router:www.lifeoutdoorliving.com.local
        healthcheck:
            interval: 1s
            retries: 70
            start_interval: 1s
            start_period: 2m0s
            timeout: 1m10s
        hostname: life-web
        image: ddev/ddev-webserver:v1.24.4-life-built
        labels:
            com.ddev.app-type: magento2
            com.ddev.approot: /Users/<USER>/RK-Projects/LOL
            com.ddev.platform: ddev
            com.ddev.site-name: life
        links:
            - elasticsearch:elasticsearch
            - rabbitmq:rabbitmq
            - redis:life.local,showroom.lifeoutdoorliving.com.local,www.lifeoutdoorliving.com.local
        networks:
            ddev_default: null
            default: null
        ports:
            - host_ip: 127.0.0.1
              mode: ingress
              protocol: tcp
              target: 80
            - host_ip: 127.0.0.1
              mode: ingress
              protocol: tcp
              target: 443
            - host_ip: 127.0.0.1
              mode: ingress
              protocol: tcp
              target: 8025
        restart: "no"
        tmpfs:
            - /var/www/html/var:size=268435456,uid=502,gid=20
        user: "502:20"
        volumes:
            - consistency: cached
              source: /Users/<USER>/RK-Projects/LOL
              target: /var/www/html
              type: bind
            - bind:
                create_host_path: true
              read_only: true
              source: /Users/<USER>/RK-Projects/LOL/.ddev
              target: /mnt/ddev_config
              type: bind
            - bind:
                create_host_path: true
              source: /Users/<USER>/RK-Projects/LOL/.ddev/xhprof
              target: /usr/local/bin/xhprof
              type: bind
            - source: ddev-global-cache
              target: /mnt/ddev-global-cache
              type: volume
              volume: {}
            - source: ddev-ssh-agent_socket_dir
              target: /home/<USER>
              type: volume
              volume: {}
        working_dir: /var/www/html/
    xhgui:
        container_name: ddev-life-xhgui
        depends_on:
            db:
                condition: service_started
                required: true
        environment:
            DDEV_DATABASE_FAMILY: mysql
            HTTP_EXPOSE: 8143:80
            HTTPS_EXPOSE: 8142:80
            TZ: Europe/Amsterdam
            VIRTUAL_HOST: life.local,showroom.lifeoutdoorliving.com.local,www.lifeoutdoorliving.com.local
            XHGUI_PDO_PASS: db
            XHGUI_PDO_USER: db
            XHGUI_SAVE_HANDLER: pdo
        image: ddev/ddev-xhgui:v1.24.4
        labels:
            com.ddev.approot: /Users/<USER>/RK-Projects/LOL
            com.ddev.site-name: life
        links:
            - db
        networks:
            ddev_default: null
            default: null
        profiles:
            - xhgui
        restart: "no"
volumes:
    database:
        external: true
        name: life-mariadb
    ddev-global-cache:
        external: true
        name: ddev-global-cache
    ddev-ssh-agent_socket_dir:
        external: true
        name: ddev-ssh-agent_socket_dir
    elasticsearch:
        name: life-elasticsearch
