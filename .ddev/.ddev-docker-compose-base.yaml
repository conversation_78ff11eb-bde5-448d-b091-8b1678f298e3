name: ${COMPOSE_PROJECT_NAME}
  #ddev-generated
services:
  
  db:
    container_name: ddev-${DDEV_SITENAME}-db
    build:
      context: './.dbimageBuild'
      args:
        BASE_IMAGE: $DDEV_DBIMAGE
        username: 'jel<PERSON>'
        uid: '502'
        gid:  20 
    image: ${DDEV_DBIMAGE}-${DDEV_SITENAME}-built
    cap_add:
      - SYS_NICE
    stop_grace_period: 60s
    working_dir: "/home/<USER>"
    volumes:
      - type: "volume"
        source: "database"
        target: "/var/lib/mysql"
        volume:
          nocopy: true
       
      # On db container ddev_config is mounted rw so we can create snapshots
      - .:/mnt/ddev_config
      - ./db_snapshots:/mnt/snapshots
       
      - ddev-global-cache:/mnt/ddev-global-cache
    restart: "no"

    # The postgres image is set up for user 999, we won't try to change that.
    user:  '$DDEV_UID:$DDEV_GID' 
    hostname: life-db
    ports:
      - "127.0.0.1:$DDEV_HOST_DB_PORT:3306"
    labels:
      com.ddev.site-name: ${DDEV_SITENAME}
      com.ddev.platform: ddev
      com.ddev.app-type: magento2
      com.ddev.approot: $DDEV_APPROOT
    environment:
      - COLUMNS
      - BITNAMI_VOLUME_DIR=
      - DDEV_DATABASE
      - DDEV_DATABASE_FAMILY
      - DDEV_GOARCH
      - DDEV_GOOS
      - DDEV_HOSTNAME
      - DDEV_PHP_VERSION
      - DDEV_PRIMARY_URL
      - DDEV_PROJECT
      - DDEV_PROJECT_TYPE
      - DDEV_ROUTER_HTTP_PORT
      - DDEV_ROUTER_HTTPS_PORT
      - DDEV_SITENAME
      - DDEV_TLD
      - DOCKER_IP=127.0.0.1
      - HOST_DOCKER_INTERNAL_IP=
      - IS_DDEV_PROJECT=true
      - LINES
      - MYSQL_HISTFILE=/mnt/ddev-global-cache/mysqlhistory/${DDEV_SITENAME}-db/mysql_history
      - PGDATABASE=db
      - PGHOST=127.0.0.1
      - PGPASSWORD=db
      - PGUSER=db
      - POSTGRES_PASSWORD=db
      - POSTGRES_USER=db
      - POSTGRES_DB=db
      - TZ=Europe/Amsterdam
      - USER=jelmer
    command: ${DDEV_DB_CONTAINER_COMMAND}
    healthcheck:
      interval: "1s"
      retries: 70
      start_period: "120s"
      start_interval: "1s"
      timeout: "70s"
   

  web:
    container_name: ddev-${DDEV_SITENAME}-web
    build:
      context: './.webimageBuild'
      args:
        BASE_IMAGE: $DDEV_WEBIMAGE
        username: 'jelmer'
        uid: '502'
        gid: '20'
        DDEV_PHP_VERSION: ${DDEV_PHP_VERSION}
        DDEV_DATABASE: ${DDEV_DATABASE}
    image: ${DDEV_WEBIMAGE}-${DDEV_SITENAME}-built
    command: /pre-start.sh

    networks: ["default", "ddev_default"]
    cap_add:
      - SYS_PTRACE
    working_dir: "/var/www/html/"

    

    volumes:
      
      - type: bind
        source: ../
        target: /var/www/html
         
        consistency: cached
         
       
      
      - .:/mnt/ddev_config:ro
      - ./xhprof:/usr/local/bin/xhprof:rw
      
      - ddev-global-cache:/mnt/ddev-global-cache
      - ddev-ssh-agent_socket_dir:/home/<USER>

    restart: "no"
    user: '$DDEV_UID:$DDEV_GID'
    hostname: life-web

    ports:
      - "127.0.0.1:$DDEV_HOST_HTTP_PORT:80"
      - "127.0.0.1:$DDEV_HOST_HTTPS_PORT:443"
      - "127.0.0.1::8025"

    environment:
    - COLUMNS
    - COREPACK_ENABLE_DOWNLOAD_PROMPT=0
    - COREPACK_HOME=/mnt/ddev-global-cache/corepack
    - DOCROOT=${DDEV_DOCROOT}
    - DDEV_COMPOSER_ROOT
    - DDEV_DATABASE
    - DDEV_DOCROOT
    - DDEV_DATABASE_FAMILY
    - DDEV_GOARCH
    - DDEV_GOOS
    - DDEV_HOSTNAME
    - DDEV_MUTAGEN_ENABLED
    - DDEV_PHP_VERSION
    - DDEV_PRIMARY_URL
    - DDEV_PROJECT
    - DDEV_PROJECT_TYPE
    - DDEV_ROUTER_HTTP_PORT
    - DDEV_ROUTER_HTTPS_PORT
    - DDEV_SITENAME
    - DDEV_TLD
    - DDEV_FILES_DIR
    - DDEV_FILES_DIRS
    - DDEV_WEB_ENTRYPOINT=/mnt/ddev_config/web-entrypoint.d
    - DDEV_WEBSERVER_TYPE
    - DDEV_XDEBUG_ENABLED
    - DDEV_XHPROF_MODE
    - DDEV_VERSION
    - DEPLOY_NAME=local
    
    - DRUSH_OPTIONS_URI=$DDEV_PRIMARY_URL
    
    - DOCKER_IP=127.0.0.1
    - HOST_DOCKER_INTERNAL_IP=
    # HTTP_EXPOSE allows for ports accepting HTTP traffic to be accessible from <site>.ddev.site:<port>
    # To expose a container port to a different host port, define the port as hostPort:containerPort
    # You can optionally expose an HTTPS port option for any ports defined in HTTP_EXPOSE.
    # To expose an HTTPS port, define the port as securePort:containerPort.
    
    - HTTP_EXPOSE=${DDEV_ROUTER_HTTP_PORT}:80,${DDEV_MAILPIT_PORT}:8025
    - HTTPS_EXPOSE=${DDEV_ROUTER_HTTPS_PORT}:80,${DDEV_MAILPIT_HTTPS_PORT}:8025
    
    - IS_DDEV_PROJECT=true
    - LINES
    - MYSQL_HISTFILE=/mnt/ddev-global-cache/mysqlhistory/${DDEV_SITENAME}-web/mysql_history
    - NODE_EXTRA_CA_CERTS=/mnt/ddev-global-cache/mkcert/rootCA.pem
    - npm_config_cache=/mnt/ddev-global-cache/npm
    - PGDATABASE=db
    - PGHOST=db
    - PGPASSWORD=db
    - PGUSER=db
    - PHP_IDE_CONFIG=serverName=${DDEV_SITENAME}.${DDEV_TLD}
    - SSH_AUTH_SOCK=/home/<USER>/socket
    - TZ=Europe/Amsterdam
    - USER=jelmer
    - VIRTUAL_HOST=${DDEV_HOSTNAME}
    - START_SCRIPT_TIMEOUT=30
    
    labels:
      com.ddev.site-name: ${DDEV_SITENAME}
      com.ddev.platform: ddev
      com.ddev.app-type: magento2
      com.ddev.approot: $DDEV_APPROOT

      

      
    external_links:
      - "ddev-router:life.local"
      - "ddev-router:showroom.lifeoutdoorliving.com.local"
      - "ddev-router:www.lifeoutdoorliving.com.local"
      
      
    healthcheck:
      interval: "1s"
      retries: 70
      start_period: "120s"
      start_interval: "1s"
      timeout: "70s"
  
  xhgui:
    image: ddev/ddev-xhgui:v1.24.4
    container_name: ddev-${DDEV_SITENAME}-xhgui
    labels:
      com.ddev.site-name: ${DDEV_SITENAME}
      com.ddev.approot: $DDEV_APPROOT
    profiles:
      - xhgui
    restart: "no"
    

    environment:
      - VIRTUAL_HOST=$DDEV_HOSTNAME
      - HTTP_EXPOSE=8143:80
      - HTTPS_EXPOSE=8142:80
      - XHGUI_SAVE_HANDLER=pdo
      - DDEV_DATABASE_FAMILY=${DDEV_DATABASE_FAMILY}
      - XHGUI_PDO_USER=db
      - XHGUI_PDO_PASS=db
      - TZ=Europe/Amsterdam
    links:
      - db
    depends_on:
      - db
networks:
  ddev_default:
    name: ddev_default
    external: true
  default:
    name: ${COMPOSE_PROJECT_NAME}_default
    
    labels:
      com.ddev.platform: ddev

volumes:
  
  database:
    name: "life-mariadb"
    external: true
   

   

  
  ddev-ssh-agent_socket_dir:
    external: true
  
  ddev-global-cache:
    name: ddev-global-cache
    external: true
  
  
  
