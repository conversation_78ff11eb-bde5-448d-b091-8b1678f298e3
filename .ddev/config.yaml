name: life
type: magento2
docroot: pub
php_version: "8.1"
webserver_type: nginx-fpm
router_http_port: "80"
router_https_port: "443"
xdebug_enabled: false
project_tld: local
additional_hostnames:
  - www.lifeoutdoorliving.com
  - showroom.lifeoutdoorliving.com
additional_fqdns: []
database:
  type: mariadb
  version: "10.4"
performance_mode: none # Configure Docker to use VirtioFS
use_dns_when_possible: false
timezone: Europe/Amsterdam
hooks:
  post-start:
    - exec: rm -f /usr/local/bin/magerun
    - exec-host: sh .ddev/init-post.sh
  pre-start:
    - exec-host: sh .ddev/init-pre.sh
omit_containers: [dba]
composer_version: "2.2"
nodejs_version: "18"
web_environment: []
